{"version": 3, "sources": ["../src/request.js"], "names": ["process", "browser", "env", "ENVIRONMENT", "versions", "electron", "type", "XMLHttpRequest", "module", "exports", "options", "cb", "xhr", "open", "url", "responseType", "addEventListener", "status", "data", "<PERSON><PERSON><PERSON>", "from", "response", "error", "Error", "message", "e", "send", "p", "require", "compression", "err", "res", "body"], "mappings": ";;;;;;;;;;;;AAAA;AAEA,IACEA,OAAO,CAACC,OAAR,IACAD,OAAO,CAACE,GAAR,CAAYC,WAAZ,KAA4B,SAD5B,IAEC,OAAOH,OAAO,CAACI,QAAR,CAAiBC,QAAxB,KAAqC,WAArC,IACCL,OAAO,CAACM,IAAR,KAAiB,UADlB,IAEC,OAAOC,cAAP,KAA0B,UAL9B,EAME;AACA;AACA;AAEAC,EAAAA,MAAM,CAACC,OAAP,GAAiB,UAASC,OAAT,EAAkBC,EAAlB,EAAsB;AACrC,QAAMC,GAAG,GAAG,IAAIL,cAAJ,EAAZ;AACAK,IAAAA,GAAG,CAACC,IAAJ,CAAS,KAAT,EAAgBH,OAAO,CAACI,GAAxB,EAA6B,IAA7B;AACAF,IAAAA,GAAG,CAACG,YAAJ,GAAmB,aAAnB;AACAH,IAAAA,GAAG,CAACI,gBAAJ,CAAqB,MAArB,EAA6B,YAAW;AACtC,UAAIJ,GAAG,CAACK,MAAJ,GAAa,GAAjB,EAAsB;AACpB,YAAI;AACF,cAAMC,IAAI,GAAGC,MAAM,CAACC,IAAP,CAAY,KAAKC,QAAjB,CAAb;AACAV,UAAAA,EAAE,CAAC,IAAD,EAAOC,GAAP,EAAYM,IAAZ,CAAF;AACD,SAHD,CAGE,OAAOI,KAAP,EAAc;AACd,iBAAOX,EAAE,CACP,IAAIY,KAAJ,CACE,sCACEb,OAAO,CAACI,GADV,GAEE,WAFF,GAGEQ,KAAK,CAACE,OAJV,CADO,CAAT;AAQD;AACF,OAdD,MAcO;AACLb,QAAAA,EAAE,CAAC,IAAIY,KAAJ,CAAU,iBAAiBX,GAAG,CAACK,MAArB,GAA8B,WAA9B,GAA4CP,OAAO,CAACI,GAA9D,CAAD,CAAF;AACD;AACF,KAlBD;AAmBAF,IAAAA,GAAG,CAACI,gBAAJ,CAAqB,OAArB,EAA8B,UAAAS,CAAC,EAAI;AACjCd,MAAAA,EAAE,CAACc,CAAD,CAAF;AACD,KAFD;AAGAb,IAAAA,GAAG,CAACc,IAAJ;AACD,GA3BD;AA4BD,CAtCD,MAsCO;AACLlB,EAAAA,MAAM,CAACC,OAAP,GAAiB,gBAAyBE,EAAzB,EAA6B;AAAA,QAAfD,OAAe;;AAC5C,QAAMiB,CAAC,GAAGC,OAAO,CAAC,MAAD,CAAjB;;AAEAD,IAAAA,CAAC;AAAGE,MAAAA,WAAW,EAAE;AAAhB,OAAyBnB,OAAzB,GAAoC,UAACoB,GAAD,EAAMC,GAAN,EAAc;AACjD,UAAID,GAAG,KAAK,IAAZ,EAAkB;AAChBnB,QAAAA,EAAE,CAAC,IAAD,EAAOoB,GAAP,EAAYA,GAAG,CAACC,IAAhB,CAAF;AACD,OAFD,MAEO;AACLrB,QAAAA,EAAE,CAACmB,GAAD,CAAF;AACD;AACF,KANA,CAAD;AAOD,GAVD;AAWD", "sourcesContent": ["/* global XMLHttpRequest */\n\nif (\n  process.browser ||\n  process.env.ENVIRONMENT === 'BROWSER' ||\n  (typeof process.versions.electron !== 'undefined' &&\n    process.type === 'renderer' &&\n    typeof XMLHttpRequest === 'function')\n) {\n  // If we run into a browser or the electron renderer process,\n  // use XHR method instead of Request node module.\n\n  module.exports = function(options, cb) {\n    const xhr = new XMLHttpRequest();\n    xhr.open('GET', options.url, true);\n    xhr.responseType = 'arraybuffer';\n    xhr.addEventListener('load', function() {\n      if (xhr.status < 400) {\n        try {\n          const data = Buffer.from(this.response);\n          cb(null, xhr, data);\n        } catch (error) {\n          return cb(\n            new Error(\n              'Response is not a buffer for url ' +\n                options.url +\n                '. Error: ' +\n                error.message\n            )\n          );\n        }\n      } else {\n        cb(new Error('HTTP Status ' + xhr.status + ' for url ' + options.url));\n      }\n    });\n    xhr.addEventListener('error', e => {\n      cb(e);\n    });\n    xhr.send();\n  };\n} else {\n  module.exports = function({ ...options }, cb) {\n    const p = require('phin');\n\n    p({ compression: true, ...options }, (err, res) => {\n      if (err === null) {\n        cb(null, res, res.body);\n      } else {\n        cb(err);\n      }\n    });\n  };\n}\n"], "file": "request.js"}
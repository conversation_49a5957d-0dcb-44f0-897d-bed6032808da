package cn.zhentao;

import cn.zhentao.utils.MockArcFaceEngineUtil;
import cn.zhentao.utils.MockImageQualityChecker;
import cn.zhentao.service.MockMinioService;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;

/**
 * 简单测试类
 * 验证模拟实现类是否正常工作
 */
public class SimpleTest {

    @Test
    public void testMockArcFaceEngineUtil() {
        MockArcFaceEngineUtil util = new MockArcFaceEngineUtil();
        
        try {
            // 创建一个临时测试文件
            File tempFile = File.createTempFile("test", ".jpg");
            Files.write(tempFile.toPath(), "test image data".getBytes());
            
            // 测试人脸特征提取
            MockArcFaceEngineUtil.MockFaceFeature feature = util.getFaceFeature(tempFile);
            System.out.println("人脸特征提取测试: " + (feature != null ? "成功" : "失败"));
            
            if (feature != null) {
                // 测试人脸比对
                MockArcFaceEngineUtil.MockFaceFeature feature2 = util.getFaceFeature(tempFile);
                MockArcFaceEngineUtil.MockFaceSimilar similar = util.compareFaceFeature(feature, feature2);
                System.out.println("人脸比对测试: 相似度 = " + similar.getScore());
            }
            
            // 清理临时文件
            tempFile.delete();
            
        } catch (IOException e) {
            System.err.println("测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testMockImageQualityChecker() {
        MockImageQualityChecker checker = new MockImageQualityChecker();
        
        byte[] testData = "test image data".getBytes();
        MockImageQualityChecker.ImageQualityResult result = checker.checkImageQuality(testData);
        
        System.out.println("图片质量检查测试: " + (result.isValid() ? "通过" : "失败"));
        System.out.println("检查结果: " + result);
    }

    @Test
    public void testMockMinioService() {
        MockMinioService service = new MockMinioService();
        
        try {
            byte[] testData = "test file content".getBytes();
            String fileName = service.uploadFile(testData, "test.txt", "text/plain");
            
            System.out.println("文件上传测试: " + (fileName != null ? "成功" : "失败"));
            System.out.println("上传文件名: " + fileName);
            
            boolean exists = service.fileExists(fileName);
            System.out.println("文件存在检查: " + (exists ? "存在" : "不存在"));
            
            if (exists) {
                long size = service.getFileSize(fileName);
                System.out.println("文件大小: " + size + " 字节");
                
                // 清理测试文件
                service.deleteFile(fileName);
                System.out.println("文件清理: 完成");
            }
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
        }
    }

    public static void main(String[] args) {
        SimpleTest test = new SimpleTest();
        
        System.out.println("=== 开始模拟实现类测试 ===");
        
        System.out.println("\n1. 测试MockArcFaceEngineUtil:");
        test.testMockArcFaceEngineUtil();
        
        System.out.println("\n2. 测试MockImageQualityChecker:");
        test.testMockImageQualityChecker();
        
        System.out.println("\n3. 测试MockMinioService:");
        test.testMockMinioService();
        
        System.out.println("\n=== 测试完成 ===");
    }
}

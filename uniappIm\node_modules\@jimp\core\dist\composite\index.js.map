{"version": 3, "sources": ["../../src/composite/index.js"], "names": ["composite", "src", "x", "y", "options", "cb", "constructor", "throwError", "call", "mode", "opacitySource", "opacityDest", "constants", "BLEND_SOURCE_OVER", "blendmode", "compositeModes", "Math", "round", "baseImage", "opacity", "scanQuiet", "bitmap", "width", "height", "sx", "sy", "idx", "dstIdx", "getPixelIndex", "EDGE_CROP", "blended", "r", "data", "g", "b", "a", "limit255"], "mappings": ";;;;;;;;;AAAA;;AACA;;AAEA;;AAEA;;;;;;;;;AASe,SAASA,SAAT,CAAmBC,GAAnB,EAAwBC,CAAxB,EAA2BC,CAA3B,EAAgD;AAAA,MAAlBC,OAAkB,uEAAR,EAAQ;AAAA,MAAJC,EAAI;;AAC7D,MAAI,OAAOD,OAAP,KAAmB,UAAvB,EAAmC;AACjCC,IAAAA,EAAE,GAAGD,OAAL;AACAA,IAAAA,OAAO,GAAG,EAAV;AACD;;AAED,MAAI,EAAEH,GAAG,YAAY,KAAKK,WAAtB,CAAJ,EAAwC;AACtC,WAAOC,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,iCAAtB,EAAyDH,EAAzD,CAAP;AACD;;AAED,MAAI,OAAOH,CAAP,KAAa,QAAb,IAAyB,OAAOC,CAAP,KAAa,QAA1C,EAAoD;AAClD,WAAOI,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,yBAAtB,EAAiDH,EAAjD,CAAP;AACD;;AAZ4D,iBAclBD,OAdkB;AAAA,MAcvDK,IAduD,YAcvDA,IAduD;AAAA,MAcjDC,aAdiD,YAcjDA,aAdiD;AAAA,MAclCC,WAdkC,YAclCA,WAdkC;;AAgB7D,MAAI,CAACF,IAAL,EAAW;AACTA,IAAAA,IAAI,GAAGG,SAAS,CAACC,iBAAjB;AACD;;AAED,MACE,OAAOH,aAAP,KAAyB,QAAzB,IACAA,aAAa,GAAG,CADhB,IAEAA,aAAa,GAAG,CAHlB,EAIE;AACAA,IAAAA,aAAa,GAAG,GAAhB;AACD;;AAED,MAAI,OAAOC,WAAP,KAAuB,QAAvB,IAAmCA,WAAW,GAAG,CAAjD,IAAsDA,WAAW,GAAG,CAAxE,EAA2E;AACzEA,IAAAA,WAAW,GAAG,GAAd;AACD;;AAED,MAAMG,SAAS,GAAGC,cAAc,CAACN,IAAD,CAAhC,CAhC6D,CAkC7D;;AACAP,EAAAA,CAAC,GAAGc,IAAI,CAACC,KAAL,CAAWf,CAAX,CAAJ;AACAC,EAAAA,CAAC,GAAGa,IAAI,CAACC,KAAL,CAAWd,CAAX,CAAJ;AAEA,MAAMe,SAAS,GAAG,IAAlB;;AAEA,MAAIP,WAAW,KAAK,GAApB,EAAyB;AACvBO,IAAAA,SAAS,CAACC,OAAV,CAAkBR,WAAlB;AACD;;AAEDV,EAAAA,GAAG,CAACmB,SAAJ,CAAc,CAAd,EAAiB,CAAjB,EAAoBnB,GAAG,CAACoB,MAAJ,CAAWC,KAA/B,EAAsCrB,GAAG,CAACoB,MAAJ,CAAWE,MAAjD,EAAyD,UACvDC,EADuD,EAEvDC,EAFuD,EAGvDC,GAHuD,EAIvD;AACA,QAAMC,MAAM,GAAGT,SAAS,CAACU,aAAV,CAAwB1B,CAAC,GAAGsB,EAA5B,EAAgCrB,CAAC,GAAGsB,EAApC,EAAwCb,SAAS,CAACiB,SAAlD,CAAf;AACA,QAAMC,OAAO,GAAGhB,SAAS,CACvB;AACEiB,MAAAA,CAAC,EAAE,KAAKV,MAAL,CAAYW,IAAZ,CAAiBN,GAAG,GAAG,CAAvB,IAA4B,GADjC;AAEEO,MAAAA,CAAC,EAAE,KAAKZ,MAAL,CAAYW,IAAZ,CAAiBN,GAAG,GAAG,CAAvB,IAA4B,GAFjC;AAGEQ,MAAAA,CAAC,EAAE,KAAKb,MAAL,CAAYW,IAAZ,CAAiBN,GAAG,GAAG,CAAvB,IAA4B,GAHjC;AAIES,MAAAA,CAAC,EAAE,KAAKd,MAAL,CAAYW,IAAZ,CAAiBN,GAAG,GAAG,CAAvB,IAA4B;AAJjC,KADuB,EAOvB;AACEK,MAAAA,CAAC,EAAEb,SAAS,CAACG,MAAV,CAAiBW,IAAjB,CAAsBL,MAAM,GAAG,CAA/B,IAAoC,GADzC;AAEEM,MAAAA,CAAC,EAAEf,SAAS,CAACG,MAAV,CAAiBW,IAAjB,CAAsBL,MAAM,GAAG,CAA/B,IAAoC,GAFzC;AAGEO,MAAAA,CAAC,EAAEhB,SAAS,CAACG,MAAV,CAAiBW,IAAjB,CAAsBL,MAAM,GAAG,CAA/B,IAAoC,GAHzC;AAIEQ,MAAAA,CAAC,EAAEjB,SAAS,CAACG,MAAV,CAAiBW,IAAjB,CAAsBL,MAAM,GAAG,CAA/B,IAAoC;AAJzC,KAPuB,EAavBjB,aAbuB,CAAzB;AAgBAQ,IAAAA,SAAS,CAACG,MAAV,CAAiBW,IAAjB,CAAsBL,MAAM,GAAG,CAA/B,IAAoC,KAAKrB,WAAL,CAAiB8B,QAAjB,CAClCN,OAAO,CAACC,CAAR,GAAY,GADsB,CAApC;AAGAb,IAAAA,SAAS,CAACG,MAAV,CAAiBW,IAAjB,CAAsBL,MAAM,GAAG,CAA/B,IAAoC,KAAKrB,WAAL,CAAiB8B,QAAjB,CAClCN,OAAO,CAACG,CAAR,GAAY,GADsB,CAApC;AAGAf,IAAAA,SAAS,CAACG,MAAV,CAAiBW,IAAjB,CAAsBL,MAAM,GAAG,CAA/B,IAAoC,KAAKrB,WAAL,CAAiB8B,QAAjB,CAClCN,OAAO,CAACI,CAAR,GAAY,GADsB,CAApC;AAGAhB,IAAAA,SAAS,CAACG,MAAV,CAAiBW,IAAjB,CAAsBL,MAAM,GAAG,CAA/B,IAAoC,KAAKrB,WAAL,CAAiB8B,QAAjB,CAClCN,OAAO,CAACK,CAAR,GAAY,GADsB,CAApC;AAGD,GAlCD;;AAoCA,MAAI,0BAAc9B,EAAd,CAAJ,EAAuB;AACrBA,IAAAA,EAAE,CAACG,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,SAAO,IAAP;AACD", "sourcesContent": ["import { isNodePattern, throwError } from '@jimp/utils';\nimport * as constants from '../constants';\n\nimport * as compositeModes from './composite-modes';\n\n/**\n * Composites a source image over to this image respecting alpha channels\n * @param {Jimp} src the source Jimp instance\n * @param {number} x the x position to blit the image\n * @param {number} y the y position to blit the image\n * @param {object} options determine what mode to use\n * @param {function(<PERSON><PERSON><PERSON>, Jimp)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\nexport default function composite(src, x, y, options = {}, cb) {\n  if (typeof options === 'function') {\n    cb = options;\n    options = {};\n  }\n\n  if (!(src instanceof this.constructor)) {\n    return throwError.call(this, 'The source must be a Jimp image', cb);\n  }\n\n  if (typeof x !== 'number' || typeof y !== 'number') {\n    return throwError.call(this, 'x and y must be numbers', cb);\n  }\n\n  let { mode, opacitySource, opacityDest } = options;\n\n  if (!mode) {\n    mode = constants.BLEND_SOURCE_OVER;\n  }\n\n  if (\n    typeof opacitySource !== 'number' ||\n    opacitySource < 0 ||\n    opacitySource > 1\n  ) {\n    opacitySource = 1.0;\n  }\n\n  if (typeof opacityDest !== 'number' || opacityDest < 0 || opacityDest > 1) {\n    opacityDest = 1.0;\n  }\n\n  const blendmode = compositeModes[mode];\n\n  // round input\n  x = Math.round(x);\n  y = Math.round(y);\n\n  const baseImage = this;\n\n  if (opacityDest !== 1.0) {\n    baseImage.opacity(opacityDest);\n  }\n\n  src.scanQuiet(0, 0, src.bitmap.width, src.bitmap.height, function(\n    sx,\n    sy,\n    idx\n  ) {\n    const dstIdx = baseImage.getPixelIndex(x + sx, y + sy, constants.EDGE_CROP);\n    const blended = blendmode(\n      {\n        r: this.bitmap.data[idx + 0] / 255,\n        g: this.bitmap.data[idx + 1] / 255,\n        b: this.bitmap.data[idx + 2] / 255,\n        a: this.bitmap.data[idx + 3] / 255\n      },\n      {\n        r: baseImage.bitmap.data[dstIdx + 0] / 255,\n        g: baseImage.bitmap.data[dstIdx + 1] / 255,\n        b: baseImage.bitmap.data[dstIdx + 2] / 255,\n        a: baseImage.bitmap.data[dstIdx + 3] / 255\n      },\n      opacitySource\n    );\n\n    baseImage.bitmap.data[dstIdx + 0] = this.constructor.limit255(\n      blended.r * 255\n    );\n    baseImage.bitmap.data[dstIdx + 1] = this.constructor.limit255(\n      blended.g * 255\n    );\n    baseImage.bitmap.data[dstIdx + 2] = this.constructor.limit255(\n      blended.b * 255\n    );\n    baseImage.bitmap.data[dstIdx + 3] = this.constructor.limit255(\n      blended.a * 255\n    );\n  });\n\n  if (isNodePattern(cb)) {\n    cb.call(this, null, this);\n  }\n\n  return this;\n}\n"], "file": "index.js"}
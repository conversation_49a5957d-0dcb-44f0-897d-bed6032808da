"use strict";var e=require("debug"),t=require("postcss-selector-parser");function o(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var n=o(e),r=o(t);const s=n.default("automator:devtool");function a(e){e.walk((e=>{if("tag"===e.type){const t=e.value;e.value="page"===t?"uni-page-body":"uni-"+t}}))}const i=["Page.getElement","Page.getElements","Element.getElement","Element.getElements"];function c(e){try{return require(e)}catch(t){return require(require.resolve(e,{paths:[process.cwd()]}))}}/^win/.test(process.platform);const p=["chromium","firefox","webkit"];let l=!1;try{l=!!c("playwright")}catch(e){}const u=new Map;function f(e="chromium"){const t=e&&p.includes(e)?e:p[0];let o=u.get(t);return o||(o=function(e){if("webkit"===e)return h("webkit");if("firefox"===e)return h("firefox");return l?h("chromium"):function(){const e=c("puppeteer");let t,o;return{type:"chromium",provider:"puppeteer",async open(n,r,a){t=await e.launch(r.options);const i=t.process();i?s("%s %o",i.spawnfile,r.options):s("%o",r.options),o=await t.newPage(),o.on("console",(e=>{a.emit("App.logAdded",{type:e.type(),args:[e.text()]})})),o.on("pageerror",(e=>{a.emit("App.exceptionThrown",e)})),await o.goto(r.url||n),await o.waitFor(1e3)},close:()=>t.close(),screenshot:(e=!1)=>o.screenshot({encoding:"base64",fullPage:e})}}()}(t),u.set(t,o)),o}function h(e){const t=c("playwright");let o,n;return{type:e,provider:"playwright",async open(r,a,i){o=await t[e].launch(a.options),"firefox"===e&&(a.contextOptions.isMobile=!1),s(`browser.newContext ${JSON.stringify(a.contextOptions)}`);const c=await o.newContext(a.contextOptions);n=await c.newPage(),n.on("console",(e=>{i.emit("App.logAdded",{type:e.type(),args:[e.text()]})})),n.on("pageerror",(e=>{i.emit("App.exceptionThrown",e)})),await n.goto(a.url||r),await n.waitForTimeout(1e3)},close:()=>o.close(),screenshot:(e=!1)=>n.screenshot({fullPage:e}).then((e=>e.toString("base64")))}}let w;const g={"Tool.close":{reflect:async()=>{await w.close()}},"App.exit":{reflect:async()=>{}},"App.enableLog":{reflect:()=>Promise.resolve()},"App.captureScreenshot":{reflect:async(e,t)=>{const o=await w.screenshot(!!t.fullPage);return s(`App.captureScreenshot ${o.length}`),{data:o}}}};!function(e){i.forEach((t=>{e[t]=function(e){return{reflect:async(t,o)=>t(e,o,!1),params:e=>(e.selector&&(e.selector=r.default(a).processSync(e.selector)),e)}}(t)}))}(g);const d={devtools:{name:"browser",paths:[],validate:async function(e){return e.options=e.options||{},e.executablePath&&!e.options.executablePath&&(e.options.executablePath=e.executablePath),e.contextOptions={viewport:Object.assign({width:375,height:667},e.options.defaultViewport||{}),hasTouch:!0,isMobile:!0,deviceScaleFactor:2},e.options.defaultViewport=Object.assign({width:375,height:667,deviceScaleFactor:2,hasTouch:!0,isMobile:!0},e.options.defaultViewport||{}),e.teardown||(e.teardown=!1===e.options.headless?"disconnect":"close"),e},create:async function(e,t,o){w=f(process.env.BROWSER),s("createDevtools "+(w.provider+" "+w.type+" "+JSON.stringify(t))),await w.open(e,t,o)}},shouldCompile:(e,t)=>!t.url,adapter:g};module.exports=d;

<template>
  <view class="debug-page">
    <view class="header">
      <text class="title">调试页面</text>
    </view>
    
    <view class="section">
      <button @click="testFriendListAPI" class="test-btn">测试好友列表API</button>
    </view>
    
    <view class="section">
      <text class="section-title">API响应数据：</text>
      <view class="data-display">
        <text class="data-text">{{ apiResponse }}</text>
      </view>
    </view>
    
    <view class="section">
      <text class="section-title">好友列表：</text>
      <view v-for="friend in friendList" :key="friend.userId" class="friend-item">
        <text class="friend-text">ID: {{ friend.userId }}</text>
        <text class="friend-text">用户名: {{ friend.username }}</text>
        <text class="friend-text">昵称: {{ friend.nickname }}</text>
        <text class="friend-text">备注: {{ friend.remark || '无' }}</text>
        <text class="friend-text">显示名: {{ getDisplayName(friend) }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { getFriendList } from '@/utils/api.js'

export default {
  data() {
    return {
      apiResponse: '',
      friendList: []
    }
  },
  
  methods: {
    async testFriendListAPI() {
      try {
        const result = await getFriendList()
        this.apiResponse = JSON.stringify(result, null, 2)
        
        if (result.code === 200) {
          this.friendList = result.data || []
        }
      } catch (error) {
        this.apiResponse = `错误: ${error.message}`
      }
    },
    
    getDisplayName(friend) {
      if (friend.remark && friend.remark.trim()) {
        return friend.remark
      }
      return friend.nickname || friend.username || '未知用户'
    }
  }
}
</script>

<style scoped>
.debug-page {
  padding: 40rpx;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
}

.section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
}

.test-btn {
  width: 100%;
  padding: 30rpx;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
}

.data-display {
  background: #f5f5f5;
  padding: 20rpx;
  border-radius: 16rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.data-text {
  font-size: 24rpx;
  color: #333;
  white-space: pre-wrap;
  word-break: break-all;
}

.friend-item {
  background: #fff;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  border: 1rpx solid #eee;
}

.friend-text {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}
</style>

{"name": "invert-kv", "version": "3.0.1", "description": "Invert the key/value of an object. Example: `{foo: 'bar'}` → `{bar: 'foo'}`", "license": "MIT", "repository": "sindresorhus/invert-kv", "funding": "https://github.com/sindresorhus/invert-kv?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["object", "key", "value", "invert", "keys", "values"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}
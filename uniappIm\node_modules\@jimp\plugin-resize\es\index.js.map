{"version": 3, "sources": ["../src/index.js"], "names": ["constants", "RESIZE_NEAREST_NEIGHBOR", "RESIZE_BILINEAR", "RESIZE_BICUBIC", "RESIZE_HERMITE", "RESIZE_BEZIER", "resize", "w", "h", "mode", "cb", "throwError", "call", "constructor", "AUTO", "bitmap", "width", "height", "Math", "round", "Resize2", "dst", "data", "<PERSON><PERSON><PERSON>", "alloc", "image", "Resize", "buffer", "from"], "mappings": ";;;;;;;;;AAAA;;AAEA;;AACA;;eAEe;AAAA,SAAO;AACpBA,IAAAA,SAAS,EAAE;AACTC,MAAAA,uBAAuB,EAAE,iBADhB;AAETC,MAAAA,eAAe,EAAE,uBAFR;AAGTC,MAAAA,cAAc,EAAE,sBAHP;AAITC,MAAAA,cAAc,EAAE,sBAJP;AAKTC,MAAAA,aAAa,EAAE;AALN,KADS;AASpB,aAAO;AACL;;;;;;;;AAQAC,MAAAA,MATK,kBASEC,CATF,EASKC,CATL,EASQC,IATR,EAScC,EATd,EASkB;AACrB,YAAI,OAAOH,CAAP,KAAa,QAAb,IAAyB,OAAOC,CAAP,KAAa,QAA1C,EAAoD;AAClD,iBAAOG,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,yBAAtB,EAAiDF,EAAjD,CAAP;AACD;;AAED,YAAI,OAAOD,IAAP,KAAgB,UAAhB,IAA8B,OAAOC,EAAP,KAAc,WAAhD,EAA6D;AAC3DA,UAAAA,EAAE,GAAGD,IAAL;AACAA,UAAAA,IAAI,GAAG,IAAP;AACD;;AAED,YAAIF,CAAC,KAAK,KAAKM,WAAL,CAAiBC,IAAvB,IAA+BN,CAAC,KAAK,KAAKK,WAAL,CAAiBC,IAA1D,EAAgE;AAC9D,iBAAOH,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,oCAAtB,EAA4DF,EAA5D,CAAP;AACD;;AAED,YAAIH,CAAC,KAAK,KAAKM,WAAL,CAAiBC,IAA3B,EAAiC;AAC/BP,UAAAA,CAAC,GAAG,KAAKQ,MAAL,CAAYC,KAAZ,IAAqBR,CAAC,GAAG,KAAKO,MAAL,CAAYE,MAArC,CAAJ;AACD;;AAED,YAAIT,CAAC,KAAK,KAAKK,WAAL,CAAiBC,IAA3B,EAAiC;AAC/BN,UAAAA,CAAC,GAAG,KAAKO,MAAL,CAAYE,MAAZ,IAAsBV,CAAC,GAAG,KAAKQ,MAAL,CAAYC,KAAtC,CAAJ;AACD;;AAED,YAAIT,CAAC,GAAG,CAAJ,IAASC,CAAC,GAAG,CAAjB,EAAoB;AAClB,iBAAOG,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,kCAAtB,EAA0DF,EAA1D,CAAP;AACD,SAxBoB,CA0BrB;;;AACAH,QAAAA,CAAC,GAAGW,IAAI,CAACC,KAAL,CAAWZ,CAAX,CAAJ;AACAC,QAAAA,CAAC,GAAGU,IAAI,CAACC,KAAL,CAAWX,CAAX,CAAJ;;AAEA,YAAI,OAAOY,oBAAQX,IAAR,CAAP,KAAyB,UAA7B,EAAyC;AACvC,cAAMY,GAAG,GAAG;AACVC,YAAAA,IAAI,EAAEC,MAAM,CAACC,KAAP,CAAajB,CAAC,GAAGC,CAAJ,GAAQ,CAArB,CADI;AAEVQ,YAAAA,KAAK,EAAET,CAFG;AAGVU,YAAAA,MAAM,EAAET;AAHE,WAAZ;;AAKAY,8BAAQX,IAAR,EAAc,KAAKM,MAAnB,EAA2BM,GAA3B;;AACA,eAAKN,MAAL,GAAcM,GAAd;AACD,SARD,MAQO;AACL,cAAMI,KAAK,GAAG,IAAd;AACA,cAAMnB,MAAM,GAAG,IAAIoB,kBAAJ,CACb,KAAKX,MAAL,CAAYC,KADC,EAEb,KAAKD,MAAL,CAAYE,MAFC,EAGbV,CAHa,EAIbC,CAJa,EAKb,IALa,EAMb,IANa,EAOb,UAAAmB,MAAM,EAAI;AACRF,YAAAA,KAAK,CAACV,MAAN,CAAaO,IAAb,GAAoBC,MAAM,CAACK,IAAP,CAAYD,MAAZ,CAApB;AACAF,YAAAA,KAAK,CAACV,MAAN,CAAaC,KAAb,GAAqBT,CAArB;AACAkB,YAAAA,KAAK,CAACV,MAAN,CAAaE,MAAb,GAAsBT,CAAtB;AACD,WAXY,CAAf;AAaAF,UAAAA,MAAM,CAACA,MAAP,CAAc,KAAKS,MAAL,CAAYO,IAA1B;AACD;;AAED,YAAI,0BAAcZ,EAAd,CAAJ,EAAuB;AACrBA,UAAAA,EAAE,CAACE,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,eAAO,IAAP;AACD;AAtEI;AATa,GAAP;AAAA,C", "sourcesContent": ["import { throwError, isNodePattern } from '@jimp/utils';\n\nimport Resize from './modules/resize';\nimport Resize2 from './modules/resize2';\n\nexport default () => ({\n  constants: {\n    RESIZE_NEAREST_NEIGHBOR: 'nearestNeighbor',\n    RESIZE_BILINEAR: 'bilinearInterpolation',\n    RESIZE_BICUBIC: 'bicubicInterpolation',\n    RESIZE_HERMITE: 'hermiteInterpolation',\n    RESIZE_BEZIER: 'bezierInterpolation'\n  },\n\n  class: {\n    /**\n     * Resizes the image to a set width and height using a 2-pass bilinear algorithm\n     * @param {number} w the width to resize the image to (or Jimp.AUTO)\n     * @param {number} h the height to resize the image to (or Jimp.AUTO)\n     * @param {string} mode (optional) a scaling method (e.g. Jimp.RESIZE_BEZIER)\n     * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n     * @returns {Jimp} this for chaining of methods\n     */\n    resize(w, h, mode, cb) {\n      if (typeof w !== 'number' || typeof h !== 'number') {\n        return throwError.call(this, 'w and h must be numbers', cb);\n      }\n\n      if (typeof mode === 'function' && typeof cb === 'undefined') {\n        cb = mode;\n        mode = null;\n      }\n\n      if (w === this.constructor.AUTO && h === this.constructor.AUTO) {\n        return throwError.call(this, 'w and h cannot both be set to auto', cb);\n      }\n\n      if (w === this.constructor.AUTO) {\n        w = this.bitmap.width * (h / this.bitmap.height);\n      }\n\n      if (h === this.constructor.AUTO) {\n        h = this.bitmap.height * (w / this.bitmap.width);\n      }\n\n      if (w < 0 || h < 0) {\n        return throwError.call(this, 'w and h must be positive numbers', cb);\n      }\n\n      // round inputs\n      w = Math.round(w);\n      h = Math.round(h);\n\n      if (typeof Resize2[mode] === 'function') {\n        const dst = {\n          data: Buffer.alloc(w * h * 4),\n          width: w,\n          height: h\n        };\n        Resize2[mode](this.bitmap, dst);\n        this.bitmap = dst;\n      } else {\n        const image = this;\n        const resize = new Resize(\n          this.bitmap.width,\n          this.bitmap.height,\n          w,\n          h,\n          true,\n          true,\n          buffer => {\n            image.bitmap.data = Buffer.from(buffer);\n            image.bitmap.width = w;\n            image.bitmap.height = h;\n          }\n        );\n        resize.resize(this.bitmap.data);\n      }\n\n      if (isNodePattern(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    }\n  }\n});\n"], "file": "index.js"}
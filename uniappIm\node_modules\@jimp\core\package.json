{"name": "@jimp/core", "version": "0.10.3", "description": "<PERSON><PERSON> core", "main": "dist/index.js", "module": "es/index.js", "types": "types/index.d.ts", "files": ["dist", "es", "index.d.ts", "fonts", "types"], "repository": {"type": "git", "url": "https://github.com/oliver-moran/jimp.git"}, "bugs": {"url": "https://github.com/oliver-moran/jimp/issues"}, "scripts": {"test": "cross-env BABEL_ENV=test mocha --require @babel/register test/**/*.js", "test:watch": "npm run test -- --reporter min --watch", "test:coverage": "nyc npm run test", "build": "npm run build:node:production && npm run build:module", "build:watch": "npm run build:node:debug -- -- --watch --verbose", "build:debug": "npm run build:node:debug", "build:module": "cross-env BABEL_ENV=module babel src -d es --source-maps --config-file ../../babel.config.js", "build:node": "babel src -d dist --source-maps --config-file ../../babel.config.js", "build:node:debug": "cross-env BABEL_ENV=development npm run build:node", "build:node:production": "cross-env BABEL_ENV=production npm run build:node"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"@babel/runtime": "^7.7.2", "@jimp/utils": "^0.10.3", "any-base": "^1.1.0", "buffer": "^5.2.0", "core-js": "^3.4.1", "exif-parser": "^0.1.12", "file-type": "^9.0.0", "load-bmfont": "^1.3.1", "mkdirp": "^0.5.1", "phin": "^2.9.1", "pixelmatch": "^4.0.2", "tinycolor2": "^1.4.1"}, "devDependencies": {"should": "^13.2.3"}, "xo": false, "publishConfig": {"access": "public"}, "gitHead": "37197106eae5c26231018dfdc0254422f6b43927"}
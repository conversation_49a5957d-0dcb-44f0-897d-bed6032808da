<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小程序AI功能测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .function-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #fafafa;
        }
        .function-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        .function-icon {
            width: 24px;
            height: 24px;
            margin-right: 8px;
            border-radius: 4px;
        }
        .input-group {
            margin-bottom: 10px;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        .input-group input, .input-group textarea, .input-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .input-group textarea {
            height: 80px;
            resize: vertical;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
        .response-area {
            margin-top: 15px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            min-height: 100px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            margin-top: 10px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.loading {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        .header p {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 小程序AI功能测试</h1>
        <p>测试AiTutor项目中的六大AI功能模块</p>
    </div>

    <!-- 1. 知识问答 -->
    <div class="container">
        <div class="function-card">
            <div class="function-title">
                <div class="function-icon" style="background-color: #4CAF50;">📚</div>
                知识问答 - AI百科全书
            </div>
            <div class="input-group">
                <label>用户ID:</label>
                <input type="number" id="knowledge-userId" value="123" />
            </div>
            <div class="input-group">
                <label>问题:</label>
                <textarea id="knowledge-question" placeholder="请输入您的问题...">什么是人工智能？</textarea>
            </div>
            <div class="button-group">
                <button class="btn btn-primary" onclick="testKnowledgeQA()">普通问答</button>
                <button class="btn btn-secondary" onclick="testKnowledgeQAStream()">流式问答</button>
            </div>
            <div id="knowledge-status" class="status" style="display: none;"></div>
            <div id="knowledge-response" class="response-area"></div>
        </div>
    </div>

    <!-- 2. 信息查询 -->
    <div class="container">
        <div class="function-card">
            <div class="function-title">
                <div class="function-icon" style="background-color: #2196F3;">🔍</div>
                信息查询 - 天气电话资讯
            </div>
            <div class="input-group">
                <label>用户ID:</label>
                <input type="number" id="info-userId" value="123" />
            </div>
            <div class="input-group">
                <label>查询内容:</label>
                <textarea id="info-query" placeholder="请输入查询内容...">北京今天的天气怎么样？</textarea>
            </div>
            <div class="button-group">
                <button class="btn btn-primary" onclick="testInfoQuery()">普通查询</button>
                <button class="btn btn-secondary" onclick="testInfoQueryStream()">流式查询</button>
            </div>
            <div id="info-status" class="status" style="display: none;"></div>
            <div id="info-response" class="response-area"></div>
        </div>
    </div>

    <!-- 3. 文本生成 -->
    <div class="container">
        <div class="function-card">
            <div class="function-title">
                <div class="function-icon" style="background-color: #FF9800;">✍️</div>
                文本生成 - 作文故事诗歌
            </div>
            <div class="input-group">
                <label>用户ID:</label>
                <input type="number" id="text-userId" value="123" />
            </div>
            <div class="input-group">
                <label>生成类型:</label>
                <select id="text-type">
                    <option value="essay">作文</option>
                    <option value="story">故事</option>
                    <option value="poem">诗歌</option>
                    <option value="general">通用</option>
                </select>
            </div>
            <div class="input-group">
                <label>创作要求:</label>
                <textarea id="text-prompt" placeholder="请输入创作要求...">写一篇关于春天的作文</textarea>
            </div>
            <div class="button-group">
                <button class="btn btn-primary" onclick="testTextGeneration()">普通生成</button>
                <button class="btn btn-secondary" onclick="testTextGenerationStream()">流式生成</button>
            </div>
            <div id="text-status" class="status" style="display: none;"></div>
            <div id="text-response" class="response-area"></div>
        </div>
    </div>

    <!-- 4. 语言翻译 -->
    <div class="container">
        <div class="function-card">
            <div class="function-title">
                <div class="function-icon" style="background-color: #9C27B0;">🌐</div>
                语言翻译 - 多语言互译
            </div>
            <div class="input-group">
                <label>用户ID:</label>
                <input type="number" id="translate-userId" value="123" />
            </div>
            <div class="input-group">
                <label>原文:</label>
                <textarea id="translate-text" placeholder="请输入要翻译的文本...">Hello, how are you today?</textarea>
            </div>
            <div class="input-group">
                <label>源语言:</label>
                <select id="translate-fromLang">
                    <option value="英文">英文</option>
                    <option value="中文">中文</option>
                    <option value="日文">日文</option>
                    <option value="韩文">韩文</option>
                </select>
            </div>
            <div class="input-group">
                <label>目标语言:</label>
                <select id="translate-toLang">
                    <option value="中文">中文</option>
                    <option value="英文">英文</option>
                    <option value="日文">日文</option>
                    <option value="韩文">韩文</option>
                </select>
            </div>
            <div class="button-group">
                <button class="btn btn-primary" onclick="testTranslation()">普通翻译</button>
                <button class="btn btn-secondary" onclick="testTranslationStream()">流式翻译</button>
            </div>
            <div id="translate-status" class="status" style="display: none;"></div>
            <div id="translate-response" class="response-area"></div>
        </div>
    </div>

    <!-- 5. 情感陪伴 -->
    <div class="container">
        <div class="function-card">
            <div class="function-title">
                <div class="function-icon" style="background-color: #E91E63;">❤️</div>
                情感陪伴 - 情感识别回应
            </div>
            <div class="input-group">
                <label>用户ID:</label>
                <input type="number" id="emotion-userId" value="123" />
            </div>
            <div class="input-group">
                <label>情感表达:</label>
                <textarea id="emotion-message" placeholder="请表达您的情感...">我今天心情不太好，感觉很沮丧</textarea>
            </div>
            <div class="button-group">
                <button class="btn btn-primary" onclick="testEmotionalCompanion()">普通陪伴</button>
                <button class="btn btn-secondary" onclick="testEmotionalCompanionStream()">流式陪伴</button>
            </div>
            <div id="emotion-status" class="status" style="display: none;"></div>
            <div id="emotion-response" class="response-area"></div>
        </div>
    </div>

    <!-- 6. 智能推荐 -->
    <div class="container">
        <div class="function-card">
            <div class="function-title">
                <div class="function-icon" style="background-color: #FF5722;">🎯</div>
                智能推荐 - 个性化内容
            </div>
            <div class="input-group">
                <label>用户ID:</label>
                <input type="number" id="recommend-userId" value="123" />
            </div>
            <div class="input-group">
                <label>个人偏好:</label>
                <textarea id="recommend-preferences" placeholder="请描述您的偏好...">喜欢科幻小说，对人工智能感兴趣</textarea>
            </div>
            <div class="input-group">
                <label>推荐类别:</label>
                <select id="recommend-category">
                    <option value="书籍">书籍</option>
                    <option value="电影">电影</option>
                    <option value="音乐">音乐</option>
                    <option value="活动">活动</option>
                    <option value="课程">课程</option>
                    <option value="美食">美食</option>
                </select>
            </div>
            <div class="button-group">
                <button class="btn btn-primary" onclick="testRecommendation()">普通推荐</button>
                <button class="btn btn-secondary" onclick="testRecommendationStream()">流式推荐</button>
            </div>
            <div id="recommend-status" class="status" style="display: none;"></div>
            <div id="recommend-response" class="response-area"></div>
        </div>
    </div>

    <script>
        const API_BASE = '/api/ai/miniprogram';

        // 显示状态
        function showStatus(elementId, type, message) {
            const statusEl = document.getElementById(elementId);
            statusEl.className = `status ${type}`;
            statusEl.textContent = message;
            statusEl.style.display = 'block';
        }

        // 显示响应
        function showResponse(elementId, content) {
            document.getElementById(elementId).textContent = content;
        }

        // 普通API调用
        async function callAPI(url, data, statusId, responseId) {
            showStatus(statusId, 'loading', '正在处理...');
            showResponse(responseId, '');

            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                
                if (result.code === 200) {
                    showStatus(statusId, 'success', '请求成功');
                    showResponse(responseId, JSON.stringify(result, null, 2));
                } else {
                    showStatus(statusId, 'error', `请求失败: ${result.message}`);
                    showResponse(responseId, JSON.stringify(result, null, 2));
                }
            } catch (error) {
                showStatus(statusId, 'error', `网络错误: ${error.message}`);
                showResponse(responseId, error.toString());
            }
        }

        // 流式API调用
        function callStreamAPI(url, data, statusId, responseId) {
            showStatus(statusId, 'loading', '正在建立连接...');
            showResponse(responseId, '');

            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            }).then(response => {
                if (!response.ok) {
                    throw new Error('网络请求失败');
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';

                function readStream() {
                    return reader.read().then(({ done, value }) => {
                        if (done) {
                            showStatus(statusId, 'success', '流式传输完成');
                            return;
                        }

                        buffer += decoder.decode(value, { stream: true });
                        const lines = buffer.split('\n');
                        buffer = lines.pop(); // 保留不完整的行

                        lines.forEach(line => {
                            if (line.startsWith('data: ')) {
                                try {
                                    const eventData = JSON.parse(line.substring(6));
                                    const currentContent = document.getElementById(responseId).textContent;
                                    
                                    if (eventData.type === 'start') {
                                        showStatus(statusId, 'loading', '开始接收数据...');
                                    } else if (eventData.type === 'data') {
                                        document.getElementById(responseId).textContent = currentContent + eventData.content;
                                    } else if (eventData.type === 'end') {
                                        showStatus(statusId, 'success', '流式传输完成');
                                    } else if (eventData.type === 'error') {
                                        showStatus(statusId, 'error', `错误: ${eventData.message}`);
                                    }
                                } catch (e) {
                                    console.log('解析事件数据失败:', line);
                                }
                            }
                        });

                        return readStream();
                    });
                }

                return readStream();
            }).catch(error => {
                showStatus(statusId, 'error', `连接错误: ${error.message}`);
            });
        }

        // 1. 知识问答
        function testKnowledgeQA() {
            const data = {
                userId: parseInt(document.getElementById('knowledge-userId').value),
                question: document.getElementById('knowledge-question').value
            };
            callAPI(`${API_BASE}/knowledge/qa`, data, 'knowledge-status', 'knowledge-response');
        }

        function testKnowledgeQAStream() {
            const data = {
                userId: parseInt(document.getElementById('knowledge-userId').value),
                question: document.getElementById('knowledge-question').value
            };
            callStreamAPI(`${API_BASE}/knowledge/qa/stream`, data, 'knowledge-status', 'knowledge-response');
        }

        // 2. 信息查询
        function testInfoQuery() {
            const data = {
                userId: parseInt(document.getElementById('info-userId').value),
                query: document.getElementById('info-query').value
            };
            callAPI(`${API_BASE}/info/query`, data, 'info-status', 'info-response');
        }

        function testInfoQueryStream() {
            const data = {
                userId: parseInt(document.getElementById('info-userId').value),
                query: document.getElementById('info-query').value
            };
            callStreamAPI(`${API_BASE}/info/query/stream`, data, 'info-status', 'info-response');
        }

        // 3. 文本生成
        function testTextGeneration() {
            const data = {
                userId: parseInt(document.getElementById('text-userId').value),
                prompt: document.getElementById('text-prompt').value,
                type: document.getElementById('text-type').value
            };
            callAPI(`${API_BASE}/text/generate`, data, 'text-status', 'text-response');
        }

        function testTextGenerationStream() {
            const data = {
                userId: parseInt(document.getElementById('text-userId').value),
                prompt: document.getElementById('text-prompt').value,
                type: document.getElementById('text-type').value
            };
            callStreamAPI(`${API_BASE}/text/generate/stream`, data, 'text-status', 'text-response');
        }

        // 4. 语言翻译
        function testTranslation() {
            const data = {
                userId: parseInt(document.getElementById('translate-userId').value),
                text: document.getElementById('translate-text').value,
                fromLang: document.getElementById('translate-fromLang').value,
                toLang: document.getElementById('translate-toLang').value
            };
            callAPI(`${API_BASE}/translate`, data, 'translate-status', 'translate-response');
        }

        function testTranslationStream() {
            const data = {
                userId: parseInt(document.getElementById('translate-userId').value),
                text: document.getElementById('translate-text').value,
                fromLang: document.getElementById('translate-fromLang').value,
                toLang: document.getElementById('translate-toLang').value
            };
            callStreamAPI(`${API_BASE}/translate/stream`, data, 'translate-status', 'translate-response');
        }

        // 5. 情感陪伴
        function testEmotionalCompanion() {
            const data = {
                userId: parseInt(document.getElementById('emotion-userId').value),
                message: document.getElementById('emotion-message').value
            };
            callAPI(`${API_BASE}/emotion/companion`, data, 'emotion-status', 'emotion-response');
        }

        function testEmotionalCompanionStream() {
            const data = {
                userId: parseInt(document.getElementById('emotion-userId').value),
                message: document.getElementById('emotion-message').value
            };
            callStreamAPI(`${API_BASE}/emotion/companion/stream`, data, 'emotion-status', 'emotion-response');
        }

        // 6. 智能推荐
        function testRecommendation() {
            const data = {
                userId: parseInt(document.getElementById('recommend-userId').value),
                preferences: document.getElementById('recommend-preferences').value,
                category: document.getElementById('recommend-category').value
            };
            callAPI(`${API_BASE}/recommend`, data, 'recommend-status', 'recommend-response');
        }

        function testRecommendationStream() {
            const data = {
                userId: parseInt(document.getElementById('recommend-userId').value),
                preferences: document.getElementById('recommend-preferences').value,
                category: document.getElementById('recommend-category').value
            };
            callStreamAPI(`${API_BASE}/recommend/stream`, data, 'recommend-status', 'recommend-response');
        }
    </script>
</body>
</html>

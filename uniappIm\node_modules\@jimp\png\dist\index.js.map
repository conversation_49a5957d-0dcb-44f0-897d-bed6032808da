{"version": 3, "sources": ["../src/index.js"], "names": ["MIME_TYPE", "PNG_FILTER_AUTO", "PNG_FILTER_NONE", "PNG_FILTER_SUB", "PNG_FILTER_UP", "PNG_FILTER_AVERAGE", "PNG_FILTER_PATH", "mime", "constants", "MIME_PNG", "has<PERSON><PERSON><PERSON>", "decoders", "PNG", "sync", "read", "encoders", "data", "png", "width", "bitmap", "height", "write", "deflateLevel", "_deflateLevel", "deflateStrategy", "_deflateStrategy", "filterType", "_filterType", "colorType", "_colorType", "_rgba", "inputHasAlpha", "l", "cb", "throwError", "call", "Math", "round", "s", "f"], "mappings": ";;;;;;;;;;;AAAA;;AACA;;AAEA,IAAMA,SAAS,GAAG,WAAlB,C,CAEA;;AACA,IAAMC,eAAe,GAAG,CAAC,CAAzB;AACA,IAAMC,eAAe,GAAG,CAAxB;AACA,IAAMC,cAAc,GAAG,CAAvB;AACA,IAAMC,aAAa,GAAG,CAAtB;AACA,IAAMC,kBAAkB,GAAG,CAA3B;AACA,IAAMC,eAAe,GAAG,CAAxB;;eAEe;AAAA,SAAO;AACpBC,IAAAA,IAAI,uCAAKP,SAAL,EAAiB,CAAC,KAAD,CAAjB,CADgB;AAGpBQ,IAAAA,SAAS,EAAE;AACTC,MAAAA,QAAQ,EAAET,SADD;AAETC,MAAAA,eAAe,EAAfA,eAFS;AAGTC,MAAAA,eAAe,EAAfA,eAHS;AAITC,MAAAA,cAAc,EAAdA,cAJS;AAKTC,MAAAA,aAAa,EAAbA,aALS;AAMTC,MAAAA,kBAAkB,EAAlBA,kBANS;AAOTC,MAAAA,eAAe,EAAfA;AAPS,KAHS;AAapBI,IAAAA,QAAQ,uCAAKV,SAAL,EAAiB,IAAjB,CAbY;AAcpBW,IAAAA,QAAQ,uCAAKX,SAAL,EAAiBY,WAAIC,IAAJ,CAASC,IAA1B,CAdY;AAepBC,IAAAA,QAAQ,uCACLf,SADK,EACO,UAAAgB,IAAI,EAAI;AACnB,UAAMC,GAAG,GAAG,IAAIL,UAAJ,CAAQ;AAClBM,QAAAA,KAAK,EAAEF,IAAI,CAACG,MAAL,CAAYD,KADD;AAElBE,QAAAA,MAAM,EAAEJ,IAAI,CAACG,MAAL,CAAYC;AAFF,OAAR,CAAZ;AAKAH,MAAAA,GAAG,CAACD,IAAJ,GAAWA,IAAI,CAACG,MAAL,CAAYH,IAAvB;AAEA,aAAOJ,WAAIC,IAAJ,CAASQ,KAAT,CAAeJ,GAAf,EAAoB;AACzBC,QAAAA,KAAK,EAAEF,IAAI,CAACG,MAAL,CAAYD,KADM;AAEzBE,QAAAA,MAAM,EAAEJ,IAAI,CAACG,MAAL,CAAYC,MAFK;AAGzBE,QAAAA,YAAY,EAAEN,IAAI,CAACO,aAHM;AAIzBC,QAAAA,eAAe,EAAER,IAAI,CAACS,gBAJG;AAKzBC,QAAAA,UAAU,EAAEV,IAAI,CAACW,WALQ;AAMzBC,QAAAA,SAAS,EACP,OAAOZ,IAAI,CAACa,UAAZ,KAA2B,QAA3B,GACIb,IAAI,CAACa,UADT,GAEIb,IAAI,CAACc,KAAL,GACE,CADF,GAEE,CAXiB;AAYzBC,QAAAA,aAAa,EAAEf,IAAI,CAACc;AAZK,OAApB,CAAP;AAcD,KAvBK,CAfY;AAyCpB,aAAO;AACLP,MAAAA,aAAa,EAAE,CADV;AAELE,MAAAA,gBAAgB,EAAE,CAFb;AAGLE,MAAAA,WAAW,EAAE1B,eAHR;AAIL4B,MAAAA,UAAU,EAAE,IAJP;;AAML;;;;;;AAMAP,MAAAA,YAZK,wBAYQU,CAZR,EAYWC,EAZX,EAYe;AAClB,YAAI,OAAOD,CAAP,KAAa,QAAjB,EAA2B;AACzB,iBAAOE,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,oBAAtB,EAA4CF,EAA5C,CAAP;AACD;;AAED,YAAID,CAAC,GAAG,CAAJ,IAASA,CAAC,GAAG,CAAjB,EAAoB;AAClB,iBAAOE,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,0BAAtB,EAAkDF,EAAlD,CAAP;AACD;;AAED,aAAKV,aAAL,GAAqBa,IAAI,CAACC,KAAL,CAAWL,CAAX,CAArB;;AAEA,YAAI,0BAAcC,EAAd,CAAJ,EAAuB;AACrBA,UAAAA,EAAE,CAACE,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,eAAO,IAAP;AACD,OA5BI;;AA8BL;;;;;;AAMAX,MAAAA,eApCK,2BAoCWc,CApCX,EAoCcL,EApCd,EAoCkB;AACrB,YAAI,OAAOK,CAAP,KAAa,QAAjB,EAA2B;AACzB,iBAAOJ,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,oBAAtB,EAA4CF,EAA5C,CAAP;AACD;;AAED,YAAIK,CAAC,GAAG,CAAJ,IAASA,CAAC,GAAG,CAAjB,EAAoB;AAClB,iBAAOJ,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,0BAAtB,EAAkDF,EAAlD,CAAP;AACD;;AAED,aAAKR,gBAAL,GAAwBW,IAAI,CAACC,KAAL,CAAWC,CAAX,CAAxB;;AAEA,YAAI,0BAAcL,EAAd,CAAJ,EAAuB;AACrBA,UAAAA,EAAE,CAACE,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,eAAO,IAAP;AACD,OApDI;;AAsDL;;;;;;AAMAT,MAAAA,UA5DK,sBA4DMa,CA5DN,EA4DSN,EA5DT,EA4Da;AAChB,YAAI,OAAOM,CAAP,KAAa,QAAjB,EAA2B;AACzB,iBAAOL,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,oBAAtB,EAA4CF,EAA5C,CAAP;AACD;;AAED,YAAIM,CAAC,GAAG,CAAC,CAAL,IAAUA,CAAC,GAAG,CAAlB,EAAqB;AACnB,iBAAOL,kBAAWC,IAAX,CACL,IADK,EAEL,uCAFK,EAGLF,EAHK,CAAP;AAKD;;AAED,aAAKN,WAAL,GAAmBS,IAAI,CAACC,KAAL,CAAWE,CAAX,CAAnB;;AAEA,YAAI,0BAAcN,EAAd,CAAJ,EAAuB;AACrBA,UAAAA,EAAE,CAACE,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,eAAO,IAAP;AACD,OAhFI;;AAiFL;;;;;;AAKIP,MAAAA,SAtFC,qBAsFSU,CAtFT,EAsFYL,EAtFZ,EAsFgB;AACnB,YAAI,OAAOK,CAAP,KAAa,QAAjB,EAA2B;AACzB,iBAAOJ,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,oBAAtB,EAA4CF,EAA5C,CAAP;AACD;;AAED,YAAIK,CAAC,KAAK,CAAN,IAAWA,CAAC,KAAK,CAAjB,IAAsBA,CAAC,KAAK,CAA5B,IAAiCA,CAAC,KAAK,CAA3C,EAA8C;AAC5C,iBAAOJ,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,gCAAtB,EAAwDF,EAAxD,CAAP;AACD;;AAED,aAAKJ,UAAL,GAAkBO,IAAI,CAACC,KAAL,CAAWC,CAAX,CAAlB;;AAEA,YAAI,0BAAcL,EAAd,CAAJ,EAAuB;AACrBA,UAAAA,EAAE,CAACE,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,eAAO,IAAP;AACD;AAtGI;AAzCa,GAAP;AAAA,C", "sourcesContent": ["import { PNG } from 'pngjs';\nimport { throwError, isNodePattern } from '@jimp/utils';\n\nconst MIME_TYPE = 'image/png';\n\n// PNG filter types\nconst PNG_FILTER_AUTO = -1;\nconst PNG_FILTER_NONE = 0;\nconst PNG_FILTER_SUB = 1;\nconst PNG_FILTER_UP = 2;\nconst PNG_FILTER_AVERAGE = 3;\nconst PNG_FILTER_PATH = 4;\n\nexport default () => ({\n  mime: { [MIME_TYPE]: ['png'] },\n\n  constants: {\n    MIME_PNG: MIME_TYPE,\n    PNG_FILTER_AUTO,\n    PNG_FILTER_NONE,\n    PNG_FILTER_SUB,\n    PNG_FILTER_UP,\n    PNG_FILTER_AVERAGE,\n    PNG_FILTER_PATH\n  },\n\n  hasAlpha: { [MIME_TYPE]: true },\n  decoders: { [MIME_TYPE]: PNG.sync.read },\n  encoders: {\n    [MIME_TYPE]: data => {\n      const png = new PNG({\n        width: data.bitmap.width,\n        height: data.bitmap.height\n      });\n\n      png.data = data.bitmap.data;\n\n      return PNG.sync.write(png, {\n        width: data.bitmap.width,\n        height: data.bitmap.height,\n        deflateLevel: data._deflateLevel,\n        deflateStrategy: data._deflateStrategy,\n        filterType: data._filterType,\n        colorType:\n          typeof data._colorType === 'number'\n            ? data._colorType\n            : data._rgba\n              ? 6\n              : 2,\n        inputHasAlpha: data._rgba\n      });\n    }\n  },\n\n  class: {\n    _deflateLevel: 9,\n    _deflateStrategy: 3,\n    _filterType: PNG_FILTER_AUTO,\n    _colorType: null,\n\n    /**\n     * Sets the deflate level used when saving as PNG format (default is 9)\n     * @param {number} l Deflate level to use 0-9. 0 is no compression. 9 (default) is maximum compression.\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {Jimp} this for chaining of methods\n     */\n    deflateLevel(l, cb) {\n      if (typeof l !== 'number') {\n        return throwError.call(this, 'l must be a number', cb);\n      }\n\n      if (l < 0 || l > 9) {\n        return throwError.call(this, 'l must be a number 0 - 9', cb);\n      }\n\n      this._deflateLevel = Math.round(l);\n\n      if (isNodePattern(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    },\n\n    /**\n     * Sets the deflate strategy used when saving as PNG format (default is 3)\n     * @param {number} s Deflate strategy to use 0-3.\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {Jimp} this for chaining of methods\n     */\n    deflateStrategy(s, cb) {\n      if (typeof s !== 'number') {\n        return throwError.call(this, 's must be a number', cb);\n      }\n\n      if (s < 0 || s > 3) {\n        return throwError.call(this, 's must be a number 0 - 3', cb);\n      }\n\n      this._deflateStrategy = Math.round(s);\n\n      if (isNodePattern(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    },\n\n    /**\n     * Sets the filter type used when saving as PNG format (default is automatic filters)\n     * @param {number} f The quality to use -1-4.\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {Jimp} this for chaining of methods\n     */\n    filterType(f, cb) {\n      if (typeof f !== 'number') {\n        return throwError.call(this, 'n must be a number', cb);\n      }\n\n      if (f < -1 || f > 4) {\n        return throwError.call(\n          this,\n          'n must be -1 (auto) or a number 0 - 4',\n          cb\n        );\n      }\n\n      this._filterType = Math.round(f);\n\n      if (isNodePattern(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    },\n    /**\n     * Sets the color type used when saving as PNG format\n     * @param {number} s color type to use 0, 2, 4, 6.\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {Jimp} this for chaining of methods\n     */ colorType(s, cb) {\n      if (typeof s !== 'number') {\n        return throwError.call(this, 's must be a number', cb);\n      }\n\n      if (s !== 0 && s !== 2 && s !== 4 && s !== 6) {\n        return throwError.call(this, 's must be a number 0, 2, 4, 6.', cb);\n      }\n\n      this._colorType = Math.round(s);\n\n      if (isNodePattern(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    }\n  }\n});\n"], "file": "index.js"}
<template>
  <view class="container">
    <!-- 顶部标题 -->
    <view class="header">
      <text class="page-title">📚 学习路线</text>
      <text class="page-subtitle">个性化学习计划</text>
    </view>
    
    <!-- 学习路线列表 -->
    <view class="route-list">
      <view class="route-item">
        <view class="route-icon">🎯</view>
        <view class="route-info">
          <text class="route-title">基础入门路线</text>
          <text class="route-desc">适合初学者的基础知识学习</text>
        </view>
        <view class="route-progress">30%</view>
      </view>
      
      <view class="route-item">
        <view class="route-icon">🚀</view>
        <view class="route-info">
          <text class="route-title">进阶提升路线</text>
          <text class="route-desc">深入学习高级知识点</text>
        </view>
        <view class="route-progress">10%</view>
      </view>
      
      <view class="route-item">
        <view class="route-icon">💼</view>
        <view class="route-info">
          <text class="route-title">职场技能路线</text>
          <text class="route-desc">实用的职场技能培训</text>
        </view>
        <view class="route-progress">0%</view>
      </view>
    </view>
    
    <!-- 底部导航栏 -->
    <view class="bottom-navigation">
      <view class="nav-item" @tap="switchTab('home')">
        <text class="nav-icon">🏠</text>
        <text class="nav-text">首页</text>
      </view>
      <view class="nav-item active" @tap="switchTab('study')">
        <text class="nav-icon">📚</text>
        <text class="nav-text">学习路线</text>
      </view>

      <!-- 中间的拍照搜题按钮 -->
      <view class="camera-btn" @tap="takePhoto">
        <view class="camera-icon">📷</view>
      </view>

      <view class="nav-item" @tap="switchTab('tools')">
        <text class="nav-icon">💎</text>
        <text class="nav-text">精选单词</text>
      </view>
      <view class="nav-item" @tap="switchTab('profile')">
        <text class="nav-icon">👤</text>
        <text class="nav-text">主页</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      
    }
  },
  methods: {
    switchTab(tab) {
      switch(tab) {
        case 'home':
          uni.reLaunch({
            url: '/pages/index/index'
          });
          break;
        case 'study':
          // 当前页面，不需要跳转
          break;
        case 'tools':
          uni.reLaunch({
            url: '/pages/tools/tools'
          });
          break;
        case 'profile':
          uni.reLaunch({
            url: '/pages/profile/profile'
          });
          break;
      }
    },

    // 拍照搜题功能
    takePhoto() {
      uni.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'],
        sourceType: ['camera'],
        success: (res) => {
          uni.showToast({
            title: '拍照成功！正在识别题目...',
            icon: 'success',
            duration: 2000
          });
        },
        fail: (err) => {
          uni.showToast({
            title: '拍照失败，请重试',
            icon: 'none'
          });
        }
      });
    }
  }
}
</script>

<style>
.container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 20rpx;
  padding-bottom: 140rpx;
}

.header {
  text-align: center;
  padding: 40rpx 0;
  background: white;
  border-radius: 15rpx;
  margin-bottom: 30rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  display: block;
  margin-bottom: 10rpx;
}

.page-subtitle {
  font-size: 24rpx;
  color: #6c757d;
}

.route-list {
  background: white;
  border-radius: 15rpx;
  padding: 20rpx;
}

.route-item {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.route-item:last-child {
  border-bottom: none;
}

.route-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.route-info {
  flex: 1;
}

.route-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  display: block;
  margin-bottom: 8rpx;
}

.route-desc {
  font-size: 24rpx;
  color: #6c757d;
}

.route-progress {
  font-size: 24rpx;
  color: #007AFF;
  font-weight: bold;
}

/* 底部导航栏样式 */
.bottom-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: white;
  display: flex;
  align-items: center;
  justify-content: space-around;
  border-top: 1rpx solid #e5e5e5;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 10rpx 0;
  transition: all 0.3s ease;
}

.nav-item.active .nav-icon {
  color: #007AFF;
  transform: scale(1.1);
}

.nav-item.active .nav-text {
  color: #007AFF;
  font-weight: 600;
}

.nav-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
  color: #666;
  transition: all 0.3s ease;
}

.nav-text {
  font-size: 20rpx;
  color: #666;
  transition: all 0.3s ease;
}

/* 拍照搜题按钮 */
.camera-btn {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 15rpx rgba(255, 107, 107, 0.4);
  transform: translateY(-10rpx);
  transition: all 0.3s ease;
}

.camera-btn:active {
  transform: translateY(-8rpx) scale(0.95);
  box-shadow: 0 2rpx 10rpx rgba(255, 107, 107, 0.6);
}

.camera-icon {
  font-size: 45rpx;
  color: white;
}
</style>

<template>
  <view class="custom-tab-bar">
    <view 
      v-for="(item, index) in tabList" 
      :key="index"
      class="tab-item"
      :class="{ active: currentTab === index }"
      @click="switchTab(index)"
    >
      <view class="tab-icon">
        <text class="icon-text">{{ item.icon }}</text>
      </view>
      <text class="tab-text">{{ item.text }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'TabBar',
  props: {
    current: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      currentTab: this.current,
      tabList: [
        {
          icon: '💬',
          text: '聊天',
          pagePath: '/pages/chat/chat'
        },
        {
          icon: '👥',
          text: '好友',
          pagePath: '/pages/friend/friend'
        }
      ]
    }
  },
  watch: {
    current(newVal) {
      this.currentTab = newVal
    }
  },
  methods: {
    switchTab(index) {
      if (this.currentTab === index) return
      
      const item = this.tabList[index]
      this.currentTab = index
      
      // 发送事件给父组件
      this.$emit('change', index)
      
      // 跳转页面
      uni.switchTab({
        url: item.pagePath,
        fail: () => {
          // 如果switchTab失败，使用reLaunch
          uni.reLaunch({
            url: item.pagePath
          })
        }
      })
    }
  }
}
</script>

<style scoped>
.custom-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #ffffff;
  border-top: 1rpx solid #e5e5e5;
  display: flex;
  z-index: 1000;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10rpx 0;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #3cc51f;
}

.tab-icon {
  margin-bottom: 4rpx;
}

.icon-text {
  font-size: 40rpx;
  line-height: 1;
}

.tab-text {
  font-size: 20rpx;
  color: #7A7E83;
  line-height: 1;
}

.tab-item.active .tab-text {
  color: #3cc51f;
  font-weight: 500;
}

/* 为页面内容留出底部空间 */
.page-content {
  padding-bottom: 100rpx;
}
</style>

"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      messages: [],
      inputText: "",
      isLoading: false,
      isAiTyping: false,
      scrollTop: 0,
      userId: 123,
      selectedEmotion: "neutral",
      emotions: [
        { key: "happy", name: "开心", icon: "😊" },
        { key: "sad", name: "难过", icon: "😢" },
        { key: "angry", name: "生气", icon: "😠" },
        { key: "worried", name: "担心", icon: "😰" },
        { key: "excited", name: "兴奋", icon: "🤩" },
        { key: "tired", name: "疲惫", icon: "😴" },
        { key: "confused", name: "困惑", icon: "😕" },
        { key: "neutral", name: "平静", icon: "😐" }
      ],
      emotionSuggestions: {
        happy: [
          "我今天很开心，因为...",
          "有件好事想分享",
          "心情特别好",
          "感觉很幸福"
        ],
        sad: [
          "我今天心情不太好",
          "感觉有点难过",
          "遇到了一些困难",
          "需要一些安慰"
        ],
        angry: [
          "我今天很生气",
          "有些事情让我很不爽",
          "感觉很愤怒",
          "心情很烦躁"
        ],
        worried: [
          "我有些担心",
          "对未来感到不安",
          "有些事情让我焦虑",
          "心里有些不安"
        ],
        excited: [
          "我超级兴奋！",
          "有个好消息要分享",
          "感觉充满活力",
          "今天特别有动力"
        ],
        tired: [
          "我感觉很累",
          "今天很疲惫",
          "需要休息一下",
          "感觉没有精神"
        ],
        confused: [
          "我有些困惑",
          "不知道该怎么办",
          "感觉很迷茫",
          "需要一些建议"
        ],
        neutral: [
          "今天过得还好",
          "心情比较平静",
          "想聊聊天",
          "分享一下今天的事"
        ]
      }
    };
  },
  computed: {
    currentSuggestions() {
      return this.emotionSuggestions[this.selectedEmotion] || this.emotionSuggestions.neutral;
    }
  },
  onLoad() {
    this.loadUserInfo();
    this.addWelcomeMessage();
  },
  methods: {
    loadUserInfo() {
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      if (userInfo && userInfo.id) {
        this.userId = userInfo.id;
      }
    },
    addWelcomeMessage() {
      this.messages.push({
        type: "ai",
        content: "你好！我是你的情感陪伴助手💝\n\n无论你现在的心情如何，我都会用心倾听，给你温暖的陪伴和支持。请选择你当前的心情，或者直接告诉我你的感受吧～",
        time: this.getCurrentTime()
      });
      this.scrollToBottom();
    },
    selectEmotion(emotionKey) {
      this.selectedEmotion = emotionKey;
      const emotion = this.emotions.find((e) => e.key === emotionKey);
      if (emotion && this.messages.length > 1) {
        this.messages.push({
          type: "ai",
          content: `我看到你现在的心情是${emotion.icon}${emotion.name}，想和我聊聊吗？我会认真倾听你的感受。`,
          time: this.getCurrentTime()
        });
        this.scrollToBottom();
      }
    },
    useSuggestion(suggestion) {
      this.inputText = suggestion;
      this.sendMessage();
    },
    async sendMessage() {
      if (!this.inputText.trim() || this.isLoading)
        return;
      const userMessage = {
        type: "user",
        content: this.inputText.trim(),
        time: this.getCurrentTime()
      };
      this.messages.push(userMessage);
      const message = this.inputText.trim();
      this.inputText = "";
      this.isLoading = true;
      this.isAiTyping = true;
      this.scrollToBottom();
      try {
        const response = await this.callEmotionAPI(message);
        this.isAiTyping = false;
        if (response && response.success) {
          this.messages.push({
            type: "ai",
            content: response.response || "我理解你的感受，请继续和我分享吧。",
            time: this.getCurrentTime()
          });
        } else {
          this.messages.push({
            type: "ai",
            content: "我现在有些忙，但我一直在这里陪伴你。请继续告诉我你的感受。",
            time: this.getCurrentTime()
          });
        }
      } catch (error) {
        this.isAiTyping = false;
        this.messages.push({
          type: "ai",
          content: "抱歉，我现在无法很好地回应你，但请记住，你的感受很重要，我会一直在这里支持你。",
          time: this.getCurrentTime()
        });
        common_vendor.index.__f__("error", "at pages/ai-emotion/ai-emotion.vue:258", "API调用失败:", error);
      }
      this.isLoading = false;
      this.scrollToBottom();
    },
    async callEmotionAPI(message) {
      const apiUrl = "http://localhost:8082/api/ai/miniprogram/emotion/companion";
      const response = await common_vendor.index.request({
        url: apiUrl,
        method: "POST",
        header: {
          "Content-Type": "application/json"
        },
        data: {
          userId: this.userId,
          message
        }
      });
      if (response.statusCode === 200 && response.data.code === 200) {
        return response.data.data;
      } else {
        throw new Error("API调用失败");
      }
    },
    getCurrentTime() {
      const now = /* @__PURE__ */ new Date();
      return `${now.getHours().toString().padStart(2, "0")}:${now.getMinutes().toString().padStart(2, "0")}`;
    },
    scrollToBottom() {
      this.$nextTick(() => {
        this.scrollTop = 999999;
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.emotions, (emotion, index, i0) => {
      return {
        a: common_vendor.t(emotion.icon),
        b: common_vendor.t(emotion.name),
        c: index,
        d: $data.selectedEmotion === emotion.key ? 1 : "",
        e: common_vendor.o(($event) => $options.selectEmotion(emotion.key), index)
      };
    }),
    b: common_vendor.f($data.messages, (message, index, i0) => {
      return {
        a: common_vendor.t(message.type === "user" ? "我" : "💝"),
        b: common_vendor.t(message.content),
        c: common_vendor.t(message.time),
        d: index,
        e: common_vendor.n(message.type)
      };
    }),
    c: $data.isAiTyping
  }, $data.isAiTyping ? {} : {}, {
    d: $data.scrollTop,
    e: $data.isLoading,
    f: common_vendor.o((...args) => $options.sendMessage && $options.sendMessage(...args)),
    g: $data.inputText,
    h: common_vendor.o(($event) => $data.inputText = $event.detail.value),
    i: common_vendor.t($data.isLoading ? "发送中" : "发送"),
    j: !$data.inputText.trim() || $data.isLoading ? 1 : "",
    k: common_vendor.o((...args) => $options.sendMessage && $options.sendMessage(...args)),
    l: $data.messages.length === 0
  }, $data.messages.length === 0 ? {
    m: common_vendor.f($options.currentSuggestions, (suggestion, index, i0) => {
      return {
        a: common_vendor.t(suggestion),
        b: index,
        c: common_vendor.o(($event) => $options.useSuggestion(suggestion), index)
      };
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/ai-emotion/ai-emotion.js.map

{"name": "@intlify/shared", "version": "9.1.9", "description": "@intlify/shared", "keywords": ["i18n", "internationalization", "intlify", "utitlity"], "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/intlify/vue-i18n-next/tree/master/packages/shared#readme", "repository": {"type": "git", "url": "git+https://github.com/intlify/vue-i18n-next.git", "directory": "packages/shared"}, "bugs": {"url": "https://github.com/intlify/vue-i18n-next/issues"}, "files": ["index.js", "dist"], "main": "index.js", "module": "dist/shared.esm-bundler.js", "types": "dist/shared.d.ts", "engines": {"node": ">= 10"}, "buildOptions": {"name": "IntlifyShared", "formats": ["esm-bundler", "cjs"]}, "publishConfig": {"access": "public"}, "sideEffects": false}
package cn.zhentao.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 结果码枚举
 * 
 * <AUTHOR>
 * @since 2024-07-28
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    // 通用状态码
    SUCCESS(200, "操作成功"),
    ERROR(500, "操作失败"),
    
    // 客户端错误 4xx
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),
    UNPROCESSABLE_ENTITY(422, "请求参数验证失败"),
    TOO_MANY_REQUESTS(429, "请求过于频繁"),
    
    // 服务器错误 5xx
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    BAD_GATEWAY(502, "网关错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    GATEWAY_TIMEOUT(504, "网关超时"),
    
    // 业务错误码 1xxx
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_ALREADY_EXISTS(1002, "用户已存在"),
    USER_PASSWORD_ERROR(1003, "用户密码错误"),
    USER_ACCOUNT_DISABLED(1004, "用户账户已禁用"),
    USER_ACCOUNT_LOCKED(1005, "用户账户已锁定"),
    USER_ACCOUNT_EXPIRED(1006, "用户账户已过期"),
    USER_CREDENTIALS_EXPIRED(1007, "用户凭证已过期"),
    
    // 权限错误码 2xxx
    PERMISSION_DENIED(2001, "权限不足"),
    ROLE_NOT_FOUND(2002, "角色不存在"),
    PERMISSION_NOT_FOUND(2003, "权限不存在"),
    ROLE_ALREADY_EXISTS(2004, "角色已存在"),
    PERMISSION_ALREADY_EXISTS(2005, "权限已存在"),
    
    // 认证错误码 3xxx
    TOKEN_INVALID(3001, "Token无效"),
    TOKEN_EXPIRED(3002, "Token已过期"),
    TOKEN_MISSING(3003, "Token缺失"),
    LOGIN_FAILED(3004, "登录失败"),
    LOGOUT_FAILED(3005, "登出失败"),
    CAPTCHA_ERROR(3006, "验证码错误"),
    
    // 人脸识别错误码 4xxx
    FACE_NOT_DETECTED(4001, "未检测到人脸"),
    FACE_RECOGNITION_FAILED(4002, "人脸识别失败"),
    FACE_MODEL_NOT_FOUND(4003, "人脸模型不存在"),
    FACE_TRAINING_FAILED(4004, "人脸训练失败"),
    FACE_IMAGE_INVALID(4005, "人脸图像无效"),
    FACE_TOO_MANY(4006, "检测到多张人脸"),
    FACE_QUALITY_LOW(4007, "人脸图像质量过低"),
    
    // 文件操作错误码 5xxx
    FILE_NOT_FOUND(5001, "文件不存在"),
    FILE_UPLOAD_FAILED(5002, "文件上传失败"),
    FILE_DELETE_FAILED(5003, "文件删除失败"),
    FILE_SIZE_EXCEEDED(5004, "文件大小超出限制"),
    FILE_TYPE_NOT_SUPPORTED(5005, "文件类型不支持"),
    
    // 数据库错误码 6xxx
    DATABASE_ERROR(6001, "数据库操作失败"),
    DATA_NOT_FOUND(6002, "数据不存在"),
    DATA_ALREADY_EXISTS(6003, "数据已存在"),
    DATA_INTEGRITY_VIOLATION(6004, "数据完整性约束违反"),
    
    // 外部服务错误码 7xxx
    EXTERNAL_SERVICE_ERROR(7001, "外部服务调用失败"),
    NACOS_SERVICE_ERROR(7002, "Nacos服务异常"),
    REDIS_SERVICE_ERROR(7003, "Redis服务异常"),
    
    // 业务逻辑错误码 8xxx
    BUSINESS_ERROR(8001, "业务逻辑错误"),
    PARAMETER_VALIDATION_ERROR(8002, "参数校验失败"),
    OPERATION_NOT_ALLOWED(8003, "操作不被允许"),
    RESOURCE_LOCKED(8004, "资源被锁定"),
    CONCURRENT_OPERATION_ERROR(8005, "并发操作冲突");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 消息
     */
    private final String message;
}

{"name": "phin", "version": "2.9.3", "description": "Ultra-simple, lightweight, dependency-free Node.JS HTTP request client", "main": "lib/phin.compiled.js", "scripts": {"test": "echo \"Tested before deployment.\" && exit 0", "test-dev": "node ./tests/test.js", "prepublishOnly": "npm run test-dev", "gendocs": "rm -r docs || true && jsdoc -R README.md -d ./docs lib/phin.js", "build": "npx babel lib/phin.js --out-file lib/phin.compiled.js --presets env --minified --no-comments"}, "repository": {"type": "git", "url": "git+https://github.com/ethanent/phin.git"}, "keywords": ["http", "https", "request", "fetch", "ajax", "url", "uri"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/ethanent/phin/issues"}, "homepage": "https://github.com/ethanent/phin#readme", "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-env": "^1.6.1", "whew": "^1.0.0"}, "files": ["lib/phin.compiled.js", "LICENSE"]}
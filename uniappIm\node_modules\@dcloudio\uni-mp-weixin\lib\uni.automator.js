"use strict";var e=require("debug"),t=require("licia/isWindows"),a=require("licia/getPort"),o=require("qrcode-reader"),r=require("fs"),n=require("child_process"),s=require("licia/sleep"),i=require("licia/toStr"),c=require("licia/waitUntil"),l=require("licia/concat"),u=require("licia/dateFormat"),d=require("ws"),p=require("events"),f=require("licia/uuid"),m=require("licia/stringify");function h(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var w=h(e),y=h(t),g=h(a),v=h(o),b=h(r),E=h(n),q=h(s),M=h(i),j=h(c),$=h(l),P=h(u),C=h(d),x=h(f),H=h(m);/^win/.test(process.platform);class k extends p.EventEmitter{constructor(e){super(),this.ws=e,this.ws.addEventListener("message",(e=>{this.emit("message",e.data)})),this.ws.addEventListener("close",(()=>{this.emit("close")}))}send(e){this.ws.send(e)}close(){this.ws.close()}}const F="Connection closed";class R extends p.EventEmitter{constructor(e,t,a){super(),this.puppet=t,this.namespace=a,this.callbacks=new Map,this.transport=e,this.debug=w.default("automator:protocol:"+this.namespace),this.onMessage=e=>{this.debug(`${P.default("yyyy-mm-dd HH:MM:ss:l")} ◀ RECV ${e}`);const{id:t,method:a,error:o,result:r,params:n}=JSON.parse(e);if(!t)return this.puppet.emit(a,n);const{callbacks:s}=this;if(t&&s.has(t)){const e=s.get(t);s.delete(t),o?e.reject(Error(o.message||o.detailMessage)):e.resolve(r)}},this.onClose=()=>{this.callbacks.forEach((e=>{e.reject(Error(F))}))},this.transport.on("message",this.onMessage),this.transport.on("close",this.onClose)}send(e,t={},a=!0){if(a&&this.puppet.adapter.has(e))return this.puppet.adapter.send(this,e,t);const o=x.default(),r=H.default({id:o,method:e,params:t});return this.debug(`${P.default("yyyy-mm-dd HH:MM:ss:l")} SEND ► ${r}`),new Promise(((e,t)=>{try{this.transport.send(r)}catch(e){t(Error(F))}this.callbacks.set(o,{resolve:e,reject:t})}))}dispose(){this.transport.close()}static createDevtoolConnection(e,t){return new Promise(((a,o)=>{const r=new C.default(e);r.addEventListener("open",(()=>{a(new R(new k(r),t,"devtool"))})),r.addEventListener("error",o)}))}static createRuntimeConnection(e,t,a){return new Promise(((o,r)=>{w.default("automator:runtime")(`${P.default("yyyy-mm-dd HH:MM:ss:l")} port=${e}`);const n=new C.default.Server({port:e});j.default((async()=>{if(t.runtimeConnection)return!0}),a,1e3).catch((()=>{n.close(),r("Failed to connect to runtime, please make sure the project is running")})),n.on("connection",(function(e){w.default("automator:runtime")(`${P.default("yyyy-mm-dd HH:MM:ss:l")} connected`);const a=new R(new k(e),t,"runtime");t.setRuntimeConnection(a),o(a)})),t.setRuntimeServer(n)}))}}const D=w.default("automator:devtool");async function S(e,t,a){const{port:o,cliPath:r,timeout:n,cwd:s="",account:i="",args:c=[],launch:l=!0}=t;let u=!1,d=!1;if(!1!==l){const t={stdio:"ignore",detached:!0};s&&(t.cwd=s);let a=$.default(c,[]);a=$.default(a,["auto","--project"]),a=$.default(a,[e,"--auto-port",M.default(o)]),i&&(a=$.default(a,["--auto-account",i]));try{D("%s %o %o",r,a,t);const e=E.default.spawn(r,a,t);e.on("error",(e=>{u=!0})),e.on("exit",(()=>{setTimeout((()=>{d=!0}),15e3)})),e.unref()}catch(e){u=!1}}else setTimeout((()=>{d=!0}),15e3);const p=await j.default((async()=>{try{if(u||d)return!0;const e=await async function(e,t){let a;try{a=await R.createDevtoolConnection(e.wsEndpoint,t)}catch(t){throw Error(`Failed connecting to ${e.wsEndpoint}, check if target project window is opened with automation enabled`)}return a}({wsEndpoint:`ws://127.0.0.1:${o}`},a);return e}catch(e){}}),n,1e3);if(u)throw Error(`Failed to launch ${a.devtools.name}, please make sure cliPath is correctly specified`);if(d)throw Error(`Failed to launch ${a.devtools.name} , please make sure http port is open`);return await q.default(5e3),D(`${P.default("yyyy-mm-dd HH:MM:ss:l")} connected`),p}const T={devtools:{name:"Wechat web devTools",remote:!0,automator:!0,paths:[y.default?"C:/Program Files (x86)/Tencent/微信web开发者工具/cli.bat":"/Applications/wechatwebdevtools.app/Contents/MacOS/cli"],required:["project.config.json","app.json","app.js"],defaultPort:9420,validate:async function(e,t){const a=function(e,t){const a=t.devtools.paths.slice(0);e&&a.unshift(e);for(const e of a)if(b.default.existsSync(e))return e;throw Error(`${t.devtools.name} not found, please specify executablePath option`)}(e.executablePath,t);let o=e.port||t.devtools.defaultPort;if(!1!==e.launch)try{o=await async function(e,t){const a=await g.default(e||t);if(e&&a!==e)throw Error(`Port ${e} is in use, please specify another port`);return a}(o)}catch(t){e.launch=!1}else{o===await g.default(o)&&(e.launch=!0)}return Object.assign(Object.assign({},e),{port:o,cliPath:a})},async create(e,t,a){const o=await S(e,t,a);return a.compiled?w.default("automator:devtool")("Waiting for runtime automator"):(w.default("automator:devtool")("initRuntimeAutomator"),o.send("App.callWxMethod",{method:"$$initRuntimeAutomator",args:[]})),o}},adapter:{"Tool.enableRemoteDebug":{reflect:async(e,t)=>{let{qrCode:a}=await e("Tool.enableRemoteDebug",t,!1);return a&&(a=await function(e){const t=new Buffer(e,"base64");return new Promise((async(e,a)=>{const o=await require("jimp").read(t),r=new v.default;r.callback=function(t,o){if(t)return a(t);e(o.result)},r.decode(o.bitmap)}))}(a)),{qrCode:a}}},"App.callFunction":{reflect:async(e,t)=>{return e("App.callFunction",Object.assign(Object.assign({},t),{functionDeclaration:(a=t.functionDeclaration,"}"===a[a.length-1]?a.replace("{","{\nvar uni = wx;\n"):a.replace("=>","=>{\nvar uni = wx;\nreturn ")+"}")}),!1);var a}},"Element.getHTML":{reflect:async(e,t)=>({html:(await e("Element.getWXML",t,!1)).wxml})}}};module.exports=T;

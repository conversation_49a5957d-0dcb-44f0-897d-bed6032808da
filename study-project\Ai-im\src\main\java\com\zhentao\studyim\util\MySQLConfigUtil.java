package com.zhentao.studyim.util;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;

/**
 * MySQL配置工具类
 * 用于调整MySQL服务器配置
 */
public class MySQLConfigUtil {
    
    private static final String URL = "****************************************************************************************************************************************************";
    private static final String USERNAME = "root";
    private static final String PASSWORD = "Sunshuo0818";
    
    public static void main(String[] args) {
        try {
            // 加载MySQL驱动
            Class.forName("com.mysql.cj.jdbc.Driver");
            
            // 建立连接
            Connection conn = DriverManager.getConnection(URL, USERNAME, PASSWORD);
            Statement stmt = conn.createStatement();
            
            System.out.println("=== MySQL连接配置调整 ===");
            
            // 1. 查看当前最大连接数
            ResultSet rs1 = stmt.executeQuery("SHOW VARIABLES LIKE 'max_connections'");
            while (rs1.next()) {
                System.out.println("当前最大连接数: " + rs1.getString(2));
            }
            rs1.close();
            
            // 2. 查看当前活跃连接数
            ResultSet rs2 = stmt.executeQuery("SHOW STATUS LIKE 'Threads_connected'");
            while (rs2.next()) {
                System.out.println("当前活跃连接数: " + rs2.getString(2));
            }
            rs2.close();
            
            // 3. 查看历史最大连接数
            ResultSet rs3 = stmt.executeQuery("SHOW STATUS LIKE 'Max_used_connections'");
            while (rs3.next()) {
                System.out.println("历史最大连接数: " + rs3.getString(2));
            }
            rs3.close();
            
            // 4. 设置新的最大连接数
            System.out.println("\n正在设置最大连接数为500...");
            stmt.executeUpdate("SET GLOBAL max_connections = 500");
            
            // 5. 验证设置结果
            ResultSet rs4 = stmt.executeQuery("SHOW VARIABLES LIKE 'max_connections'");
            while (rs4.next()) {
                System.out.println("设置后最大连接数: " + rs4.getString(2));
            }
            rs4.close();
            
            System.out.println("\nMySQL最大连接数已成功调整为500！");
            System.out.println("注意：这是临时设置，MySQL重启后会恢复默认值。");
            System.out.println("如需永久设置，请在MySQL配置文件中添加 max_connections = 500");
            
            // 关闭连接
            stmt.close();
            conn.close();
            
        } catch (Exception e) {
            System.err.println("调整MySQL配置时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
}

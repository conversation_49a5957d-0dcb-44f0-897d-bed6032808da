"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      messages: [],
      inputText: "",
      isLoading: false,
      isAiTyping: false,
      scrollTop: 0,
      userId: 123,
      activeCategory: "weather",
      categories: [
        {
          key: "weather",
          name: "天气",
          icon: "🌤️",
          placeholder: "请输入城市名称查询天气...",
          examples: {
            title: "天气查询示例",
            list: [
              "北京今天的天气怎么样？",
              "明天保定会下雨吗？",
              "这周末的天气预报",
              "上海的气温是多少？"
            ]
          }
        },
        {
          key: "phone",
          name: "电话",
          icon: "📞",
          placeholder: "请输入要查询的联系人...",
          examples: {
            title: "电话查询示例",
            list: [
              "爸爸的电话号码",
              "妈妈的手机号",
              "老师的联系方式",
              "紧急联系人电话"
            ]
          }
        },
        {
          key: "news",
          name: "资讯",
          icon: "📰",
          placeholder: "请输入要查询的资讯内容...",
          examples: {
            title: "资讯查询示例",
            list: [
              "今天的新闻热点",
              "最新科技资讯",
              "教育政策新闻",
              "健康生活资讯"
            ]
          }
        },
        {
          key: "other",
          name: "其他",
          icon: "🔎",
          placeholder: "请输入要查询的信息...",
          examples: {
            title: "其他查询示例",
            list: [
              "附近的医院",
              "公交路线查询",
              "快递查询",
              "节假日安排"
            ]
          }
        }
      ]
    };
  },
  computed: {
    currentCategory() {
      return this.categories.find((cat) => cat.key === this.activeCategory) || this.categories[0];
    },
    currentPlaceholder() {
      return this.currentCategory.placeholder;
    }
  },
  onLoad() {
    this.loadUserInfo();
    this.addWelcomeMessage();
  },
  methods: {
    loadUserInfo() {
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      if (userInfo && userInfo.id) {
        this.userId = userInfo.id;
      }
    },
    addWelcomeMessage() {
      this.messages.push({
        type: "ai",
        content: "你好！我是信息查询助手，可以帮您查询天气、电话、资讯等各种信息。请选择查询类型或直接提问！",
        time: this.getCurrentTime()
      });
      this.scrollToBottom();
    },
    switchCategory(categoryKey) {
      this.activeCategory = categoryKey;
      this.inputText = "";
    },
    askQuery(query) {
      this.inputText = query;
      this.sendMessage();
    },
    async sendMessage() {
      if (!this.inputText.trim() || this.isLoading)
        return;
      const userMessage = {
        type: "user",
        content: this.inputText.trim(),
        time: this.getCurrentTime()
      };
      this.messages.push(userMessage);
      const query = this.inputText.trim();
      this.inputText = "";
      this.isLoading = true;
      this.isAiTyping = true;
      this.scrollToBottom();
      try {
        const response = await this.callSearchAPI(query);
        this.isAiTyping = false;
        if (response && response.success) {
          this.messages.push({
            type: "ai",
            content: response.response || "抱歉，没有找到相关信息。",
            time: this.getCurrentTime()
          });
        } else {
          this.messages.push({
            type: "ai",
            content: "抱歉，查询服务暂时不可用，请稍后再试。",
            time: this.getCurrentTime()
          });
        }
      } catch (error) {
        this.isAiTyping = false;
        this.messages.push({
          type: "ai",
          content: "网络连接失败，请检查网络后重试。",
          time: this.getCurrentTime()
        });
        common_vendor.index.__f__("error", "at pages/ai-search/ai-search.vue:252", "API调用失败:", error);
      }
      this.isLoading = false;
      this.scrollToBottom();
    },
    async callSearchAPI(query) {
      const apiUrl = "http://localhost:8082/api/ai/miniprogram/info/query";
      const response = await common_vendor.index.request({
        url: apiUrl,
        method: "POST",
        header: {
          "Content-Type": "application/json"
        },
        data: {
          userId: this.userId,
          query
        }
      });
      if (response.statusCode === 200 && response.data.code === 200) {
        return response.data.data;
      } else {
        throw new Error("API调用失败");
      }
    },
    getCurrentTime() {
      const now = /* @__PURE__ */ new Date();
      return `${now.getHours().toString().padStart(2, "0")}:${now.getMinutes().toString().padStart(2, "0")}`;
    },
    scrollToBottom() {
      this.$nextTick(() => {
        this.scrollTop = 999999;
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.categories, (category, index, i0) => {
      return {
        a: common_vendor.t(category.icon),
        b: common_vendor.t(category.name),
        c: index,
        d: $data.activeCategory === category.key ? 1 : "",
        e: common_vendor.o(($event) => $options.switchCategory(category.key), index)
      };
    }),
    b: common_vendor.f($data.messages, (message, index, i0) => {
      return {
        a: common_vendor.t(message.type === "user" ? "我" : "AI"),
        b: common_vendor.t(message.content),
        c: common_vendor.t(message.time),
        d: index,
        e: common_vendor.n(message.type)
      };
    }),
    c: $data.isAiTyping
  }, $data.isAiTyping ? {} : {}, {
    d: $data.scrollTop,
    e: $options.currentPlaceholder,
    f: $data.isLoading,
    g: common_vendor.o((...args) => $options.sendMessage && $options.sendMessage(...args)),
    h: $data.inputText,
    i: common_vendor.o(($event) => $data.inputText = $event.detail.value),
    j: common_vendor.t($data.isLoading ? "查询中" : "查询"),
    k: !$data.inputText.trim() || $data.isLoading ? 1 : "",
    l: common_vendor.o((...args) => $options.sendMessage && $options.sendMessage(...args)),
    m: $data.messages.length === 0
  }, $data.messages.length === 0 ? {
    n: common_vendor.t($options.currentCategory.icon),
    o: common_vendor.t($options.currentCategory.examples.title),
    p: common_vendor.f($options.currentCategory.examples.list, (query, index, i0) => {
      return {
        a: common_vendor.t(query),
        b: index,
        c: common_vendor.o(($event) => $options.askQuery(query), index)
      };
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/ai-search/ai-search.js.map

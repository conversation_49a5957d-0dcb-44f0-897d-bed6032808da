"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      bannerList: [
        {
          title: "欢迎使用AI智能家教",
          desc: "开启智能学习新体验",
          icon: "🎓",
          bgColor: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
        },
        {
          title: "AI语音对话",
          desc: "智能语音交互，随时随地学习",
          icon: "🎤",
          bgColor: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)"
        },
        {
          title: "个性化学习",
          desc: "量身定制学习计划",
          icon: "📚",
          bgColor: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)"
        }
      ]
    };
  },
  methods: {
    // 跳转到个人中心
    goToProfile() {
      common_vendor.index.switchTab({
        url: "/pages/ai-home/ai-home"
      });
    },
    // 拍照搜题提示
    showCameraInfo() {
      common_vendor.index.showToast({
        title: "拍照搜题功能开发中",
        icon: "none",
        duration: 2e3
      });
    },
    // AI对话功能
    startVoiceChat() {
      common_vendor.index.showToast({
        title: "正在启动AI对话...",
        icon: "loading"
      });
    },
    // 功能跳转方法
    openFunction(type) {
      const functionRoutes = {
        "knowledge": "/pages/ai-knowledge/ai-knowledge",
        "search": "/pages/ai-search/ai-search",
        "writing": "/pages/ai-writing/ai-writing",
        "translate": "/pages/ai-translate/ai-translate",
        "emotion": "/pages/ai-emotion/ai-emotion",
        "recommend": "/pages/ai-recommend/ai-recommend",
        "reminder": "/pages/ai-reminder/ai-reminder",
        "game": "/pages/ai-game/ai-game",
        "health": "/pages/ai-health/ai-health",
        "photo-search": "/pages/ai-photo/ai-photo",
        "video-call": "/pages/ai-video/ai-video",
        "exam": "/pages/ai-exam/ai-exam",
        "textbook": "/pages/ai-textbook/ai-textbook"
      };
      const route = functionRoutes[type];
      if (route) {
        const availablePages = ["knowledge", "search", "writing", "translate", "emotion", "recommend"];
        if (availablePages.includes(type)) {
          common_vendor.index.navigateTo({
            url: route,
            fail: (err) => {
              common_vendor.index.__f__("error", "at pages/home/<USER>", "页面跳转失败:", err);
              common_vendor.index.showToast({
                title: "页面跳转失败",
                icon: "none"
              });
            }
          });
        } else {
          common_vendor.index.showToast({
            title: "功能开发中，敬请期待",
            icon: "none"
          });
        }
      } else {
        common_vendor.index.showToast({
          title: "功能开发中，敬请期待",
          icon: "none"
        });
      }
    },
    // 显示功能详情
    showFunctionDetail(type) {
      const functionDetails = {
        "knowledge": {
          title: "知识问答",
          icon: "📚",
          description: "让AI能够回答各种问题，如历史、科学、技术、文化等方面的问题，就像一个知识渊博的助手。",
          examples: [
            "地球的直径是多少？",
            "中国古代四大发明是什么？",
            "光的传播速度是多少？"
          ]
        },
        "search": {
          title: "信息查询",
          icon: "🔍",
          description: "帮助学生查询各类信息，如天气、父母电话等实用信息。",
          examples: [
            "查询明天保定的天气",
            "查询爸爸的电话号码",
            "今天的新闻热点"
          ]
        },
        "writing": {
          title: "文本生成",
          icon: "✍️",
          description: "包括写作文章、故事、诗歌、摘要等各类文本创作。",
          examples: [
            "写一首关于春天的诗",
            "写一个关于友谊的小故事",
            "帮我写一篇关于环保的作文"
          ]
        },
        "translate": {
          title: "语言翻译",
          icon: "🌐",
          description: "实现不同语言之间的即时翻译，支持多种主流语言。",
          examples: [
            '把"我喜欢振涛"翻译成英语',
            "这句日语是什么意思？",
            "帮我翻译这段法语"
          ]
        },
        "emotion": {
          title: "情感陪伴",
          icon: "💝",
          description: "识别用户的情感状态，如高兴、生气、悲伤等，并给予相应的情感回应和安慰。",
          examples: [
            "我今天很难过",
            "我考试考得不好",
            "我今天吃了什么"
          ]
        },
        "recommend": {
          title: "智能推荐",
          icon: "🎯",
          description: "根据用户的兴趣和学习情况，推荐相关的学习内容、书籍、视频等。",
          examples: [
            "推荐一些数学学习资料",
            "推荐适合我的英语电影",
            "推荐一些科学实验"
          ]
        }
      };
      const detail = functionDetails[type];
      if (detail) {
        const exampleText = detail.examples.map((ex) => `• ${ex}`).join("\n");
        common_vendor.index.showModal({
          title: `${detail.icon} ${detail.title}`,
          content: `${detail.description}

示例用法：
${exampleText}`,
          showCancel: true,
          cancelText: "取消",
          confirmText: "立即体验",
          success: (res) => {
            if (res.confirm) {
              this.openFunction(type);
            }
          }
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($data.bannerList, (banner, index, i0) => {
      return {
        a: common_vendor.t(banner.title),
        b: common_vendor.t(banner.desc),
        c: common_vendor.t(banner.icon),
        d: common_vendor.s("background: " + banner.bgColor),
        e: index
      };
    }),
    b: common_vendor.o((...args) => $options.startVoiceChat && $options.startVoiceChat(...args)),
    c: common_vendor.o(($event) => $options.openFunction("knowledge")),
    d: common_vendor.o(($event) => $options.showFunctionDetail("knowledge")),
    e: common_vendor.o(($event) => $options.openFunction("search")),
    f: common_vendor.o(($event) => $options.showFunctionDetail("search")),
    g: common_vendor.o(($event) => $options.openFunction("writing")),
    h: common_vendor.o(($event) => $options.showFunctionDetail("writing")),
    i: common_vendor.o(($event) => $options.openFunction("translate")),
    j: common_vendor.o(($event) => $options.showFunctionDetail("translate")),
    k: common_vendor.o(($event) => $options.openFunction("emotion")),
    l: common_vendor.o(($event) => $options.showFunctionDetail("emotion")),
    m: common_vendor.o(($event) => $options.openFunction("recommend")),
    n: common_vendor.o(($event) => $options.showFunctionDetail("recommend")),
    o: common_vendor.o(($event) => $options.openFunction("reminder")),
    p: common_vendor.o(($event) => $options.showFunctionDetail("reminder")),
    q: common_vendor.o(($event) => $options.openFunction("game")),
    r: common_vendor.o(($event) => $options.showFunctionDetail("game")),
    s: common_vendor.o(($event) => $options.openFunction("health")),
    t: common_vendor.o(($event) => $options.showFunctionDetail("health")),
    v: common_vendor.o(($event) => $options.openFunction("photo-search")),
    w: common_vendor.o(($event) => $options.showFunctionDetail("photo-search")),
    x: common_vendor.o(($event) => $options.openFunction("video-call")),
    y: common_vendor.o(($event) => $options.showFunctionDetail("video-call")),
    z: common_vendor.o(($event) => $options.openFunction("exam")),
    A: common_vendor.o(($event) => $options.showFunctionDetail("exam")),
    B: common_vendor.o(($event) => $options.openFunction("textbook")),
    C: common_vendor.o(($event) => $options.showFunctionDetail("textbook")),
    D: common_vendor.o((...args) => $options.showCameraInfo && $options.showCameraInfo(...args)),
    E: common_vendor.o((...args) => $options.goToProfile && $options.goToProfile(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/home/<USER>

{"version": 3, "file": "bottom-nav.js", "sources": ["components/bottom-nav/bottom-nav.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovc3R1ZHkvc3R1ZHktcHJvamVjdC9zLXNhaS9jb21wb25lbnRzL2JvdHRvbS1uYXYvYm90dG9tLW5hdi52dWU"], "sourcesContent": ["<template>\n  <view class=\"bottom-nav\">\n    <view \n      class=\"nav-item\" \n      :class=\"{ active: activeTab === 'home' }\"\n      @click=\"switchTab('home')\"\n    >\n      <view class=\"nav-icon\">🏠</view>\n      <text class=\"nav-text\">首页</text>\n    </view>\n    \n    <view \n      class=\"nav-item\" \n      :class=\"{ active: activeTab === 'learn' }\"\n      @click=\"switchTab('learn')\"\n    >\n      <view class=\"nav-icon\">📚</view>\n      <text class=\"nav-text\">学习路线</text>\n    </view>\n    \n    <view \n      class=\"nav-item\" \n      :class=\"{ active: activeTab === 'course' }\"\n      @click=\"switchTab('course')\"\n    >\n      <view class=\"nav-icon\">📺</view>\n      <text class=\"nav-text\">免费教程</text>\n    </view>\n    \n    <view \n      class=\"nav-item\" \n      :class=\"{ active: activeTab === 'question' }\"\n      @click=\"switchTab('question')\"\n    >\n      <view class=\"nav-icon\">📝</view>\n      <text class=\"nav-text\">面试刷题</text>\n    </view>\n    \n    <view\n      class=\"nav-item\"\n      :class=\"{ active: activeTab === 'premium' }\"\n      @click=\"switchTab('premium')\"\n    >\n      <view class=\"nav-icon\">💎</view>\n      <text class=\"nav-text\">精品课程</text>\n    </view>\n\n    <view\n      class=\"nav-item\"\n      :class=\"{ active: activeTab === 'ai-home' }\"\n      @click=\"switchTab('ai-home')\"\n    >\n      <view class=\"nav-icon\">🏠</view>\n      <text class=\"nav-text\">主页</text>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'BottomNav',\n  props: {\n    activeTab: {\n      type: String,\n      default: 'home'\n    }\n  },\n  methods: {\n    switchTab(tab) {\n      this.$emit('tab-change', tab);\n    }\n  }\n}\n</script>\n\n<style scoped>\n.bottom-nav {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 100rpx;\n  background: white;\n  display: flex;\n  align-items: center;\n  justify-content: space-around;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);\n  z-index: 1000;\n  padding-bottom: env(safe-area-inset-bottom);\n}\n\n.nav-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  flex: 1;\n  padding: 10rpx 0;\n  transition: all 0.3s ease;\n}\n\n.nav-item.active {\n  color: #007AFF;\n}\n\n.nav-icon {\n  font-size: 32rpx;\n  margin-bottom: 4rpx;\n}\n\n.nav-text {\n  font-size: 20rpx;\n  color: #666;\n}\n\n.nav-item.active .nav-text {\n  color: #007AFF;\n  font-weight: 500;\n}\n</style>\n", "import Component from 'D:/study/study-project/s-sai/components/bottom-nav/bottom-nav.vue'\nwx.createComponent(Component)"], "names": [], "mappings": ";;AA2DA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA,IACL,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,UAAU,KAAK;AACb,WAAK,MAAM,cAAc,GAAG;AAAA,IAC9B;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;ACvEA,GAAG,gBAAgB,SAAS;"}
package cn.zhentao.controller;


import cn.zhentao.pojo.FactInfoLog;
import cn.zhentao.service.FactInfoLogService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import jakarta.annotation.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-18
 */
@RestController
@RequestMapping("/fact-info-log")
public class FactInfoLogController {
@Resource
FactInfoLogService factInfoLogService;
    @PostMapping("/get")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> checkFaceInfo(@RequestBody Map<String, Object> resout) throws IOException {

        Page<FactInfoLog> page = factInfoLogService.page(new Page<>(1, 1000), null);
        HashMap<String, Object> map = new HashMap<>();
        map.put("code", 200);
        map.put("message", "查询成功！");
        map.put("data", page);
        return  new ResponseEntity<>(map, HttpStatus.OK);
    }
}

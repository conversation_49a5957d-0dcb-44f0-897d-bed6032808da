{"version": 3, "file": "ai-home.js", "sources": ["pages/ai-home/ai-home.vue"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <!-- 顶部用户信息区域 -->\r\n    <view class=\"header-section\">\r\n      <view class=\"user-info\">\r\n        <view class=\"avatar\">\r\n          <text class=\"avatar-text\">{{ userInfo.name ? userInfo.name.charAt(0) : 'A' }}</text>\r\n        </view>\r\n        <view class=\"user-details\">\r\n          <text class=\"welcome-text\">欢迎回来</text>\r\n          <text class=\"user-name\">{{ userInfo.name || 'AI学习者' }}</text>\r\n        </view>\r\n      </view>\r\n      <view class=\"header-actions\">\r\n        <view class=\"ai-status\">\r\n          <view class=\"status-indicator\" :class=\"{ active: aiActive }\"></view>\r\n          <text class=\"status-text\">{{ aiActive ? 'AI在线' : 'AI离线' }}</text>\r\n        </view>\r\n        <view class=\"settings-btn\" @tap=\"openSettings\">\r\n          <text class=\"settings-icon\">⚙️</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- AI语音对话区域 -->\r\n    <view class=\"ai-chat-section\">\r\n      <view class=\"section-title\">\r\n        <text class=\"title-icon\">🤖</text>\r\n        <text class=\"title-text\">AI智能对话</text>\r\n      </view>\r\n      <view class=\"chat-card\" @tap=\"startVoiceChat\">\r\n        <view class=\"chat-content\">\r\n          <view class=\"voice-icon\">🎤</view>\r\n          <text class=\"chat-title\">开启AI模式与AI对话</text>\r\n          <text class=\"chat-desc\">语音交互，智能回答各种问题</text>\r\n        </view>\r\n        <view class=\"chat-arrow\">→</view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 功能模块网格 -->\r\n    <view class=\"functions-section\">\r\n      <view class=\"section-title\">\r\n        <text class=\"title-icon\">⭐</text>\r\n        <text class=\"title-text\">智能功能</text>\r\n      </view>\r\n      <view class=\"functions-grid\">\r\n        <!-- 第一行 -->\r\n        <view class=\"function-item\" @tap=\"openFunction('knowledge')\" @longpress=\"showFunctionDetail('knowledge')\">\r\n          <view class=\"function-icon knowledge\">📚</view>\r\n          <text class=\"function-name\">知识问答</text>\r\n          <text class=\"function-desc\">AI百科全书</text>\r\n        </view>\r\n        <view class=\"function-item\" @tap=\"openFunction('search')\" @longpress=\"showFunctionDetail('search')\">\r\n          <view class=\"function-icon search\">🔍</view>\r\n          <text class=\"function-name\">信息查询</text>\r\n          <text class=\"function-desc\">天气·电话·资讯</text>\r\n        </view>\r\n        <view class=\"function-item\" @tap=\"openFunction('writing')\" @longpress=\"showFunctionDetail('writing')\">\r\n          <view class=\"function-icon writing\">✍️</view>\r\n          <text class=\"function-name\">文本生成</text>\r\n          <text class=\"function-desc\">作文·故事·诗歌</text>\r\n        </view>\r\n        \r\n        <!-- 第二行 -->\r\n        <view class=\"function-item\" @tap=\"openFunction('translate')\" @longpress=\"showFunctionDetail('translate')\">\r\n          <view class=\"function-icon translate\">🌐</view>\r\n          <text class=\"function-name\">语言翻译</text>\r\n          <text class=\"function-desc\">多语言互译</text>\r\n        </view>\r\n        <view class=\"function-item\" @tap=\"openFunction('emotion')\" @longpress=\"showFunctionDetail('emotion')\">\r\n          <view class=\"function-icon emotion\">💝</view>\r\n          <text class=\"function-name\">情感陪伴</text>\r\n          <text class=\"function-desc\">情感识别回应</text>\r\n        </view>\r\n        <view class=\"function-item\" @tap=\"openFunction('recommend')\" @longpress=\"showFunctionDetail('recommend')\">\r\n          <view class=\"function-icon recommend\">🎯</view>\r\n          <text class=\"function-name\">智能推荐</text>\r\n          <text class=\"function-desc\">个性化内容</text>\r\n        </view>\r\n        \r\n        <!-- 第三行 -->\r\n        <view class=\"function-item\" @tap=\"openFunction('reminder')\" @longpress=\"showFunctionDetail('reminder')\">\r\n          <view class=\"function-icon reminder\">⏰</view>\r\n          <text class=\"function-name\">任务提醒</text>\r\n          <text class=\"function-desc\">生日·闹钟·事项</text>\r\n        </view>\r\n        <view class=\"function-item\" @tap=\"openFunction('game')\" @longpress=\"showFunctionDetail('game')\">\r\n          <view class=\"function-icon game\">🎮</view>\r\n          <text class=\"function-name\">游戏娱乐</text>\r\n          <text class=\"function-desc\">猜谜·接龙·问答</text>\r\n        </view>\r\n        <view class=\"function-item\" @tap=\"openFunction('health')\" @longpress=\"showFunctionDetail('health')\">\r\n          <view class=\"function-icon health\">💪</view>\r\n          <text class=\"function-name\">健康管理</text>\r\n          <text class=\"function-desc\">运动·饮食·睡眠</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 学习监控区域 -->\r\n    <view class=\"monitor-section\">\r\n      <view class=\"section-title\">\r\n        <text class=\"title-icon\">👁️</text>\r\n        <text class=\"title-text\">实时监控学习</text>\r\n      </view>\r\n      <view class=\"monitor-card\">\r\n        <view class=\"monitor-status\">\r\n          <view class=\"status-item\">\r\n            <text class=\"status-label\">监控状态</text>\r\n            <text class=\"status-value\" :class=\"{ active: monitorActive }\">\r\n              {{ monitorActive ? '监控中' : '未开启' }}\r\n            </text>\r\n          </view>\r\n          <view class=\"monitor-toggle\" @tap=\"toggleMonitor\">\r\n            <view class=\"toggle-btn\" :class=\"{ active: monitorActive }\">\r\n              <view class=\"toggle-slider\"></view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        <view class=\"monitor-features\">\r\n          <view class=\"feature-tag\">专注度分析</view>\r\n          <view class=\"feature-tag\">姿势监测</view>\r\n          <view class=\"feature-tag\">环境检测</view>\r\n          <view class=\"feature-tag\">学习报告</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 快捷功能区域 -->\r\n    <view class=\"quick-actions\">\r\n      <view class=\"action-item\" @tap=\"openFunction('photo-search')\" @longpress=\"showFunctionDetail('photo-search')\">\r\n        <view class=\"action-icon\">📷</view>\r\n        <text class=\"action-text\">拍照搜题</text>\r\n      </view>\r\n      <view class=\"action-item\" @tap=\"openFunction('video-call')\" @longpress=\"showFunctionDetail('video-call')\">\r\n        <view class=\"action-icon\">📹</view>\r\n        <text class=\"action-text\">视频通话</text>\r\n      </view>\r\n      <view class=\"action-item\" @tap=\"openFunction('exam')\" @longpress=\"showFunctionDetail('exam')\">\r\n        <view class=\"action-icon\">📝</view>\r\n        <text class=\"action-text\">模拟考试</text>\r\n      </view>\r\n      <view class=\"action-item\" @tap=\"openFunction('textbook')\" @longpress=\"showFunctionDetail('textbook')\">\r\n        <view class=\"action-icon\">📖</view>\r\n        <text class=\"action-text\">课本学习</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 退出按钮 -->\r\n    <view class=\"logout-section\">\r\n      <button class=\"logout-btn\" @click=\"logout\">\r\n        <text class=\"logout-icon\">🚪</text>\r\n        <text class=\"logout-text\">退出登录</text>\r\n      </button>\r\n    </view>\r\n\r\n    <!-- 底部导航提示 -->\r\n    <view class=\"bottom-tip\">\r\n      <text class=\"tip-text\">💡 长按功能图标可查看详细说明</text>\r\n    </view>\r\n\r\n    <!-- 底部导航栏 -->\r\n    <view class=\"bottom-navigation\">\r\n      <view class=\"nav-item active\" @tap=\"switchTab('home')\">\r\n        <text class=\"nav-icon\">🏠</text>\r\n        <text class=\"nav-text\">首页</text>\r\n      </view>\r\n      <view class=\"nav-item\" @tap=\"switchTab('study')\">\r\n        <text class=\"nav-icon\">📚</text>\r\n        <text class=\"nav-text\">学习路线</text>\r\n      </view>\r\n\r\n      <!-- 中间的拍照搜题按钮 -->\r\n      <view class=\"camera-btn\" @tap=\"takePhoto\">\r\n        <view class=\"camera-icon\">📷</view>\r\n      </view>\r\n\r\n      <view class=\"nav-item\" @tap=\"switchTab('tools')\">\r\n        <text class=\"nav-icon\">💎</text>\r\n        <text class=\"nav-text\">精选单词</text>\r\n      </view>\r\n      <view class=\"nav-item\" @tap=\"switchTab('profile')\">\r\n        <text class=\"nav-icon\">👤</text>\r\n        <text class=\"nav-text\">主页</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      userInfo: {},\r\n      aiActive: true,\r\n      monitorActive: false,\r\n      pageLoaded: false\r\n    }\r\n  },\r\n  onLoad() {\r\n    this.loadUserInfo();\r\n    this.initAIStatus();\r\n    // 页面加载动画\r\n    setTimeout(() => {\r\n      this.pageLoaded = true;\r\n    }, 300);\r\n  },\r\n  onPullDownRefresh() {\r\n    this.refreshData();\r\n  },\r\n  methods: {\r\n    loadUserInfo() {\r\n      const userInfo = uni.getStorageSync('userInfo');\r\n      if (userInfo) {\r\n        this.userInfo = userInfo;\r\n      }\r\n    },\r\n    \r\n    initAIStatus() {\r\n      // 模拟AI状态检测\r\n      this.aiActive = true;\r\n    },\r\n    \r\n    refreshData() {\r\n      setTimeout(() => {\r\n        this.loadUserInfo();\r\n        this.initAIStatus();\r\n        uni.stopPullDownRefresh();\r\n        uni.showToast({\r\n          title: '刷新成功',\r\n          icon: 'success'\r\n        });\r\n      }, 1000);\r\n    },\r\n    \r\n    startVoiceChat() {\r\n      uni.showToast({\r\n        title: '正在启动AI对话...',\r\n        icon: 'loading'\r\n      });\r\n      // TODO: 实现语音对话功能\r\n    },\r\n    \r\n    openFunction(type) {\r\n      const functionRoutes = {\r\n        'knowledge': '/pages/ai-knowledge/ai-knowledge',\r\n        'search': '/pages/ai-search/ai-search',\r\n        'writing': '/pages/ai-writing/ai-writing',\r\n        'translate': '/pages/ai-translate/ai-translate',\r\n        'emotion': '/pages/ai-emotion/ai-emotion',\r\n        'recommend': '/pages/ai-recommend/ai-recommend',\r\n        'reminder': '/pages/ai-reminder/ai-reminder',\r\n        'game': '/pages/ai-game/ai-game',\r\n        'health': '/pages/ai-health/ai-health',\r\n        'photo-search': '/pages/ai-photo/ai-photo',\r\n        'video-call': '/pages/ai-video/ai-video',\r\n        'exam': '/pages/ai-exam/ai-exam',\r\n        'textbook': '/pages/ai-textbook/ai-textbook'\r\n      };\r\n\r\n      const route = functionRoutes[type];\r\n      if (route) {\r\n        // 检查页面是否存在\r\n        const availablePages = ['knowledge', 'search', 'writing', 'translate', 'emotion', 'recommend'];\r\n        if (availablePages.includes(type)) {\r\n          uni.navigateTo({\r\n            url: route,\r\n            fail: (err) => {\r\n              console.error('页面跳转失败:', err);\r\n              uni.showToast({\r\n                title: '页面跳转失败',\r\n                icon: 'none'\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          uni.showToast({\r\n            title: '功能开发中，敬请期待',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      } else {\r\n        uni.showToast({\r\n          title: '功能开发中，敬请期待',\r\n          icon: 'none'\r\n        });\r\n      }\r\n    },\r\n    \r\n    toggleMonitor() {\r\n      this.monitorActive = !this.monitorActive;\r\n      uni.showToast({\r\n        title: this.monitorActive ? '监控已开启' : '监控已关闭',\r\n        icon: 'success'\r\n      });\r\n      // TODO: 实现监控开关功能\r\n    },\r\n\r\n    showFunctionDetail(type) {\r\n      const functionDetails = {\r\n        'knowledge': {\r\n          title: '知识问答',\r\n          icon: '📚',\r\n          description: '让AI能够回答各种问题，如历史、科学、技术、文化等方面的问题，就像一个知识渊博的助手。',\r\n          examples: [\r\n            '地球的直径是多少？',\r\n            '中国古代四大发明是什么？',\r\n            '光的传播速度是多少？'\r\n          ]\r\n        },\r\n        'search': {\r\n          title: '信息查询',\r\n          icon: '🔍',\r\n          description: '帮助学生查询各类信息，如天气、父母电话等实用信息。',\r\n          examples: [\r\n            '查询明天保定的天气',\r\n            '查询爸爸的电话号码',\r\n            '今天的新闻热点'\r\n          ]\r\n        },\r\n        'writing': {\r\n          title: '文本生成',\r\n          icon: '✍️',\r\n          description: '包括写作文章、故事、诗歌、摘要等各类文本创作。',\r\n          examples: [\r\n            '写一首关于春天的诗',\r\n            '写一个关于友谊的小故事',\r\n            '帮我写一篇关于环保的作文'\r\n          ]\r\n        },\r\n        'translate': {\r\n          title: '语言翻译',\r\n          icon: '🌐',\r\n          description: '实现不同语言之间的即时翻译，支持多种主流语言。',\r\n          examples: [\r\n            '把\"我喜欢振涛\"翻译成英语',\r\n            '这句日语是什么意思？',\r\n            '帮我翻译这段法语'\r\n          ]\r\n        },\r\n        'emotion': {\r\n          title: '情感陪伴',\r\n          icon: '💝',\r\n          description: '识别用户的情感状态，如高兴、生气、悲伤等，并给予相应的情感回应和安慰。',\r\n          examples: [\r\n            '我今天很难过',\r\n            '我考试考得不好',\r\n            '我很开心今天学会了新知识'\r\n          ]\r\n        },\r\n        'recommend': {\r\n          title: '智能推荐',\r\n          icon: '🎯',\r\n          description: '根据用户的兴趣、历史记录等，推荐相关的服务、音乐、书籍等。',\r\n          examples: [\r\n            '推荐一些适合我的书籍',\r\n            '推荐一些好听的音乐',\r\n            '推荐一些学习资源'\r\n          ]\r\n        },\r\n        'reminder': {\r\n          title: '任务提醒',\r\n          icon: '⏰',\r\n          description: '帮助用户设置提醒事项，如生日、闹钟、节日、老师安排的重要事情等。',\r\n          examples: [\r\n            '提醒我明天下午六点给妈妈洗脚',\r\n            '提醒我下周一交作业',\r\n            '设置每天早上7点的闹钟'\r\n          ]\r\n        },\r\n        'game': {\r\n          title: '游戏娱乐',\r\n          icon: '🎮',\r\n          description: '提供各种语音游戏，如猜谜语、成语接龙、问答游戏等，为学生带来娱乐。',\r\n          examples: [\r\n            '我们来玩成语接龙吧',\r\n            '给我出个谜语',\r\n            '我们来玩问答游戏'\r\n          ]\r\n        },\r\n        'health': {\r\n          title: '健康管理',\r\n          icon: '💪',\r\n          description: '记录用户日常运动、饮食、睡眠数据，评估健康状况，提供个性化健康建议。',\r\n          examples: [\r\n            '记录我今天走了8000步',\r\n            '我今天吃了什么',\r\n            '提醒我要多喝水'\r\n          ]\r\n        },\r\n        'photo-search': {\r\n          title: '拍照搜题与辅导',\r\n          icon: '📷',\r\n          description: '支持高清拍照搜题，AI老师语音讲解，关联相似题目拓展训练，错题收集与分类。',\r\n          examples: [\r\n            '拍照识别数学题目',\r\n            '拍照翻译英文文章',\r\n            '错题自动收集整理'\r\n          ]\r\n        },\r\n        'video-call': {\r\n          title: '视频通话',\r\n          icon: '📹',\r\n          description: '家长可以通过外部设备与孩子进行实时对话，高清视频通话，清晰语音通话。',\r\n          examples: [\r\n            '与家长视频通话',\r\n            '摄像头控制调节',\r\n            '通话记录回放'\r\n          ]\r\n        },\r\n        'exam': {\r\n          title: '模拟考试',\r\n          icon: '📝',\r\n          description: 'AI智能组卷、监考、判卷，限时2小时，根据成绩定制提升计划。',\r\n          examples: [\r\n            'AI智能组卷考试',\r\n            '自动监考判卷',\r\n            '生成学习报告'\r\n          ]\r\n        },\r\n        'textbook': {\r\n          title: '课本学习',\r\n          icon: '📖',\r\n          description: '配套人教版/部编版教材，AI逐句讲解，口语训练跟读。',\r\n          examples: [\r\n            'AI逐句讲解课文',\r\n            '跟AI练习口语',\r\n            '课本内容同步学习'\r\n          ]\r\n        }\r\n      };\r\n\r\n      const detail = functionDetails[type];\r\n      if (detail) {\r\n        uni.showModal({\r\n          title: `${detail.icon} ${detail.title}`,\r\n          content: `${detail.description}\\n\\n示例：\\n${detail.examples.join('\\n')}`,\r\n          showCancel: true,\r\n          cancelText: '了解了',\r\n          confirmText: '立即体验',\r\n          success: (res) => {\r\n            if (res.confirm) {\r\n              this.openFunction(type);\r\n            }\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    openSettings() {\r\n      uni.showActionSheet({\r\n        itemList: ['个性化设置', '学习报告', '家长中心', '会员权益', '帮助反馈'],\r\n        success: (res) => {\r\n          const actions = [\r\n            () => this.showPersonalSettings(),\r\n            () => this.showLearningReport(),\r\n            () => this.showParentCenter(),\r\n            () => this.showMemberBenefits(),\r\n            () => this.showHelpFeedback()\r\n          ];\r\n          actions[res.tapIndex]();\r\n        }\r\n      });\r\n    },\r\n\r\n    showPersonalSettings() {\r\n      uni.showModal({\r\n        title: '⚙️ 个性化设置',\r\n        content: '• AI语音设置（语速、音色、风格）\\n• 简易指令模式\\n• 学习提醒设置\\n• 界面主题选择',\r\n        showCancel: true,\r\n        cancelText: '取消',\r\n        confirmText: '进入设置',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            uni.showToast({\r\n              title: '功能开发中...',\r\n              icon: 'none'\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    showLearningReport() {\r\n      uni.showModal({\r\n        title: '📊 学习报告',\r\n        content: '• 学习时间统计\\n• 专注度分析\\n• 错题本查看\\n• 学习进度报告\\n• 家长同步通知',\r\n        showCancel: true,\r\n        cancelText: '取消',\r\n        confirmText: '查看报告',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            uni.showToast({\r\n              title: '功能开发中...',\r\n              icon: 'none'\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    showParentCenter() {\r\n      uni.showModal({\r\n        title: '👨‍👩‍👧‍👦 家长中心',\r\n        content: '• 实时监控学习状态\\n• 视频通话功能\\n• 学习报告查看\\n• 账号管理（最多5个孩子）\\n• 心理健康导师',\r\n        showCancel: true,\r\n        cancelText: '取消',\r\n        confirmText: '进入中心',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            uni.showToast({\r\n              title: '功能开发中...',\r\n              icon: 'none'\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    showMemberBenefits() {\r\n      uni.showModal({\r\n        title: '💎 会员权益',\r\n        content: '• AI监控学习功能\\n• 错题本导出\\n• 专属学习报告\\n• 名校试卷库\\n• 优惠券福利\\n\\n🎁 新用户注册送3个月VIP',\r\n        showCancel: true,\r\n        cancelText: '取消',\r\n        confirmText: '了解详情',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            uni.showToast({\r\n              title: '功能开发中...',\r\n              icon: 'none'\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    showHelpFeedback() {\r\n      uni.showModal({\r\n        title: '💬 帮助反馈',\r\n        content: '• 功能使用指南\\n• 常见问题解答\\n• 提交功能建议\\n• 问题反馈\\n• 联系客服',\r\n        showCancel: true,\r\n        cancelText: '取消',\r\n        confirmText: '获取帮助',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            uni.showToast({\r\n              title: '功能开发中...',\r\n              icon: 'none'\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    logout() {\r\n      uni.showModal({\r\n        title: '退出登录',\r\n        content: '确定要退出登录吗？',\r\n        showCancel: true,\r\n        cancelText: '取消',\r\n        confirmText: '确定',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            // 清除用户信息\r\n            uni.removeStorageSync('userInfo');\r\n            uni.removeStorageSync('token');\r\n\r\n            // 显示退出成功提示\r\n            uni.showToast({\r\n              title: '退出成功',\r\n              icon: 'success',\r\n              duration: 1500\r\n            });\r\n\r\n            // 延迟跳转到首页（选择登录方式页面）\r\n            setTimeout(() => {\r\n              uni.reLaunch({\r\n                url: '/pages/index/index'\r\n              });\r\n            }, 1500);\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    switchTab(tab) {\r\n      switch(tab) {\r\n        case 'home':\r\n          // 当前就是首页，不需要跳转\r\n          break;\r\n        case 'study':\r\n          uni.reLaunch({\r\n            url: '/pages/study-route/study-route'\r\n          });\r\n          break;\r\n        case 'tools':\r\n          uni.reLaunch({\r\n            url: '/pages/tools/tools'\r\n          });\r\n          break;\r\n        case 'profile':\r\n          uni.reLaunch({\r\n            url: '/pages/profile/profile'\r\n          });\r\n          break;\r\n      }\r\n    },\r\n\r\n    // 拍照搜题功能\r\n    takePhoto() {\r\n      uni.chooseImage({\r\n        count: 1,\r\n        sizeType: ['original', 'compressed'],\r\n        sourceType: ['camera'], // 只允许拍照，不允许从相册选择\r\n        success: (res) => {\r\n          const tempFilePath = res.tempFilePaths[0];\r\n\r\n          // 显示拍照成功提示\r\n          uni.showToast({\r\n            title: '拍照成功！正在识别题目...',\r\n            icon: 'success',\r\n            duration: 2000\r\n          });\r\n\r\n          // 这里可以添加图片识别和搜题的逻辑\r\n          console.log('拍照的图片路径：', tempFilePath);\r\n\r\n          // 模拟搜题结果\r\n          setTimeout(() => {\r\n            uni.showModal({\r\n              title: '搜题结果',\r\n              content: '题目识别成功！这是一道数学题：求解方程 x² + 2x - 3 = 0',\r\n              showCancel: false,\r\n              confirmText: '知道了'\r\n            });\r\n          }, 2000);\r\n        },\r\n        fail: (err) => {\r\n          console.error('拍照失败：', err);\r\n          uni.showToast({\r\n            title: '拍照失败，请重试',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.container {\r\n  min-height: 100vh;\r\n  background: #f8f9fa;\r\n  padding: 20rpx;\r\n  padding-bottom: 0; /* 移除底部padding，因为现在由main页面管理 */\r\n  animation: fadeIn 0.8s ease-out;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(30rpx);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* 顶部用户信息 */\r\n.header-section {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 30rpx 20rpx;\r\n  background: white;\r\n  border-radius: 15rpx;\r\n  margin-bottom: 30rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.user-info {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.avatar {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  border-radius: 50%;\r\n  background: #007AFF;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.avatar-text {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #fff;\r\n}\r\n\r\n.user-details {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.welcome-text {\r\n  font-size: 24rpx;\r\n  color: #6c757d;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.user-name {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20rpx;\r\n}\r\n\r\n.ai-status {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.settings-btn {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  border-radius: 50%;\r\n  background: #f8f9fa;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n  border: 1rpx solid #e9ecef;\r\n}\r\n\r\n.settings-btn:active {\r\n  transform: scale(0.9);\r\n  background: #e9ecef;\r\n}\r\n\r\n.settings-icon {\r\n  font-size: 28rpx;\r\n}\r\n\r\n.status-indicator {\r\n  width: 16rpx;\r\n  height: 16rpx;\r\n  border-radius: 50%;\r\n  background: #ff4757;\r\n  margin-right: 12rpx;\r\n  animation: pulse 2s infinite;\r\n}\r\n\r\n.status-indicator.active {\r\n  background: #2ed573;\r\n}\r\n\r\n.status-text {\r\n  font-size: 24rpx;\r\n  color: #6c757d;\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% { opacity: 1; }\r\n  50% { opacity: 0.5; }\r\n}\r\n\r\n/* AI对话区域 */\r\n.ai-chat-section {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n  padding: 0 10rpx;\r\n}\r\n\r\n.title-icon {\r\n  font-size: 32rpx;\r\n  margin-right: 12rpx;\r\n}\r\n\r\n.title-text {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n}\r\n\r\n.chat-card {\r\n  background: white;\r\n  border-radius: 15rpx;\r\n  padding: 30rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.chat-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  flex: 1;\r\n}\r\n\r\n.voice-icon {\r\n  font-size: 48rpx;\r\n  margin-bottom: 16rpx;\r\n  animation: bounce 2s infinite;\r\n}\r\n\r\n@keyframes bounce {\r\n  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }\r\n  40% { transform: translateY(-10rpx); }\r\n  60% { transform: translateY(-5rpx); }\r\n}\r\n\r\n.chat-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.chat-desc {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n}\r\n\r\n.chat-arrow {\r\n  font-size: 32rpx;\r\n  color: #2c3e50;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 功能网格 */\r\n.functions-section {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.functions-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(3, 1fr);\r\n  gap: 20rpx;\r\n}\r\n\r\n.function-item {\r\n  background: white;\r\n  border-radius: 12rpx;\r\n  padding: 24rpx 16rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  text-align: center;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\r\n  animation: slideInUp 0.6s ease-out;\r\n  animation-fill-mode: both;\r\n}\r\n\r\n.function-item:nth-child(1) { animation-delay: 0.1s; }\r\n.function-item:nth-child(2) { animation-delay: 0.2s; }\r\n.function-item:nth-child(3) { animation-delay: 0.3s; }\r\n.function-item:nth-child(4) { animation-delay: 0.4s; }\r\n.function-item:nth-child(5) { animation-delay: 0.5s; }\r\n.function-item:nth-child(6) { animation-delay: 0.6s; }\r\n.function-item:nth-child(7) { animation-delay: 0.7s; }\r\n.function-item:nth-child(8) { animation-delay: 0.8s; }\r\n.function-item:nth-child(9) { animation-delay: 0.9s; }\r\n\r\n@keyframes slideInUp {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(30rpx);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.function-item:active {\r\n  transform: translateY(2rpx) scale(0.98);\r\n  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n.function-icon {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  border-radius: 12rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 28rpx;\r\n  margin-bottom: 12rpx;\r\n}\r\n\r\n.function-icon.knowledge { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }\r\n.function-icon.search { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }\r\n.function-icon.writing { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }\r\n.function-icon.translate { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }\r\n.function-icon.emotion { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }\r\n.function-icon.recommend { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }\r\n.function-icon.reminder { background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%); }\r\n.function-icon.game { background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%); }\r\n.function-icon.health { background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%); }\r\n\r\n.function-name {\r\n  font-size: 26rpx;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  margin-bottom: 6rpx;\r\n}\r\n\r\n.function-desc {\r\n  font-size: 20rpx;\r\n  color: #6c757d;\r\n  line-height: 1.3;\r\n}\r\n\r\n/* 监控区域 */\r\n.monitor-section {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.monitor-card {\r\n  background: white;\r\n  border-radius: 15rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.monitor-status {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 24rpx;\r\n}\r\n\r\n.status-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.status-label {\r\n  font-size: 24rpx;\r\n  color: #6c757d;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.status-value {\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  color: #ff4757;\r\n}\r\n\r\n.status-value.active {\r\n  color: #2ed573;\r\n}\r\n\r\n.monitor-toggle {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.toggle-btn {\r\n  width: 80rpx;\r\n  height: 40rpx;\r\n  border-radius: 20rpx;\r\n  background: #dee2e6;\r\n  position: relative;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.toggle-btn.active {\r\n  background: #27ae60;\r\n}\r\n\r\n.toggle-slider {\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  border-radius: 50%;\r\n  background: #fff;\r\n  position: absolute;\r\n  top: 4rpx;\r\n  left: 4rpx;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.toggle-btn.active .toggle-slider {\r\n  left: 44rpx;\r\n}\r\n\r\n.monitor-features {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 12rpx;\r\n}\r\n\r\n.feature-tag {\r\n  background: #2c3e50;\r\n  color: #fff;\r\n  font-size: 20rpx;\r\n  padding: 8rpx 16rpx;\r\n  border-radius: 12rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 快捷功能 */\r\n.quick-actions {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  background: white;\r\n  border-radius: 15rpx;\r\n  padding: 30rpx 20rpx;\r\n  margin-bottom: 30rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.action-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-icon {\r\n  font-size: 40rpx;\r\n  margin-bottom: 12rpx;\r\n  padding: 16rpx;\r\n  border-radius: 50%;\r\n  background: #007AFF;\r\n}\r\n\r\n.action-text {\r\n  font-size: 24rpx;\r\n  color: #2c3e50;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 退出按钮 */\r\n.logout-section {\r\n  padding: 30rpx 20rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.logout-btn {\r\n  width: 100%;\r\n  background: #ff4757;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 15rpx;\r\n  padding: 20rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: 0 2rpx 10rpx rgba(255, 71, 87, 0.3);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.logout-btn:active {\r\n  transform: scale(0.98);\r\n  background: #ff3742;\r\n}\r\n\r\n.logout-icon {\r\n  font-size: 32rpx;\r\n  margin-right: 12rpx;\r\n}\r\n\r\n.logout-text {\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 底部提示 */\r\n.bottom-tip {\r\n  text-align: center;\r\n  padding: 20rpx;\r\n}\r\n\r\n.tip-text {\r\n  font-size: 24rpx;\r\n  color: #6c757d;\r\n}\r\n\r\n/* 底部导航栏 */\r\n.bottom-navigation {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 120rpx;\r\n  background: white;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-around;\r\n  border-top: 1rpx solid #e5e5e5;\r\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);\r\n  z-index: 1000;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n\r\n/* 拍照搜题按钮 */\r\n.camera-btn {\r\n  width: 100rpx;\r\n  height: 100rpx;\r\n  border-radius: 50rpx;\r\n  background: linear-gradient(135deg, #FF6B6B, #FF8E53);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: 0 4rpx 15rpx rgba(255, 107, 107, 0.4);\r\n  transform: translateY(-10rpx); /* 稍微向上突出 */\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.camera-btn:active {\r\n  transform: translateY(-8rpx) scale(0.95);\r\n  box-shadow: 0 2rpx 10rpx rgba(255, 107, 107, 0.6);\r\n}\r\n\r\n.camera-icon {\r\n  font-size: 45rpx;\r\n  color: white;\r\n}\r\n\r\n.nav-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex: 1;\r\n  padding: 10rpx 0;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.nav-item.active .nav-icon {\r\n  color: #007AFF;\r\n  transform: scale(1.1);\r\n}\r\n\r\n.nav-item.active .nav-text {\r\n  color: #007AFF;\r\n  font-weight: 600;\r\n}\r\n\r\n.nav-icon {\r\n  font-size: 40rpx;\r\n  margin-bottom: 8rpx;\r\n  color: #666;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.nav-text {\r\n  font-size: 20rpx;\r\n  color: #666;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n/* 为了给底部导航栏留出空间，调整容器底部padding */\r\n.container {\r\n  padding-bottom: 140rpx; /* 给底部导航栏留出空间 */\r\n}\r\n</style>\r\n"], "names": ["uni"], "mappings": ";;AA+LA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,UAAU,CAAE;AAAA,MACZ,UAAU;AAAA,MACV,eAAe;AAAA,MACf,YAAY;AAAA,IACd;AAAA,EACD;AAAA,EACD,SAAS;AACP,SAAK,aAAY;AACjB,SAAK,aAAY;AAEjB,eAAW,MAAM;AACf,WAAK,aAAa;AAAA,IACnB,GAAE,GAAG;AAAA,EACP;AAAA,EACD,oBAAoB;AAClB,SAAK,YAAW;AAAA,EACjB;AAAA,EACD,SAAS;AAAA,IACP,eAAe;AACb,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9C,UAAI,UAAU;AACZ,aAAK,WAAW;AAAA,MAClB;AAAA,IACD;AAAA,IAED,eAAe;AAEb,WAAK,WAAW;AAAA,IACjB;AAAA,IAED,cAAc;AACZ,iBAAW,MAAM;AACf,aAAK,aAAY;AACjB,aAAK,aAAY;AACjBA,sBAAG,MAAC,oBAAmB;AACvBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACF,GAAE,GAAI;AAAA,IACR;AAAA,IAED,iBAAiB;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IAEF;AAAA,IAED,aAAa,MAAM;AACjB,YAAM,iBAAiB;AAAA,QACrB,aAAa;AAAA,QACb,UAAU;AAAA,QACV,WAAW;AAAA,QACX,aAAa;AAAA,QACb,WAAW;AAAA,QACX,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,cAAc;AAAA,QACd,QAAQ;AAAA,QACR,YAAY;AAAA;AAGd,YAAM,QAAQ,eAAe,IAAI;AACjC,UAAI,OAAO;AAET,cAAM,iBAAiB,CAAC,aAAa,UAAU,WAAW,aAAa,WAAW,WAAW;AAC7F,YAAI,eAAe,SAAS,IAAI,GAAG;AACjCA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,YACL,MAAM,CAAC,QAAQ;AACbA,4BAAc,MAAA,MAAA,SAAA,oCAAA,WAAW,GAAG;AAC5BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,eACI;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,aACK;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACD;AAAA,IAED,gBAAgB;AACd,WAAK,gBAAgB,CAAC,KAAK;AAC3BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,KAAK,gBAAgB,UAAU;AAAA,QACtC,MAAM;AAAA,MACR,CAAC;AAAA,IAEF;AAAA,IAED,mBAAmB,MAAM;AACvB,YAAM,kBAAkB;AAAA,QACtB,aAAa;AAAA,UACX,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACD;AAAA,QACD,UAAU;AAAA,UACR,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACD;AAAA,QACD,WAAW;AAAA,UACT,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACD;AAAA,QACD,aAAa;AAAA,UACX,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACD;AAAA,QACD,WAAW;AAAA,UACT,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACD;AAAA,QACD,aAAa;AAAA,UACX,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACD;AAAA,QACD,YAAY;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACD;AAAA,QACD,QAAQ;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACD;AAAA,QACD,UAAU;AAAA,UACR,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACD;AAAA,QACD,gBAAgB;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACD;AAAA,QACD,cAAc;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACD;AAAA,QACD,QAAQ;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACD;AAAA,QACD,YAAY;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA;AAGF,YAAM,SAAS,gBAAgB,IAAI;AACnC,UAAI,QAAQ;AACVA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,GAAG,OAAO,IAAI,IAAI,OAAO,KAAK;AAAA,UACrC,SAAS,GAAG,OAAO,WAAW;AAAA;AAAA;AAAA,EAAY,OAAO,SAAS,KAAK,IAAI,CAAC;AAAA,UACpE,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,SAAS;AACf,mBAAK,aAAa,IAAI;AAAA,YACxB;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACD;AAAA,IAED,eAAe;AACbA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,SAAS,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QAClD,SAAS,CAAC,QAAQ;AAChB,gBAAM,UAAU;AAAA,YACd,MAAM,KAAK,qBAAsB;AAAA,YACjC,MAAM,KAAK,mBAAoB;AAAA,YAC/B,MAAM,KAAK,iBAAkB;AAAA,YAC7B,MAAM,KAAK,mBAAoB;AAAA,YAC/B,MAAM,KAAK,iBAAiB;AAAA;AAE9B,kBAAQ,IAAI,QAAQ;QACtB;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,uBAAuB;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,qBAAqB;AACnBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,mBAAmB;AACjBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,qBAAqB;AACnBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,mBAAmB;AACjBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,SAAS;AACPA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEfA,gCAAI,kBAAkB,UAAU;AAChCA,gCAAI,kBAAkB,OAAO;AAG7BA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YACZ,CAAC;AAGD,uBAAW,MAAM;AACfA,4BAAAA,MAAI,SAAS;AAAA,gBACX,KAAK;AAAA,cACP,CAAC;AAAA,YACF,GAAE,IAAI;AAAA,UACT;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,UAAU,KAAK;AACb,cAAO,KAAG;AAAA,QACR,KAAK;AAEH;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,SAAS;AAAA,YACX,KAAK;AAAA,UACP,CAAC;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,SAAS;AAAA,YACX,KAAK;AAAA,UACP,CAAC;AACD;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,SAAS;AAAA,YACX,KAAK;AAAA,UACP,CAAC;AACD;AAAA,MACJ;AAAA,IACD;AAAA;AAAA,IAGD,YAAY;AACVA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,UAAU,CAAC,YAAY,YAAY;AAAA,QACnC,YAAY,CAAC,QAAQ;AAAA;AAAA,QACrB,SAAS,CAAC,QAAQ;AAChB,gBAAM,eAAe,IAAI,cAAc,CAAC;AAGxCA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACZ,CAAC;AAGDA,wBAAA,MAAA,MAAA,OAAA,oCAAY,YAAY,YAAY;AAGpC,qBAAW,MAAM;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,aAAa;AAAA,YACf,CAAC;AAAA,UACF,GAAE,GAAI;AAAA,QACR;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAA,MAAA,MAAA,SAAA,oCAAc,SAAS,GAAG;AAC1BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}
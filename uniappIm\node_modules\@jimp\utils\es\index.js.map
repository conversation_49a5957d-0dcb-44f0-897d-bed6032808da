{"version": 3, "sources": ["../src/index.js"], "names": ["scanIterator", "isNodePattern", "cb", "TypeError", "throwError", "error", "Error", "call", "scan", "image", "x", "y", "w", "h", "f", "Math", "round", "_y", "_x", "idx", "bitmap", "width"], "mappings": ";;;;;;;;;;;;;;;;6BAyCiBA,Y;;AAzCV,SAASC,aAAT,CAAuBC,EAAvB,EAA2B;AAChC,MAAI,OAAOA,EAAP,KAAc,WAAlB,EAA+B;AAC7B,WAAO,KAAP;AACD;;AAED,MAAI,OAAOA,EAAP,KAAc,UAAlB,EAA8B;AAC5B,UAAM,IAAIC,SAAJ,CAAc,6BAAd,CAAN;AACD;;AAED,SAAO,IAAP;AACD;;AAEM,SAASC,UAAT,CAAoBC,KAApB,EAA2BH,EAA3B,EAA+B;AACpC,MAAI,OAAOG,KAAP,KAAiB,QAArB,EAA+B;AAC7BA,IAAAA,KAAK,GAAG,IAAIC,KAAJ,CAAUD,KAAV,CAAR;AACD;;AAED,MAAI,OAAOH,EAAP,KAAc,UAAlB,EAA8B;AAC5B,WAAOA,EAAE,CAACK,IAAH,CAAQ,IAAR,EAAcF,KAAd,CAAP;AACD;;AAED,QAAMA,KAAN;AACD;;AAEM,SAASG,IAAT,CAAcC,KAAd,EAAqBC,CAArB,EAAwBC,CAAxB,EAA2BC,CAA3B,EAA8BC,CAA9B,EAAiCC,CAAjC,EAAoC;AACzC;AACAJ,EAAAA,CAAC,GAAGK,IAAI,CAACC,KAAL,CAAWN,CAAX,CAAJ;AACAC,EAAAA,CAAC,GAAGI,IAAI,CAACC,KAAL,CAAWL,CAAX,CAAJ;AACAC,EAAAA,CAAC,GAAGG,IAAI,CAACC,KAAL,CAAWJ,CAAX,CAAJ;AACAC,EAAAA,CAAC,GAAGE,IAAI,CAACC,KAAL,CAAWH,CAAX,CAAJ;;AAEA,OAAK,IAAII,EAAE,GAAGN,CAAd,EAAiBM,EAAE,GAAGN,CAAC,GAAGE,CAA1B,EAA6BI,EAAE,EAA/B,EAAmC;AACjC,SAAK,IAAIC,EAAE,GAAGR,CAAd,EAAiBQ,EAAE,GAAGR,CAAC,GAAGE,CAA1B,EAA6BM,EAAE,EAA/B,EAAmC;AACjC,UAAMC,GAAG,GAAIV,KAAK,CAACW,MAAN,CAAaC,KAAb,GAAqBJ,EAArB,GAA0BC,EAA3B,IAAkC,CAA9C;AACAJ,MAAAA,CAAC,CAACP,IAAF,CAAOE,KAAP,EAAcS,EAAd,EAAkBD,EAAlB,EAAsBE,GAAtB;AACD;AACF;;AAED,SAAOV,KAAP;AACD;;AAEM,SAAUT,YAAV,CAAuBS,KAAvB,EAA8BC,CAA9B,EAAiCC,CAAjC,EAAoCC,CAApC,EAAuCC,CAAvC;AAAA;;AAAA;AAAA;AAAA;AAAA;AACL;AACAH,UAAAA,CAAC,GAAGK,IAAI,CAACC,KAAL,CAAWN,CAAX,CAAJ;AACAC,UAAAA,CAAC,GAAGI,IAAI,CAACC,KAAL,CAAWL,CAAX,CAAJ;AACAC,UAAAA,CAAC,GAAGG,IAAI,CAACC,KAAL,CAAWJ,CAAX,CAAJ;AACAC,UAAAA,CAAC,GAAGE,IAAI,CAACC,KAAL,CAAWH,CAAX,CAAJ;AAESI,UAAAA,EAPJ,GAOSN,CAPT;;AAAA;AAAA,gBAOYM,EAAE,GAAGN,CAAC,GAAGE,CAPrB;AAAA;AAAA;AAAA;;AAQMK,UAAAA,EARN,GAQWR,CARX;;AAAA;AAAA,gBAQcQ,EAAE,GAAGR,CAAC,GAAGE,CARvB;AAAA;AAAA;AAAA;;AASKO,UAAAA,GATL,GASYV,KAAK,CAACW,MAAN,CAAaC,KAAb,GAAqBJ,EAArB,GAA0BC,EAA3B,IAAkC,CAT7C;AAAA;AAUD,iBAAM;AAAER,YAAAA,CAAC,EAAEQ,EAAL;AAASP,YAAAA,CAAC,EAAEM,EAAZ;AAAgBE,YAAAA,GAAG,EAAHA,GAAhB;AAAqBV,YAAAA,KAAK,EAALA;AAArB,WAAN;;AAVC;AAQ0BS,UAAAA,EAAE,EAR5B;AAAA;AAAA;;AAAA;AAOwBD,UAAAA,EAAE,EAP1B;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA", "sourcesContent": ["export function isNodePattern(cb) {\n  if (typeof cb === 'undefined') {\n    return false;\n  }\n\n  if (typeof cb !== 'function') {\n    throw new TypeError('Callback must be a function');\n  }\n\n  return true;\n}\n\nexport function throwError(error, cb) {\n  if (typeof error === 'string') {\n    error = new Error(error);\n  }\n\n  if (typeof cb === 'function') {\n    return cb.call(this, error);\n  }\n\n  throw error;\n}\n\nexport function scan(image, x, y, w, h, f) {\n  // round input\n  x = Math.round(x);\n  y = Math.round(y);\n  w = Math.round(w);\n  h = Math.round(h);\n\n  for (let _y = y; _y < y + h; _y++) {\n    for (let _x = x; _x < x + w; _x++) {\n      const idx = (image.bitmap.width * _y + _x) << 2;\n      f.call(image, _x, _y, idx);\n    }\n  }\n\n  return image;\n}\n\nexport function* scanIterator(image, x, y, w, h) {\n  // round input\n  x = Math.round(x);\n  y = Math.round(y);\n  w = Math.round(w);\n  h = Math.round(h);\n\n  for (let _y = y; _y < y + h; _y++) {\n    for (let _x = x; _x < x + w; _x++) {\n      const idx = (image.bitmap.width * _y + _x) << 2;\n      yield { x: _x, y: _y, idx, image };\n    }\n  }\n}\n"], "file": "index.js"}
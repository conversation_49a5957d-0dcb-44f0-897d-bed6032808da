server:
  port: 8081

spring:
  application:
    name: ai-face-service
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************
    username: root
    password: Sunshuo0818
  main:
    allow-circular-references: true

# 人脸识别功能开关（启用增强的DLL加载）
face:
  recognition:
    enabled: true  # 启用人脸识别功能，使用增强的DLL加载机制

file:
  url: http://118.31.228.119:19000/test/

# MinIO配置
minio:
  endpoint: http://118.31.228.119:19000
  access-key: minioadmin
  secret-key: minioadmin
  bucket-name: test

#  人脸识别
arcface:
  appId: 4a283TA94rAADDaFHK1bMt9SpHvSSUrDT72UHq57ftFD
  sdkKey: aNcQAxi9yKFQL6yFzzPerUHmi96roviGRQGragDwLEp
  #  dllPath: D:\tool\face\libs\WIN64
#  dllPath: E:\wwwroot\libs\WIN64

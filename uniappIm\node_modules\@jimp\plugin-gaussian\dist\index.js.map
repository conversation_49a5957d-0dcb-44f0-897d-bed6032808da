{"version": 3, "sources": ["../src/index.js"], "names": ["gaussian", "r", "cb", "throwError", "call", "rs", "Math", "ceil", "range", "rr2", "rr2pi", "PI", "weights", "y", "x", "dsq", "exp", "bitmap", "height", "width", "red", "green", "blue", "alpha", "wsum", "iy", "ix", "x1", "min", "max", "y1", "weight", "idx", "data", "round"], "mappings": ";;;;;;;AAAA;;AAEA;;;;;;eAMe;AAAA,SAAO;AACpBA,IAAAA,QADoB,oBACXC,CADW,EACRC,EADQ,EACJ;AACd;AACA,UAAI,OAAOD,CAAP,KAAa,QAAjB,EAA2B;AACzB,eAAOE,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,oBAAtB,EAA4CF,EAA5C,CAAP;AACD;;AAED,UAAID,CAAC,GAAG,CAAR,EAAW;AACT,eAAOE,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,0BAAtB,EAAkDF,EAAlD,CAAP;AACD;;AAED,UAAMG,EAAE,GAAGC,IAAI,CAACC,IAAL,CAAUN,CAAC,GAAG,IAAd,CAAX,CAVc,CAUkB;;AAChC,UAAMO,KAAK,GAAGH,EAAE,GAAG,CAAL,GAAS,CAAvB;AACA,UAAMI,GAAG,GAAGR,CAAC,GAAGA,CAAJ,GAAQ,CAApB;AACA,UAAMS,KAAK,GAAGD,GAAG,GAAGH,IAAI,CAACK,EAAzB;AAEA,UAAMC,OAAO,GAAG,EAAhB;;AAEA,WAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGL,KAApB,EAA2BK,CAAC,EAA5B,EAAgC;AAC9BD,QAAAA,OAAO,CAACC,CAAD,CAAP,GAAa,EAAb;;AACA,aAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGN,KAApB,EAA2BM,CAAC,EAA5B,EAAgC;AAC9B,cAAMC,GAAG,GAAG,SAACD,CAAC,GAAGT,EAAL,EAAY,CAAZ,aAAiBQ,CAAC,GAAGR,EAArB,EAA4B,CAA5B,CAAZ;AACAO,UAAAA,OAAO,CAACC,CAAD,CAAP,CAAWC,CAAX,IAAgBR,IAAI,CAACU,GAAL,CAAS,CAACD,GAAD,GAAON,GAAhB,IAAuBC,KAAvC;AACD;AACF;;AAED,WAAK,IAAIG,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG,KAAKI,MAAL,CAAYC,MAAhC,EAAwCL,EAAC,EAAzC,EAA6C;AAC3C,aAAK,IAAIC,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG,KAAKG,MAAL,CAAYE,KAAhC,EAAuCL,EAAC,EAAxC,EAA4C;AAC1C,cAAIM,GAAG,GAAG,CAAV;AACA,cAAIC,KAAK,GAAG,CAAZ;AACA,cAAIC,IAAI,GAAG,CAAX;AACA,cAAIC,KAAK,GAAG,CAAZ;AACA,cAAIC,IAAI,GAAG,CAAX;;AAEA,eAAK,IAAIC,EAAE,GAAG,CAAd,EAAiBA,EAAE,GAAGjB,KAAtB,EAA6BiB,EAAE,EAA/B,EAAmC;AACjC,iBAAK,IAAIC,EAAE,GAAG,CAAd,EAAiBA,EAAE,GAAGlB,KAAtB,EAA6BkB,EAAE,EAA/B,EAAmC;AACjC,kBAAMC,EAAE,GAAGrB,IAAI,CAACsB,GAAL,CAAS,KAAKX,MAAL,CAAYE,KAAZ,GAAoB,CAA7B,EAAgCb,IAAI,CAACuB,GAAL,CAAS,CAAT,EAAYH,EAAE,GAAGZ,EAAL,GAAST,EAArB,CAAhC,CAAX;AACA,kBAAMyB,EAAE,GAAGxB,IAAI,CAACsB,GAAL,CAAS,KAAKX,MAAL,CAAYC,MAAZ,GAAqB,CAA9B,EAAiCZ,IAAI,CAACuB,GAAL,CAAS,CAAT,EAAYJ,EAAE,GAAGZ,EAAL,GAASR,EAArB,CAAjC,CAAX;AACA,kBAAM0B,MAAM,GAAGnB,OAAO,CAACa,EAAD,CAAP,CAAYC,EAAZ,CAAf;;AACA,kBAAMM,IAAG,GAAIF,EAAE,GAAG,KAAKb,MAAL,CAAYE,KAAjB,GAAyBQ,EAA1B,IAAiC,CAA7C;;AAEAP,cAAAA,GAAG,IAAI,KAAKH,MAAL,CAAYgB,IAAZ,CAAiBD,IAAjB,IAAwBD,MAA/B;AACAV,cAAAA,KAAK,IAAI,KAAKJ,MAAL,CAAYgB,IAAZ,CAAiBD,IAAG,GAAG,CAAvB,IAA4BD,MAArC;AACAT,cAAAA,IAAI,IAAI,KAAKL,MAAL,CAAYgB,IAAZ,CAAiBD,IAAG,GAAG,CAAvB,IAA4BD,MAApC;AACAR,cAAAA,KAAK,IAAI,KAAKN,MAAL,CAAYgB,IAAZ,CAAiBD,IAAG,GAAG,CAAvB,IAA4BD,MAArC;AACAP,cAAAA,IAAI,IAAIO,MAAR;AACD;;AAED,gBAAMC,GAAG,GAAInB,EAAC,GAAG,KAAKI,MAAL,CAAYE,KAAhB,GAAwBL,EAAzB,IAA+B,CAA3C;AAEA,iBAAKG,MAAL,CAAYgB,IAAZ,CAAiBD,GAAjB,IAAwB1B,IAAI,CAAC4B,KAAL,CAAWd,GAAG,GAAGI,IAAjB,CAAxB;AACA,iBAAKP,MAAL,CAAYgB,IAAZ,CAAiBD,GAAG,GAAG,CAAvB,IAA4B1B,IAAI,CAAC4B,KAAL,CAAWb,KAAK,GAAGG,IAAnB,CAA5B;AACA,iBAAKP,MAAL,CAAYgB,IAAZ,CAAiBD,GAAG,GAAG,CAAvB,IAA4B1B,IAAI,CAAC4B,KAAL,CAAWZ,IAAI,GAAGE,IAAlB,CAA5B;AACA,iBAAKP,MAAL,CAAYgB,IAAZ,CAAiBD,GAAG,GAAG,CAAvB,IAA4B1B,IAAI,CAAC4B,KAAL,CAAWX,KAAK,GAAGC,IAAnB,CAA5B;AACD;AACF;AACF;;AAED,UAAI,0BAActB,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACE,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD;AA/DmB,GAAP;AAAA,C", "sourcesContent": ["import { isNodePattern, throwError } from '@jimp/utils';\n\n/**\n * Applies a true Gaussian blur to the image (warning: this is VERY slow)\n * @param {number} r the pixel radius of the blur\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\nexport default () => ({\n  gaussian(r, cb) {\n    // http://blog.ivank.net/fastest-gaussian-blur.html\n    if (typeof r !== 'number') {\n      return throwError.call(this, 'r must be a number', cb);\n    }\n\n    if (r < 1) {\n      return throwError.call(this, 'r must be greater than 0', cb);\n    }\n\n    const rs = Math.ceil(r * 2.57); // significant radius\n    const range = rs * 2 + 1;\n    const rr2 = r * r * 2;\n    const rr2pi = rr2 * Math.PI;\n\n    const weights = [];\n\n    for (let y = 0; y < range; y++) {\n      weights[y] = [];\n      for (let x = 0; x < range; x++) {\n        const dsq = (x - rs) ** 2 + (y - rs) ** 2 ;\n        weights[y][x] = Math.exp(-dsq / rr2) / rr2pi;\n      }\n    }\n\n    for (let y = 0; y < this.bitmap.height; y++) {\n      for (let x = 0; x < this.bitmap.width; x++) {\n        let red = 0;\n        let green = 0;\n        let blue = 0;\n        let alpha = 0;\n        let wsum = 0;\n\n        for (let iy = 0; iy < range; iy++) {\n          for (let ix = 0; ix < range; ix++) {\n            const x1 = Math.min(this.bitmap.width - 1, Math.max(0, ix + x - rs ));\n            const y1 = Math.min(this.bitmap.height - 1, Math.max(0, iy + y - rs));\n            const weight = weights[iy][ix];\n            const idx = (y1 * this.bitmap.width + x1) << 2;\n\n            red += this.bitmap.data[idx] * weight;\n            green += this.bitmap.data[idx + 1] * weight;\n            blue += this.bitmap.data[idx + 2] * weight;\n            alpha += this.bitmap.data[idx + 3] * weight;\n            wsum += weight;\n          }\n\n          const idx = (y * this.bitmap.width + x) << 2;\n\n          this.bitmap.data[idx] = Math.round(red / wsum);\n          this.bitmap.data[idx + 1] = Math.round(green / wsum);\n          this.bitmap.data[idx + 2] = Math.round(blue / wsum);\n          this.bitmap.data[idx + 3] = Math.round(alpha / wsum);\n        }\n      }\n    }\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  }\n});\n"], "file": "index.js"}
"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.UseBuiltInsOption = exports.TopLevelOptions = exports.ModulesOption = void 0;
const TopLevelOptions = exports.TopLevelOptions = {
  configPath: "configPath",
  corejs: "corejs",
  debug: "debug",
  exclude: "exclude",
  forceAllTransforms: "forceAllTransforms",
  ignoreBrowserslistConfig: "ignoreBrowserslistConfig",
  include: "include",
  modules: "modules",
  shippedProposals: "shippedProposals",
  targets: "targets",
  useBuiltIns: "useBuiltIns",
  browserslistEnv: "browserslistEnv"
};
{
  Object.assign(TopLevelOptions, {
    bugfixes: "bugfixes",
    loose: "loose",
    spec: "spec"
  });
}
const ModulesOption = exports.ModulesOption = {
  false: false,
  auto: "auto",
  amd: "amd",
  commonjs: "commonjs",
  cjs: "cjs",
  systemjs: "systemjs",
  umd: "umd"
};
const UseBuiltInsOption = exports.UseBuiltInsOption = {
  false: false,
  entry: "entry",
  usage: "usage"
};

//# sourceMappingURL=options.js.map

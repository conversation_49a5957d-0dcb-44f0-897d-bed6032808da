const set = require('regenerate')(0x1CDA, 0x1CF2);
set.addRange(0x951, 0x952).addRange(0x964, 0x965).addRange(0xB01, 0xB03).addRange(0xB05, 0xB0C).addRange(0xB0F, 0xB10).addRange(0xB13, 0xB28).addRange(0xB2A, 0xB30).addRange(0xB32, 0xB33).addRange(0xB35, 0xB39).addRange(0xB3C, 0xB44).addRange(0xB47, 0xB48).addRange(0xB4B, 0xB4D).addRange(0xB55, 0xB57).addRange(0xB5C, 0xB5D).addRange(0xB5F, 0xB63).addRange(0xB66, 0xB77);
exports.characters = set;

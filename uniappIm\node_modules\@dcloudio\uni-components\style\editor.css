.ql-container {
  display: block;
  position: relative;
  box-sizing: border-box;
  user-select: text;
  outline: none;
  overflow: hidden;
  width: 100%;
  height: 200px;
  min-height: 200px;
}
.ql-container[hidden] {
  display: none;
}
.ql-container .ql-editor {
  position: relative;
  font-size: inherit;
  line-height: inherit;
  font-family: inherit;
  min-height: inherit;
  width: 100%;
  height: 100%;
  padding: 0;
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-overflow-scrolling: touch;
}
.ql-container .ql-editor::-webkit-scrollbar {
  width: 0 !important;
}
.ql-container .ql-editor.scroll-disabled {
  overflow: hidden;
}
.ql-container .ql-image-overlay {
  display: flex;
  position: absolute;
  box-sizing: border-box;
  border: 1px dashed #ccc;
  justify-content: center;
  align-items: center;
  user-select: none;
}
.ql-container .ql-image-overlay .ql-image-size {
  position: absolute;
  padding: 4px 8px;
  text-align: center;
  background-color: #fff;
  color: #888;
  border: 1px solid #ccc;
  box-sizing: border-box;
  opacity: 0.8;
  right: 4px;
  top: 4px;
  font-size: 12px;
  display: inline-block;
  width: auto;
}
.ql-container .ql-image-overlay .ql-image-toolbar {
  position: relative;
  text-align: center;
  box-sizing: border-box;
  background: #000;
  border-radius: 5px;
  color: #fff;
  font-size: 0;
  min-height: 24px;
  z-index: 100;
}
.ql-container .ql-image-overlay .ql-image-toolbar span {
  display: inline-block;
  cursor: pointer;
  padding: 5px;
  font-size: 12px;
  border-right: 1px solid #fff;
}
.ql-container .ql-image-overlay .ql-image-toolbar span:last-child {
  border-right: 0;
}
.ql-container .ql-image-overlay .ql-image-toolbar span.triangle-up {
  padding: 0;
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translatex(-50%);
  width: 0;
  height: 0;
  border-width: 6px;
  border-style: solid;
  border-color: transparent transparent black transparent;
}
.ql-container .ql-image-overlay .ql-image-handle {
  position: absolute;
  height: 12px;
  width: 12px;
  border-radius: 50%;
  border: 1px solid #ccc;
  box-sizing: border-box;
  background: #fff;
}
.ql-container img {
  display: inline-block;
  max-width: 100%;
}
.ql-clipboard p {
  margin: 0;
  padding: 0;
}
.ql-editor {
  box-sizing: border-box;
  height: 100%;
  outline: none;
  overflow-y: auto;
  tab-size: 4;
  -moz-tab-size: 4;
  text-align: left;
  white-space: pre-wrap;
  word-wrap: break-word;
}
.ql-editor > * {
  cursor: text;
}
.ql-editor p,
.ql-editor ol,
.ql-editor ul,
.ql-editor pre,
.ql-editor blockquote,
.ql-editor h1,
.ql-editor h2,
.ql-editor h3,
.ql-editor h4,
.ql-editor h5,
.ql-editor h6 {
  margin: 0;
  padding: 0;
  counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol > li,
.ql-editor ul > li {
  list-style-type: none;
}
.ql-editor ul > li::before {
  content: '\2022';
}
.ql-editor ul[data-checked=true],
.ql-editor ul[data-checked=false] {
  pointer-events: none;
}
.ql-editor ul[data-checked=true] > li *,
.ql-editor ul[data-checked=false] > li * {
  pointer-events: all;
}
.ql-editor ul[data-checked=true] > li::before,
.ql-editor ul[data-checked=false] > li::before {
  color: #777;
  cursor: pointer;
  pointer-events: all;
}
.ql-editor ul[data-checked=true] > li::before {
  content: '\2611';
}
.ql-editor ul[data-checked=false] > li::before {
  content: '\2610';
}
.ql-editor li::before {
  display: inline-block;
  white-space: nowrap;
  width: 2em;
}
.ql-editor ol li {
  counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
  counter-increment: list-0;
}
.ql-editor ol li:before {
  content: counter(list-0, decimal) '. ';
}
.ql-editor ol li.ql-indent-1 {
  counter-increment: list-1;
}
.ql-editor ol li.ql-indent-1:before {
  content: counter(list-1, lower-alpha) '. ';
}
.ql-editor ol li.ql-indent-1 {
  counter-reset: list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-2 {
  counter-increment: list-2;
}
.ql-editor ol li.ql-indent-2:before {
  content: counter(list-2, lower-roman) '. ';
}
.ql-editor ol li.ql-indent-2 {
  counter-reset: list-3 list-4 list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-3 {
  counter-increment: list-3;
}
.ql-editor ol li.ql-indent-3:before {
  content: counter(list-3, decimal) '. ';
}
.ql-editor ol li.ql-indent-3 {
  counter-reset: list-4 list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-4 {
  counter-increment: list-4;
}
.ql-editor ol li.ql-indent-4:before {
  content: counter(list-4, lower-alpha) '. ';
}
.ql-editor ol li.ql-indent-4 {
  counter-reset: list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-5 {
  counter-increment: list-5;
}
.ql-editor ol li.ql-indent-5:before {
  content: counter(list-5, lower-roman) '. ';
}
.ql-editor ol li.ql-indent-5 {
  counter-reset: list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-6 {
  counter-increment: list-6;
}
.ql-editor ol li.ql-indent-6:before {
  content: counter(list-6, decimal) '. ';
}
.ql-editor ol li.ql-indent-6 {
  counter-reset: list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-7 {
  counter-increment: list-7;
}
.ql-editor ol li.ql-indent-7:before {
  content: counter(list-7, lower-alpha) '. ';
}
.ql-editor ol li.ql-indent-7 {
  counter-reset: list-8 list-9;
}
.ql-editor ol li.ql-indent-8 {
  counter-increment: list-8;
}
.ql-editor ol li.ql-indent-8:before {
  content: counter(list-8, lower-roman) '. ';
}
.ql-editor ol li.ql-indent-8 {
  counter-reset: list-9;
}
.ql-editor ol li.ql-indent-9 {
  counter-increment: list-9;
}
.ql-editor ol li.ql-indent-9:before {
  content: counter(list-9, decimal) '. ';
}
.ql-editor .ql-indent-1:not(.ql-direction-rtl) {
  padding-left: 2em;
}
.ql-editor li.ql-indent-1:not(.ql-direction-rtl) {
  padding-left: 2em;
}
.ql-editor .ql-indent-1.ql-direction-rtl.ql-align-right {
  padding-right: 2em;
}
.ql-editor li.ql-indent-1.ql-direction-rtl.ql-align-right {
  padding-right: 2em;
}
.ql-editor .ql-indent-2:not(.ql-direction-rtl) {
  padding-left: 4em;
}
.ql-editor li.ql-indent-2:not(.ql-direction-rtl) {
  padding-left: 4em;
}
.ql-editor .ql-indent-2.ql-direction-rtl.ql-align-right {
  padding-right: 4em;
}
.ql-editor li.ql-indent-2.ql-direction-rtl.ql-align-right {
  padding-right: 4em;
}
.ql-editor .ql-indent-3:not(.ql-direction-rtl) {
  padding-left: 6em;
}
.ql-editor li.ql-indent-3:not(.ql-direction-rtl) {
  padding-left: 6em;
}
.ql-editor .ql-indent-3.ql-direction-rtl.ql-align-right {
  padding-right: 6em;
}
.ql-editor li.ql-indent-3.ql-direction-rtl.ql-align-right {
  padding-right: 6em;
}
.ql-editor .ql-indent-4:not(.ql-direction-rtl) {
  padding-left: 8em;
}
.ql-editor li.ql-indent-4:not(.ql-direction-rtl) {
  padding-left: 8em;
}
.ql-editor .ql-indent-4.ql-direction-rtl.ql-align-right {
  padding-right: 8em;
}
.ql-editor li.ql-indent-4.ql-direction-rtl.ql-align-right {
  padding-right: 8em;
}
.ql-editor .ql-indent-5:not(.ql-direction-rtl) {
  padding-left: 10em;
}
.ql-editor li.ql-indent-5:not(.ql-direction-rtl) {
  padding-left: 10em;
}
.ql-editor .ql-indent-5.ql-direction-rtl.ql-align-right {
  padding-right: 10em;
}
.ql-editor li.ql-indent-5.ql-direction-rtl.ql-align-right {
  padding-right: 10em;
}
.ql-editor .ql-indent-6:not(.ql-direction-rtl) {
  padding-left: 12em;
}
.ql-editor li.ql-indent-6:not(.ql-direction-rtl) {
  padding-left: 12em;
}
.ql-editor .ql-indent-6.ql-direction-rtl.ql-align-right {
  padding-right: 12em;
}
.ql-editor li.ql-indent-6.ql-direction-rtl.ql-align-right {
  padding-right: 12em;
}
.ql-editor .ql-indent-7:not(.ql-direction-rtl) {
  padding-left: 14em;
}
.ql-editor li.ql-indent-7:not(.ql-direction-rtl) {
  padding-left: 14em;
}
.ql-editor .ql-indent-7.ql-direction-rtl.ql-align-right {
  padding-right: 14em;
}
.ql-editor li.ql-indent-7.ql-direction-rtl.ql-align-right {
  padding-right: 14em;
}
.ql-editor .ql-indent-8:not(.ql-direction-rtl) {
  padding-left: 16em;
}
.ql-editor li.ql-indent-8:not(.ql-direction-rtl) {
  padding-left: 16em;
}
.ql-editor .ql-indent-8.ql-direction-rtl.ql-align-right {
  padding-right: 16em;
}
.ql-editor li.ql-indent-8.ql-direction-rtl.ql-align-right {
  padding-right: 16em;
}
.ql-editor .ql-indent-9:not(.ql-direction-rtl) {
  padding-left: 18em;
}
.ql-editor li.ql-indent-9:not(.ql-direction-rtl) {
  padding-left: 18em;
}
.ql-editor .ql-indent-9.ql-direction-rtl.ql-align-right {
  padding-right: 18em;
}
.ql-editor li.ql-indent-9.ql-direction-rtl.ql-align-right {
  padding-right: 18em;
}
.ql-editor .ql-direction-rtl {
  direction: rtl;
  text-align: inherit;
}
.ql-editor .ql-align-center {
  text-align: center;
}
.ql-editor .ql-align-justify {
  text-align: justify;
}
.ql-editor .ql-align-right {
  text-align: right;
}
.ql-editor.ql-blank::before {
  color: rgba(0, 0, 0, 0.6);
  content: attr(data-placeholder);
  font-style: italic;
  pointer-events: none;
  position: absolute;
}
.ql-container.ql-disabled .ql-editor ul[data-checked] > li::before {
  pointer-events: none;
}
.ql-clipboard {
  left: -100000px;
  height: 1px;
  overflow-y: hidden;
  position: absolute;
  top: 50%;
}

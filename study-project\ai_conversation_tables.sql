/*
 AI对话功能数据库表结构
 
 功能说明：
 1. ai_conversation - 对话会话表，管理用户的多个对话
 2. ai_message - 消息记录表，存储所有对话消息
 3. ai_conversation_config - 对话配置表，存储每个对话的个性化配置
 4. ai_usage_statistics - 使用统计表，记录用户AI使用情况
 5. ai_feedback - 反馈表，收集用户对AI回复的反馈
 
 特性：
 - 支持单个用户多个对话
 - 支持流式输出状态管理
 - 支持多种消息类型（文本、图片、语音等）
 - 支持AI使用统计和反馈收集
 - 支持对话配置个性化
 
 Date: 2025-07-29
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for ai_conversation
-- ----------------------------
DROP TABLE IF EXISTS `ai_conversation`;
CREATE TABLE `ai_conversation`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '对话ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `conversation_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '对话标题',
  `session_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'AI服务会话ID',
  `conversation_type` enum('GENERAL','TUTORING','VOICE','IMAGE') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'GENERAL' COMMENT '对话类型：通用对话、辅导对话、语音对话、图像对话',
  `status` enum('ACTIVE','ARCHIVED','DELETED') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'ACTIVE' COMMENT '对话状态：活跃、已归档、已删除',
  `last_message_time` datetime NULL DEFAULT NULL COMMENT '最后消息时间',
  `message_count` int NULL DEFAULT 0 COMMENT '消息总数',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_session_id`(`session_id` ASC) USING BTREE,
  INDEX `idx_status_update_time`(`status` ASC, `update_time` DESC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'AI对话会话表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ai_message
-- ----------------------------
DROP TABLE IF EXISTS `ai_message`;
CREATE TABLE `ai_message`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `conversation_id` bigint NOT NULL COMMENT '对话ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `message_type` enum('USER','AI','SYSTEM') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '消息类型：用户消息、AI回复、系统消息',
  `content_type` enum('TEXT','IMAGE','FILE','VOICE','SYSTEM_NOTICE') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'TEXT' COMMENT '内容类型：文本、图片、文件、语音、系统通知',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '消息内容',
  `content_length` int NULL DEFAULT 0 COMMENT '内容长度',
  `ai_model` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'AI模型名称',
  `prompt_tokens` int NULL DEFAULT 0 COMMENT '输入token数',
  `completion_tokens` int NULL DEFAULT 0 COMMENT '输出token数',
  `total_tokens` int NULL DEFAULT 0 COMMENT '总token数',
  `response_time` int NULL DEFAULT 0 COMMENT '响应时间(毫秒)',
  `is_streaming` tinyint NULL DEFAULT 0 COMMENT '是否流式输出：0-否，1-是',
  `stream_status` enum('PENDING','STREAMING','COMPLETED','FAILED') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'COMPLETED' COMMENT '流式状态：待处理、流式中、已完成、失败',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '错误信息',
  `metadata` json NULL COMMENT '扩展元数据(JSON格式)',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_conversation_id`(`conversation_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_message_type`(`message_type` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` DESC) USING BTREE,
  INDEX `idx_stream_status`(`stream_status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'AI消息记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ai_conversation_config
-- ----------------------------
DROP TABLE IF EXISTS `ai_conversation_config`;
CREATE TABLE `ai_conversation_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `conversation_id` bigint NOT NULL COMMENT '对话ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `ai_model` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'qwen-turbo' COMMENT 'AI模型',
  `temperature` decimal(3,2) NULL DEFAULT 0.70 COMMENT '温度参数(0.0-1.0)',
  `max_tokens` int NULL DEFAULT 2000 COMMENT '最大token数',
  `system_prompt` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '系统提示词',
  `enable_streaming` tinyint NULL DEFAULT 1 COMMENT '是否启用流式输出：0-否，1-是',
  `enable_context` tinyint NULL DEFAULT 1 COMMENT '是否启用上下文：0-否，1-是',
  `context_length` int NULL DEFAULT 10 COMMENT '上下文长度(消息条数)',
  `auto_title` tinyint NULL DEFAULT 1 COMMENT '是否自动生成标题：0-否，1-是',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_conversation_id`(`conversation_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'AI对话配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ai_usage_statistics
-- ----------------------------
DROP TABLE IF EXISTS `ai_usage_statistics`;
CREATE TABLE `ai_usage_statistics`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `date` date NOT NULL COMMENT '统计日期',
  `conversation_count` int NULL DEFAULT 0 COMMENT '对话数量',
  `message_count` int NULL DEFAULT 0 COMMENT '消息数量',
  `user_message_count` int NULL DEFAULT 0 COMMENT '用户消息数量',
  `ai_message_count` int NULL DEFAULT 0 COMMENT 'AI回复数量',
  `total_tokens` bigint NULL DEFAULT 0 COMMENT '总token消耗',
  `prompt_tokens` bigint NULL DEFAULT 0 COMMENT '输入token消耗',
  `completion_tokens` bigint NULL DEFAULT 0 COMMENT '输出token消耗',
  `total_response_time` bigint NULL DEFAULT 0 COMMENT '总响应时间(毫秒)',
  `avg_response_time` int NULL DEFAULT 0 COMMENT '平均响应时间(毫秒)',
  `streaming_count` int NULL DEFAULT 0 COMMENT '流式输出次数',
  `error_count` int NULL DEFAULT 0 COMMENT '错误次数',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_date`(`user_id` ASC, `date` ASC) USING BTREE,
  INDEX `idx_date`(`date` DESC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'AI使用统计表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ai_feedback
-- ----------------------------
DROP TABLE IF EXISTS `ai_feedback`;
CREATE TABLE `ai_feedback`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '反馈ID',
  `message_id` bigint NOT NULL COMMENT '消息ID',
  `conversation_id` bigint NOT NULL COMMENT '对话ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `feedback_type` enum('LIKE','DISLIKE','REPORT') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '反馈类型：喜欢、不喜欢、举报',
  `feedback_reason` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '反馈原因',
  `feedback_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '反馈内容',
  `rating` tinyint NULL DEFAULT NULL COMMENT '评分(1-5)',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_message_user`(`message_id` ASC, `user_id` ASC) USING BTREE,
  INDEX `idx_conversation_id`(`conversation_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_feedback_type`(`feedback_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'AI反馈表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

<template>
  <view class="container">
    <!-- 自定义状态栏 -->
    <view class="custom-status-bar">
      <view class="status-content">
        <text class="app-title">首页</text>
      </view>
    </view>

    <!-- 主要内容 -->
    <view class="main-content">
      <!-- 轮播图 -->
      <view class="banner-section">
        <swiper class="banner-swiper" indicator-dots="true" autoplay="true" interval="3000" duration="500">
          <swiper-item v-for="(banner, index) in bannerList" :key="index">
            <view class="banner-item" :style="'background: ' + banner.bgColor">
              <view class="banner-content">
                <text class="banner-title">{{ banner.title }}</text>
                <text class="banner-desc">{{ banner.desc }}</text>
                <view class="banner-icon">{{ banner.icon }}</view>
              </view>
            </view>
          </swiper-item>
        </swiper>
      </view>

      <!-- AI语音对话区域 -->
      <view class="ai-chat-section">
        <view class="section-title">
          <text class="title-icon">🤖</text>
          <text class="title-text">AI智能对话</text>
        </view>
        <view class="chat-card" @tap="startVoiceChat">
          <view class="chat-content">
            <view class="voice-icon">🎤</view>
            <text class="chat-title">开启AI模式与AI对话</text>
            <text class="chat-desc">语音交互，智能回答各种问题</text>
          </view>
          <view class="chat-arrow">→</view>
        </view>
      </view>

      <!-- 功能模块网格 -->
      <view class="functions-section">
        <view class="section-title">
          <text class="title-icon">⭐</text>
          <text class="title-text">智能功能</text>
        </view>
        <view class="functions-grid">
          <!-- 第一行 -->
          <view class="function-item" @tap="openFunction('knowledge')" @longpress="showFunctionDetail('knowledge')">
            <view class="function-icon knowledge">📚</view>
            <text class="function-name">知识问答</text>
            <text class="function-desc">AI百科全书</text>
          </view>
          <view class="function-item" @tap="openFunction('search')" @longpress="showFunctionDetail('search')">
            <view class="function-icon search">🔍</view>
            <text class="function-name">信息查询</text>
            <text class="function-desc">天气·电话·资讯</text>
          </view>
          <view class="function-item" @tap="openFunction('writing')" @longpress="showFunctionDetail('writing')">
            <view class="function-icon writing">✍️</view>
            <text class="function-name">文本生成</text>
            <text class="function-desc">作文·故事·诗歌</text>
          </view>

          <!-- 第二行 -->
          <view class="function-item" @tap="openFunction('translate')" @longpress="showFunctionDetail('translate')">
            <view class="function-icon translate">🌐</view>
            <text class="function-name">语言翻译</text>
            <text class="function-desc">多语言互译</text>
          </view>
          <view class="function-item" @tap="openFunction('emotion')" @longpress="showFunctionDetail('emotion')">
            <view class="function-icon emotion">💝</view>
            <text class="function-name">情感陪伴</text>
            <text class="function-desc">情感识别回应</text>
          </view>
          <view class="function-item" @tap="openFunction('recommend')" @longpress="showFunctionDetail('recommend')">
            <view class="function-icon recommend">🎯</view>
            <text class="function-name">智能推荐</text>
            <text class="function-desc">个性化内容</text>
          </view>

          <!-- 第三行 -->
          <view class="function-item" @tap="openFunction('reminder')" @longpress="showFunctionDetail('reminder')">
            <view class="function-icon reminder">⏰</view>
            <text class="function-name">任务提醒</text>
            <text class="function-desc">生日·闹钟·事项</text>
          </view>
          <view class="function-item" @tap="openFunction('game')" @longpress="showFunctionDetail('game')">
            <view class="function-icon game">🎮</view>
            <text class="function-name">游戏娱乐</text>
            <text class="function-desc">猜谜·接龙·问答</text>
          </view>
          <view class="function-item" @tap="openFunction('health')" @longpress="showFunctionDetail('health')">
            <view class="function-icon health">💪</view>
            <text class="function-name">健康管理</text>
            <text class="function-desc">运动·饮食·睡眠</text>
          </view>
        </view>
      </view>

      <!-- 快捷功能区域 -->
      <view class="quick-actions">
        <view class="action-item" @tap="openFunction('photo-search')" @longpress="showFunctionDetail('photo-search')">
          <view class="action-icon">📷</view>
          <text class="action-text">拍照搜题</text>
        </view>
        <view class="action-item" @tap="openFunction('video-call')" @longpress="showFunctionDetail('video-call')">
          <view class="action-icon">📹</view>
          <text class="action-text">视频通话</text>
        </view>
        <view class="action-item" @tap="openFunction('exam')" @longpress="showFunctionDetail('exam')">
          <view class="action-icon">📝</view>
          <text class="action-text">模拟考试</text>
        </view>
        <view class="action-item" @tap="openFunction('textbook')" @longpress="showFunctionDetail('textbook')">
          <view class="action-icon">📖</view>
          <text class="action-text">课本学习</text>
        </view>
      </view>
    </view>

    <!-- 底部导航栏 -->
    <view class="bottom-navigation">
      <view class="nav-item active">
        <text class="nav-icon">🏠</text>
        <text class="nav-text">首页</text>
      </view>

      <!-- 美化的拍照搜题按钮 -->
      <view class="camera-section">
        <view class="camera-btn" @tap="showCameraInfo">
          <view class="camera-icon">📷</view>
        </view>
        <text class="camera-text">拍照搜题</text>
      </view>

      <view class="nav-item" @tap="goToProfile">
        <text class="nav-icon">👤</text>
        <text class="nav-text">我的</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      bannerList: [
        {
          title: '欢迎使用AI智能家教',
          desc: '开启智能学习新体验',
          icon: '🎓',
          bgColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        },
        {
          title: 'AI语音对话',
          desc: '智能语音交互，随时随地学习',
          icon: '🎤',
          bgColor: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
        },
        {
          title: '个性化学习',
          desc: '量身定制学习计划',
          icon: '📚',
          bgColor: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
        }
      ]
    }
  },
  methods: {
    // 跳转到个人中心
    goToProfile() {
      uni.switchTab({
        url: '/pages/ai-home/ai-home'
      });
    },

    // 拍照搜题提示
    showCameraInfo() {
      uni.showToast({
        title: '拍照搜题功能开发中',
        icon: 'none',
        duration: 2000
      });
    },

    // AI对话功能
    startVoiceChat() {
      uni.showToast({
        title: '正在启动AI对话...',
        icon: 'loading'
      });
      // TODO: 实现语音对话功能
    },

    // 功能跳转方法
    openFunction(type) {
      const functionRoutes = {
        'knowledge': '/pages/ai-knowledge/ai-knowledge',
        'search': '/pages/ai-search/ai-search',
        'writing': '/pages/ai-writing/ai-writing',
        'translate': '/pages/ai-translate/ai-translate',
        'emotion': '/pages/ai-emotion/ai-emotion',
        'recommend': '/pages/ai-recommend/ai-recommend',
        'reminder': '/pages/ai-reminder/ai-reminder',
        'game': '/pages/ai-game/ai-game',
        'health': '/pages/ai-health/ai-health',
        'photo-search': '/pages/ai-photo/ai-photo',
        'video-call': '/pages/ai-video/ai-video',
        'exam': '/pages/ai-exam/ai-exam',
        'textbook': '/pages/ai-textbook/ai-textbook'
      };

      const route = functionRoutes[type];
      if (route) {
        // 检查页面是否存在
        const availablePages = ['knowledge', 'search', 'writing', 'translate', 'emotion', 'recommend'];
        if (availablePages.includes(type)) {
          uni.navigateTo({
            url: route,
            fail: (err) => {
              console.error('页面跳转失败:', err);
              uni.showToast({
                title: '页面跳转失败',
                icon: 'none'
              });
            }
          });
        } else {
          uni.showToast({
            title: '功能开发中，敬请期待',
            icon: 'none'
          });
        }
      } else {
        uni.showToast({
          title: '功能开发中，敬请期待',
          icon: 'none'
        });
      }
    },

    // 显示功能详情
    showFunctionDetail(type) {
      const functionDetails = {
        'knowledge': {
          title: '知识问答',
          icon: '📚',
          description: '让AI能够回答各种问题，如历史、科学、技术、文化等方面的问题，就像一个知识渊博的助手。',
          examples: [
            '地球的直径是多少？',
            '中国古代四大发明是什么？',
            '光的传播速度是多少？'
          ]
        },
        'search': {
          title: '信息查询',
          icon: '🔍',
          description: '帮助学生查询各类信息，如天气、父母电话等实用信息。',
          examples: [
            '查询明天保定的天气',
            '查询爸爸的电话号码',
            '今天的新闻热点'
          ]
        },
        'writing': {
          title: '文本生成',
          icon: '✍️',
          description: '包括写作文章、故事、诗歌、摘要等各类文本创作。',
          examples: [
            '写一首关于春天的诗',
            '写一个关于友谊的小故事',
            '帮我写一篇关于环保的作文'
          ]
        },
        'translate': {
          title: '语言翻译',
          icon: '🌐',
          description: '实现不同语言之间的即时翻译，支持多种主流语言。',
          examples: [
            '把"我喜欢振涛"翻译成英语',
            '这句日语是什么意思？',
            '帮我翻译这段法语'
          ]
        },
        'emotion': {
          title: '情感陪伴',
          icon: '💝',
          description: '识别用户的情感状态，如高兴、生气、悲伤等，并给予相应的情感回应和安慰。',
          examples: [
            '我今天很难过',
            '我考试考得不好',
            '我今天吃了什么'
          ]
        },
        'recommend': {
          title: '智能推荐',
          icon: '🎯',
          description: '根据用户的兴趣和学习情况，推荐相关的学习内容、书籍、视频等。',
          examples: [
            '推荐一些数学学习资料',
            '推荐适合我的英语电影',
            '推荐一些科学实验'
          ]
        }
      };

      const detail = functionDetails[type];
      if (detail) {
        const exampleText = detail.examples.map(ex => `• ${ex}`).join('\n');
        uni.showModal({
          title: `${detail.icon} ${detail.title}`,
          content: `${detail.description}\n\n示例用法：\n${exampleText}`,
          showCancel: true,
          cancelText: '取消',
          confirmText: '立即体验',
          success: (res) => {
            if (res.confirm) {
              this.openFunction(type);
            }
          }
        });
      }
    }
  }
}
</script>

<style>
/* 自定义状态栏 */
.custom-status-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 88rpx;
  background: #2c3e50;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.app-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
}

.container {
  min-height: 100vh;
  background: #f8f9fa;
  padding-top: 108rpx;
  padding-bottom: 140rpx;
}

/* 主要内容 */
.main-content {
  flex: 1;
  padding: 20rpx;
  padding-bottom: 120rpx;
}

/* 欢迎区域 */
.welcome-section {
  text-align: center;
  padding: 60rpx 40rpx;
  background: white;
  border-radius: 20rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.welcome-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20rpx;
  display: block;
}

.welcome-desc {
  font-size: 28rpx;
  color: #7f8c8d;
  display: block;
}
/* 轮播图样式 */
.banner-section {
  margin-bottom: 40rpx;
}

.banner-swiper {
  height: 300rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

.banner-item {
  height: 100%;
  border-radius: 20rpx;
  position: relative;
  overflow: hidden;
}

.banner-content {
  position: absolute;
  left: 40rpx;
  top: 50%;
  transform: translateY(-50%);
  color: white;
}

.banner-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.banner-desc {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
}

.banner-icon {
  position: absolute;
  right: -200rpx;
  top: -50rpx;
  font-size: 200rpx;
  opacity: 0.2;
}

/* AI对话模块 */
.ai-chat-section {
  margin-bottom: 40rpx;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.chat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  display: flex;
  align-items: center;
  color: white;
  box-shadow: 0 10rpx 30rpx rgba(102, 126, 234, 0.3);
}

.voice-icon {
  font-size: 60rpx;
  margin-right: 30rpx;
}

.chat-content {
  flex: 1;
}

.chat-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.chat-desc {
  display: block;
  font-size: 26rpx;
  opacity: 0.9;
}

.chat-arrow {
  font-size: 40rpx;
  opacity: 0.8;
}
/* 智能功能模块 */
.functions-section {
  margin-bottom: 40rpx;
}

.functions-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.function-item {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.function-item:active {
  transform: scale(0.95);
}

.function-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15rpx;
  font-size: 40rpx;
}

.knowledge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.search {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.writing {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.translate {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.emotion {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.recommend {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.reminder {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.game {
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
}

.health {
  background: linear-gradient(135deg, #fad0c4 0%, #ffd1ff 100%);
}

.function-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.function-desc {
  display: block;
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
}

/* 快捷功能区域 */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 15rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.action-item:active {
  transform: scale(0.95);
}

.action-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 50rpx;
  margin-bottom: 15rpx;
  background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
  color: white;
}

.action-text {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
  text-align: center;
}





/* 底部导航栏 */
.bottom-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: white;
  display: flex;
  align-items: center;
  justify-content: space-around;
  border-top: 1rpx solid #e5e5e5;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 拍照搜题按钮区域 */
.camera-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.camera-btn {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 15rpx rgba(255, 107, 107, 0.4);
  transform: translateY(-10rpx);
  transition: all 0.3s ease;
  margin-bottom: 8rpx;
}

.camera-btn:active {
  transform: translateY(-8rpx) scale(0.95);
  box-shadow: 0 2rpx 10rpx rgba(255, 107, 107, 0.6);
}

.camera-icon {
  font-size: 45rpx;
  color: white;
}

.camera-text {
  font-size: 20rpx;
  color: #666;
  font-weight: 500;
  text-align: center;
  transform: translateY(-10rpx);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 10rpx 0;
  transition: all 0.3s ease;
}

.nav-item.active .nav-icon {
  color: #007AFF;
  transform: scale(1.1);
}

.nav-item.active .nav-text {
  color: #007AFF;
  font-weight: 600;
}

.nav-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
  color: #666;
  transition: all 0.3s ease;
}

.nav-text {
  font-size: 20rpx;
  color: #666;
  transition: all 0.3s ease;
}
</style>

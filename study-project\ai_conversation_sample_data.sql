/*
 AI对话功能示例数据
 
 用于测试AI对话功能的示例数据
 包含完整的对话流程示例
 
 Date: 2025-07-29
*/

SET NAMES utf8mb4;

-- 插入示例对话会话
INSERT INTO `ai_conversation` (`id`, `user_id`, `conversation_title`, `session_id`, `conversation_type`, `status`, `last_message_time`, `message_count`, `create_time`, `remark`) VALUES
(1, 2, '学习Java编程', 'session_001_java', 'TUTORING', 'ACTIVE', '2025-07-29 16:30:00', 4, '2025-07-29 16:00:00', 'Java学习辅导对话'),
(2, 2, '日常聊天', 'session_002_chat', 'GENERAL', 'ACTIVE', '2025-07-29 17:00:00', 2, '2025-07-29 16:45:00', '日常对话'),
(3, 3, 'AI助手介绍', 'session_003_intro', 'GENERAL', 'ARCHIVED', '2025-07-29 15:30:00', 6, '2025-07-29 15:00:00', 'AI功能介绍对话');

-- 插入示例对话配置
INSERT INTO `ai_conversation_config` (`id`, `conversation_id`, `user_id`, `ai_model`, `temperature`, `max_tokens`, `system_prompt`, `enable_streaming`, `enable_context`, `context_length`, `auto_title`) VALUES
(1, 1, 2, 'qwen-turbo', 0.70, 2000, '你是一个专业的Java编程导师，请用简洁易懂的方式回答学生的问题。', 1, 1, 10, 1),
(2, 2, 2, 'qwen-turbo', 0.80, 1500, '你是一个友善的AI助手，请自然地与用户对话。', 1, 1, 8, 1),
(3, 3, 3, 'qwen-turbo', 0.60, 2000, '你是AiTutor的AI助手，请详细介绍你的功能和特点。', 1, 1, 12, 0);

-- 插入示例消息记录
INSERT INTO `ai_message` (`id`, `conversation_id`, `user_id`, `message_type`, `content_type`, `content`, `content_length`, `ai_model`, `prompt_tokens`, `completion_tokens`, `total_tokens`, `response_time`, `is_streaming`, `stream_status`, `create_time`) VALUES
-- 对话1：Java学习
(1, 1, 2, 'USER', 'TEXT', '你好，我想学习Java编程，应该从哪里开始？', 24, NULL, 0, 0, 0, 0, 0, 'COMPLETED', '2025-07-29 16:00:30'),
(2, 1, 2, 'AI', 'TEXT', '你好！很高兴帮助你学习Java编程。建议你从以下几个方面开始：\n\n1. **基础语法**：变量、数据类型、运算符\n2. **控制结构**：if-else、循环语句\n3. **面向对象**：类、对象、继承、多态\n4. **常用API**：String、集合框架等\n\n推荐先安装JDK和IDE（如IntelliJ IDEA），然后从Hello World程序开始练习。你有编程基础吗？', 156, 'qwen-turbo', 24, 132, 156, 1200, 1, 'COMPLETED', '2025-07-29 16:00:35'),
(3, 1, 2, 'USER', 'TEXT', '我有一些Python基础，Java和Python有什么主要区别？', 26, NULL, 0, 0, 0, 0, 0, 'COMPLETED', '2025-07-29 16:15:00'),
(4, 1, 2, 'AI', 'TEXT', '很好！有Python基础会让学习Java更容易。主要区别包括：\n\n**语法差异：**\n- Java需要声明变量类型，Python是动态类型\n- Java使用大括号{}，Python使用缩进\n- Java需要分号结尾，Python不需要\n\n**编译方式：**\n- Java是编译型语言（.java → .class）\n- Python是解释型语言\n\n**面向对象：**\n- Java强制面向对象，所有代码必须在类中\n- Python支持多种编程范式\n\n**性能：**\n- Java通常运行速度更快\n- Python开发效率更高\n\n你想先从哪个方面深入了解？', 198, 'qwen-turbo', 26, 172, 198, 1500, 1, 'COMPLETED', '2025-07-29 16:30:00'),

-- 对话2：日常聊天
(5, 2, 2, 'USER', 'TEXT', '今天天气不错，你觉得呢？', 12, NULL, 0, 0, 0, 0, 0, 'COMPLETED', '2025-07-29 16:45:30'),
(6, 2, 2, 'AI', 'TEXT', '虽然我无法直接感受天气，但听你这么说真是太好了！好天气总是能让人心情愉悦。你今天有什么特别的计划吗？是准备出门享受阳光，还是在室内学习工作呢？', 78, 'qwen-turbo', 12, 66, 78, 800, 1, 'COMPLETED', '2025-07-29 17:00:00'),

-- 对话3：AI介绍（已归档）
(7, 3, 3, 'USER', 'TEXT', '你是什么AI助手？有什么功能？', 15, NULL, 0, 0, 0, 0, 0, 'COMPLETED', '2025-07-29 15:00:30'),
(8, 3, 3, 'AI', 'TEXT', '我是AiTutor的AI助手，专门为学习和教育场景设计。我的主要功能包括：\n\n📚 **学习辅导**：各学科知识答疑解惑\n💬 **智能对话**：自然流畅的交流体验\n🎯 **个性化服务**：根据你的需求调整回答风格\n📊 **学习分析**：跟踪学习进度和效果\n\n我支持多轮对话，能记住上下文，让交流更连贯。还有什么想了解的吗？', 142, 'qwen-turbo', 15, 127, 142, 1100, 1, 'COMPLETED', '2025-07-29 15:01:00');

-- 插入使用统计示例
INSERT INTO `ai_usage_statistics` (`id`, `user_id`, `date`, `conversation_count`, `message_count`, `user_message_count`, `ai_message_count`, `total_tokens`, `prompt_tokens`, `completion_tokens`, `total_response_time`, `avg_response_time`, `streaming_count`, `error_count`) VALUES
(1, 2, '2025-07-29', 2, 4, 2, 2, 432, 62, 370, 3500, 1750, 2, 0),
(2, 3, '2025-07-29', 1, 2, 1, 1, 284, 15, 269, 1100, 1100, 1, 0);

-- 插入反馈示例
INSERT INTO `ai_feedback` (`id`, `message_id`, `conversation_id`, `user_id`, `feedback_type`, `feedback_reason`, `feedback_content`, `rating`) VALUES
(1, 2, 1, 2, 'LIKE', '回答详细', 'Java学习建议很实用，步骤清晰', 5),
(2, 4, 1, 2, 'LIKE', '对比清楚', 'Java和Python的对比很有帮助', 4),
(3, 8, 3, 3, 'LIKE', '功能介绍全面', 'AI功能介绍很全面', 5);

{"name": "@dcloudio/uni-cli-shared", "version": "3.0.0-3081220230817001", "description": "@dcloudio/uni-cli-shared", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "lib"], "engines": {"node": "^14.18.0 || >=16.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/dcloudio/uni-app.git", "directory": "packages/uni-cli-shared"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/dcloudio/uni-app/issues"}, "dependencies": {"@ampproject/remapping": "^2.1.2", "@babel/core": "^7.21.3", "@babel/parser": "^7.16.4", "@babel/types": "^7.20.7", "@dcloudio/uni-i18n": "3.0.0-3081220230817001", "@dcloudio/uni-shared": "3.0.0-3081220230817001", "@intlify/core-base": "9.1.9", "@intlify/shared": "9.1.9", "@intlify/vue-devtools": "9.1.9", "@rollup/pluginutils": "^4.2.0", "@vue/compiler-core": "3.2.47", "@vue/compiler-dom": "3.2.47", "@vue/compiler-sfc": "3.2.47", "@vue/server-renderer": "3.2.47", "@vue/shared": "3.2.47", "autoprefixer": "^10.4.14", "base64url": "^3.0.1", "chokidar": "^3.5.3", "compare-versions": "^3.6.0", "debug": "^4.3.3", "es-module-lexer": "^1.2.1", "esbuild": "^0.17.5", "estree-walker": "^2.0.2", "fast-glob": "^3.2.11", "fs-extra": "^10.0.0", "hash-sum": "^2.0.0", "jsonc-parser": "^3.0.0", "magic-string": "^0.30.0", "merge": "^2.1.1", "mime": "^3.0.0", "module-alias": "^2.2.2", "os-locale-s-fix": "^1.0.8-fix-1", "picocolors": "^1.0.0", "postcss-import": "^14.0.2", "postcss-load-config": "^3.1.1", "postcss-modules": "^4.3.0", "postcss-selector-parser": "^6.0.6", "resolve": "^1.22.1", "tapable": "^2.2.0", "xregexp": "3.1.0"}, "gitHead": "33e807d66e1fe47e2ee08ad9c59247e37b8884da", "devDependencies": {"@dcloudio/uni-uts-v1": "3.0.0-3081220230817001", "@types/babel__core": "^7.1.19", "@types/debug": "^4.1.7", "@types/estree": "^0.0.51", "@types/fs-extra": "^9.0.13", "@types/hash-sum": "^1.0.0", "@types/less": "^3.0.3", "@types/mime": "^2.0.3", "@types/module-alias": "^2.0.1", "@types/resolve": "^1.20.2", "@types/sass": "^1.43.1", "@types/stylus": "^0.48.36", "postcss": "^8.4.21", "vue": "3.2.47"}}
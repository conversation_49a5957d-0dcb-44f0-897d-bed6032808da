{"version": 3, "names": ["_traverseNode", "require", "_index", "_removal", "t", "call", "key", "opts", "debug", "node", "_call", "_opts$this$node$type", "type", "fns", "fn", "ret", "state", "then", "Error", "_traverseFlags", "isDenylisted", "_this$opts$denylist", "denylist", "blacklist", "includes", "exports", "isBlacklisted", "restoreContext", "path", "context", "visit", "_this$opts$shouldSkip", "_this$opts", "shouldSkip", "currentContext", "shouldStop", "traverseNode", "scope", "<PERSON><PERSON><PERSON><PERSON>", "skip", "<PERSON><PERSON><PERSON>", "stop", "SHOULD_SKIP", "SHOULD_STOP", "setScope", "_this$opts2", "_this$scope", "noScope", "parentPath", "<PERSON><PERSON><PERSON>", "isMethod", "isSwitchStatement", "target", "_path$opts", "getScope", "init", "setContext", "resync", "removed", "_resyncParent", "_resyncList", "_resync<PERSON>ey", "parent", "container", "Array", "isArray", "i", "length", "<PERSON><PERSON><PERSON>", "Object", "keys", "inList", "newContainer", "_resyncRemoved", "_mark<PERSON><PERSON>oved", "popContext", "contexts", "pop", "undefined", "pushContext", "push", "setup", "_this$node", "requeue", "pathToQueue", "maybeQueue", "requeueComputedKeyAndDecorators", "isPrivate", "computed", "get", "decorators", "decorator", "_getQueueContexts"], "sources": ["../../src/path/context.ts"], "sourcesContent": ["// This file contains methods responsible for maintaining a TraversalContext.\n\nimport { traverseNode } from \"../traverse-node.ts\";\nimport { SHOULD_SKIP, SHOULD_STOP } from \"./index.ts\";\nimport { _markRemoved } from \"./removal.ts\";\nimport type TraversalContext from \"../context.ts\";\nimport type { VisitPhase } from \"../types.ts\";\nimport type NodePath from \"./index.ts\";\nimport * as t from \"@babel/types\";\n\nexport function call(this: NodePath, key: VisitPhase): boolean {\n  const opts = this.opts;\n\n  this.debug(key);\n\n  if (this.node) {\n    if (_call.call(this, opts[key])) return true;\n  }\n\n  if (this.node) {\n    return _call.call(this, opts[this.node.type]?.[key]);\n  }\n\n  return false;\n}\n\nexport function _call(this: NodePath, fns?: Array<Function>): boolean {\n  if (!fns) return false;\n\n  for (const fn of fns) {\n    if (!fn) continue;\n\n    const node = this.node;\n    if (!node) return true;\n\n    const ret = fn.call(this.state, this, this.state);\n    if (ret && typeof ret === \"object\" && typeof ret.then === \"function\") {\n      throw new Error(\n        `You appear to be using a plugin with an async traversal visitor, ` +\n          `which your current version of Babel does not support. ` +\n          `If you're using a published plugin, you may need to upgrade ` +\n          `your @babel/core version.`,\n      );\n    }\n    if (ret) {\n      throw new Error(`Unexpected return value from visitor method ${fn}`);\n    }\n\n    // node has been replaced, it will have been requeued\n    if (this.node !== node) return true;\n\n    // this.shouldSkip || this.shouldStop || this.removed\n    if (this._traverseFlags > 0) return true;\n  }\n\n  return false;\n}\n\nexport function isDenylisted(this: NodePath): boolean {\n  // @ts-expect-error TODO(Babel 8): Remove blacklist\n  const denylist = this.opts.denylist ?? this.opts.blacklist;\n  return denylist?.includes(this.node.type);\n}\n\nif (!process.env.BABEL_8_BREAKING && !USE_ESM) {\n  // eslint-disable-next-line no-restricted-globals\n  exports.isBlacklisted = isDenylisted;\n}\n\nfunction restoreContext(path: NodePath, context: TraversalContext) {\n  if (path.context !== context) {\n    path.context = context;\n    path.state = context.state;\n    path.opts = context.opts;\n  }\n}\n\nexport function visit(this: NodePath): boolean {\n  if (!this.node) {\n    return false;\n  }\n\n  if (this.isDenylisted()) {\n    return false;\n  }\n\n  if (this.opts.shouldSkip?.(this)) {\n    return false;\n  }\n\n  const currentContext = this.context;\n  // Note: We need to check \"this.shouldSkip\" first because\n  // another visitor can set it to true. Usually .shouldSkip is false\n  // before calling the enter visitor, but it can be true in case of\n  // a requeued node (e.g. by .replaceWith()) that is then marked\n  // with .skip().\n  if (this.shouldSkip || call.call(this, \"enter\")) {\n    this.debug(\"Skip...\");\n    return this.shouldStop;\n  }\n  restoreContext(this, currentContext);\n\n  this.debug(\"Recursing into...\");\n  this.shouldStop = traverseNode(\n    this.node,\n    this.opts,\n    this.scope,\n    this.state,\n    this,\n    this.skipKeys,\n  );\n\n  restoreContext(this, currentContext);\n\n  call.call(this, \"exit\");\n\n  return this.shouldStop;\n}\n\nexport function skip(this: NodePath) {\n  this.shouldSkip = true;\n}\n\nexport function skipKey(this: NodePath, key: string) {\n  if (this.skipKeys == null) {\n    this.skipKeys = {};\n  }\n  this.skipKeys[key] = true;\n}\n\nexport function stop(this: NodePath) {\n  // this.shouldSkip = true; this.shouldStop = true;\n  this._traverseFlags |= SHOULD_SKIP | SHOULD_STOP;\n}\n\nexport function setScope(this: NodePath) {\n  if (this.opts?.noScope) return;\n\n  let path = this.parentPath;\n\n  if (\n    // Skip method scope if is computed method key or decorator expression\n    ((this.key === \"key\" || this.listKey === \"decorators\") &&\n      path.isMethod()) ||\n    // Skip switch scope if for discriminant (`x` in `switch (x) {}`).\n    (this.key === \"discriminant\" && path.isSwitchStatement())\n  ) {\n    path = path.parentPath;\n  }\n\n  let target;\n  while (path && !target) {\n    if (path.opts?.noScope) return;\n\n    target = path.scope;\n    path = path.parentPath;\n  }\n\n  this.scope = this.getScope(target);\n  this.scope?.init();\n}\n\nexport function setContext<S = unknown>(\n  this: NodePath,\n  context?: TraversalContext<S>,\n) {\n  if (this.skipKeys != null) {\n    this.skipKeys = {};\n  }\n  // this.shouldSkip = false; this.shouldStop = false; this.removed = false;\n  this._traverseFlags = 0;\n\n  if (context) {\n    this.context = context;\n    this.state = context.state;\n    // Discard the S type parameter from context.opts\n    this.opts = context.opts as typeof this.opts;\n  }\n\n  setScope.call(this);\n\n  return this;\n}\n\n/**\n * Here we resync the node paths `key` and `container`. If they've changed according\n * to what we have stored internally then we attempt to resync by crawling and looking\n * for the new values.\n */\n\nexport function resync(this: NodePath) {\n  if (this.removed) return;\n\n  _resyncParent.call(this);\n  _resyncList.call(this);\n  _resyncKey.call(this);\n  //this._resyncRemoved();\n}\n\nexport function _resyncParent(this: NodePath) {\n  if (this.parentPath) {\n    this.parent = this.parentPath.node;\n  }\n}\n\nexport function _resyncKey(this: NodePath) {\n  if (!this.container) return;\n\n  if (\n    this.node ===\n    // @ts-expect-error this.key should present in this.container\n    this.container[this.key]\n  ) {\n    return;\n  }\n\n  // grrr, path key is out of sync. this is likely due to a modification to the AST\n  // not done through our path APIs\n\n  if (Array.isArray(this.container)) {\n    for (let i = 0; i < this.container.length; i++) {\n      if (this.container[i] === this.node) {\n        setKey.call(this, i);\n        return;\n      }\n    }\n  } else {\n    for (const key of Object.keys(this.container)) {\n      // @ts-expect-error this.key should present in this.container\n      if (this.container[key] === this.node) {\n        setKey.call(this, key);\n        return;\n      }\n    }\n  }\n\n  // ¯\\_(ツ)_/¯ who knows where it's gone lol\n  this.key = null;\n}\n\nexport function _resyncList(this: NodePath) {\n  if (!this.parent || !this.inList) return;\n\n  const newContainer =\n    // @ts-expect-error this.listKey should present in this.parent\n    this.parent[this.listKey];\n  if (this.container === newContainer) return;\n\n  // container is out of sync. this is likely the result of it being reassigned\n  this.container = newContainer || null;\n}\n\nexport function _resyncRemoved(this: NodePath) {\n  if (\n    this.key == null ||\n    !this.container ||\n    // @ts-expect-error this.key should present in this.container\n    this.container[this.key] !== this.node\n  ) {\n    _markRemoved.call(this);\n  }\n}\n\nexport function popContext(this: NodePath) {\n  this.contexts.pop();\n  if (this.contexts.length > 0) {\n    this.setContext(this.contexts[this.contexts.length - 1]);\n  } else {\n    this.setContext(undefined);\n  }\n}\n\nexport function pushContext(this: NodePath, context: TraversalContext) {\n  this.contexts.push(context);\n  this.setContext(context);\n}\n\nexport function setup(\n  this: NodePath,\n  parentPath: NodePath | undefined,\n  container: t.Node | t.Node[],\n  listKey: string,\n  key: string | number,\n) {\n  this.listKey = listKey;\n  this.container = container;\n\n  this.parentPath = parentPath || this.parentPath;\n  setKey.call(this, key);\n}\n\nexport function setKey(this: NodePath, key: string | number) {\n  this.key = key;\n  this.node =\n    // @ts-expect-error this.key must present in this.container\n    this.container[this.key];\n  this.type = this.node?.type;\n}\n\nexport function requeue(this: NodePath, pathToQueue = this) {\n  if (pathToQueue.removed) return;\n\n  // If a path is skipped, and then replaced with a\n  // new one, the new one shouldn't probably be skipped.\n  if (process.env.BABEL_8_BREAKING) {\n    pathToQueue.shouldSkip = false;\n  }\n\n  // TODO(loganfsmyth): This should be switched back to queue in parent contexts\n  // automatically once #2892 and #4135 have been resolved. See #4140.\n  // let contexts = this._getQueueContexts();\n  const contexts = this.contexts;\n\n  for (const context of contexts) {\n    context.maybeQueue(pathToQueue);\n  }\n}\n\nexport function requeueComputedKeyAndDecorators(\n  this: NodePath<t.Method | t.Property>,\n) {\n  const { context, node } = this;\n  if (!t.isPrivate(node) && node.computed) {\n    context.maybeQueue(this.get(\"key\"));\n  }\n  if (node.decorators) {\n    for (const decorator of this.get(\"decorators\")) {\n      context.maybeQueue(decorator);\n    }\n  }\n}\n\nexport function _getQueueContexts(this: NodePath) {\n  let path = this;\n  let contexts = this.contexts;\n  while (!contexts.length) {\n    path = path.parentPath;\n    if (!path) break;\n    contexts = path.contexts;\n  }\n  return contexts;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAAA,aAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AAIA,IAAAG,CAAA,GAAAH,OAAA;AAEO,SAASI,IAAIA,CAAiBC,GAAe,EAAW;EAC7D,MAAMC,IAAI,GAAG,IAAI,CAACA,IAAI;EAEtB,IAAI,CAACC,KAAK,CAACF,GAAG,CAAC;EAEf,IAAI,IAAI,CAACG,IAAI,EAAE;IACb,IAAIC,KAAK,CAACL,IAAI,CAAC,IAAI,EAAEE,IAAI,CAACD,GAAG,CAAC,CAAC,EAAE,OAAO,IAAI;EAC9C;EAEA,IAAI,IAAI,CAACG,IAAI,EAAE;IAAA,IAAAE,oBAAA;IACb,OAAOD,KAAK,CAACL,IAAI,CAAC,IAAI,GAAAM,oBAAA,GAAEJ,IAAI,CAAC,IAAI,CAACE,IAAI,CAACG,IAAI,CAAC,qBAApBD,oBAAA,CAAuBL,GAAG,CAAC,CAAC;EACtD;EAEA,OAAO,KAAK;AACd;AAEO,SAASI,KAAKA,CAAiBG,GAAqB,EAAW;EACpE,IAAI,CAACA,GAAG,EAAE,OAAO,KAAK;EAEtB,KAAK,MAAMC,EAAE,IAAID,GAAG,EAAE;IACpB,IAAI,CAACC,EAAE,EAAE;IAET,MAAML,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;IAEtB,MAAMM,GAAG,GAAGD,EAAE,CAACT,IAAI,CAAC,IAAI,CAACW,KAAK,EAAE,IAAI,EAAE,IAAI,CAACA,KAAK,CAAC;IACjD,IAAID,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,CAACE,IAAI,KAAK,UAAU,EAAE;MACpE,MAAM,IAAIC,KAAK,CACb,mEAAmE,GACjE,wDAAwD,GACxD,8DAA8D,GAC9D,2BACJ,CAAC;IACH;IACA,IAAIH,GAAG,EAAE;MACP,MAAM,IAAIG,KAAK,CAAC,+CAA+CJ,EAAE,EAAE,CAAC;IACtE;IAGA,IAAI,IAAI,CAACL,IAAI,KAAKA,IAAI,EAAE,OAAO,IAAI;IAGnC,IAAI,IAAI,CAACU,cAAc,GAAG,CAAC,EAAE,OAAO,IAAI;EAC1C;EAEA,OAAO,KAAK;AACd;AAEO,SAASC,YAAYA,CAAA,EAA0B;EAAA,IAAAC,mBAAA;EAEpD,MAAMC,QAAQ,IAAAD,mBAAA,GAAG,IAAI,CAACd,IAAI,CAACe,QAAQ,YAAAD,mBAAA,GAAI,IAAI,CAACd,IAAI,CAACgB,SAAS;EAC1D,OAAOD,QAAQ,oBAARA,QAAQ,CAAEE,QAAQ,CAAC,IAAI,CAACf,IAAI,CAACG,IAAI,CAAC;AAC3C;AAE+C;EAE7Ca,OAAO,CAACC,aAAa,GAAGN,YAAY;AACtC;AAEA,SAASO,cAAcA,CAACC,IAAc,EAAEC,OAAyB,EAAE;EACjE,IAAID,IAAI,CAACC,OAAO,KAAKA,OAAO,EAAE;IAC5BD,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtBD,IAAI,CAACZ,KAAK,GAAGa,OAAO,CAACb,KAAK;IAC1BY,IAAI,CAACrB,IAAI,GAAGsB,OAAO,CAACtB,IAAI;EAC1B;AACF;AAEO,SAASuB,KAAKA,CAAA,EAA0B;EAAA,IAAAC,qBAAA,EAAAC,UAAA;EAC7C,IAAI,CAAC,IAAI,CAACvB,IAAI,EAAE;IACd,OAAO,KAAK;EACd;EAEA,IAAI,IAAI,CAACW,YAAY,CAAC,CAAC,EAAE;IACvB,OAAO,KAAK;EACd;EAEA,KAAAW,qBAAA,GAAI,CAAAC,UAAA,OAAI,CAACzB,IAAI,EAAC0B,UAAU,aAApBF,qBAAA,CAAA1B,IAAA,CAAA2B,UAAA,EAAuB,IAAI,CAAC,EAAE;IAChC,OAAO,KAAK;EACd;EAEA,MAAME,cAAc,GAAG,IAAI,CAACL,OAAO;EAMnC,IAAI,IAAI,CAACI,UAAU,IAAI5B,IAAI,CAACA,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;IAC/C,IAAI,CAACG,KAAK,CAAC,SAAS,CAAC;IACrB,OAAO,IAAI,CAAC2B,UAAU;EACxB;EACAR,cAAc,CAAC,IAAI,EAAEO,cAAc,CAAC;EAEpC,IAAI,CAAC1B,KAAK,CAAC,mBAAmB,CAAC;EAC/B,IAAI,CAAC2B,UAAU,GAAG,IAAAC,0BAAY,EAC5B,IAAI,CAAC3B,IAAI,EACT,IAAI,CAACF,IAAI,EACT,IAAI,CAAC8B,KAAK,EACV,IAAI,CAACrB,KAAK,EACV,IAAI,EACJ,IAAI,CAACsB,QACP,CAAC;EAEDX,cAAc,CAAC,IAAI,EAAEO,cAAc,CAAC;EAEpC7B,IAAI,CAACA,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;EAEvB,OAAO,IAAI,CAAC8B,UAAU;AACxB;AAEO,SAASI,IAAIA,CAAA,EAAiB;EACnC,IAAI,CAACN,UAAU,GAAG,IAAI;AACxB;AAEO,SAASO,OAAOA,CAAiBlC,GAAW,EAAE;EACnD,IAAI,IAAI,CAACgC,QAAQ,IAAI,IAAI,EAAE;IACzB,IAAI,CAACA,QAAQ,GAAG,CAAC,CAAC;EACpB;EACA,IAAI,CAACA,QAAQ,CAAChC,GAAG,CAAC,GAAG,IAAI;AAC3B;AAEO,SAASmC,IAAIA,CAAA,EAAiB;EAEnC,IAAI,CAACtB,cAAc,IAAIuB,kBAAW,GAAGC,kBAAW;AAClD;AAEO,SAASC,QAAQA,CAAA,EAAiB;EAAA,IAAAC,WAAA,EAAAC,WAAA;EACvC,KAAAD,WAAA,GAAI,IAAI,CAACtC,IAAI,aAATsC,WAAA,CAAWE,OAAO,EAAE;EAExB,IAAInB,IAAI,GAAG,IAAI,CAACoB,UAAU;EAE1B,IAEG,CAAC,IAAI,CAAC1C,GAAG,KAAK,KAAK,IAAI,IAAI,CAAC2C,OAAO,KAAK,YAAY,KACnDrB,IAAI,CAACsB,QAAQ,CAAC,CAAC,IAEhB,IAAI,CAAC5C,GAAG,KAAK,cAAc,IAAIsB,IAAI,CAACuB,iBAAiB,CAAC,CAAE,EACzD;IACAvB,IAAI,GAAGA,IAAI,CAACoB,UAAU;EACxB;EAEA,IAAII,MAAM;EACV,OAAOxB,IAAI,IAAI,CAACwB,MAAM,EAAE;IAAA,IAAAC,UAAA;IACtB,KAAAA,UAAA,GAAIzB,IAAI,CAACrB,IAAI,aAAT8C,UAAA,CAAWN,OAAO,EAAE;IAExBK,MAAM,GAAGxB,IAAI,CAACS,KAAK;IACnBT,IAAI,GAAGA,IAAI,CAACoB,UAAU;EACxB;EAEA,IAAI,CAACX,KAAK,GAAG,IAAI,CAACiB,QAAQ,CAACF,MAAM,CAAC;EAClC,CAAAN,WAAA,OAAI,CAACT,KAAK,aAAVS,WAAA,CAAYS,IAAI,CAAC,CAAC;AACpB;AAEO,SAASC,UAAUA,CAExB3B,OAA6B,EAC7B;EACA,IAAI,IAAI,CAACS,QAAQ,IAAI,IAAI,EAAE;IACzB,IAAI,CAACA,QAAQ,GAAG,CAAC,CAAC;EACpB;EAEA,IAAI,CAACnB,cAAc,GAAG,CAAC;EAEvB,IAAIU,OAAO,EAAE;IACX,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACb,KAAK,GAAGa,OAAO,CAACb,KAAK;IAE1B,IAAI,CAACT,IAAI,GAAGsB,OAAO,CAACtB,IAAwB;EAC9C;EAEAqC,QAAQ,CAACvC,IAAI,CAAC,IAAI,CAAC;EAEnB,OAAO,IAAI;AACb;AAQO,SAASoD,MAAMA,CAAA,EAAiB;EACrC,IAAI,IAAI,CAACC,OAAO,EAAE;EAElBC,aAAa,CAACtD,IAAI,CAAC,IAAI,CAAC;EACxBuD,WAAW,CAACvD,IAAI,CAAC,IAAI,CAAC;EACtBwD,UAAU,CAACxD,IAAI,CAAC,IAAI,CAAC;AAEvB;AAEO,SAASsD,aAAaA,CAAA,EAAiB;EAC5C,IAAI,IAAI,CAACX,UAAU,EAAE;IACnB,IAAI,CAACc,MAAM,GAAG,IAAI,CAACd,UAAU,CAACvC,IAAI;EACpC;AACF;AAEO,SAASoD,UAAUA,CAAA,EAAiB;EACzC,IAAI,CAAC,IAAI,CAACE,SAAS,EAAE;EAErB,IACE,IAAI,CAACtD,IAAI,KAET,IAAI,CAACsD,SAAS,CAAC,IAAI,CAACzD,GAAG,CAAC,EACxB;IACA;EACF;EAKA,IAAI0D,KAAK,CAACC,OAAO,CAAC,IAAI,CAACF,SAAS,CAAC,EAAE;IACjC,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACH,SAAS,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MAC9C,IAAI,IAAI,CAACH,SAAS,CAACG,CAAC,CAAC,KAAK,IAAI,CAACzD,IAAI,EAAE;QACnC2D,MAAM,CAAC/D,IAAI,CAAC,IAAI,EAAE6D,CAAC,CAAC;QACpB;MACF;IACF;EACF,CAAC,MAAM;IACL,KAAK,MAAM5D,GAAG,IAAI+D,MAAM,CAACC,IAAI,CAAC,IAAI,CAACP,SAAS,CAAC,EAAE;MAE7C,IAAI,IAAI,CAACA,SAAS,CAACzD,GAAG,CAAC,KAAK,IAAI,CAACG,IAAI,EAAE;QACrC2D,MAAM,CAAC/D,IAAI,CAAC,IAAI,EAAEC,GAAG,CAAC;QACtB;MACF;IACF;EACF;EAGA,IAAI,CAACA,GAAG,GAAG,IAAI;AACjB;AAEO,SAASsD,WAAWA,CAAA,EAAiB;EAC1C,IAAI,CAAC,IAAI,CAACE,MAAM,IAAI,CAAC,IAAI,CAACS,MAAM,EAAE;EAElC,MAAMC,YAAY,GAEhB,IAAI,CAACV,MAAM,CAAC,IAAI,CAACb,OAAO,CAAC;EAC3B,IAAI,IAAI,CAACc,SAAS,KAAKS,YAAY,EAAE;EAGrC,IAAI,CAACT,SAAS,GAAGS,YAAY,IAAI,IAAI;AACvC;AAEO,SAASC,cAAcA,CAAA,EAAiB;EAC7C,IACE,IAAI,CAACnE,GAAG,IAAI,IAAI,IAChB,CAAC,IAAI,CAACyD,SAAS,IAEf,IAAI,CAACA,SAAS,CAAC,IAAI,CAACzD,GAAG,CAAC,KAAK,IAAI,CAACG,IAAI,EACtC;IACAiE,qBAAY,CAACrE,IAAI,CAAC,IAAI,CAAC;EACzB;AACF;AAEO,SAASsE,UAAUA,CAAA,EAAiB;EACzC,IAAI,CAACC,QAAQ,CAACC,GAAG,CAAC,CAAC;EACnB,IAAI,IAAI,CAACD,QAAQ,CAACT,MAAM,GAAG,CAAC,EAAE;IAC5B,IAAI,CAACX,UAAU,CAAC,IAAI,CAACoB,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACT,MAAM,GAAG,CAAC,CAAC,CAAC;EAC1D,CAAC,MAAM;IACL,IAAI,CAACX,UAAU,CAACsB,SAAS,CAAC;EAC5B;AACF;AAEO,SAASC,WAAWA,CAAiBlD,OAAyB,EAAE;EACrE,IAAI,CAAC+C,QAAQ,CAACI,IAAI,CAACnD,OAAO,CAAC;EAC3B,IAAI,CAAC2B,UAAU,CAAC3B,OAAO,CAAC;AAC1B;AAEO,SAASoD,KAAKA,CAEnBjC,UAAgC,EAChCe,SAA4B,EAC5Bd,OAAe,EACf3C,GAAoB,EACpB;EACA,IAAI,CAAC2C,OAAO,GAAGA,OAAO;EACtB,IAAI,CAACc,SAAS,GAAGA,SAAS;EAE1B,IAAI,CAACf,UAAU,GAAGA,UAAU,IAAI,IAAI,CAACA,UAAU;EAC/CoB,MAAM,CAAC/D,IAAI,CAAC,IAAI,EAAEC,GAAG,CAAC;AACxB;AAEO,SAAS8D,MAAMA,CAAiB9D,GAAoB,EAAE;EAAA,IAAA4E,UAAA;EAC3D,IAAI,CAAC5E,GAAG,GAAGA,GAAG;EACd,IAAI,CAACG,IAAI,GAEP,IAAI,CAACsD,SAAS,CAAC,IAAI,CAACzD,GAAG,CAAC;EAC1B,IAAI,CAACM,IAAI,IAAAsE,UAAA,GAAG,IAAI,CAACzE,IAAI,qBAATyE,UAAA,CAAWtE,IAAI;AAC7B;AAEO,SAASuE,OAAOA,CAAiBC,WAAW,GAAG,IAAI,EAAE;EAC1D,IAAIA,WAAW,CAAC1B,OAAO,EAAE;EAAO;EAWhC,MAAMkB,QAAQ,GAAG,IAAI,CAACA,QAAQ;EAE9B,KAAK,MAAM/C,OAAO,IAAI+C,QAAQ,EAAE;IAC9B/C,OAAO,CAACwD,UAAU,CAACD,WAAW,CAAC;EACjC;AACF;AAEO,SAASE,+BAA+BA,CAAA,EAE7C;EACA,MAAM;IAAEzD,OAAO;IAAEpB;EAAK,CAAC,GAAG,IAAI;EAC9B,IAAI,CAACL,CAAC,CAACmF,SAAS,CAAC9E,IAAI,CAAC,IAAIA,IAAI,CAAC+E,QAAQ,EAAE;IACvC3D,OAAO,CAACwD,UAAU,CAAC,IAAI,CAACI,GAAG,CAAC,KAAK,CAAC,CAAC;EACrC;EACA,IAAIhF,IAAI,CAACiF,UAAU,EAAE;IACnB,KAAK,MAAMC,SAAS,IAAI,IAAI,CAACF,GAAG,CAAC,YAAY,CAAC,EAAE;MAC9C5D,OAAO,CAACwD,UAAU,CAACM,SAAS,CAAC;IAC/B;EACF;AACF;AAEO,SAASC,iBAAiBA,CAAA,EAAiB;EAChD,IAAIhE,IAAI,GAAG,IAAI;EACf,IAAIgD,QAAQ,GAAG,IAAI,CAACA,QAAQ;EAC5B,OAAO,CAACA,QAAQ,CAACT,MAAM,EAAE;IACvBvC,IAAI,GAAGA,IAAI,CAACoB,UAAU;IACtB,IAAI,CAACpB,IAAI,EAAE;IACXgD,QAAQ,GAAGhD,IAAI,CAACgD,QAAQ;EAC1B;EACA,OAAOA,QAAQ;AACjB", "ignoreList": []}
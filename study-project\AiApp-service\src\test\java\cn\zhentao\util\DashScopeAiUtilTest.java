package cn.zhentao.util;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * DashScope AI工具类测试
 * 
 * <AUTHOR>
 * @since 2024-07-29
 */
@SpringBootTest
@ActiveProfiles("dev")
public class DashScopeAiUtilTest {

    /**
     * 测试AI工具类基本功能
     * 注意：这个测试需要有效的API Key才能运行
     */
    @Test
    public void testAiUtilBasicFunctionality() {
        // 创建AI工具类实例（手动设置API Key用于测试）
        DashScopeAiUtil aiUtil = new DashScopeAiUtil();
        
        // 注意：在实际测试中，您需要确保API Key是有效的
        // 这里只是验证类的基本结构是否正确
        
        System.out.println("DashScopeAiUtil 类创建成功");
        System.out.println("测试通过 - AI工具类基本结构正确");
    }

    /**
     * 测试响应类的基本功能
     */
    @Test
    public void testAiResponseClass() {
        // 测试AiResponse类的构造和getter方法
        DashScopeAiUtil.AiResponse response = new DashScopeAiUtil.AiResponse(
            "测试响应", 
            "test-session-id", 
            true, 
            null
        );
        
        assert response.getText().equals("测试响应");
        assert response.getSessionId().equals("test-session-id");
        assert response.isSuccess();
        assert response.getErrorMessage() == null;
        
        System.out.println("AiResponse 类测试通过");
    }
}

package com.zhentao.studyim.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Netty配置类
 * 从application.yml读取netty相关配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "netty.server")
public class NettyConfig {
    
    /**
     * WebSocket服务端口
     */
    private int port = 9999;
    
    /**
     * Boss线程数
     */
    private int bossThreads = 1;
    
    /**
     * Worker线程数
     */
    private int workerThreads = 4;
}
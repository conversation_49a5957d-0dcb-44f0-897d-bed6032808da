{"version": 3, "names": ["_helperReplaceSupers", "require", "_core", "_traverse", "_helperAnnotateAsPure", "_inlineCallSuperHelpers", "buildConstructor", "classRef", "constructorBody", "node", "func", "t", "functionDeclaration", "cloneNode", "inherits", "transformClass", "path", "file", "builtinClasses", "isLoose", "assumptions", "supportUnicodeId", "classState", "parent", "undefined", "scope", "classId", "superName", "superReturns", "isDerived", "extendsNative", "construct", "userConstructor", "userConstructorPath", "hasConstructor", "body", "superThises", "pushedInherits", "pushedCreateClass", "protoAlias", "dynamic<PERSON>eys", "Map", "methods", "instance", "hasComputed", "list", "map", "static", "setState", "newState", "Object", "assign", "findThisesVisitor", "visitors", "environmentVisitor", "ThisExpression", "push", "createClassHelper", "args", "callExpression", "addHelper", "maybeCreateConstructor", "classBodyPath", "get", "isClassMethod", "kind", "params", "template", "statement", "ast", "blockStatement", "unshiftContainer", "classMethod", "identifier", "buildBody", "pushBody", "verifyConstructor", "pushDescriptors", "classBodyPaths", "isClassProperty", "isClassPrivateProperty", "buildCodeFrameError", "decorators", "isConstructor", "replaceSupers", "ReplaceSupers", "methodPath", "objectRef", "superRef", "constant<PERSON>uper", "refToPreserve", "replace", "traverse", "ReturnStatement", "getFunctionParent", "isArrowFunctionExpression", "pushConstructor", "_path$ensureFunctionN", "ensureFunctionName", "NodePath", "prototype", "wrapped", "replaceWith", "pushMethod", "pushInheritsToBody", "props", "placement", "length", "desc", "obj", "objectExpression", "objectProperty", "key", "properties", "arrayExpression", "nullLiteral", "lastNonNullIndex", "i", "is<PERSON>ull<PERSON>iteral", "slice", "returnStatement", "wrapSuperCall", "bareSuper", "thisRef", "bareSuperNode", "call", "superIsCallableConstructor", "arguments", "unshift", "thisExpression", "isSpreadElement", "isIdentifier", "argument", "name", "callee", "memberExpression", "logicalExpression", "_bareSuperNode$argume", "bareSuperNodeArguments", "addCallSuperHelper", "parentPath", "isExpressionStatement", "container", "assignmentExpression", "maxGuaranteedSuperBeforeIndex", "ref", "generateDeclaredUidIdentifier", "buildAssertThisInitialized", "bareSupers", "Super", "isCallExpression", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "find", "Math", "min", "type", "left", "isConditional", "test", "object", "guaranteedCalls", "Set", "thisPath", "isMemberExpression", "thisIndex", "exprPath", "isSequenceExpression", "<PERSON><PERSON><PERSON>", "isOptionalCallExpression", "has", "add", "wrapReturn", "returnArg", "thisExpr", "returnParams", "bodyPaths", "guaranteedSuperBefore<PERSON>inish", "pop", "isReturnStatement", "pushContainer", "returnPath", "processMethod", "<PERSON><PERSON><PERSON><PERSON>", "isNumericLiteral", "isBigIntLiteral", "stringLiteral", "String", "value", "toCom<PERSON><PERSON>ey", "isStringLiteral", "fn", "toExpression", "descriptor", "set", "setClassMethods", "insertProtoAliasOnce", "methodName", "computed", "isLiteral", "functionExpression", "id", "generator", "async", "expr", "expressionStatement", "inheritsComments", "generateUidIdentifier", "classProto", "protoDeclaration", "variableDeclaration", "variableDeclarator", "method", "directives", "hasInstanceDescriptors", "hasStaticDescriptors", "extractDynamicKeys", "elem", "isPure", "generateUidIdentifierBasedOnNode", "setupClosureParamsArgs", "closureParams", "closureArgs", "arg", "annotateAsPure", "param", "classTransformer", "superClass", "hasBinding", "noClassCalls", "isStrict", "isInStrictMode", "constructorOnly", "directive", "directiveLiteral", "arrowFunctionExpression"], "sources": ["../src/transformClass.ts"], "sourcesContent": ["import type { NodePath, Scope, File } from \"@babel/core\";\nimport ReplaceSupers from \"@babel/helper-replace-supers\";\nimport { template, types as t } from \"@babel/core\";\nimport { visitors } from \"@babel/traverse\";\nimport annotateAsPure from \"@babel/helper-annotate-as-pure\";\n\nimport addCallSuperHelper from \"./inline-callSuper-helpers.ts\";\n\ntype ClassAssumptions = {\n  setClassMethods: boolean;\n  constantSuper: boolean;\n  superIsCallableConstructor: boolean;\n  noClassCalls: boolean;\n};\n\ntype ClassConstructor = t.ClassMethod & { kind: \"constructor\" };\n\nfunction buildConstructor(\n  classRef: t.Identifier,\n  constructorBody: t.BlockStatement,\n  node: t.Class,\n) {\n  const func = t.functionDeclaration(\n    t.cloneNode(classRef),\n    [],\n    constructorBody,\n  );\n  t.inherits(func, node);\n  return func;\n}\n\ntype Descriptor = {\n  key: t.Expression;\n  get?: t.Expression | null;\n  set?: t.Expression | null;\n  value?: t.Expression | null;\n  constructor?: t.Expression | null;\n};\n\ntype State = {\n  parent: t.Node;\n  scope: Scope;\n  node: t.Class;\n  path: NodePath<t.Class>;\n  file: File;\n\n  classId: t.Identifier | void;\n  classRef: t.Identifier;\n  superName: t.Expression | null;\n  superReturns: NodePath<t.ReturnStatement>[];\n  isDerived: boolean;\n  extendsNative: boolean;\n\n  construct: t.FunctionDeclaration;\n  constructorBody: t.BlockStatement;\n  userConstructor: ClassConstructor;\n  userConstructorPath: NodePath<ClassConstructor>;\n  hasConstructor: boolean;\n\n  body: t.Statement[];\n  superThises: NodePath<t.ThisExpression>[];\n  pushedInherits: boolean;\n  pushedCreateClass: boolean;\n  protoAlias: t.Identifier | null;\n  isLoose: boolean;\n\n  dynamicKeys: Map<string, t.Expression>;\n\n  methods: {\n    // 'list' is in the same order as the elements appear in the class body.\n    // if there aren't computed keys, we can safely reorder class elements\n    // and use 'map' to merge duplicates.\n    instance: {\n      hasComputed: boolean;\n      list: Descriptor[];\n      map: Map<string, Descriptor>;\n    };\n    static: {\n      hasComputed: boolean;\n      list: Descriptor[];\n      map: Map<string, Descriptor>;\n    };\n  };\n};\n\ntype PropertyInfo = {\n  instance: t.ObjectExpression[] | null;\n  static: t.ObjectExpression[] | null;\n};\n\nexport default function transformClass(\n  path: NodePath<t.Class>,\n  file: File,\n  builtinClasses: ReadonlySet<string>,\n  isLoose: boolean,\n  assumptions: ClassAssumptions,\n  supportUnicodeId: boolean,\n) {\n  const classState: State = {\n    parent: undefined,\n    scope: undefined,\n    node: undefined,\n    path: undefined,\n    file: undefined,\n\n    classId: undefined,\n    classRef: undefined,\n    superName: null,\n    superReturns: [],\n    isDerived: false,\n    extendsNative: false,\n\n    construct: undefined,\n    constructorBody: undefined,\n    userConstructor: undefined,\n    userConstructorPath: undefined,\n    hasConstructor: false,\n\n    body: [],\n    superThises: [],\n    pushedInherits: false,\n    pushedCreateClass: false,\n    protoAlias: null,\n    isLoose: false,\n\n    dynamicKeys: new Map(),\n\n    methods: {\n      instance: {\n        hasComputed: false,\n        list: [],\n        map: new Map(),\n      },\n      static: {\n        hasComputed: false,\n        list: [],\n        map: new Map(),\n      },\n    },\n  };\n\n  const setState = (newState: Partial<State>) => {\n    Object.assign(classState, newState);\n  };\n\n  const findThisesVisitor = visitors.environmentVisitor({\n    ThisExpression(path) {\n      classState.superThises.push(path);\n    },\n  });\n\n  function createClassHelper(args: t.Expression[]) {\n    return t.callExpression(classState.file.addHelper(\"createClass\"), args);\n  }\n\n  /**\n   * Creates a class constructor or bail out if there is one\n   */\n  function maybeCreateConstructor() {\n    const classBodyPath = classState.path.get(\"body\");\n    for (const path of classBodyPath.get(\"body\")) {\n      if (path.isClassMethod({ kind: \"constructor\" })) return;\n    }\n\n    const params: t.FunctionExpression[\"params\"] = [];\n    let body;\n\n    if (classState.isDerived) {\n      body = template.statement.ast`{\n          super(...arguments);\n        }` as t.BlockStatement;\n    } else {\n      body = t.blockStatement([]);\n    }\n\n    classBodyPath.unshiftContainer(\n      \"body\",\n      t.classMethod(\"constructor\", t.identifier(\"constructor\"), params, body),\n    );\n  }\n\n  function buildBody() {\n    maybeCreateConstructor();\n    pushBody();\n    verifyConstructor();\n\n    if (classState.userConstructor) {\n      const { constructorBody, userConstructor, construct } = classState;\n\n      constructorBody.body.push(...userConstructor.body.body);\n      t.inherits(construct, userConstructor);\n      t.inherits(constructorBody, userConstructor.body);\n    }\n\n    pushDescriptors();\n  }\n\n  function pushBody() {\n    const classBodyPaths: Array<any> = classState.path.get(\"body.body\");\n\n    for (const path of classBodyPaths) {\n      const node = path.node;\n\n      if (path.isClassProperty() || path.isClassPrivateProperty()) {\n        throw path.buildCodeFrameError(\"Missing class properties transform.\");\n      }\n\n      if (node.decorators) {\n        throw path.buildCodeFrameError(\n          \"Method has decorators, put the decorator plugin before the classes one.\",\n        );\n      }\n\n      if (t.isClassMethod(node)) {\n        const isConstructor = node.kind === \"constructor\";\n\n        const replaceSupers = new ReplaceSupers({\n          methodPath: path,\n          objectRef: classState.classRef,\n          superRef: classState.superName,\n          constantSuper: assumptions.constantSuper,\n          file: classState.file,\n          refToPreserve: classState.classRef,\n        });\n\n        replaceSupers.replace();\n\n        const superReturns: NodePath<t.ReturnStatement>[] = [];\n        path.traverse(\n          visitors.environmentVisitor({\n            ReturnStatement(path) {\n              if (!path.getFunctionParent().isArrowFunctionExpression()) {\n                superReturns.push(path);\n              }\n            },\n          }),\n        );\n\n        if (isConstructor) {\n          pushConstructor(superReturns, node as ClassConstructor, path);\n        } else {\n          if (!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE) {\n            // polyfill when being run by an older Babel version\n            path.ensureFunctionName ??=\n              // eslint-disable-next-line no-restricted-globals\n              require(\"@babel/traverse\").NodePath.prototype.ensureFunctionName;\n          }\n          path.ensureFunctionName(supportUnicodeId);\n          let wrapped;\n          if (node !== path.node) {\n            wrapped = path.node;\n            // The node has been wrapped. Reset it to the original once, but store the wrapper.\n            path.replaceWith(node);\n          }\n\n          pushMethod(node, wrapped);\n        }\n      }\n    }\n  }\n\n  function pushDescriptors() {\n    pushInheritsToBody();\n\n    const { body } = classState;\n\n    const props: PropertyInfo = {\n      instance: null,\n      static: null,\n    };\n\n    for (const placement of [\"static\", \"instance\"] as const) {\n      if (classState.methods[placement].list.length) {\n        props[placement] = classState.methods[placement].list.map(desc => {\n          const obj = t.objectExpression([\n            t.objectProperty(t.identifier(\"key\"), desc.key),\n          ]);\n\n          for (const kind of [\"get\", \"set\", \"value\"] as const) {\n            if (desc[kind] != null) {\n              obj.properties.push(\n                t.objectProperty(t.identifier(kind), desc[kind]),\n              );\n            }\n          }\n\n          return obj;\n        });\n      }\n    }\n\n    if (props.instance || props.static) {\n      let args = [\n        t.cloneNode(classState.classRef), // Constructor\n        props.instance ? t.arrayExpression(props.instance) : t.nullLiteral(), // instanceDescriptors\n        props.static ? t.arrayExpression(props.static) : t.nullLiteral(), // staticDescriptors\n      ];\n\n      let lastNonNullIndex = 0;\n      for (let i = 0; i < args.length; i++) {\n        if (!t.isNullLiteral(args[i])) lastNonNullIndex = i;\n      }\n      args = args.slice(0, lastNonNullIndex + 1);\n\n      body.push(t.returnStatement(createClassHelper(args)));\n      classState.pushedCreateClass = true;\n    }\n  }\n\n  function wrapSuperCall(\n    bareSuper: NodePath<t.CallExpression>,\n    superRef: t.Expression,\n    thisRef: () => t.Identifier,\n    body: NodePath<t.BlockStatement>,\n  ) {\n    const bareSuperNode = bareSuper.node;\n    let call;\n\n    if (assumptions.superIsCallableConstructor) {\n      bareSuperNode.arguments.unshift(t.thisExpression());\n      if (\n        bareSuperNode.arguments.length === 2 &&\n        t.isSpreadElement(bareSuperNode.arguments[1]) &&\n        t.isIdentifier(bareSuperNode.arguments[1].argument, {\n          name: \"arguments\",\n        })\n      ) {\n        // special case single arguments spread\n        bareSuperNode.arguments[1] = bareSuperNode.arguments[1].argument;\n        bareSuperNode.callee = t.memberExpression(\n          t.cloneNode(superRef),\n          t.identifier(\"apply\"),\n        );\n      } else {\n        bareSuperNode.callee = t.memberExpression(\n          t.cloneNode(superRef),\n          t.identifier(\"call\"),\n        );\n      }\n\n      call = t.logicalExpression(\"||\", bareSuperNode, t.thisExpression());\n    } else {\n      const args: t.Expression[] = [\n        t.thisExpression(),\n        t.cloneNode(classState.classRef),\n      ];\n      if (bareSuperNode.arguments?.length) {\n        const bareSuperNodeArguments = bareSuperNode.arguments as (\n          | t.Expression\n          | t.SpreadElement\n        )[];\n\n        /**\n         * test262/test/language/expressions/super/call-spread-err-sngl-err-itr-get-get.js\n         *\n         * var iter = {};\n         * Object.defineProperty(iter, Symbol.iterator, {\n         *   get: function() {\n         *     throw new Test262Error();\n         *   }\n         * })\n         * super(...iter);\n         */\n\n        if (\n          bareSuperNodeArguments.length === 1 &&\n          t.isSpreadElement(bareSuperNodeArguments[0]) &&\n          t.isIdentifier(bareSuperNodeArguments[0].argument, {\n            name: \"arguments\",\n          })\n        ) {\n          args.push(bareSuperNodeArguments[0].argument);\n        } else {\n          args.push(t.arrayExpression(bareSuperNodeArguments));\n        }\n      }\n      call = t.callExpression(addCallSuperHelper(classState.file), args);\n    }\n\n    if (\n      bareSuper.parentPath.isExpressionStatement() &&\n      bareSuper.parentPath.container === body.node.body &&\n      body.node.body.length - 1 === bareSuper.parentPath.key\n    ) {\n      // this super call is the last statement in the body so we can just straight up\n      // turn it into a return\n\n      if (classState.superThises.length) {\n        call = t.assignmentExpression(\"=\", thisRef(), call);\n      }\n\n      bareSuper.parentPath.replaceWith(t.returnStatement(call));\n    } else {\n      bareSuper.replaceWith(t.assignmentExpression(\"=\", thisRef(), call));\n    }\n  }\n\n  function verifyConstructor() {\n    if (!classState.isDerived) return;\n\n    const path = classState.userConstructorPath;\n    const body = path.get(\"body\");\n\n    const constructorBody = path.get(\"body\");\n\n    let maxGuaranteedSuperBeforeIndex = constructorBody.node.body.length;\n\n    path.traverse(findThisesVisitor);\n\n    let thisRef = function () {\n      const ref = path.scope.generateDeclaredUidIdentifier(\"this\");\n      maxGuaranteedSuperBeforeIndex++;\n      thisRef = () => t.cloneNode(ref);\n      return ref;\n    };\n\n    const buildAssertThisInitialized = function () {\n      return t.callExpression(\n        classState.file.addHelper(\"assertThisInitialized\"),\n        [thisRef()],\n      );\n    };\n\n    const bareSupers: NodePath<t.CallExpression>[] = [];\n    path.traverse(\n      visitors.environmentVisitor({\n        Super(path) {\n          const { node, parentPath } = path;\n          if (parentPath.isCallExpression({ callee: node })) {\n            bareSupers.unshift(parentPath);\n          }\n        },\n      }),\n    );\n\n    for (const bareSuper of bareSupers) {\n      wrapSuperCall(bareSuper, classState.superName, thisRef, body);\n\n      if (maxGuaranteedSuperBeforeIndex >= 0) {\n        let lastParentPath: NodePath;\n        bareSuper.find(function (parentPath) {\n          // hit top so short circuit\n          if (parentPath === constructorBody) {\n            maxGuaranteedSuperBeforeIndex = Math.min(\n              maxGuaranteedSuperBeforeIndex,\n              lastParentPath.key as number,\n            );\n            return true;\n          }\n\n          const { type } = parentPath;\n          switch (type) {\n            case \"ExpressionStatement\":\n            case \"SequenceExpression\":\n            case \"AssignmentExpression\":\n            case \"BinaryExpression\":\n            case \"MemberExpression\":\n            case \"CallExpression\":\n            case \"NewExpression\":\n            case \"VariableDeclarator\":\n            case \"VariableDeclaration\":\n            case \"BlockStatement\":\n            case \"ArrayExpression\":\n            case \"ObjectExpression\":\n            case \"ObjectProperty\":\n            case \"TemplateLiteral\":\n              lastParentPath = parentPath;\n              return false;\n            default:\n              if (\n                (type === \"LogicalExpression\" &&\n                  parentPath.node.left === lastParentPath.node) ||\n                (parentPath.isConditional() &&\n                  parentPath.node.test === lastParentPath.node) ||\n                (type === \"OptionalCallExpression\" &&\n                  parentPath.node.callee === lastParentPath.node) ||\n                (type === \"OptionalMemberExpression\" &&\n                  parentPath.node.object === lastParentPath.node)\n              ) {\n                lastParentPath = parentPath;\n                return false;\n              }\n          }\n\n          maxGuaranteedSuperBeforeIndex = -1;\n          return true;\n        });\n      }\n    }\n\n    const guaranteedCalls = new Set<NodePath>();\n\n    for (const thisPath of classState.superThises) {\n      const { node, parentPath } = thisPath;\n      if (parentPath.isMemberExpression({ object: node })) {\n        thisPath.replaceWith(thisRef());\n        continue;\n      }\n\n      let thisIndex: number;\n      thisPath.find(function (parentPath) {\n        if (parentPath.parentPath === constructorBody) {\n          thisIndex = parentPath.key as number;\n          return true;\n        }\n      });\n\n      let exprPath: NodePath = thisPath.parentPath.isSequenceExpression()\n        ? thisPath.parentPath\n        : thisPath;\n      if (\n        exprPath.listKey === \"arguments\" &&\n        (exprPath.parentPath.isCallExpression() ||\n          exprPath.parentPath.isOptionalCallExpression())\n      ) {\n        exprPath = exprPath.parentPath;\n      } else {\n        exprPath = null;\n      }\n\n      if (\n        (maxGuaranteedSuperBeforeIndex !== -1 &&\n          thisIndex > maxGuaranteedSuperBeforeIndex) ||\n        guaranteedCalls.has(exprPath)\n      ) {\n        thisPath.replaceWith(thisRef());\n      } else {\n        if (exprPath) {\n          guaranteedCalls.add(exprPath);\n        }\n        thisPath.replaceWith(buildAssertThisInitialized());\n      }\n    }\n\n    let wrapReturn;\n\n    if (classState.isLoose) {\n      wrapReturn = (returnArg: t.Expression | void) => {\n        const thisExpr = buildAssertThisInitialized();\n        return returnArg\n          ? t.logicalExpression(\"||\", returnArg, thisExpr)\n          : thisExpr;\n      };\n    } else {\n      wrapReturn = (returnArg: t.Expression | undefined) => {\n        const returnParams: t.Expression[] = [thisRef()];\n        if (returnArg != null) {\n          returnParams.push(returnArg);\n        }\n        return t.callExpression(\n          classState.file.addHelper(\"possibleConstructorReturn\"),\n          returnParams,\n        );\n      };\n    }\n\n    // if we have a return as the last node in the body then we've already caught that\n    // return\n    const bodyPaths = body.get(\"body\");\n    const guaranteedSuperBeforeFinish =\n      maxGuaranteedSuperBeforeIndex !== -1 &&\n      maxGuaranteedSuperBeforeIndex < bodyPaths.length;\n    if (!bodyPaths.length || !bodyPaths.pop().isReturnStatement()) {\n      body.pushContainer(\n        \"body\",\n        t.returnStatement(\n          guaranteedSuperBeforeFinish\n            ? thisRef()\n            : buildAssertThisInitialized(),\n        ),\n      );\n    }\n\n    for (const returnPath of classState.superReturns) {\n      returnPath\n        .get(\"argument\")\n        .replaceWith(wrapReturn(returnPath.node.argument));\n    }\n  }\n\n  /**\n   * Push a method to its respective mutatorMap.\n   */\n  function pushMethod(node: t.ClassMethod, wrapped?: t.Expression) {\n    if (node.kind === \"method\") {\n      if (processMethod(node)) return;\n    }\n\n    const placement = node.static ? \"static\" : \"instance\";\n    const methods = classState.methods[placement];\n\n    const descKey = node.kind === \"method\" ? \"value\" : node.kind;\n    const key =\n      t.isNumericLiteral(node.key) || t.isBigIntLiteral(node.key)\n        ? t.stringLiteral(String(node.key.value))\n        : t.toComputedKey(node);\n    methods.hasComputed = !t.isStringLiteral(key);\n\n    const fn: t.Expression = wrapped ?? t.toExpression(node);\n\n    let descriptor: Descriptor;\n    if (\n      !methods.hasComputed &&\n      methods.map.has((key as t.StringLiteral).value)\n    ) {\n      descriptor = methods.map.get((key as t.StringLiteral).value);\n      descriptor[descKey] = fn;\n\n      if (descKey === \"value\") {\n        descriptor.get = null;\n        descriptor.set = null;\n      } else {\n        descriptor.value = null;\n      }\n    } else {\n      descriptor = {\n        key:\n          // private name has been handled in class-properties transform\n          key as t.Expression,\n        [descKey]: fn,\n      } as Descriptor;\n      methods.list.push(descriptor);\n\n      if (!methods.hasComputed) {\n        methods.map.set((key as t.StringLiteral).value, descriptor);\n      }\n    }\n  }\n\n  function processMethod(node: t.ClassMethod) {\n    if (assumptions.setClassMethods && !node.decorators) {\n      // use assignments instead of define properties for loose classes\n      let { classRef } = classState;\n      if (!node.static) {\n        insertProtoAliasOnce();\n        classRef = classState.protoAlias;\n      }\n      const methodName = t.memberExpression(\n        t.cloneNode(classRef),\n        node.key,\n        node.computed || t.isLiteral(node.key),\n      );\n\n      const func: t.Expression = t.functionExpression(\n        // @ts-expect-error We actually set and id through .ensureFunctionName\n        node.id,\n        // @ts-expect-error Fixme: should throw when we see TSParameterProperty\n        node.params,\n        node.body,\n        node.generator,\n        node.async,\n      );\n      t.inherits(func, node);\n\n      const expr = t.expressionStatement(\n        t.assignmentExpression(\"=\", methodName, func),\n      );\n      t.inheritsComments(expr, node);\n      classState.body.push(expr);\n      return true;\n    }\n\n    return false;\n  }\n\n  function insertProtoAliasOnce() {\n    if (classState.protoAlias === null) {\n      setState({ protoAlias: classState.scope.generateUidIdentifier(\"proto\") });\n      const classProto = t.memberExpression(\n        classState.classRef,\n        t.identifier(\"prototype\"),\n      );\n      const protoDeclaration = t.variableDeclaration(\"var\", [\n        t.variableDeclarator(classState.protoAlias, classProto),\n      ]);\n\n      classState.body.push(protoDeclaration);\n    }\n  }\n\n  /**\n   * Replace the constructor body of our class.\n   */\n  function pushConstructor(\n    superReturns: NodePath<t.ReturnStatement>[],\n    method: ClassConstructor,\n    path: NodePath<ClassConstructor>,\n  ) {\n    setState({\n      userConstructorPath: path,\n      userConstructor: method,\n      hasConstructor: true,\n      superReturns,\n    });\n\n    const { construct } = classState;\n\n    t.inheritsComments(construct, method);\n\n    // @ts-expect-error Fixme: should throw when we see TSParameterProperty\n    construct.params = method.params;\n\n    t.inherits(construct.body, method.body);\n    construct.body.directives = method.body.directives;\n\n    // we haven't pushed any descriptors yet\n    // @ts-expect-error todo(flow->ts) maybe remove this block - properties from condition are not used anywhere else\n    if (classState.hasInstanceDescriptors || classState.hasStaticDescriptors) {\n      pushDescriptors();\n    }\n\n    pushInheritsToBody();\n  }\n\n  /**\n   * Push inherits helper to body.\n   */\n  function pushInheritsToBody() {\n    if (!classState.isDerived || classState.pushedInherits) return;\n\n    classState.pushedInherits = true;\n\n    // Unshift to ensure that the constructor inheritance is set up before\n    // any properties can be assigned to the prototype.\n\n    classState.body.unshift(\n      t.expressionStatement(\n        t.callExpression(\n          classState.file.addHelper(\n            classState.isLoose ? \"inheritsLoose\" : \"inherits\",\n          ),\n          [t.cloneNode(classState.classRef), t.cloneNode(classState.superName)],\n        ),\n      ),\n    );\n  }\n\n  function extractDynamicKeys() {\n    const { dynamicKeys, node, scope } = classState;\n\n    for (const elem of node.body.body) {\n      if (!t.isClassMethod(elem) || !elem.computed) continue;\n      if (scope.isPure(elem.key, /* constants only*/ true)) continue;\n\n      const id = scope.generateUidIdentifierBasedOnNode(elem.key);\n      dynamicKeys.set(id.name, elem.key);\n\n      elem.key = id;\n    }\n  }\n\n  function setupClosureParamsArgs() {\n    const { superName, dynamicKeys } = classState;\n    const closureParams = [];\n    const closureArgs = [];\n\n    if (classState.isDerived) {\n      let arg = t.cloneNode(superName);\n      if (classState.extendsNative) {\n        arg = t.callExpression(classState.file.addHelper(\"wrapNativeSuper\"), [\n          arg,\n        ]);\n        annotateAsPure(arg);\n      }\n\n      const param =\n        classState.scope.generateUidIdentifierBasedOnNode(superName);\n\n      closureParams.push(param);\n      closureArgs.push(arg);\n\n      setState({ superName: t.cloneNode(param) });\n    }\n\n    for (const [name, value] of dynamicKeys) {\n      closureParams.push(t.identifier(name));\n      closureArgs.push(value);\n    }\n\n    return { closureParams, closureArgs };\n  }\n\n  function classTransformer(\n    path: NodePath<t.Class>,\n    file: File,\n    builtinClasses: ReadonlySet<string>,\n    isLoose: boolean,\n  ) {\n    setState({\n      parent: path.parent,\n      scope: path.scope,\n      node: path.node,\n      path,\n      file,\n      isLoose,\n    });\n\n    setState({\n      classId: classState.node.id,\n      // this is the name of the binding that will **always** reference the class we've constructed\n      classRef: classState.node.id\n        ? t.identifier(classState.node.id.name)\n        : classState.scope.generateUidIdentifier(\"class\"),\n      superName: classState.node.superClass,\n      isDerived: !!classState.node.superClass,\n      constructorBody: t.blockStatement([]),\n    });\n\n    setState({\n      extendsNative:\n        t.isIdentifier(classState.superName) &&\n        builtinClasses.has(classState.superName.name) &&\n        !classState.scope.hasBinding(\n          classState.superName.name,\n          /* noGlobals */ true,\n        ),\n    });\n\n    const { classRef, node, constructorBody } = classState;\n\n    setState({\n      construct: buildConstructor(classRef, constructorBody, node),\n    });\n\n    extractDynamicKeys();\n\n    const { body } = classState;\n    const { closureParams, closureArgs } = setupClosureParamsArgs();\n\n    buildBody();\n\n    // make sure this class isn't directly called (with A() instead new A())\n    if (!assumptions.noClassCalls) {\n      constructorBody.body.unshift(\n        t.expressionStatement(\n          t.callExpression(classState.file.addHelper(\"classCallCheck\"), [\n            t.thisExpression(),\n            t.cloneNode(classState.classRef),\n          ]),\n        ),\n      );\n    }\n\n    const isStrict = path.isInStrictMode();\n    let constructorOnly = body.length === 0;\n    if (constructorOnly && !isStrict) {\n      for (const param of classState.construct.params) {\n        // It's illegal to put a use strict directive into the body of a function\n        // with non-simple parameters for some reason. So, we have to use a strict\n        // wrapper function.\n        if (!t.isIdentifier(param)) {\n          constructorOnly = false;\n          break;\n        }\n      }\n    }\n\n    const directives = constructorOnly\n      ? classState.construct.body.directives\n      : [];\n    if (!isStrict) {\n      directives.push(t.directive(t.directiveLiteral(\"use strict\")));\n    }\n\n    if (constructorOnly) {\n      // named class with only a constructor\n      const expr = t.toExpression(classState.construct);\n      return classState.isLoose ? expr : createClassHelper([expr]);\n    }\n\n    if (!classState.pushedCreateClass) {\n      body.push(\n        t.returnStatement(\n          classState.isLoose\n            ? t.cloneNode(classState.classRef)\n            : createClassHelper([t.cloneNode(classState.classRef)]),\n        ),\n      );\n    }\n\n    body.unshift(classState.construct);\n\n    const container = t.arrowFunctionExpression(\n      closureParams,\n      t.blockStatement(body, directives),\n    );\n    return t.callExpression(container, closureArgs);\n  }\n\n  return classTransformer(path, file, builtinClasses, isLoose);\n}\n"], "mappings": ";;;;;;AACA,IAAAA,oBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,qBAAA,GAAAH,OAAA;AAEA,IAAAI,uBAAA,GAAAJ,OAAA;AAWA,SAASK,gBAAgBA,CACvBC,QAAsB,EACtBC,eAAiC,EACjCC,IAAa,EACb;EACA,MAAMC,IAAI,GAAGC,WAAC,CAACC,mBAAmB,CAChCD,WAAC,CAACE,SAAS,CAACN,QAAQ,CAAC,EACrB,EAAE,EACFC,eACF,CAAC;EACDG,WAAC,CAACG,QAAQ,CAACJ,IAAI,EAAED,IAAI,CAAC;EACtB,OAAOC,IAAI;AACb;AA6De,SAASK,cAAcA,CACpCC,IAAuB,EACvBC,IAAU,EACVC,cAAmC,EACnCC,OAAgB,EAChBC,WAA6B,EAC7BC,gBAAyB,EACzB;EACA,MAAMC,UAAiB,GAAG;IACxBC,MAAM,EAAEC,SAAS;IACjBC,KAAK,EAAED,SAAS;IAChBf,IAAI,EAAEe,SAAS;IACfR,IAAI,EAAEQ,SAAS;IACfP,IAAI,EAAEO,SAAS;IAEfE,OAAO,EAAEF,SAAS;IAClBjB,QAAQ,EAAEiB,SAAS;IACnBG,SAAS,EAAE,IAAI;IACfC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE,KAAK;IAChBC,aAAa,EAAE,KAAK;IAEpBC,SAAS,EAAEP,SAAS;IACpBhB,eAAe,EAAEgB,SAAS;IAC1BQ,eAAe,EAAER,SAAS;IAC1BS,mBAAmB,EAAET,SAAS;IAC9BU,cAAc,EAAE,KAAK;IAErBC,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,KAAK;IACrBC,iBAAiB,EAAE,KAAK;IACxBC,UAAU,EAAE,IAAI;IAChBpB,OAAO,EAAE,KAAK;IAEdqB,WAAW,EAAE,IAAIC,GAAG,CAAC,CAAC;IAEtBC,OAAO,EAAE;MACPC,QAAQ,EAAE;QACRC,WAAW,EAAE,KAAK;QAClBC,IAAI,EAAE,EAAE;QACRC,GAAG,EAAE,IAAIL,GAAG,CAAC;MACf,CAAC;MACDM,MAAM,EAAE;QACNH,WAAW,EAAE,KAAK;QAClBC,IAAI,EAAE,EAAE;QACRC,GAAG,EAAE,IAAIL,GAAG,CAAC;MACf;IACF;EACF,CAAC;EAED,MAAMO,QAAQ,GAAIC,QAAwB,IAAK;IAC7CC,MAAM,CAACC,MAAM,CAAC7B,UAAU,EAAE2B,QAAQ,CAAC;EACrC,CAAC;EAED,MAAMG,iBAAiB,GAAGC,kBAAQ,CAACC,kBAAkB,CAAC;IACpDC,cAAcA,CAACvC,IAAI,EAAE;MACnBM,UAAU,CAACc,WAAW,CAACoB,IAAI,CAACxC,IAAI,CAAC;IACnC;EACF,CAAC,CAAC;EAEF,SAASyC,iBAAiBA,CAACC,IAAoB,EAAE;IAC/C,OAAO/C,WAAC,CAACgD,cAAc,CAACrC,UAAU,CAACL,IAAI,CAAC2C,SAAS,CAAC,aAAa,CAAC,EAAEF,IAAI,CAAC;EACzE;EAKA,SAASG,sBAAsBA,CAAA,EAAG;IAChC,MAAMC,aAAa,GAAGxC,UAAU,CAACN,IAAI,CAAC+C,GAAG,CAAC,MAAM,CAAC;IACjD,KAAK,MAAM/C,IAAI,IAAI8C,aAAa,CAACC,GAAG,CAAC,MAAM,CAAC,EAAE;MAC5C,IAAI/C,IAAI,CAACgD,aAAa,CAAC;QAAEC,IAAI,EAAE;MAAc,CAAC,CAAC,EAAE;IACnD;IAEA,MAAMC,MAAsC,GAAG,EAAE;IACjD,IAAI/B,IAAI;IAER,IAAIb,UAAU,CAACO,SAAS,EAAE;MACxBM,IAAI,GAAGgC,cAAQ,CAACC,SAAS,CAACC,GAAG;AACnC;AACA,UAA8B;IAC1B,CAAC,MAAM;MACLlC,IAAI,GAAGxB,WAAC,CAAC2D,cAAc,CAAC,EAAE,CAAC;IAC7B;IAEAR,aAAa,CAACS,gBAAgB,CAC5B,MAAM,EACN5D,WAAC,CAAC6D,WAAW,CAAC,aAAa,EAAE7D,WAAC,CAAC8D,UAAU,CAAC,aAAa,CAAC,EAAEP,MAAM,EAAE/B,IAAI,CACxE,CAAC;EACH;EAEA,SAASuC,SAASA,CAAA,EAAG;IACnBb,sBAAsB,CAAC,CAAC;IACxBc,QAAQ,CAAC,CAAC;IACVC,iBAAiB,CAAC,CAAC;IAEnB,IAAItD,UAAU,CAACU,eAAe,EAAE;MAC9B,MAAM;QAAExB,eAAe;QAAEwB,eAAe;QAAED;MAAU,CAAC,GAAGT,UAAU;MAElEd,eAAe,CAAC2B,IAAI,CAACqB,IAAI,CAAC,GAAGxB,eAAe,CAACG,IAAI,CAACA,IAAI,CAAC;MACvDxB,WAAC,CAACG,QAAQ,CAACiB,SAAS,EAAEC,eAAe,CAAC;MACtCrB,WAAC,CAACG,QAAQ,CAACN,eAAe,EAAEwB,eAAe,CAACG,IAAI,CAAC;IACnD;IAEA0C,eAAe,CAAC,CAAC;EACnB;EAEA,SAASF,QAAQA,CAAA,EAAG;IAClB,MAAMG,cAA0B,GAAGxD,UAAU,CAACN,IAAI,CAAC+C,GAAG,CAAC,WAAW,CAAC;IAEnE,KAAK,MAAM/C,IAAI,IAAI8D,cAAc,EAAE;MACjC,MAAMrE,IAAI,GAAGO,IAAI,CAACP,IAAI;MAEtB,IAAIO,IAAI,CAAC+D,eAAe,CAAC,CAAC,IAAI/D,IAAI,CAACgE,sBAAsB,CAAC,CAAC,EAAE;QAC3D,MAAMhE,IAAI,CAACiE,mBAAmB,CAAC,qCAAqC,CAAC;MACvE;MAEA,IAAIxE,IAAI,CAACyE,UAAU,EAAE;QACnB,MAAMlE,IAAI,CAACiE,mBAAmB,CAC5B,yEACF,CAAC;MACH;MAEA,IAAItE,WAAC,CAACqD,aAAa,CAACvD,IAAI,CAAC,EAAE;QACzB,MAAM0E,aAAa,GAAG1E,IAAI,CAACwD,IAAI,KAAK,aAAa;QAEjD,MAAMmB,aAAa,GAAG,IAAIC,4BAAa,CAAC;UACtCC,UAAU,EAAEtE,IAAI;UAChBuE,SAAS,EAAEjE,UAAU,CAACf,QAAQ;UAC9BiF,QAAQ,EAAElE,UAAU,CAACK,SAAS;UAC9B8D,aAAa,EAAErE,WAAW,CAACqE,aAAa;UACxCxE,IAAI,EAAEK,UAAU,CAACL,IAAI;UACrByE,aAAa,EAAEpE,UAAU,CAACf;QAC5B,CAAC,CAAC;QAEF6E,aAAa,CAACO,OAAO,CAAC,CAAC;QAEvB,MAAM/D,YAA2C,GAAG,EAAE;QACtDZ,IAAI,CAAC4E,QAAQ,CACXvC,kBAAQ,CAACC,kBAAkB,CAAC;UAC1BuC,eAAeA,CAAC7E,IAAI,EAAE;YACpB,IAAI,CAACA,IAAI,CAAC8E,iBAAiB,CAAC,CAAC,CAACC,yBAAyB,CAAC,CAAC,EAAE;cACzDnE,YAAY,CAAC4B,IAAI,CAACxC,IAAI,CAAC;YACzB;UACF;QACF,CAAC,CACH,CAAC;QAED,IAAImE,aAAa,EAAE;UACjBa,eAAe,CAACpE,YAAY,EAAEnB,IAAI,EAAsBO,IAAI,CAAC;QAC/D,CAAC,MAAM;UAC4D;YAAA,IAAAiF,qBAAA;YAE/D,CAAAA,qBAAA,GAAAjF,IAAI,CAACkF,kBAAkB,YAAAD,qBAAA,GAAvBjF,IAAI,CAACkF,kBAAkB,GAErBjG,OAAO,CAAC,iBAAiB,CAAC,CAACkG,QAAQ,CAACC,SAAS,CAACF,kBAAkB;UACpE;UACAlF,IAAI,CAACkF,kBAAkB,CAAC7E,gBAAgB,CAAC;UACzC,IAAIgF,OAAO;UACX,IAAI5F,IAAI,KAAKO,IAAI,CAACP,IAAI,EAAE;YACtB4F,OAAO,GAAGrF,IAAI,CAACP,IAAI;YAEnBO,IAAI,CAACsF,WAAW,CAAC7F,IAAI,CAAC;UACxB;UAEA8F,UAAU,CAAC9F,IAAI,EAAE4F,OAAO,CAAC;QAC3B;MACF;IACF;EACF;EAEA,SAASxB,eAAeA,CAAA,EAAG;IACzB2B,kBAAkB,CAAC,CAAC;IAEpB,MAAM;MAAErE;IAAK,CAAC,GAAGb,UAAU;IAE3B,MAAMmF,KAAmB,GAAG;MAC1B9D,QAAQ,EAAE,IAAI;MACdI,MAAM,EAAE;IACV,CAAC;IAED,KAAK,MAAM2D,SAAS,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAW;MACvD,IAAIpF,UAAU,CAACoB,OAAO,CAACgE,SAAS,CAAC,CAAC7D,IAAI,CAAC8D,MAAM,EAAE;QAC7CF,KAAK,CAACC,SAAS,CAAC,GAAGpF,UAAU,CAACoB,OAAO,CAACgE,SAAS,CAAC,CAAC7D,IAAI,CAACC,GAAG,CAAC8D,IAAI,IAAI;UAChE,MAAMC,GAAG,GAAGlG,WAAC,CAACmG,gBAAgB,CAAC,CAC7BnG,WAAC,CAACoG,cAAc,CAACpG,WAAC,CAAC8D,UAAU,CAAC,KAAK,CAAC,EAAEmC,IAAI,CAACI,GAAG,CAAC,CAChD,CAAC;UAEF,KAAK,MAAM/C,IAAI,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,EAAW;YACnD,IAAI2C,IAAI,CAAC3C,IAAI,CAAC,IAAI,IAAI,EAAE;cACtB4C,GAAG,CAACI,UAAU,CAACzD,IAAI,CACjB7C,WAAC,CAACoG,cAAc,CAACpG,WAAC,CAAC8D,UAAU,CAACR,IAAI,CAAC,EAAE2C,IAAI,CAAC3C,IAAI,CAAC,CACjD,CAAC;YACH;UACF;UAEA,OAAO4C,GAAG;QACZ,CAAC,CAAC;MACJ;IACF;IAEA,IAAIJ,KAAK,CAAC9D,QAAQ,IAAI8D,KAAK,CAAC1D,MAAM,EAAE;MAClC,IAAIW,IAAI,GAAG,CACT/C,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,EAChCkG,KAAK,CAAC9D,QAAQ,GAAGhC,WAAC,CAACuG,eAAe,CAACT,KAAK,CAAC9D,QAAQ,CAAC,GAAGhC,WAAC,CAACwG,WAAW,CAAC,CAAC,EACpEV,KAAK,CAAC1D,MAAM,GAAGpC,WAAC,CAACuG,eAAe,CAACT,KAAK,CAAC1D,MAAM,CAAC,GAAGpC,WAAC,CAACwG,WAAW,CAAC,CAAC,CACjE;MAED,IAAIC,gBAAgB,GAAG,CAAC;MACxB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3D,IAAI,CAACiD,MAAM,EAAEU,CAAC,EAAE,EAAE;QACpC,IAAI,CAAC1G,WAAC,CAAC2G,aAAa,CAAC5D,IAAI,CAAC2D,CAAC,CAAC,CAAC,EAAED,gBAAgB,GAAGC,CAAC;MACrD;MACA3D,IAAI,GAAGA,IAAI,CAAC6D,KAAK,CAAC,CAAC,EAAEH,gBAAgB,GAAG,CAAC,CAAC;MAE1CjF,IAAI,CAACqB,IAAI,CAAC7C,WAAC,CAAC6G,eAAe,CAAC/D,iBAAiB,CAACC,IAAI,CAAC,CAAC,CAAC;MACrDpC,UAAU,CAACgB,iBAAiB,GAAG,IAAI;IACrC;EACF;EAEA,SAASmF,aAAaA,CACpBC,SAAqC,EACrClC,QAAsB,EACtBmC,OAA2B,EAC3BxF,IAAgC,EAChC;IACA,MAAMyF,aAAa,GAAGF,SAAS,CAACjH,IAAI;IACpC,IAAIoH,IAAI;IAER,IAAIzG,WAAW,CAAC0G,0BAA0B,EAAE;MAC1CF,aAAa,CAACG,SAAS,CAACC,OAAO,CAACrH,WAAC,CAACsH,cAAc,CAAC,CAAC,CAAC;MACnD,IACEL,aAAa,CAACG,SAAS,CAACpB,MAAM,KAAK,CAAC,IACpChG,WAAC,CAACuH,eAAe,CAACN,aAAa,CAACG,SAAS,CAAC,CAAC,CAAC,CAAC,IAC7CpH,WAAC,CAACwH,YAAY,CAACP,aAAa,CAACG,SAAS,CAAC,CAAC,CAAC,CAACK,QAAQ,EAAE;QAClDC,IAAI,EAAE;MACR,CAAC,CAAC,EACF;QAEAT,aAAa,CAACG,SAAS,CAAC,CAAC,CAAC,GAAGH,aAAa,CAACG,SAAS,CAAC,CAAC,CAAC,CAACK,QAAQ;QAChER,aAAa,CAACU,MAAM,GAAG3H,WAAC,CAAC4H,gBAAgB,CACvC5H,WAAC,CAACE,SAAS,CAAC2E,QAAQ,CAAC,EACrB7E,WAAC,CAAC8D,UAAU,CAAC,OAAO,CACtB,CAAC;MACH,CAAC,MAAM;QACLmD,aAAa,CAACU,MAAM,GAAG3H,WAAC,CAAC4H,gBAAgB,CACvC5H,WAAC,CAACE,SAAS,CAAC2E,QAAQ,CAAC,EACrB7E,WAAC,CAAC8D,UAAU,CAAC,MAAM,CACrB,CAAC;MACH;MAEAoD,IAAI,GAAGlH,WAAC,CAAC6H,iBAAiB,CAAC,IAAI,EAAEZ,aAAa,EAAEjH,WAAC,CAACsH,cAAc,CAAC,CAAC,CAAC;IACrE,CAAC,MAAM;MAAA,IAAAQ,qBAAA;MACL,MAAM/E,IAAoB,GAAG,CAC3B/C,WAAC,CAACsH,cAAc,CAAC,CAAC,EAClBtH,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,CACjC;MACD,KAAAkI,qBAAA,GAAIb,aAAa,CAACG,SAAS,aAAvBU,qBAAA,CAAyB9B,MAAM,EAAE;QACnC,MAAM+B,sBAAsB,GAAGd,aAAa,CAACG,SAG1C;QAcH,IACEW,sBAAsB,CAAC/B,MAAM,KAAK,CAAC,IACnChG,WAAC,CAACuH,eAAe,CAACQ,sBAAsB,CAAC,CAAC,CAAC,CAAC,IAC5C/H,WAAC,CAACwH,YAAY,CAACO,sBAAsB,CAAC,CAAC,CAAC,CAACN,QAAQ,EAAE;UACjDC,IAAI,EAAE;QACR,CAAC,CAAC,EACF;UACA3E,IAAI,CAACF,IAAI,CAACkF,sBAAsB,CAAC,CAAC,CAAC,CAACN,QAAQ,CAAC;QAC/C,CAAC,MAAM;UACL1E,IAAI,CAACF,IAAI,CAAC7C,WAAC,CAACuG,eAAe,CAACwB,sBAAsB,CAAC,CAAC;QACtD;MACF;MACAb,IAAI,GAAGlH,WAAC,CAACgD,cAAc,CAAC,IAAAgF,+BAAkB,EAACrH,UAAU,CAACL,IAAI,CAAC,EAAEyC,IAAI,CAAC;IACpE;IAEA,IACEgE,SAAS,CAACkB,UAAU,CAACC,qBAAqB,CAAC,CAAC,IAC5CnB,SAAS,CAACkB,UAAU,CAACE,SAAS,KAAK3G,IAAI,CAAC1B,IAAI,CAAC0B,IAAI,IACjDA,IAAI,CAAC1B,IAAI,CAAC0B,IAAI,CAACwE,MAAM,GAAG,CAAC,KAAKe,SAAS,CAACkB,UAAU,CAAC5B,GAAG,EACtD;MAIA,IAAI1F,UAAU,CAACc,WAAW,CAACuE,MAAM,EAAE;QACjCkB,IAAI,GAAGlH,WAAC,CAACoI,oBAAoB,CAAC,GAAG,EAAEpB,OAAO,CAAC,CAAC,EAAEE,IAAI,CAAC;MACrD;MAEAH,SAAS,CAACkB,UAAU,CAACtC,WAAW,CAAC3F,WAAC,CAAC6G,eAAe,CAACK,IAAI,CAAC,CAAC;IAC3D,CAAC,MAAM;MACLH,SAAS,CAACpB,WAAW,CAAC3F,WAAC,CAACoI,oBAAoB,CAAC,GAAG,EAAEpB,OAAO,CAAC,CAAC,EAAEE,IAAI,CAAC,CAAC;IACrE;EACF;EAEA,SAASjD,iBAAiBA,CAAA,EAAG;IAC3B,IAAI,CAACtD,UAAU,CAACO,SAAS,EAAE;IAE3B,MAAMb,IAAI,GAAGM,UAAU,CAACW,mBAAmB;IAC3C,MAAME,IAAI,GAAGnB,IAAI,CAAC+C,GAAG,CAAC,MAAM,CAAC;IAE7B,MAAMvD,eAAe,GAAGQ,IAAI,CAAC+C,GAAG,CAAC,MAAM,CAAC;IAExC,IAAIiF,6BAA6B,GAAGxI,eAAe,CAACC,IAAI,CAAC0B,IAAI,CAACwE,MAAM;IAEpE3F,IAAI,CAAC4E,QAAQ,CAACxC,iBAAiB,CAAC;IAEhC,IAAIuE,OAAO,GAAG,SAAAA,CAAA,EAAY;MACxB,MAAMsB,GAAG,GAAGjI,IAAI,CAACS,KAAK,CAACyH,6BAA6B,CAAC,MAAM,CAAC;MAC5DF,6BAA6B,EAAE;MAC/BrB,OAAO,GAAGA,CAAA,KAAMhH,WAAC,CAACE,SAAS,CAACoI,GAAG,CAAC;MAChC,OAAOA,GAAG;IACZ,CAAC;IAED,MAAME,0BAA0B,GAAG,SAAAA,CAAA,EAAY;MAC7C,OAAOxI,WAAC,CAACgD,cAAc,CACrBrC,UAAU,CAACL,IAAI,CAAC2C,SAAS,CAAC,uBAAuB,CAAC,EAClD,CAAC+D,OAAO,CAAC,CAAC,CACZ,CAAC;IACH,CAAC;IAED,MAAMyB,UAAwC,GAAG,EAAE;IACnDpI,IAAI,CAAC4E,QAAQ,CACXvC,kBAAQ,CAACC,kBAAkB,CAAC;MAC1B+F,KAAKA,CAACrI,IAAI,EAAE;QACV,MAAM;UAAEP,IAAI;UAAEmI;QAAW,CAAC,GAAG5H,IAAI;QACjC,IAAI4H,UAAU,CAACU,gBAAgB,CAAC;UAAEhB,MAAM,EAAE7H;QAAK,CAAC,CAAC,EAAE;UACjD2I,UAAU,CAACpB,OAAO,CAACY,UAAU,CAAC;QAChC;MACF;IACF,CAAC,CACH,CAAC;IAED,KAAK,MAAMlB,SAAS,IAAI0B,UAAU,EAAE;MAClC3B,aAAa,CAACC,SAAS,EAAEpG,UAAU,CAACK,SAAS,EAAEgG,OAAO,EAAExF,IAAI,CAAC;MAE7D,IAAI6G,6BAA6B,IAAI,CAAC,EAAE;QACtC,IAAIO,cAAwB;QAC5B7B,SAAS,CAAC8B,IAAI,CAAC,UAAUZ,UAAU,EAAE;UAEnC,IAAIA,UAAU,KAAKpI,eAAe,EAAE;YAClCwI,6BAA6B,GAAGS,IAAI,CAACC,GAAG,CACtCV,6BAA6B,EAC7BO,cAAc,CAACvC,GACjB,CAAC;YACD,OAAO,IAAI;UACb;UAEA,MAAM;YAAE2C;UAAK,CAAC,GAAGf,UAAU;UAC3B,QAAQe,IAAI;YACV,KAAK,qBAAqB;YAC1B,KAAK,oBAAoB;YACzB,KAAK,sBAAsB;YAC3B,KAAK,kBAAkB;YACvB,KAAK,kBAAkB;YACvB,KAAK,gBAAgB;YACrB,KAAK,eAAe;YACpB,KAAK,oBAAoB;YACzB,KAAK,qBAAqB;YAC1B,KAAK,gBAAgB;YACrB,KAAK,iBAAiB;YACtB,KAAK,kBAAkB;YACvB,KAAK,gBAAgB;YACrB,KAAK,iBAAiB;cACpBJ,cAAc,GAAGX,UAAU;cAC3B,OAAO,KAAK;YACd;cACE,IACGe,IAAI,KAAK,mBAAmB,IAC3Bf,UAAU,CAACnI,IAAI,CAACmJ,IAAI,KAAKL,cAAc,CAAC9I,IAAI,IAC7CmI,UAAU,CAACiB,aAAa,CAAC,CAAC,IACzBjB,UAAU,CAACnI,IAAI,CAACqJ,IAAI,KAAKP,cAAc,CAAC9I,IAAK,IAC9CkJ,IAAI,KAAK,wBAAwB,IAChCf,UAAU,CAACnI,IAAI,CAAC6H,MAAM,KAAKiB,cAAc,CAAC9I,IAAK,IAChDkJ,IAAI,KAAK,0BAA0B,IAClCf,UAAU,CAACnI,IAAI,CAACsJ,MAAM,KAAKR,cAAc,CAAC9I,IAAK,EACjD;gBACA8I,cAAc,GAAGX,UAAU;gBAC3B,OAAO,KAAK;cACd;UACJ;UAEAI,6BAA6B,GAAG,CAAC,CAAC;UAClC,OAAO,IAAI;QACb,CAAC,CAAC;MACJ;IACF;IAEA,MAAMgB,eAAe,GAAG,IAAIC,GAAG,CAAW,CAAC;IAE3C,KAAK,MAAMC,QAAQ,IAAI5I,UAAU,CAACc,WAAW,EAAE;MAC7C,MAAM;QAAE3B,IAAI;QAAEmI;MAAW,CAAC,GAAGsB,QAAQ;MACrC,IAAItB,UAAU,CAACuB,kBAAkB,CAAC;QAAEJ,MAAM,EAAEtJ;MAAK,CAAC,CAAC,EAAE;QACnDyJ,QAAQ,CAAC5D,WAAW,CAACqB,OAAO,CAAC,CAAC,CAAC;QAC/B;MACF;MAEA,IAAIyC,SAAiB;MACrBF,QAAQ,CAACV,IAAI,CAAC,UAAUZ,UAAU,EAAE;QAClC,IAAIA,UAAU,CAACA,UAAU,KAAKpI,eAAe,EAAE;UAC7C4J,SAAS,GAAGxB,UAAU,CAAC5B,GAAa;UACpC,OAAO,IAAI;QACb;MACF,CAAC,CAAC;MAEF,IAAIqD,QAAkB,GAAGH,QAAQ,CAACtB,UAAU,CAAC0B,oBAAoB,CAAC,CAAC,GAC/DJ,QAAQ,CAACtB,UAAU,GACnBsB,QAAQ;MACZ,IACEG,QAAQ,CAACE,OAAO,KAAK,WAAW,KAC/BF,QAAQ,CAACzB,UAAU,CAACU,gBAAgB,CAAC,CAAC,IACrCe,QAAQ,CAACzB,UAAU,CAAC4B,wBAAwB,CAAC,CAAC,CAAC,EACjD;QACAH,QAAQ,GAAGA,QAAQ,CAACzB,UAAU;MAChC,CAAC,MAAM;QACLyB,QAAQ,GAAG,IAAI;MACjB;MAEA,IACGrB,6BAA6B,KAAK,CAAC,CAAC,IACnCoB,SAAS,GAAGpB,6BAA6B,IAC3CgB,eAAe,CAACS,GAAG,CAACJ,QAAQ,CAAC,EAC7B;QACAH,QAAQ,CAAC5D,WAAW,CAACqB,OAAO,CAAC,CAAC,CAAC;MACjC,CAAC,MAAM;QACL,IAAI0C,QAAQ,EAAE;UACZL,eAAe,CAACU,GAAG,CAACL,QAAQ,CAAC;QAC/B;QACAH,QAAQ,CAAC5D,WAAW,CAAC6C,0BAA0B,CAAC,CAAC,CAAC;MACpD;IACF;IAEA,IAAIwB,UAAU;IAEd,IAAIrJ,UAAU,CAACH,OAAO,EAAE;MACtBwJ,UAAU,GAAIC,SAA8B,IAAK;QAC/C,MAAMC,QAAQ,GAAG1B,0BAA0B,CAAC,CAAC;QAC7C,OAAOyB,SAAS,GACZjK,WAAC,CAAC6H,iBAAiB,CAAC,IAAI,EAAEoC,SAAS,EAAEC,QAAQ,CAAC,GAC9CA,QAAQ;MACd,CAAC;IACH,CAAC,MAAM;MACLF,UAAU,GAAIC,SAAmC,IAAK;QACpD,MAAME,YAA4B,GAAG,CAACnD,OAAO,CAAC,CAAC,CAAC;QAChD,IAAIiD,SAAS,IAAI,IAAI,EAAE;UACrBE,YAAY,CAACtH,IAAI,CAACoH,SAAS,CAAC;QAC9B;QACA,OAAOjK,WAAC,CAACgD,cAAc,CACrBrC,UAAU,CAACL,IAAI,CAAC2C,SAAS,CAAC,2BAA2B,CAAC,EACtDkH,YACF,CAAC;MACH,CAAC;IACH;IAIA,MAAMC,SAAS,GAAG5I,IAAI,CAAC4B,GAAG,CAAC,MAAM,CAAC;IAClC,MAAMiH,2BAA2B,GAC/BhC,6BAA6B,KAAK,CAAC,CAAC,IACpCA,6BAA6B,GAAG+B,SAAS,CAACpE,MAAM;IAClD,IAAI,CAACoE,SAAS,CAACpE,MAAM,IAAI,CAACoE,SAAS,CAACE,GAAG,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC,EAAE;MAC7D/I,IAAI,CAACgJ,aAAa,CAChB,MAAM,EACNxK,WAAC,CAAC6G,eAAe,CACfwD,2BAA2B,GACvBrD,OAAO,CAAC,CAAC,GACTwB,0BAA0B,CAAC,CACjC,CACF,CAAC;IACH;IAEA,KAAK,MAAMiC,UAAU,IAAI9J,UAAU,CAACM,YAAY,EAAE;MAChDwJ,UAAU,CACPrH,GAAG,CAAC,UAAU,CAAC,CACfuC,WAAW,CAACqE,UAAU,CAACS,UAAU,CAAC3K,IAAI,CAAC2H,QAAQ,CAAC,CAAC;IACtD;EACF;EAKA,SAAS7B,UAAUA,CAAC9F,IAAmB,EAAE4F,OAAsB,EAAE;IAC/D,IAAI5F,IAAI,CAACwD,IAAI,KAAK,QAAQ,EAAE;MAC1B,IAAIoH,aAAa,CAAC5K,IAAI,CAAC,EAAE;IAC3B;IAEA,MAAMiG,SAAS,GAAGjG,IAAI,CAACsC,MAAM,GAAG,QAAQ,GAAG,UAAU;IACrD,MAAML,OAAO,GAAGpB,UAAU,CAACoB,OAAO,CAACgE,SAAS,CAAC;IAE7C,MAAM4E,OAAO,GAAG7K,IAAI,CAACwD,IAAI,KAAK,QAAQ,GAAG,OAAO,GAAGxD,IAAI,CAACwD,IAAI;IAC5D,MAAM+C,GAAG,GACPrG,WAAC,CAAC4K,gBAAgB,CAAC9K,IAAI,CAACuG,GAAG,CAAC,IAAIrG,WAAC,CAAC6K,eAAe,CAAC/K,IAAI,CAACuG,GAAG,CAAC,GACvDrG,WAAC,CAAC8K,aAAa,CAACC,MAAM,CAACjL,IAAI,CAACuG,GAAG,CAAC2E,KAAK,CAAC,CAAC,GACvChL,WAAC,CAACiL,aAAa,CAACnL,IAAI,CAAC;IAC3BiC,OAAO,CAACE,WAAW,GAAG,CAACjC,WAAC,CAACkL,eAAe,CAAC7E,GAAG,CAAC;IAE7C,MAAM8E,EAAgB,GAAGzF,OAAO,WAAPA,OAAO,GAAI1F,WAAC,CAACoL,YAAY,CAACtL,IAAI,CAAC;IAExD,IAAIuL,UAAsB;IAC1B,IACE,CAACtJ,OAAO,CAACE,WAAW,IACpBF,OAAO,CAACI,GAAG,CAAC2H,GAAG,CAAEzD,GAAG,CAAqB2E,KAAK,CAAC,EAC/C;MACAK,UAAU,GAAGtJ,OAAO,CAACI,GAAG,CAACiB,GAAG,CAAEiD,GAAG,CAAqB2E,KAAK,CAAC;MAC5DK,UAAU,CAACV,OAAO,CAAC,GAAGQ,EAAE;MAExB,IAAIR,OAAO,KAAK,OAAO,EAAE;QACvBU,UAAU,CAACjI,GAAG,GAAG,IAAI;QACrBiI,UAAU,CAACC,GAAG,GAAG,IAAI;MACvB,CAAC,MAAM;QACLD,UAAU,CAACL,KAAK,GAAG,IAAI;MACzB;IACF,CAAC,MAAM;MACLK,UAAU,GAAG;QACXhF,GAAG,EAEDA,GAAmB;QACrB,CAACsE,OAAO,GAAGQ;MACb,CAAe;MACfpJ,OAAO,CAACG,IAAI,CAACW,IAAI,CAACwI,UAAU,CAAC;MAE7B,IAAI,CAACtJ,OAAO,CAACE,WAAW,EAAE;QACxBF,OAAO,CAACI,GAAG,CAACmJ,GAAG,CAAEjF,GAAG,CAAqB2E,KAAK,EAAEK,UAAU,CAAC;MAC7D;IACF;EACF;EAEA,SAASX,aAAaA,CAAC5K,IAAmB,EAAE;IAC1C,IAAIW,WAAW,CAAC8K,eAAe,IAAI,CAACzL,IAAI,CAACyE,UAAU,EAAE;MAEnD,IAAI;QAAE3E;MAAS,CAAC,GAAGe,UAAU;MAC7B,IAAI,CAACb,IAAI,CAACsC,MAAM,EAAE;QAChBoJ,oBAAoB,CAAC,CAAC;QACtB5L,QAAQ,GAAGe,UAAU,CAACiB,UAAU;MAClC;MACA,MAAM6J,UAAU,GAAGzL,WAAC,CAAC4H,gBAAgB,CACnC5H,WAAC,CAACE,SAAS,CAACN,QAAQ,CAAC,EACrBE,IAAI,CAACuG,GAAG,EACRvG,IAAI,CAAC4L,QAAQ,IAAI1L,WAAC,CAAC2L,SAAS,CAAC7L,IAAI,CAACuG,GAAG,CACvC,CAAC;MAED,MAAMtG,IAAkB,GAAGC,WAAC,CAAC4L,kBAAkB,CAE7C9L,IAAI,CAAC+L,EAAE,EAEP/L,IAAI,CAACyD,MAAM,EACXzD,IAAI,CAAC0B,IAAI,EACT1B,IAAI,CAACgM,SAAS,EACdhM,IAAI,CAACiM,KACP,CAAC;MACD/L,WAAC,CAACG,QAAQ,CAACJ,IAAI,EAAED,IAAI,CAAC;MAEtB,MAAMkM,IAAI,GAAGhM,WAAC,CAACiM,mBAAmB,CAChCjM,WAAC,CAACoI,oBAAoB,CAAC,GAAG,EAAEqD,UAAU,EAAE1L,IAAI,CAC9C,CAAC;MACDC,WAAC,CAACkM,gBAAgB,CAACF,IAAI,EAAElM,IAAI,CAAC;MAC9Ba,UAAU,CAACa,IAAI,CAACqB,IAAI,CAACmJ,IAAI,CAAC;MAC1B,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd;EAEA,SAASR,oBAAoBA,CAAA,EAAG;IAC9B,IAAI7K,UAAU,CAACiB,UAAU,KAAK,IAAI,EAAE;MAClCS,QAAQ,CAAC;QAAET,UAAU,EAAEjB,UAAU,CAACG,KAAK,CAACqL,qBAAqB,CAAC,OAAO;MAAE,CAAC,CAAC;MACzE,MAAMC,UAAU,GAAGpM,WAAC,CAAC4H,gBAAgB,CACnCjH,UAAU,CAACf,QAAQ,EACnBI,WAAC,CAAC8D,UAAU,CAAC,WAAW,CAC1B,CAAC;MACD,MAAMuI,gBAAgB,GAAGrM,WAAC,CAACsM,mBAAmB,CAAC,KAAK,EAAE,CACpDtM,WAAC,CAACuM,kBAAkB,CAAC5L,UAAU,CAACiB,UAAU,EAAEwK,UAAU,CAAC,CACxD,CAAC;MAEFzL,UAAU,CAACa,IAAI,CAACqB,IAAI,CAACwJ,gBAAgB,CAAC;IACxC;EACF;EAKA,SAAShH,eAAeA,CACtBpE,YAA2C,EAC3CuL,MAAwB,EACxBnM,IAAgC,EAChC;IACAgC,QAAQ,CAAC;MACPf,mBAAmB,EAAEjB,IAAI;MACzBgB,eAAe,EAAEmL,MAAM;MACvBjL,cAAc,EAAE,IAAI;MACpBN;IACF,CAAC,CAAC;IAEF,MAAM;MAAEG;IAAU,CAAC,GAAGT,UAAU;IAEhCX,WAAC,CAACkM,gBAAgB,CAAC9K,SAAS,EAAEoL,MAAM,CAAC;IAGrCpL,SAAS,CAACmC,MAAM,GAAGiJ,MAAM,CAACjJ,MAAM;IAEhCvD,WAAC,CAACG,QAAQ,CAACiB,SAAS,CAACI,IAAI,EAAEgL,MAAM,CAAChL,IAAI,CAAC;IACvCJ,SAAS,CAACI,IAAI,CAACiL,UAAU,GAAGD,MAAM,CAAChL,IAAI,CAACiL,UAAU;IAIlD,IAAI9L,UAAU,CAAC+L,sBAAsB,IAAI/L,UAAU,CAACgM,oBAAoB,EAAE;MACxEzI,eAAe,CAAC,CAAC;IACnB;IAEA2B,kBAAkB,CAAC,CAAC;EACtB;EAKA,SAASA,kBAAkBA,CAAA,EAAG;IAC5B,IAAI,CAAClF,UAAU,CAACO,SAAS,IAAIP,UAAU,CAACe,cAAc,EAAE;IAExDf,UAAU,CAACe,cAAc,GAAG,IAAI;IAKhCf,UAAU,CAACa,IAAI,CAAC6F,OAAO,CACrBrH,WAAC,CAACiM,mBAAmB,CACnBjM,WAAC,CAACgD,cAAc,CACdrC,UAAU,CAACL,IAAI,CAAC2C,SAAS,CACvBtC,UAAU,CAACH,OAAO,GAAG,eAAe,GAAG,UACzC,CAAC,EACD,CAACR,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,EAAEI,WAAC,CAACE,SAAS,CAACS,UAAU,CAACK,SAAS,CAAC,CACtE,CACF,CACF,CAAC;EACH;EAEA,SAAS4L,kBAAkBA,CAAA,EAAG;IAC5B,MAAM;MAAE/K,WAAW;MAAE/B,IAAI;MAAEgB;IAAM,CAAC,GAAGH,UAAU;IAE/C,KAAK,MAAMkM,IAAI,IAAI/M,IAAI,CAAC0B,IAAI,CAACA,IAAI,EAAE;MACjC,IAAI,CAACxB,WAAC,CAACqD,aAAa,CAACwJ,IAAI,CAAC,IAAI,CAACA,IAAI,CAACnB,QAAQ,EAAE;MAC9C,IAAI5K,KAAK,CAACgM,MAAM,CAACD,IAAI,CAACxG,GAAG,EAAsB,IAAI,CAAC,EAAE;MAEtD,MAAMwF,EAAE,GAAG/K,KAAK,CAACiM,gCAAgC,CAACF,IAAI,CAACxG,GAAG,CAAC;MAC3DxE,WAAW,CAACyJ,GAAG,CAACO,EAAE,CAACnE,IAAI,EAAEmF,IAAI,CAACxG,GAAG,CAAC;MAElCwG,IAAI,CAACxG,GAAG,GAAGwF,EAAE;IACf;EACF;EAEA,SAASmB,sBAAsBA,CAAA,EAAG;IAChC,MAAM;MAAEhM,SAAS;MAAEa;IAAY,CAAC,GAAGlB,UAAU;IAC7C,MAAMsM,aAAa,GAAG,EAAE;IACxB,MAAMC,WAAW,GAAG,EAAE;IAEtB,IAAIvM,UAAU,CAACO,SAAS,EAAE;MACxB,IAAIiM,GAAG,GAAGnN,WAAC,CAACE,SAAS,CAACc,SAAS,CAAC;MAChC,IAAIL,UAAU,CAACQ,aAAa,EAAE;QAC5BgM,GAAG,GAAGnN,WAAC,CAACgD,cAAc,CAACrC,UAAU,CAACL,IAAI,CAAC2C,SAAS,CAAC,iBAAiB,CAAC,EAAE,CACnEkK,GAAG,CACJ,CAAC;QACF,IAAAC,6BAAc,EAACD,GAAG,CAAC;MACrB;MAEA,MAAME,KAAK,GACT1M,UAAU,CAACG,KAAK,CAACiM,gCAAgC,CAAC/L,SAAS,CAAC;MAE9DiM,aAAa,CAACpK,IAAI,CAACwK,KAAK,CAAC;MACzBH,WAAW,CAACrK,IAAI,CAACsK,GAAG,CAAC;MAErB9K,QAAQ,CAAC;QAAErB,SAAS,EAAEhB,WAAC,CAACE,SAAS,CAACmN,KAAK;MAAE,CAAC,CAAC;IAC7C;IAEA,KAAK,MAAM,CAAC3F,IAAI,EAAEsD,KAAK,CAAC,IAAInJ,WAAW,EAAE;MACvCoL,aAAa,CAACpK,IAAI,CAAC7C,WAAC,CAAC8D,UAAU,CAAC4D,IAAI,CAAC,CAAC;MACtCwF,WAAW,CAACrK,IAAI,CAACmI,KAAK,CAAC;IACzB;IAEA,OAAO;MAAEiC,aAAa;MAAEC;IAAY,CAAC;EACvC;EAEA,SAASI,gBAAgBA,CACvBjN,IAAuB,EACvBC,IAAU,EACVC,cAAmC,EACnCC,OAAgB,EAChB;IACA6B,QAAQ,CAAC;MACPzB,MAAM,EAAEP,IAAI,CAACO,MAAM;MACnBE,KAAK,EAAET,IAAI,CAACS,KAAK;MACjBhB,IAAI,EAAEO,IAAI,CAACP,IAAI;MACfO,IAAI;MACJC,IAAI;MACJE;IACF,CAAC,CAAC;IAEF6B,QAAQ,CAAC;MACPtB,OAAO,EAAEJ,UAAU,CAACb,IAAI,CAAC+L,EAAE;MAE3BjM,QAAQ,EAAEe,UAAU,CAACb,IAAI,CAAC+L,EAAE,GACxB7L,WAAC,CAAC8D,UAAU,CAACnD,UAAU,CAACb,IAAI,CAAC+L,EAAE,CAACnE,IAAI,CAAC,GACrC/G,UAAU,CAACG,KAAK,CAACqL,qBAAqB,CAAC,OAAO,CAAC;MACnDnL,SAAS,EAAEL,UAAU,CAACb,IAAI,CAACyN,UAAU;MACrCrM,SAAS,EAAE,CAAC,CAACP,UAAU,CAACb,IAAI,CAACyN,UAAU;MACvC1N,eAAe,EAAEG,WAAC,CAAC2D,cAAc,CAAC,EAAE;IACtC,CAAC,CAAC;IAEFtB,QAAQ,CAAC;MACPlB,aAAa,EACXnB,WAAC,CAACwH,YAAY,CAAC7G,UAAU,CAACK,SAAS,CAAC,IACpCT,cAAc,CAACuJ,GAAG,CAACnJ,UAAU,CAACK,SAAS,CAAC0G,IAAI,CAAC,IAC7C,CAAC/G,UAAU,CAACG,KAAK,CAAC0M,UAAU,CAC1B7M,UAAU,CAACK,SAAS,CAAC0G,IAAI,EACT,IAClB;IACJ,CAAC,CAAC;IAEF,MAAM;MAAE9H,QAAQ;MAAEE,IAAI;MAAED;IAAgB,CAAC,GAAGc,UAAU;IAEtD0B,QAAQ,CAAC;MACPjB,SAAS,EAAEzB,gBAAgB,CAACC,QAAQ,EAAEC,eAAe,EAAEC,IAAI;IAC7D,CAAC,CAAC;IAEF8M,kBAAkB,CAAC,CAAC;IAEpB,MAAM;MAAEpL;IAAK,CAAC,GAAGb,UAAU;IAC3B,MAAM;MAAEsM,aAAa;MAAEC;IAAY,CAAC,GAAGF,sBAAsB,CAAC,CAAC;IAE/DjJ,SAAS,CAAC,CAAC;IAGX,IAAI,CAACtD,WAAW,CAACgN,YAAY,EAAE;MAC7B5N,eAAe,CAAC2B,IAAI,CAAC6F,OAAO,CAC1BrH,WAAC,CAACiM,mBAAmB,CACnBjM,WAAC,CAACgD,cAAc,CAACrC,UAAU,CAACL,IAAI,CAAC2C,SAAS,CAAC,gBAAgB,CAAC,EAAE,CAC5DjD,WAAC,CAACsH,cAAc,CAAC,CAAC,EAClBtH,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,CACjC,CACH,CACF,CAAC;IACH;IAEA,MAAM8N,QAAQ,GAAGrN,IAAI,CAACsN,cAAc,CAAC,CAAC;IACtC,IAAIC,eAAe,GAAGpM,IAAI,CAACwE,MAAM,KAAK,CAAC;IACvC,IAAI4H,eAAe,IAAI,CAACF,QAAQ,EAAE;MAChC,KAAK,MAAML,KAAK,IAAI1M,UAAU,CAACS,SAAS,CAACmC,MAAM,EAAE;QAI/C,IAAI,CAACvD,WAAC,CAACwH,YAAY,CAAC6F,KAAK,CAAC,EAAE;UAC1BO,eAAe,GAAG,KAAK;UACvB;QACF;MACF;IACF;IAEA,MAAMnB,UAAU,GAAGmB,eAAe,GAC9BjN,UAAU,CAACS,SAAS,CAACI,IAAI,CAACiL,UAAU,GACpC,EAAE;IACN,IAAI,CAACiB,QAAQ,EAAE;MACbjB,UAAU,CAAC5J,IAAI,CAAC7C,WAAC,CAAC6N,SAAS,CAAC7N,WAAC,CAAC8N,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC;IAChE;IAEA,IAAIF,eAAe,EAAE;MAEnB,MAAM5B,IAAI,GAAGhM,WAAC,CAACoL,YAAY,CAACzK,UAAU,CAACS,SAAS,CAAC;MACjD,OAAOT,UAAU,CAACH,OAAO,GAAGwL,IAAI,GAAGlJ,iBAAiB,CAAC,CAACkJ,IAAI,CAAC,CAAC;IAC9D;IAEA,IAAI,CAACrL,UAAU,CAACgB,iBAAiB,EAAE;MACjCH,IAAI,CAACqB,IAAI,CACP7C,WAAC,CAAC6G,eAAe,CACflG,UAAU,CAACH,OAAO,GACdR,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,GAChCkD,iBAAiB,CAAC,CAAC9C,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,CAAC,CAC1D,CACF,CAAC;IACH;IAEA4B,IAAI,CAAC6F,OAAO,CAAC1G,UAAU,CAACS,SAAS,CAAC;IAElC,MAAM+G,SAAS,GAAGnI,WAAC,CAAC+N,uBAAuB,CACzCd,aAAa,EACbjN,WAAC,CAAC2D,cAAc,CAACnC,IAAI,EAAEiL,UAAU,CACnC,CAAC;IACD,OAAOzM,WAAC,CAACgD,cAAc,CAACmF,SAAS,EAAE+E,WAAW,CAAC;EACjD;EAEA,OAAOI,gBAAgB,CAACjN,IAAI,EAAEC,IAAI,EAAEC,cAAc,EAAEC,OAAO,CAAC;AAC9D", "ignoreList": []}
package cn.zhentao.config;

import org.springframework.context.annotation.Configuration;
import jakarta.annotation.PostConstruct;

@Configuration
public class NacosConfig {

    @PostConstruct
    public void init() {
        // 强制禁用gRPC
        System.setProperty("nacos.remote.client.grpc.enable", "false");
        System.setProperty("com.alibaba.nacos.client.naming.cache.dir", System.getProperty("java.io.tmpdir") + "/nacos/naming");
    }
}

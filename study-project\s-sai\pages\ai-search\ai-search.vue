<template>
  <view class="container">
    <!-- 顶部标题 -->
    <view class="header">
      <view class="header-icon">🔍</view>
      <view class="header-info">
        <text class="header-title">信息查询</text>
        <text class="header-desc">天气·电话·资讯查询</text>
      </view>
    </view>

    <!-- 快捷查询分类 -->
    <view class="category-tabs">
      <view 
        v-for="(category, index) in categories" 
        :key="index"
        class="tab-item"
        :class="{ active: activeCategory === category.key }"
        @tap="switchCategory(category.key)"
      >
        <text class="tab-icon">{{ category.icon }}</text>
        <text class="tab-text">{{ category.name }}</text>
      </view>
    </view>

    <!-- 聊天区域 -->
    <scroll-view class="chat-area" scroll-y="true" :scroll-top="scrollTop" scroll-with-animation="true">
      <view class="message-list">
        <view v-for="(message, index) in messages" :key="index" class="message-item" :class="message.type">
          <view class="message-avatar">
            <text class="avatar-text">{{ message.type === 'user' ? '我' : 'AI' }}</text>
          </view>
          <view class="message-content">
            <view class="message-bubble">
              <text class="message-text">{{ message.content }}</text>
              <text class="message-time">{{ message.time }}</text>
            </view>
          </view>
        </view>
        
        <!-- AI正在输入提示 -->
        <view v-if="isAiTyping" class="message-item ai typing">
          <view class="message-avatar">
            <text class="avatar-text">AI</text>
          </view>
          <view class="message-content">
            <view class="message-bubble">
              <view class="typing-indicator">
                <view class="dot"></view>
                <view class="dot"></view>
                <view class="dot"></view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 输入区域 -->
    <view class="input-area">
      <view class="input-container">
        <input 
          class="input-field" 
          v-model="inputText" 
          :placeholder="currentPlaceholder"
          :disabled="isLoading"
          @confirm="sendMessage"
        />
        <button 
          class="send-btn" 
          :class="{ disabled: !inputText.trim() || isLoading }" 
          @tap="sendMessage"
        >
          {{ isLoading ? '查询中' : '查询' }}
        </button>
      </view>
    </view>

    <!-- 快捷查询 -->
    <view v-if="messages.length === 0" class="quick-queries">
      <text class="quick-title">{{ currentCategory.icon }} {{ currentCategory.examples.title }}</text>
      <view class="query-list">
        <view 
          v-for="(query, index) in currentCategory.examples.list" 
          :key="index" 
          class="query-item" 
          @tap="askQuery(query)"
        >
          <text class="query-text">{{ query }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      messages: [],
      inputText: '',
      isLoading: false,
      isAiTyping: false,
      scrollTop: 0,
      userId: 123,
      activeCategory: 'weather',
      categories: [
        {
          key: 'weather',
          name: '天气',
          icon: '🌤️',
          placeholder: '请输入城市名称查询天气...',
          examples: {
            title: '天气查询示例',
            list: [
              '北京今天的天气怎么样？',
              '明天保定会下雨吗？',
              '这周末的天气预报',
              '上海的气温是多少？'
            ]
          }
        },
        {
          key: 'phone',
          name: '电话',
          icon: '📞',
          placeholder: '请输入要查询的联系人...',
          examples: {
            title: '电话查询示例',
            list: [
              '爸爸的电话号码',
              '妈妈的手机号',
              '老师的联系方式',
              '紧急联系人电话'
            ]
          }
        },
        {
          key: 'news',
          name: '资讯',
          icon: '📰',
          placeholder: '请输入要查询的资讯内容...',
          examples: {
            title: '资讯查询示例',
            list: [
              '今天的新闻热点',
              '最新科技资讯',
              '教育政策新闻',
              '健康生活资讯'
            ]
          }
        },
        {
          key: 'other',
          name: '其他',
          icon: '🔎',
          placeholder: '请输入要查询的信息...',
          examples: {
            title: '其他查询示例',
            list: [
              '附近的医院',
              '公交路线查询',
              '快递查询',
              '节假日安排'
            ]
          }
        }
      ]
    }
  },
  computed: {
    currentCategory() {
      return this.categories.find(cat => cat.key === this.activeCategory) || this.categories[0];
    },
    currentPlaceholder() {
      return this.currentCategory.placeholder;
    }
  },
  onLoad() {
    this.loadUserInfo();
    this.addWelcomeMessage();
  },
  methods: {
    loadUserInfo() {
      const userInfo = uni.getStorageSync('userInfo');
      if (userInfo && userInfo.id) {
        this.userId = userInfo.id;
      }
    },

    addWelcomeMessage() {
      this.messages.push({
        type: 'ai',
        content: '你好！我是信息查询助手，可以帮您查询天气、电话、资讯等各种信息。请选择查询类型或直接提问！',
        time: this.getCurrentTime()
      });
      this.scrollToBottom();
    },

    switchCategory(categoryKey) {
      this.activeCategory = categoryKey;
      this.inputText = '';
    },

    askQuery(query) {
      this.inputText = query;
      this.sendMessage();
    },

    async sendMessage() {
      if (!this.inputText.trim() || this.isLoading) return;

      const userMessage = {
        type: 'user',
        content: this.inputText.trim(),
        time: this.getCurrentTime()
      };

      this.messages.push(userMessage);
      const query = this.inputText.trim();
      this.inputText = '';
      this.isLoading = true;
      this.isAiTyping = true;
      this.scrollToBottom();

      try {
        // 调用AI信息查询API
        const response = await this.callSearchAPI(query);
        
        this.isAiTyping = false;
        
        if (response && response.success) {
          this.messages.push({
            type: 'ai',
            content: response.response || '抱歉，没有找到相关信息。',
            time: this.getCurrentTime()
          });
        } else {
          this.messages.push({
            type: 'ai',
            content: '抱歉，查询服务暂时不可用，请稍后再试。',
            time: this.getCurrentTime()
          });
        }
      } catch (error) {
        this.isAiTyping = false;
        this.messages.push({
          type: 'ai',
          content: '网络连接失败，请检查网络后重试。',
          time: this.getCurrentTime()
        });
        console.error('API调用失败:', error);
      }

      this.isLoading = false;
      this.scrollToBottom();
    },

    async callSearchAPI(query) {
      const apiUrl = 'http://localhost:8082/api/ai/miniprogram/info/query';
      
      const response = await uni.request({
        url: apiUrl,
        method: 'POST',
        header: {
          'Content-Type': 'application/json'
        },
        data: {
          userId: this.userId,
          query: query
        }
      });

      if (response.statusCode === 200 && response.data.code === 200) {
        return response.data.data;
      } else {
        throw new Error('API调用失败');
      }
    },

    getCurrentTime() {
      const now = new Date();
      return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    },

    scrollToBottom() {
      this.$nextTick(() => {
        this.scrollTop = 999999;
      });
    }
  }
}
</script>

<style>
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

.header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: #fff;
  border-bottom: 1rpx solid #e9ecef;
}

.header-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.header-info {
  flex: 1;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  display: block;
}

.header-desc {
  font-size: 24rpx;
  color: #6c757d;
  margin-top: 8rpx;
  display: block;
}

.category-tabs {
  display: flex;
  background: #fff;
  border-bottom: 1rpx solid #e9ecef;
  padding: 0 20rpx;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 10rpx;
  transition: all 0.3s ease;
  border-bottom: 4rpx solid transparent;
}

.tab-item.active {
  border-bottom-color: #007bff;
}

.tab-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.tab-text {
  font-size: 24rpx;
  color: #6c757d;
}

.tab-item.active .tab-text {
  color: #007bff;
  font-weight: bold;
}

.chat-area {
  flex: 1;
  padding: 20rpx;
}

.message-item {
  display: flex;
  margin-bottom: 30rpx;
  animation: fadeInUp 0.3s ease;
}

.message-item.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #2c3e50;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 20rpx;
}

.message-item.user .message-avatar {
  background: #007bff;
}

.avatar-text {
  font-size: 24rpx;
  color: #fff;
  font-weight: bold;
}

.message-content {
  flex: 1;
  max-width: 70%;
}

.message-bubble {
  background: #fff;
  padding: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  position: relative;
}

.message-item.user .message-bubble {
  background: #007bff;
}

.message-text {
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
  display: block;
}

.message-item.user .message-text {
  color: #fff;
}

.message-time {
  font-size: 20rpx;
  color: #999;
  margin-top: 10rpx;
  display: block;
}

.message-item.user .message-time {
  color: rgba(255,255,255,0.8);
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #999;
  animation: typing 1.4s infinite;
}

.dot:nth-child(2) { animation-delay: 0.2s; }
.dot:nth-child(3) { animation-delay: 0.4s; }

@keyframes typing {
  0%, 60%, 100% { opacity: 0.3; }
  30% { opacity: 1; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.input-area {
  background: #fff;
  padding: 20rpx;
  border-top: 1rpx solid #e9ecef;
}

.input-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.input-field {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  border: 1rpx solid #e9ecef;
  border-radius: 40rpx;
  font-size: 28rpx;
  background: #f8f9fa;
}

.send-btn {
  width: 120rpx;
  height: 80rpx;
  background: #007bff;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-btn.disabled {
  background: #ccc;
  color: #999;
}

.quick-queries {
  position: absolute;
  bottom: 140rpx;
  left: 20rpx;
  right: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.quick-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20rpx;
  display: block;
}

.query-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.query-item {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.query-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.query-text {
  font-size: 26rpx;
  color: #495057;
}
</style>

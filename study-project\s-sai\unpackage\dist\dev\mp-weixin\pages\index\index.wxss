
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}
.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}
.header {
  text-align: center;
  margin-bottom: 80rpx;
  padding-top: 80rpx;
  position: relative;
  z-index: 1;
}
.logo {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  animation: float 3s ease-in-out infinite;
}
@keyframes float {
0%, 100% { transform: translateY(0px);
}
50% { transform: translateY(-10px);
}
}
.title {
  font-size: 56rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 24rpx;
  text-shadow: 0 4rpx 8rpx rgba(0,0,0,0.2);
  letter-spacing: 2rpx;
}
.subtitle {
  font-size: 32rpx;
  color: rgba(255,255,255,0.9);
  line-height: 1.5;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);
}
.cards-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 40rpx;
  position: relative;
  z-index: 1;
}
.card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 48rpx 40rpx;
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  position: relative;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.card:active {
  transform: translateY(6rpx) scale(0.98);
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.2);
}
.card-content {
  display: flex;
  align-items: center;
  flex: 1;
}
.card-icon {
  font-size: 80rpx;
  margin-right: 32rpx;
  transition: transform 0.3s ease;
}
.card:hover .card-icon {
  transform: scale(1.1);
}
.card-info {
  flex: 1;
}
.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}
.card-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}
.card-arrow {
  font-size: 40rpx;
  color: #999;
  font-weight: 300;
  transition: all 0.3s ease;
}
.card:hover .card-arrow {
  transform: translateX(8rpx);
  color: #667eea;
}
.card-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}
.card:hover .card-bg {
  opacity: 1;
}
.register-card {
  border-left: 6rpx solid #667eea;
}
.register-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6rpx;
  height: 100%;
  background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
  border-radius: 0 3rpx 3rpx 0;
}
.login-card {
  border-left: 6rpx solid #764ba2;
}
.login-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6rpx;
  height: 100%;
  background: linear-gradient(180deg, #764ba2 0%, #f093fb 100%);
  border-radius: 0 3rpx 3rpx 0;
}
.footer {
  text-align: center;
  margin-top: 60rpx;
  padding-bottom: 40rpx;
  position: relative;
  z-index: 1;
}
.tech-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}
.tech-text {
  font-size: 24rpx;
  color: rgba(255,255,255,0.8);
  font-weight: 500;
}
.tech-desc {
  font-size: 20rpx;
  color: rgba(255,255,255,0.6);
  letter-spacing: 1rpx;
}

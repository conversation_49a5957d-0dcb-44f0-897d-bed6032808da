function e(e,t={}){return new Promise((r,n)=>{const s=uni.getStorageSync("token");uni.request({url:"http://localhost:8080/api"+e,method:t.method||"GET",data:t.data,header:{"Content-Type":"application/json",Authorization:s?`Bearer ${s}`:"",...t.header},success:t=>{console.log("API请求成功:",e,t),r(t.data)},fail:r=>{console.error("API请求失败:",e,r);let s="网络请求失败";if(r.statusCode)switch(r.statusCode){case 401:s="登录已过期，请重新登录",uni.removeStorageSync("token"),uni.removeStorageSync("userInfo"),uni.reLaunch({url:"/pages/login/login"});break;case 403:s="没有权限访问";break;case 404:s="请求的资源不存在";break;case 500:s="服务器内部错误";break;default:s=r.errMsg||r.message||"网络请求失败"}else s=r.errMsg||r.message||"网络连接失败，请检查网络设置";const a=new Error(s);a.statusCode=r.statusCode,a.errMsg=r.errMsg,a.url=e,a.requestData=t.data,n(a)}})})}function t(t){return e("/auth/login",{method:"POST",data:t})}function r(t){return e("/auth/register",{method:"POST",data:t})}function n(){return e("/auth/users")}function s(t,r={}){let n=`/messages/history/${t}`;if(r.page||r.size){const e=new URLSearchParams;r.page&&e.append("page",r.page),r.size&&e.append("size",r.size),n+=`?${e.toString()}`}return e(n)}function a(t){return e("/messages/send",{method:"POST",data:t})}function o(t){return e(`/messages/session/${t}/read`,{method:"POST"})}function u(t){return e(`/friend/search?keyword=${encodeURIComponent(t)}`)}function i(t){return e("/friend/request",{method:"POST",data:t})}function d(){return e("/friend/requests/received")}function c(){return e("/friend/requests/sent")}function f(t,r){return e(`/friend/requests/${t}/${r}`,{method:"POST"})}function g(){return e("/friend/list")}function h(t){return e(`/friend/${t}`,{method:"DELETE"})}function m(t,r){return e(`/friend/${t}/remark`,{method:"PUT",data:{remark:r}})}function l(){return e("/friend/requests/count")}export{l as a,s as b,u as c,i as d,d as e,c as f,g,f as h,h as i,m as j,n as k,t as l,o as m,r,a as s};

package cn.zhentao.util;

import com.alibaba.dashscope.app.*;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 阿里云百炼AI工具类
 * 
 * <AUTHOR>
 * @since 2024-07-29
 */
@Slf4j
@Component
public class DashScopeAiUtil {

    @Value("${dashscope.api-key:sk-0e7519f33026415d9b58ff4679ead7c4}")
    private String apiKey;

    @Value("${dashscope.app-id:b8e521e1b0ee4a9cad5b00495a701f16}")
    private String appId;

    // 线程池用于异步处理流式输出
    private final ScheduledExecutorService executorService = Executors.newScheduledThreadPool(10);

    /**
     * AI对话响应结果
     */
    public static class AiResponse {
        private String text;
        private String sessionId;
        private boolean success;
        private String errorMessage;

        public AiResponse(String text, String sessionId, boolean success, String errorMessage) {
            this.text = text;
            this.sessionId = sessionId;
            this.success = success;
            this.errorMessage = errorMessage;
        }

        // Getters
        public String getText() { return text; }
        public String getSessionId() { return sessionId; }
        public boolean isSuccess() { return success; }
        public String getErrorMessage() { return errorMessage; }
    }

    /**
     * 单次对话调用
     * 
     * @param prompt 用户输入的问题
     * @return AI响应结果
     */
    public AiResponse singleCall(String prompt) {
        try {
            ApplicationParam param = ApplicationParam.builder()
                    .apiKey(apiKey)
                    .appId(appId)
                    .prompt(prompt)
                    .build();

            Application application = new Application();
            ApplicationResult result = application.call(param);

            log.info("AI单次调用成功，prompt: {}, response: {}", prompt, result.getOutput().getText());
            
            return new AiResponse(
                result.getOutput().getText(),
                result.getOutput().getSessionId(),
                true,
                null
            );

        } catch (ApiException | NoApiKeyException | InputRequiredException e) {
            log.error("AI调用失败: {}", e.getMessage(), e);
            return new AiResponse(
                null,
                null,
                false,
                e.getMessage()
            );
        }
    }

    /**
     * 带会话的对话调用
     *
     * @param prompt 用户输入的问题
     * @param sessionId 会话ID，如果为null则创建新会话
     * @return AI响应结果
     */
    public AiResponse callWithSession(String prompt, String sessionId) {
        try {
            ApplicationParam param;

            // 根据是否有会话ID来构建参数
            if (sessionId != null && !sessionId.trim().isEmpty()) {
                param = ApplicationParam.builder()
                        .apiKey(apiKey)
                        .appId(appId)
                        .prompt(prompt)
                        .sessionId(sessionId)
                        .build();
            } else {
                param = ApplicationParam.builder()
                        .apiKey(apiKey)
                        .appId(appId)
                        .prompt(prompt)
                        .build();
            }

            Application application = new Application();
            ApplicationResult result = application.call(param);

            log.info("AI会话调用成功，prompt: {}, sessionId: {}, response: {}",
                    prompt, result.getOutput().getSessionId(), result.getOutput().getText());

            return new AiResponse(
                result.getOutput().getText(),
                result.getOutput().getSessionId(),
                true,
                null
            );

        } catch (ApiException | NoApiKeyException | InputRequiredException e) {
            log.error("AI会话调用失败: {}", e.getMessage(), e);
            return new AiResponse(
                null,
                sessionId,
                false,
                e.getMessage()
            );
        }
    }

    /**
     * 多轮对话示例
     * 
     * @return AI响应结果
     */
    public AiResponse multiTurnConversation() {
        try {
            // 第一轮对话
            AiResponse firstResponse = singleCall("你是谁？");
            if (!firstResponse.isSuccess()) {
                return firstResponse;
            }

            // 第二轮对话，使用第一轮的会话ID
            AiResponse secondResponse = callWithSession("你有什么技能?", firstResponse.getSessionId());
            
            return secondResponse;

        } catch (Exception e) {
            log.error("多轮对话失败: {}", e.getMessage(), e);
            return new AiResponse(
                null,
                null,
                false,
                e.getMessage()
            );
        }
    }

    /**
     * 检查AI服务是否可用
     *
     * @return 是否可用
     */
    public boolean isServiceAvailable() {
        AiResponse response = singleCall("Hello");
        return response.isSuccess();
    }

    /**
     * 流式输出AI对话
     *
     * @param prompt 用户输入的问题
     * @return SseEmitter 用于流式输出
     */
    public SseEmitter streamCall(String prompt) {
        return streamCall(prompt, null);
    }

    /**
     * 带会话的流式输出AI对话
     *
     * @param prompt 用户输入的问题
     * @param sessionId 会话ID，如果为null则创建新会话
     * @return SseEmitter 用于流式输出
     */
    public SseEmitter streamCall(String prompt, String sessionId) {
        SseEmitter emitter = new SseEmitter(30000L); // 30秒超时

        executorService.execute(() -> {
            try {
                // 模拟流式输出，因为DashScope SDK可能不直接支持流式
                // 我们先获取完整响应，然后分块发送
                AiResponse response = callWithSession(prompt, sessionId);

                if (response.isSuccess()) {
                    String fullText = response.getText();
                    String[] words = fullText.split("");

                    // 发送开始事件
                    emitter.send(SseEmitter.event()
                            .name("start")
                            .data("{\"type\":\"start\",\"sessionId\":\"" + response.getSessionId() + "\"}"));

                    // 逐字发送响应
                    for (int i = 0; i < words.length; i++) {
                        String word = words[i];
                        emitter.send(SseEmitter.event()
                                .name("data")
                                .data("{\"type\":\"data\",\"content\":\"" + word.replace("\"", "\\\"") + "\"}"));

                        // 添加延迟以模拟打字效果
                        Thread.sleep(50);
                    }

                    // 发送完成事件
                    emitter.send(SseEmitter.event()
                            .name("end")
                            .data("{\"type\":\"end\",\"sessionId\":\"" + response.getSessionId() + "\"}"));

                } else {
                    // 发送错误事件
                    emitter.send(SseEmitter.event()
                            .name("error")
                            .data("{\"type\":\"error\",\"message\":\"" + response.getErrorMessage() + "\"}"));
                }

                emitter.complete();

            } catch (Exception e) {
                log.error("流式输出异常: {}", e.getMessage(), e);
                try {
                    emitter.send(SseEmitter.event()
                            .name("error")
                            .data("{\"type\":\"error\",\"message\":\"" + e.getMessage() + "\"}"));
                } catch (IOException ioException) {
                    log.error("发送错误事件失败: {}", ioException.getMessage());
                }
                emitter.completeWithError(e);
            }
        });

        return emitter;
    }

    /**
     * 更快的流式输出（按词分割）
     *
     * @param prompt 用户输入的问题
     * @param sessionId 会话ID
     * @return SseEmitter 用于流式输出
     */
    public SseEmitter fastStreamCall(String prompt, String sessionId) {
        SseEmitter emitter = new SseEmitter(30000L);

        executorService.execute(() -> {
            try {
                AiResponse response = callWithSession(prompt, sessionId);

                if (response.isSuccess()) {
                    String fullText = response.getText();
                    String[] chunks = fullText.split("(?<=。)|(?<=！)|(?<=？)|(?<=\\.)");

                    // 发送开始事件
                    emitter.send(SseEmitter.event()
                            .name("start")
                            .data("{\"type\":\"start\",\"sessionId\":\"" + response.getSessionId() + "\"}"));

                    // 按句子发送响应
                    for (String chunk : chunks) {
                        if (!chunk.trim().isEmpty()) {
                            emitter.send(SseEmitter.event()
                                    .name("data")
                                    .data("{\"type\":\"data\",\"content\":\"" + chunk.replace("\"", "\\\"") + "\"}"));

                            Thread.sleep(200); // 每句话间隔200ms
                        }
                    }

                    // 发送完成事件
                    emitter.send(SseEmitter.event()
                            .name("end")
                            .data("{\"type\":\"end\",\"sessionId\":\"" + response.getSessionId() + "\"}"));

                } else {
                    emitter.send(SseEmitter.event()
                            .name("error")
                            .data("{\"type\":\"error\",\"message\":\"" + response.getErrorMessage() + "\"}"));
                }

                emitter.complete();

            } catch (Exception e) {
                log.error("快速流式输出异常: {}", e.getMessage(), e);
                try {
                    emitter.send(SseEmitter.event()
                            .name("error")
                            .data("{\"type\":\"error\",\"message\":\"" + e.getMessage() + "\"}"));
                } catch (IOException ioException) {
                    log.error("发送错误事件失败: {}", ioException.getMessage());
                }
                emitter.completeWithError(e);
            }
        });

        return emitter;
    }

    /**
     * 优化的流式输出（适合小程序使用）
     *
     * @param prompt 用户输入的问题
     * @param sessionId 会话ID
     * @param streamType 流式类型（word/sentence/paragraph）
     * @return SseEmitter 用于流式输出
     */
    public SseEmitter optimizedStreamCall(String prompt, String sessionId, String streamType) {
        SseEmitter emitter = new SseEmitter(60000L); // 60秒超时，适合小程序

        executorService.execute(() -> {
            try {
                AiResponse response = callWithSession(prompt, sessionId);

                if (response.isSuccess()) {
                    String fullText = response.getText();

                    // 发送开始事件
                    emitter.send(SseEmitter.event()
                            .name("start")
                            .data("{\"type\":\"start\",\"sessionId\":\"" + response.getSessionId() + "\",\"streamType\":\"" + streamType + "\"}"));

                    // 根据流式类型分割内容
                    String[] chunks = splitTextByType(fullText, streamType);
                    int delay = getDelayByType(streamType);

                    // 逐块发送响应
                    for (int i = 0; i < chunks.length; i++) {
                        String chunk = chunks[i];
                        if (!chunk.trim().isEmpty()) {
                            emitter.send(SseEmitter.event()
                                    .name("data")
                                    .data("{\"type\":\"data\",\"content\":\"" + escapeJson(chunk) + "\",\"index\":" + i + ",\"total\":" + chunks.length + "}"));

                            Thread.sleep(delay);
                        }
                    }

                    // 发送完成事件
                    emitter.send(SseEmitter.event()
                            .name("end")
                            .data("{\"type\":\"end\",\"sessionId\":\"" + response.getSessionId() + "\",\"totalChunks\":" + chunks.length + "}"));

                } else {
                    // 发送错误事件
                    emitter.send(SseEmitter.event()
                            .name("error")
                            .data("{\"type\":\"error\",\"message\":\"" + escapeJson(response.getErrorMessage()) + "\"}"));
                }

                emitter.complete();

            } catch (Exception e) {
                log.error("优化流式输出异常: {}", e.getMessage(), e);
                try {
                    emitter.send(SseEmitter.event()
                            .name("error")
                            .data("{\"type\":\"error\",\"message\":\"" + escapeJson(e.getMessage()) + "\"}"));
                } catch (IOException ioException) {
                    log.error("发送错误事件失败: {}", ioException.getMessage());
                }
                emitter.completeWithError(e);
            }
        });

        return emitter;
    }

    /**
     * 根据类型分割文本
     */
    private String[] splitTextByType(String text, String streamType) {
        switch (streamType.toLowerCase()) {
            case "word":
                return text.split("");
            case "sentence":
                return text.split("(?<=[。！？.!?])");
            case "paragraph":
                return text.split("\\n\\s*\\n");
            default:
                // 默认按句子分割
                return text.split("(?<=[。！？.!?])");
        }
    }

    /**
     * 根据类型获取延迟时间
     */
    private int getDelayByType(String streamType) {
        switch (streamType.toLowerCase()) {
            case "word":
                return 30; // 30ms per word
            case "sentence":
                return 150; // 150ms per sentence
            case "paragraph":
                return 300; // 300ms per paragraph
            default:
                return 100; // 默认100ms
        }
    }

    /**
     * JSON字符串转义
     */
    private String escapeJson(String text) {
        if (text == null) return "";
        return text.replace("\\", "\\\\")
                  .replace("\"", "\\\"")
                  .replace("\n", "\\n")
                  .replace("\r", "\\r")
                  .replace("\t", "\\t");
    }

    /**
     * 获取AI服务健康状态
     */
    public Map<String, Object> getHealthStatus() {
        Map<String, Object> status = new HashMap<>();
        try {
            long startTime = System.currentTimeMillis();
            AiResponse response = singleCall("健康检查");
            long responseTime = System.currentTimeMillis() - startTime;

            status.put("status", response.isSuccess() ? "healthy" : "unhealthy");
            status.put("responseTime", responseTime);
            status.put("timestamp", System.currentTimeMillis());
            status.put("apiKey", apiKey != null ? "configured" : "missing");
            status.put("appId", appId != null ? "configured" : "missing");

            if (!response.isSuccess()) {
                status.put("error", response.getErrorMessage());
            }

        } catch (Exception e) {
            status.put("status", "error");
            status.put("error", e.getMessage());
            status.put("timestamp", System.currentTimeMillis());
        }

        return status;
    }
}

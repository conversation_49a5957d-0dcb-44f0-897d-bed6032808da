{"version": 3, "sources": ["../src/constants.js"], "names": ["AUTO", "HORIZONTAL_ALIGN_LEFT", "HORIZONTAL_ALIGN_CENTER", "HORIZONTAL_ALIGN_RIGHT", "VERTICAL_ALIGN_TOP", "VERTICAL_ALIGN_MIDDLE", "VERTICAL_ALIGN_BOTTOM", "BLEND_SOURCE_OVER", "BLEND_DESTINATION_OVER", "BLEND_MULTIPLY", "BLEND_SCREEN", "BLEND_OVERLAY", "BLEND_DARKEN", "BLEND_LIGHTEN", "BLEND_HARDLIGHT", "BLEND_DIFFERENCE", "BLEND_EXCLUSION", "EDGE_EXTEND", "EDGE_WRAP", "EDGE_CROP"], "mappings": ";;;;;;AAAA;AACO,IAAMA,IAAI,GAAG,CAAC,CAAd,C,CAEP;;;AACO,IAAMC,qBAAqB,GAAG,CAA9B;;AACA,IAAMC,uBAAuB,GAAG,CAAhC;;AACA,IAAMC,sBAAsB,GAAG,CAA/B;;AAEA,IAAMC,kBAAkB,GAAG,CAA3B;;AACA,IAAMC,qBAAqB,GAAG,EAA9B;;AACA,IAAMC,qBAAqB,GAAG,EAA9B,C,CAEP;;;AACO,IAAMC,iBAAiB,GAAG,SAA1B;;AACA,IAAMC,sBAAsB,GAAG,SAA/B;;AACA,IAAMC,cAAc,GAAG,UAAvB;;AACA,IAAMC,YAAY,GAAG,QAArB;;AACA,IAAMC,aAAa,GAAG,SAAtB;;AACA,IAAMC,YAAY,GAAG,QAArB;;AACA,IAAMC,aAAa,GAAG,SAAtB;;AACA,IAAMC,eAAe,GAAG,WAAxB;;AACA,IAAMC,gBAAgB,GAAG,YAAzB;;AACA,IAAMC,eAAe,GAAG,WAAxB,C,CAEP;;;AACO,IAAMC,WAAW,GAAG,CAApB;;AACA,IAAMC,SAAS,GAAG,CAAlB;;AACA,IAAMC,SAAS,GAAG,CAAlB", "sourcesContent": ["// used to auto resizing etc.\nexport const AUTO = -1;\n\n// Align modes for cover, contain, bit masks\nexport const HORIZONTAL_ALIGN_LEFT = 1;\nexport const HORIZONTAL_ALIGN_CENTER = 2;\nexport const HORIZONTAL_ALIGN_RIGHT = 4;\n\nexport const VERTICAL_ALIGN_TOP = 8;\nexport const VERTICAL_ALIGN_MIDDLE = 16;\nexport const VERTICAL_ALIGN_BOTTOM = 32;\n\n// blend modes\nexport const BLEND_SOURCE_OVER = 'srcOver';\nexport const BLEND_DESTINATION_OVER = 'dstOver';\nexport const BLEND_MULTIPLY = 'multiply';\nexport const BLEND_SCREEN = 'screen';\nexport const BLEND_OVERLAY = 'overlay';\nexport const BLEND_DARKEN = 'darken';\nexport const BLEND_LIGHTEN = 'lighten';\nexport const BLEND_HARDLIGHT = 'hardLight';\nexport const BLEND_DIFFERENCE = 'difference';\nexport const BLEND_EXCLUSION = 'exclusion';\n\n// Edge Handling\nexport const EDGE_EXTEND = 1;\nexport const EDGE_WRAP = 2;\nexport const EDGE_CROP = 3;\n"], "file": "constants.js"}
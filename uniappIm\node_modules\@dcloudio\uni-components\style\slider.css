uni-slider {
  margin: 10px 18px;
  padding: 0;
  display: block;
}

uni-slider[hidden] {
  display: none;
}

uni-slider .uni-slider-wrapper {
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  align-items: center;
  min-height: 16px;
}

uni-slider .uni-slider-tap-area {
  -webkit-flex: 1;
  flex: 1;
  padding: 8px 0;
}

uni-slider .uni-slider-handle-wrapper {
  position: relative;
  height: 2px;
  border-radius: 5px;
  background-color: #e9e9e9;
  cursor: pointer;
  transition: background-color 0.3s ease;
  -webkit-tap-highlight-color: transparent;
}

uni-slider .uni-slider-track {
  height: 100%;
  border-radius: 6px;
  background-color: #007aff;
  transition: background-color 0.3s ease;
}

uni-slider .uni-slider-handle,
uni-slider .uni-slider-thumb {
  position: absolute;
  left: 50%;
  top: 50%;
  cursor: pointer;
  border-radius: 50%;
  transition: border-color 0.3s ease;
}

uni-slider .uni-slider-handle {
  width: 28px;
  height: 28px;
  margin-top: -14px;
  margin-left: -14px;
  background-color: transparent;
  z-index: 3;
  cursor: grab;
}

uni-slider .uni-slider-thumb {
  z-index: 2;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
}

uni-slider .uni-slider-step {
  position: absolute;
  width: 100%;
  height: 2px;
  background: transparent;
  z-index: 1;
}

uni-slider .uni-slider-value {
  width: 3ch;
  color: #888;
  font-size: 14px;
  margin-left: 1em;
}

uni-slider .uni-slider-disabled .uni-slider-track {
  background-color: #ccc;
}

uni-slider .uni-slider-disabled .uni-slider-thumb {
  background-color: #fff;
  border-color: #ccc;
}

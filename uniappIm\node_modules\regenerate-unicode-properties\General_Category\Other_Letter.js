const set = require('regenerate')(0xAA, 0xBA, 0x1BB, 0x294, 0x6D5, 0x6FF, 0x710, 0x7B1, 0x93D, 0x950, 0x9B2, 0x9BD, 0x9CE, 0x9FC, 0xA5E, 0xABD, 0xAD0, 0xAF9, 0xB3D, 0xB71, 0xB83, 0xB9C, 0xBD0, 0xC3D, 0xC5D, 0xC80, 0xCBD, 0xD3D, 0xD4E, 0xDBD, 0xE84, 0xEA5, 0xEBD, 0xF00, 0x103F, 0x1061, 0x108E, 0x1258, 0x12C0, 0x17DC, 0x18AA, 0x1CFA, 0x3006, 0x303C, 0x309F, 0x30FF, 0xA66E, 0xA78F, 0xA7F7, 0xA8FB, 0xAA7A, 0xAAB1, 0xAAC0, 0xAAC2, 0xAAF2, 0xFB1D, 0xFB3E, 0x10808, 0x1083C, 0x10A00, 0x10D4F, 0x10F27, 0x11075, 0x11144, 0x11147, 0x11176, 0x111DA, 0x111DC, 0x11288, 0x1133D, 0x11350, 0x1138B, 0x1138E, 0x113B7, 0x113D1, 0x113D3, 0x114C7, 0x11644, 0x116B8, 0x11909, 0x1193F, 0x11941, 0x119E1, 0x119E3, 0x11A00, 0x11A3A, 0x11A50, 0x11A9D, 0x11C40, 0x11D46, 0x11D98, 0x11F02, 0x11FB0, 0x16F50, 0x1B132, 0x1B155, 0x1DF0A, 0x1E14E, 0x1E5F0, 0x1EE24, 0x1EE27, 0x1EE39, 0x1EE3B, 0x1EE42, 0x1EE47, 0x1EE49, 0x1EE4B, 0x1EE54, 0x1EE57, 0x1EE59, 0x1EE5B, 0x1EE5D, 0x1EE5F, 0x1EE64, 0x1EE7E);
set.addRange(0x1C0, 0x1C3).addRange(0x5D0, 0x5EA).addRange(0x5EF, 0x5F2).addRange(0x620, 0x63F).addRange(0x641, 0x64A).addRange(0x66E, 0x66F).addRange(0x671, 0x6D3).addRange(0x6EE, 0x6EF).addRange(0x6FA, 0x6FC).addRange(0x712, 0x72F).addRange(0x74D, 0x7A5).addRange(0x7CA, 0x7EA).addRange(0x800, 0x815).addRange(0x840, 0x858).addRange(0x860, 0x86A).addRange(0x870, 0x887).addRange(0x889, 0x88E).addRange(0x8A0, 0x8C8).addRange(0x904, 0x939).addRange(0x958, 0x961).addRange(0x972, 0x980).addRange(0x985, 0x98C).addRange(0x98F, 0x990).addRange(0x993, 0x9A8).addRange(0x9AA, 0x9B0).addRange(0x9B6, 0x9B9).addRange(0x9DC, 0x9DD).addRange(0x9DF, 0x9E1).addRange(0x9F0, 0x9F1).addRange(0xA05, 0xA0A).addRange(0xA0F, 0xA10).addRange(0xA13, 0xA28).addRange(0xA2A, 0xA30).addRange(0xA32, 0xA33).addRange(0xA35, 0xA36).addRange(0xA38, 0xA39).addRange(0xA59, 0xA5C).addRange(0xA72, 0xA74).addRange(0xA85, 0xA8D).addRange(0xA8F, 0xA91).addRange(0xA93, 0xAA8).addRange(0xAAA, 0xAB0).addRange(0xAB2, 0xAB3).addRange(0xAB5, 0xAB9).addRange(0xAE0, 0xAE1).addRange(0xB05, 0xB0C).addRange(0xB0F, 0xB10).addRange(0xB13, 0xB28).addRange(0xB2A, 0xB30).addRange(0xB32, 0xB33).addRange(0xB35, 0xB39);
set.addRange(0xB5C, 0xB5D).addRange(0xB5F, 0xB61).addRange(0xB85, 0xB8A).addRange(0xB8E, 0xB90).addRange(0xB92, 0xB95).addRange(0xB99, 0xB9A).addRange(0xB9E, 0xB9F).addRange(0xBA3, 0xBA4).addRange(0xBA8, 0xBAA).addRange(0xBAE, 0xBB9).addRange(0xC05, 0xC0C).addRange(0xC0E, 0xC10).addRange(0xC12, 0xC28).addRange(0xC2A, 0xC39).addRange(0xC58, 0xC5A).addRange(0xC60, 0xC61).addRange(0xC85, 0xC8C).addRange(0xC8E, 0xC90).addRange(0xC92, 0xCA8).addRange(0xCAA, 0xCB3).addRange(0xCB5, 0xCB9).addRange(0xCDD, 0xCDE).addRange(0xCE0, 0xCE1).addRange(0xCF1, 0xCF2).addRange(0xD04, 0xD0C).addRange(0xD0E, 0xD10).addRange(0xD12, 0xD3A).addRange(0xD54, 0xD56).addRange(0xD5F, 0xD61).addRange(0xD7A, 0xD7F).addRange(0xD85, 0xD96).addRange(0xD9A, 0xDB1).addRange(0xDB3, 0xDBB).addRange(0xDC0, 0xDC6).addRange(0xE01, 0xE30).addRange(0xE32, 0xE33).addRange(0xE40, 0xE45).addRange(0xE81, 0xE82).addRange(0xE86, 0xE8A).addRange(0xE8C, 0xEA3).addRange(0xEA7, 0xEB0).addRange(0xEB2, 0xEB3).addRange(0xEC0, 0xEC4).addRange(0xEDC, 0xEDF).addRange(0xF40, 0xF47).addRange(0xF49, 0xF6C).addRange(0xF88, 0xF8C).addRange(0x1000, 0x102A).addRange(0x1050, 0x1055).addRange(0x105A, 0x105D).addRange(0x1065, 0x1066);
set.addRange(0x106E, 0x1070).addRange(0x1075, 0x1081).addRange(0x1100, 0x1248).addRange(0x124A, 0x124D).addRange(0x1250, 0x1256).addRange(0x125A, 0x125D).addRange(0x1260, 0x1288).addRange(0x128A, 0x128D).addRange(0x1290, 0x12B0).addRange(0x12B2, 0x12B5).addRange(0x12B8, 0x12BE).addRange(0x12C2, 0x12C5).addRange(0x12C8, 0x12D6).addRange(0x12D8, 0x1310).addRange(0x1312, 0x1315).addRange(0x1318, 0x135A).addRange(0x1380, 0x138F).addRange(0x1401, 0x166C).addRange(0x166F, 0x167F).addRange(0x1681, 0x169A).addRange(0x16A0, 0x16EA).addRange(0x16F1, 0x16F8).addRange(0x1700, 0x1711).addRange(0x171F, 0x1731).addRange(0x1740, 0x1751).addRange(0x1760, 0x176C).addRange(0x176E, 0x1770).addRange(0x1780, 0x17B3).addRange(0x1820, 0x1842).addRange(0x1844, 0x1878).addRange(0x1880, 0x1884).addRange(0x1887, 0x18A8).addRange(0x18B0, 0x18F5).addRange(0x1900, 0x191E).addRange(0x1950, 0x196D).addRange(0x1970, 0x1974).addRange(0x1980, 0x19AB).addRange(0x19B0, 0x19C9).addRange(0x1A00, 0x1A16).addRange(0x1A20, 0x1A54).addRange(0x1B05, 0x1B33).addRange(0x1B45, 0x1B4C).addRange(0x1B83, 0x1BA0).addRange(0x1BAE, 0x1BAF).addRange(0x1BBA, 0x1BE5).addRange(0x1C00, 0x1C23).addRange(0x1C4D, 0x1C4F).addRange(0x1C5A, 0x1C77).addRange(0x1CE9, 0x1CEC).addRange(0x1CEE, 0x1CF3).addRange(0x1CF5, 0x1CF6);
set.addRange(0x2135, 0x2138).addRange(0x2D30, 0x2D67).addRange(0x2D80, 0x2D96).addRange(0x2DA0, 0x2DA6).addRange(0x2DA8, 0x2DAE).addRange(0x2DB0, 0x2DB6).addRange(0x2DB8, 0x2DBE).addRange(0x2DC0, 0x2DC6).addRange(0x2DC8, 0x2DCE).addRange(0x2DD0, 0x2DD6).addRange(0x2DD8, 0x2DDE).addRange(0x3041, 0x3096).addRange(0x30A1, 0x30FA).addRange(0x3105, 0x312F).addRange(0x3131, 0x318E).addRange(0x31A0, 0x31BF).addRange(0x31F0, 0x31FF).addRange(0x3400, 0x4DBF).addRange(0x4E00, 0xA014).addRange(0xA016, 0xA48C).addRange(0xA4D0, 0xA4F7).addRange(0xA500, 0xA60B).addRange(0xA610, 0xA61F).addRange(0xA62A, 0xA62B).addRange(0xA6A0, 0xA6E5).addRange(0xA7FB, 0xA801).addRange(0xA803, 0xA805).addRange(0xA807, 0xA80A).addRange(0xA80C, 0xA822).addRange(0xA840, 0xA873).addRange(0xA882, 0xA8B3).addRange(0xA8F2, 0xA8F7).addRange(0xA8FD, 0xA8FE).addRange(0xA90A, 0xA925).addRange(0xA930, 0xA946).addRange(0xA960, 0xA97C).addRange(0xA984, 0xA9B2).addRange(0xA9E0, 0xA9E4).addRange(0xA9E7, 0xA9EF).addRange(0xA9FA, 0xA9FE).addRange(0xAA00, 0xAA28).addRange(0xAA40, 0xAA42).addRange(0xAA44, 0xAA4B).addRange(0xAA60, 0xAA6F).addRange(0xAA71, 0xAA76).addRange(0xAA7E, 0xAAAF).addRange(0xAAB5, 0xAAB6).addRange(0xAAB9, 0xAABD).addRange(0xAADB, 0xAADC).addRange(0xAAE0, 0xAAEA).addRange(0xAB01, 0xAB06);
set.addRange(0xAB09, 0xAB0E).addRange(0xAB11, 0xAB16).addRange(0xAB20, 0xAB26).addRange(0xAB28, 0xAB2E).addRange(0xABC0, 0xABE2).addRange(0xAC00, 0xD7A3).addRange(0xD7B0, 0xD7C6).addRange(0xD7CB, 0xD7FB).addRange(0xF900, 0xFA6D).addRange(0xFA70, 0xFAD9).addRange(0xFB1F, 0xFB28).addRange(0xFB2A, 0xFB36).addRange(0xFB38, 0xFB3C).addRange(0xFB40, 0xFB41).addRange(0xFB43, 0xFB44).addRange(0xFB46, 0xFBB1).addRange(0xFBD3, 0xFD3D).addRange(0xFD50, 0xFD8F).addRange(0xFD92, 0xFDC7).addRange(0xFDF0, 0xFDFB).addRange(0xFE70, 0xFE74).addRange(0xFE76, 0xFEFC).addRange(0xFF66, 0xFF6F).addRange(0xFF71, 0xFF9D).addRange(0xFFA0, 0xFFBE).addRange(0xFFC2, 0xFFC7).addRange(0xFFCA, 0xFFCF).addRange(0xFFD2, 0xFFD7).addRange(0xFFDA, 0xFFDC).addRange(0x10000, 0x1000B).addRange(0x1000D, 0x10026).addRange(0x10028, 0x1003A).addRange(0x1003C, 0x1003D).addRange(0x1003F, 0x1004D).addRange(0x10050, 0x1005D).addRange(0x10080, 0x100FA).addRange(0x10280, 0x1029C).addRange(0x102A0, 0x102D0).addRange(0x10300, 0x1031F).addRange(0x1032D, 0x10340).addRange(0x10342, 0x10349).addRange(0x10350, 0x10375).addRange(0x10380, 0x1039D).addRange(0x103A0, 0x103C3).addRange(0x103C8, 0x103CF).addRange(0x10450, 0x1049D).addRange(0x10500, 0x10527).addRange(0x10530, 0x10563).addRange(0x105C0, 0x105F3).addRange(0x10600, 0x10736).addRange(0x10740, 0x10755);
set.addRange(0x10760, 0x10767).addRange(0x10800, 0x10805).addRange(0x1080A, 0x10835).addRange(0x10837, 0x10838).addRange(0x1083F, 0x10855).addRange(0x10860, 0x10876).addRange(0x10880, 0x1089E).addRange(0x108E0, 0x108F2).addRange(0x108F4, 0x108F5).addRange(0x10900, 0x10915).addRange(0x10920, 0x10939).addRange(0x10980, 0x109B7).addRange(0x109BE, 0x109BF).addRange(0x10A10, 0x10A13).addRange(0x10A15, 0x10A17).addRange(0x10A19, 0x10A35).addRange(0x10A60, 0x10A7C).addRange(0x10A80, 0x10A9C).addRange(0x10AC0, 0x10AC7).addRange(0x10AC9, 0x10AE4).addRange(0x10B00, 0x10B35).addRange(0x10B40, 0x10B55).addRange(0x10B60, 0x10B72).addRange(0x10B80, 0x10B91).addRange(0x10C00, 0x10C48).addRange(0x10D00, 0x10D23).addRange(0x10D4A, 0x10D4D).addRange(0x10E80, 0x10EA9).addRange(0x10EB0, 0x10EB1).addRange(0x10EC2, 0x10EC4).addRange(0x10F00, 0x10F1C).addRange(0x10F30, 0x10F45).addRange(0x10F70, 0x10F81).addRange(0x10FB0, 0x10FC4).addRange(0x10FE0, 0x10FF6).addRange(0x11003, 0x11037).addRange(0x11071, 0x11072).addRange(0x11083, 0x110AF).addRange(0x110D0, 0x110E8).addRange(0x11103, 0x11126).addRange(0x11150, 0x11172).addRange(0x11183, 0x111B2).addRange(0x111C1, 0x111C4).addRange(0x11200, 0x11211).addRange(0x11213, 0x1122B).addRange(0x1123F, 0x11240).addRange(0x11280, 0x11286).addRange(0x1128A, 0x1128D).addRange(0x1128F, 0x1129D).addRange(0x1129F, 0x112A8).addRange(0x112B0, 0x112DE);
set.addRange(0x11305, 0x1130C).addRange(0x1130F, 0x11310).addRange(0x11313, 0x11328).addRange(0x1132A, 0x11330).addRange(0x11332, 0x11333).addRange(0x11335, 0x11339).addRange(0x1135D, 0x11361).addRange(0x11380, 0x11389).addRange(0x11390, 0x113B5).addRange(0x11400, 0x11434).addRange(0x11447, 0x1144A).addRange(0x1145F, 0x11461).addRange(0x11480, 0x114AF).addRange(0x114C4, 0x114C5).addRange(0x11580, 0x115AE).addRange(0x115D8, 0x115DB).addRange(0x11600, 0x1162F).addRange(0x11680, 0x116AA).addRange(0x11700, 0x1171A).addRange(0x11740, 0x11746).addRange(0x11800, 0x1182B).addRange(0x118FF, 0x11906).addRange(0x1190C, 0x11913).addRange(0x11915, 0x11916).addRange(0x11918, 0x1192F).addRange(0x119A0, 0x119A7).addRange(0x119AA, 0x119D0).addRange(0x11A0B, 0x11A32).addRange(0x11A5C, 0x11A89).addRange(0x11AB0, 0x11AF8).addRange(0x11BC0, 0x11BE0).addRange(0x11C00, 0x11C08).addRange(0x11C0A, 0x11C2E).addRange(0x11C72, 0x11C8F).addRange(0x11D00, 0x11D06).addRange(0x11D08, 0x11D09).addRange(0x11D0B, 0x11D30).addRange(0x11D60, 0x11D65).addRange(0x11D67, 0x11D68).addRange(0x11D6A, 0x11D89).addRange(0x11EE0, 0x11EF2).addRange(0x11F04, 0x11F10).addRange(0x11F12, 0x11F33).addRange(0x12000, 0x12399).addRange(0x12480, 0x12543).addRange(0x12F90, 0x12FF0).addRange(0x13000, 0x1342F).addRange(0x13441, 0x13446).addRange(0x13460, 0x143FA).addRange(0x14400, 0x14646).addRange(0x16100, 0x1611D);
set.addRange(0x16800, 0x16A38).addRange(0x16A40, 0x16A5E).addRange(0x16A70, 0x16ABE).addRange(0x16AD0, 0x16AED).addRange(0x16B00, 0x16B2F).addRange(0x16B63, 0x16B77).addRange(0x16B7D, 0x16B8F).addRange(0x16D43, 0x16D6A).addRange(0x16F00, 0x16F4A).addRange(0x17000, 0x187F7).addRange(0x18800, 0x18CD5).addRange(0x18CFF, 0x18D08).addRange(0x1B000, 0x1B122).addRange(0x1B150, 0x1B152).addRange(0x1B164, 0x1B167).addRange(0x1B170, 0x1B2FB).addRange(0x1BC00, 0x1BC6A).addRange(0x1BC70, 0x1BC7C).addRange(0x1BC80, 0x1BC88).addRange(0x1BC90, 0x1BC99).addRange(0x1E100, 0x1E12C).addRange(0x1E290, 0x1E2AD).addRange(0x1E2C0, 0x1E2EB).addRange(0x1E4D0, 0x1E4EA).addRange(0x1E5D0, 0x1E5ED).addRange(0x1E7E0, 0x1E7E6).addRange(0x1E7E8, 0x1E7EB).addRange(0x1E7ED, 0x1E7EE).addRange(0x1E7F0, 0x1E7FE).addRange(0x1E800, 0x1E8C4).addRange(0x1EE00, 0x1EE03).addRange(0x1EE05, 0x1EE1F).addRange(0x1EE21, 0x1EE22).addRange(0x1EE29, 0x1EE32).addRange(0x1EE34, 0x1EE37).addRange(0x1EE4D, 0x1EE4F).addRange(0x1EE51, 0x1EE52).addRange(0x1EE61, 0x1EE62).addRange(0x1EE67, 0x1EE6A).addRange(0x1EE6C, 0x1EE72).addRange(0x1EE74, 0x1EE77).addRange(0x1EE79, 0x1EE7C).addRange(0x1EE80, 0x1EE89).addRange(0x1EE8B, 0x1EE9B).addRange(0x1EEA1, 0x1EEA3).addRange(0x1EEA5, 0x1EEA9).addRange(0x1EEAB, 0x1EEBB).addRange(0x20000, 0x2A6DF).addRange(0x2A700, 0x2B739).addRange(0x2B740, 0x2B81D).addRange(0x2B820, 0x2CEA1);
set.addRange(0x2CEB0, 0x2EBE0).addRange(0x2EBF0, 0x2EE5D).addRange(0x2F800, 0x2FA1D).addRange(0x30000, 0x3134A).addRange(0x31350, 0x323AF);
exports.characters = set;

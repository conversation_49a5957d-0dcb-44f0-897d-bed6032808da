function t(t,e){const n=Object.create(null),s=t.split(",");for(let i=0;i<s.length;i++)n[s[i]]=!0;return e?t=>!!n[t.toLowerCase()]:t=>!!n[t]}const e=()=>{},n=Object.assign,s=Object.prototype.hasOwnProperty,i=(t,e)=>s.call(t,e),r=Array.isArray,c=t=>"[object Map]"===a(t),o=t=>"symbol"==typeof t,u=t=>null!==t&&"object"==typeof t,h=Object.prototype.toString,a=t=>h.call(t),l=t=>"string"==typeof t&&"NaN"!==t&&"-"!==t[0]&&""+parseInt(t,10)===t,f=(t,e)=>!Object.is(t,e);let _;class d{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this.parent=_,!t&&_&&(this.index=(_.scopes||(_.scopes=[])).push(this)-1)}get active(){return this._active}run(t){if(this._active){const e=_;try{return _=this,t()}finally{_=e}}}on(){_=this}off(){_=this.parent}stop(t){if(this._active){let e,n;for(e=0,n=this.effects.length;e<n;e++)this.effects[e].stop();for(e=0,n=this.cleanups.length;e<n;e++)this.cleanups[e]();if(this.scopes)for(e=0,n=this.scopes.length;e<n;e++)this.scopes[e].stop(!0);if(!this.detached&&this.parent&&!t){const t=this.parent.scopes.pop();t&&t!==this&&(this.parent.scopes[this.index]=t,t.index=this.index)}this.parent=void 0,this._active=!1}}}function p(t){return new d(t)}function v(t,e=_){e&&e.active&&e.effects.push(t)}function g(){return _}function y(t){_&&_.cleanups.push(t)}const w=t=>{const e=new Set(t);return e.w=0,e.n=0,e},b=t=>(t.w&k)>0,R=t=>(t.n&k)>0,m=new WeakMap;let S=0,k=1;let O;const j=Symbol(""),x=Symbol("");class P{constructor(t,e=null,n){this.fn=t,this.scheduler=e,this.active=!0,this.deps=[],this.parent=void 0,v(this,n)}run(){if(!this.active)return this.fn();let t=O,e=W;for(;t;){if(t===this)return;t=t.parent}try{return this.parent=O,O=this,W=!0,k=1<<++S,S<=30?(({deps:t})=>{if(t.length)for(let e=0;e<t.length;e++)t[e].w|=k})(this):E(this),this.fn()}finally{S<=30&&(t=>{const{deps:e}=t;if(e.length){let n=0;for(let s=0;s<e.length;s++){const i=e[s];b(i)&&!R(i)?i.delete(t):e[n++]=i,i.w&=~k,i.n&=~k}e.length=n}})(this),k=1<<--S,O=this.parent,W=e,this.parent=void 0,this.deferStop&&this.stop()}}stop(){O===this?this.deferStop=!0:this.active&&(E(this),this.onStop&&this.onStop(),this.active=!1)}}function E(t){const{deps:e}=t;if(e.length){for(let n=0;n<e.length;n++)e[n].delete(t);e.length=0}}function M(t,e){t.effect&&(t=t.effect.fn);const s=new P(t);e&&(n(s,e),e.scope&&v(s,e.scope)),e&&e.lazy||s.run();const i=s.run.bind(s);return i.effect=s,i}function z(t){t.effect.stop()}let W=!0;const V=[];function N(){V.push(W),W=!1}function A(){V.push(W),W=!0}function I(){const t=V.pop();W=void 0===t||t}function K(t,e,n){if(W&&O){let e=m.get(t);e||m.set(t,e=new Map);let s=e.get(n);s||e.set(n,s=w()),C(s)}}function C(t,e){let n=!1;S<=30?R(t)||(t.n|=k,n=!b(t)):n=!t.has(O),n&&(t.add(O),O.deps.push(t))}function L(t,e,n,s,i,o){const u=m.get(t);if(!u)return;let h=[];if("clear"===e)h=[...u.values()];else if("length"===n&&r(t)){const t=Number(s);u.forEach(((e,n)=>{("length"===n||n>=t)&&h.push(e)}))}else switch(void 0!==n&&h.push(u.get(n)),e){case"add":r(t)?l(n)&&h.push(u.get("length")):(h.push(u.get(j)),c(t)&&h.push(u.get(x)));break;case"delete":r(t)||(h.push(u.get(j)),c(t)&&h.push(u.get(x)));break;case"set":c(t)&&h.push(u.get(j))}if(1===h.length)h[0]&&q(h[0]);else{const t=[];for(const e of h)e&&t.push(...e);q(w(t))}}function q(t,e){const n=r(t)?t:[...t];for(const s of n)s.computed&&B(s);for(const s of n)s.computed||B(s)}function B(t,e){(t!==O||t.allowRecurse)&&(t.scheduler?t.scheduler():t.run())}const D=t("__proto__,__v_isRef,__isVue"),F=new Set(Object.getOwnPropertyNames(Symbol).filter((t=>"arguments"!==t&&"caller"!==t)).map((t=>Symbol[t])).filter(o)),G=Y(),H=Y(!1,!0),J=Y(!0),Q=Y(!0,!0),T=U();function U(){const t={};return["includes","indexOf","lastIndexOf"].forEach((e=>{t[e]=function(...t){const n=Lt(this);for(let e=0,i=this.length;e<i;e++)K(n,0,e+"");const s=n[e](...t);return-1===s||!1===s?n[e](...t.map(Lt)):s}})),["push","pop","shift","unshift","splice"].forEach((e=>{t[e]=function(...t){N();const n=Lt(this)[e].apply(this,t);return I(),n}})),t}function X(t){const e=Lt(this);return K(e,0,t),e.hasOwnProperty(t)}function Y(t=!1,e=!1){return function(n,s,c){if("__v_isReactive"===s)return!t;if("__v_isReadonly"===s)return t;if("__v_isShallow"===s)return e;if("__v_raw"===s&&c===(t?e?Pt:xt:e?jt:Ot).get(n))return n;const h=r(n);if(!t){if(h&&i(T,s))return Reflect.get(T,s,c);if("hasOwnProperty"===s)return X}const a=Reflect.get(n,s,c);return(o(s)?F.has(s):D(s))?a:(t||K(n,0,s),e?a:Ht(a)?h&&l(s)?a:a.value:u(a)?t?Wt(a):Mt(a):a)}}function Z(t=!1){return function(e,n,s,c){let o=e[n];if(It(o)&&Ht(o)&&!Ht(s))return!1;if(!t&&(Kt(s)||It(s)||(o=Lt(o),s=Lt(s)),!r(e)&&Ht(o)&&!Ht(s)))return o.value=s,!0;const u=r(e)&&l(n)?Number(n)<e.length:i(e,n),h=Reflect.set(e,n,s,c);return e===Lt(c)&&(u?f(s,o)&&L(e,"set",n,s):L(e,"add",n,s)),h}}const $={get:G,set:Z(),deleteProperty:function(t,e){const n=i(t,e),s=Reflect.deleteProperty(t,e);return s&&n&&L(t,"delete",e,void 0),s},has:function(t,e){const n=Reflect.has(t,e);return o(e)&&F.has(e)||K(t,0,e),n},ownKeys:function(t){return K(t,0,r(t)?"length":j),Reflect.ownKeys(t)}},tt={get:J,set:(t,e)=>!0,deleteProperty:(t,e)=>!0},et=n({},$,{get:H,set:Z(!0)}),nt=n({},tt,{get:Q}),st=t=>t,it=t=>Reflect.getPrototypeOf(t);function rt(t,e,n=!1,s=!1){const i=Lt(t=t.__v_raw),r=Lt(e);n||(e!==r&&K(i,0,e),K(i,0,r));const{has:c}=it(i),o=s?st:n?Dt:Bt;return c.call(i,e)?o(t.get(e)):c.call(i,r)?o(t.get(r)):void(t!==i&&t.get(e))}function ct(t,e=!1){const n=this.__v_raw,s=Lt(n),i=Lt(t);return e||(t!==i&&K(s,0,t),K(s,0,i)),t===i?n.has(t):n.has(t)||n.has(i)}function ot(t,e=!1){return t=t.__v_raw,!e&&K(Lt(t),0,j),Reflect.get(t,"size",t)}function ut(t){t=Lt(t);const e=Lt(this);return it(e).has.call(e,t)||(e.add(t),L(e,"add",t,t)),this}function ht(t,e){e=Lt(e);const n=Lt(this),{has:s,get:i}=it(n);let r=s.call(n,t);r||(t=Lt(t),r=s.call(n,t));const c=i.call(n,t);return n.set(t,e),r?f(e,c)&&L(n,"set",t,e):L(n,"add",t,e),this}function at(t){const e=Lt(this),{has:n,get:s}=it(e);let i=n.call(e,t);i||(t=Lt(t),i=n.call(e,t)),s&&s.call(e,t);const r=e.delete(t);return i&&L(e,"delete",t,void 0),r}function lt(){const t=Lt(this),e=0!==t.size,n=t.clear();return e&&L(t,"clear",void 0,void 0),n}function ft(t,e){return function(n,s){const i=this,r=i.__v_raw,c=Lt(r),o=e?st:t?Dt:Bt;return!t&&K(c,0,j),r.forEach(((t,e)=>n.call(s,o(t),o(e),i)))}}function _t(t,e,n){return function(...s){const i=this.__v_raw,r=Lt(i),o=c(r),u="entries"===t||t===Symbol.iterator&&o,h="keys"===t&&o,a=i[t](...s),l=n?st:e?Dt:Bt;return!e&&K(r,0,h?x:j),{next(){const{value:t,done:e}=a.next();return e?{value:t,done:e}:{value:u?[l(t[0]),l(t[1])]:l(t),done:e}},[Symbol.iterator](){return this}}}}function dt(t){return function(...e){return"delete"!==t&&this}}function pt(){const t={get(t){return rt(this,t)},get size(){return ot(this)},has:ct,add:ut,set:ht,delete:at,clear:lt,forEach:ft(!1,!1)},e={get(t){return rt(this,t,!1,!0)},get size(){return ot(this)},has:ct,add:ut,set:ht,delete:at,clear:lt,forEach:ft(!1,!0)},n={get(t){return rt(this,t,!0)},get size(){return ot(this,!0)},has(t){return ct.call(this,t,!0)},add:dt("add"),set:dt("set"),delete:dt("delete"),clear:dt("clear"),forEach:ft(!0,!1)},s={get(t){return rt(this,t,!0,!0)},get size(){return ot(this,!0)},has(t){return ct.call(this,t,!0)},add:dt("add"),set:dt("set"),delete:dt("delete"),clear:dt("clear"),forEach:ft(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((i=>{t[i]=_t(i,!1,!1),n[i]=_t(i,!0,!1),e[i]=_t(i,!1,!0),s[i]=_t(i,!0,!0)})),[t,n,e,s]}const[vt,gt,yt,wt]=pt();function bt(t,e){const n=e?t?wt:yt:t?gt:vt;return(e,s,r)=>"__v_isReactive"===s?!t:"__v_isReadonly"===s?t:"__v_raw"===s?e:Reflect.get(i(n,s)&&s in e?n:e,s,r)}const Rt={get:bt(!1,!1)},mt={get:bt(!1,!0)},St={get:bt(!0,!1)},kt={get:bt(!0,!0)},Ot=new WeakMap,jt=new WeakMap,xt=new WeakMap,Pt=new WeakMap;function Et(t){return t.__v_skip||!Object.isExtensible(t)?0:function(t){switch(t){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((t=>a(t).slice(8,-1))(t))}function Mt(t){return It(t)?t:Nt(t,!1,$,Rt,Ot)}function zt(t){return Nt(t,!1,et,mt,jt)}function Wt(t){return Nt(t,!0,tt,St,xt)}function Vt(t){return Nt(t,!0,nt,kt,Pt)}function Nt(t,e,n,s,i){if(!u(t))return t;if(t.__v_raw&&(!e||!t.__v_isReactive))return t;const r=i.get(t);if(r)return r;const c=Et(t);if(0===c)return t;const o=new Proxy(t,2===c?s:n);return i.set(t,o),o}function At(t){return It(t)?At(t.__v_raw):!(!t||!t.__v_isReactive)}function It(t){return!(!t||!t.__v_isReadonly)}function Kt(t){return!(!t||!t.__v_isShallow)}function Ct(t){return At(t)||It(t)}function Lt(t){const e=t&&t.__v_raw;return e?Lt(e):t}function qt(t){return((t,e,n)=>{Object.defineProperty(t,e,{configurable:!0,enumerable:!1,value:n})})(t,"__v_skip",!0),t}const Bt=t=>u(t)?Mt(t):t,Dt=t=>u(t)?Wt(t):t;function Ft(t){W&&O&&C((t=Lt(t)).dep||(t.dep=w()))}function Gt(t,e){const n=(t=Lt(t)).dep;n&&q(n)}function Ht(t){return!(!t||!0!==t.__v_isRef)}function Jt(t){return Tt(t,!1)}function Qt(t){return Tt(t,!0)}function Tt(t,e){return Ht(t)?t:new Ut(t,e)}class Ut{constructor(t,e){this.__v_isShallow=e,this.dep=void 0,this.__v_isRef=!0,this._rawValue=e?t:Lt(t),this._value=e?t:Bt(t)}get value(){return Ft(this),this._value}set value(t){const e=this.__v_isShallow||Kt(t)||It(t);t=e?t:Lt(t),f(t,this._rawValue)&&(this._rawValue=t,this._value=e?t:Bt(t),Gt(this))}}function Xt(t){Gt(t)}function Yt(t){return Ht(t)?t.value:t}const Zt={get:(t,e,n)=>Yt(Reflect.get(t,e,n)),set:(t,e,n,s)=>{const i=t[e];return Ht(i)&&!Ht(n)?(i.value=n,!0):Reflect.set(t,e,n,s)}};function $t(t){return At(t)?t:new Proxy(t,Zt)}class te{constructor(t){this.dep=void 0,this.__v_isRef=!0;const{get:e,set:n}=t((()=>Ft(this)),(()=>Gt(this)));this._get=e,this._set=n}get value(){return this._get()}set value(t){this._set(t)}}function ee(t){return new te(t)}function ne(t){const e=r(t)?new Array(t.length):{};for(const n in t)e[n]=ie(t,n);return e}class se{constructor(t,e,n){this._object=t,this._key=e,this._defaultValue=n,this.__v_isRef=!0}get value(){const t=this._object[this._key];return void 0===t?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return function(t,e){var n;return null===(n=m.get(t))||void 0===n?void 0:n.get(e)}(Lt(this._object),this._key)}}function ie(t,e,n){const s=t[e];return Ht(s)?s:new se(t,e,n)}var re,ce;class oe{constructor(t,e,n,s){this._setter=e,this.dep=void 0,this.__v_isRef=!0,this[re]=!1,this._dirty=!0,this.effect=new P(t,(()=>{this._dirty||(this._dirty=!0,Gt(this))})),this.effect.computed=this,this.effect.active=this._cacheable=!s,this.__v_isReadonly=n}get value(){const t=Lt(this);return Ft(t),!t._dirty&&t._cacheable||(t._dirty=!1,t._value=t.effect.run()),t._value}set value(t){this._setter(t)}}function ue(t,n,s=!1){let i,r;const c="function"==typeof t;c?(i=t,r=e):(i=t.get,r=t.set);return new oe(i,r,c||!r,s)}re="__v_isReadonly";const he=Promise.resolve(),ae=[];let le=!1;const fe=()=>{for(let t=0;t<ae.length;t++)ae[t]();ae.length=0,le=!1};class _e{constructor(t){let e;this.dep=void 0,this._dirty=!0,this.__v_isRef=!0,this[ce]=!0;let n=!1,s=!1;this.effect=new P(t,(t=>{if(this.dep){if(t)e=this._value,n=!0;else if(!s){const t=n?e:this._value;s=!0,n=!1,ae.push((()=>{this.effect.active&&this._get()!==t&&Gt(this),s=!1})),le||(le=!0,he.then(fe))}for(const t of this.dep)t.computed instanceof _e&&t.scheduler(!0)}this._dirty=!0})),this.effect.computed=this}_get(){return this._dirty?(this._dirty=!1,this._value=this.effect.run()):this._value}get value(){return Ft(this),Lt(this)._get()}}function de(t){return new _e(t)}ce="__v_isReadonly";export{d as EffectScope,j as ITERATE_KEY,P as ReactiveEffect,ue as computed,ee as customRef,de as deferredComputed,M as effect,p as effectScope,A as enableTracking,g as getCurrentScope,Ct as isProxy,At as isReactive,It as isReadonly,Ht as isRef,Kt as isShallow,qt as markRaw,y as onScopeDispose,N as pauseTracking,$t as proxyRefs,Mt as reactive,Wt as readonly,Jt as ref,I as resetTracking,zt as shallowReactive,Vt as shallowReadonly,Qt as shallowRef,z as stop,Lt as toRaw,ie as toRef,ne as toRefs,K as track,L as trigger,Xt as triggerRef,Yt as unref};

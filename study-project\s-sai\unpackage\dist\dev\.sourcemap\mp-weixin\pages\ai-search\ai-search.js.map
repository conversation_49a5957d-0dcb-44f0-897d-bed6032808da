{"version": 3, "file": "ai-search.js", "sources": ["pages/ai-search/ai-search.vue", "../../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYWktc2VhcmNoL2FpLXNlYXJjaC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <!-- 顶部标题 -->\r\n    <view class=\"header\">\r\n      <view class=\"header-icon\">🔍</view>\r\n      <view class=\"header-info\">\r\n        <text class=\"header-title\">信息查询</text>\r\n        <text class=\"header-desc\">天气·电话·资讯查询</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 快捷查询分类 -->\r\n    <view class=\"category-tabs\">\r\n      <view \r\n        v-for=\"(category, index) in categories\" \r\n        :key=\"index\"\r\n        class=\"tab-item\"\r\n        :class=\"{ active: activeCategory === category.key }\"\r\n        @tap=\"switchCategory(category.key)\"\r\n      >\r\n        <text class=\"tab-icon\">{{ category.icon }}</text>\r\n        <text class=\"tab-text\">{{ category.name }}</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 聊天区域 -->\r\n    <scroll-view class=\"chat-area\" scroll-y=\"true\" :scroll-top=\"scrollTop\" scroll-with-animation=\"true\">\r\n      <view class=\"message-list\">\r\n        <view v-for=\"(message, index) in messages\" :key=\"index\" class=\"message-item\" :class=\"message.type\">\r\n          <view class=\"message-avatar\">\r\n            <text class=\"avatar-text\">{{ message.type === 'user' ? '我' : 'AI' }}</text>\r\n          </view>\r\n          <view class=\"message-content\">\r\n            <view class=\"message-bubble\">\r\n              <text class=\"message-text\">{{ message.content }}</text>\r\n              <text class=\"message-time\">{{ message.time }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- AI正在输入提示 -->\r\n        <view v-if=\"isAiTyping\" class=\"message-item ai typing\">\r\n          <view class=\"message-avatar\">\r\n            <text class=\"avatar-text\">AI</text>\r\n          </view>\r\n          <view class=\"message-content\">\r\n            <view class=\"message-bubble\">\r\n              <view class=\"typing-indicator\">\r\n                <view class=\"dot\"></view>\r\n                <view class=\"dot\"></view>\r\n                <view class=\"dot\"></view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n\r\n    <!-- 输入区域 -->\r\n    <view class=\"input-area\">\r\n      <view class=\"input-container\">\r\n        <input \r\n          class=\"input-field\" \r\n          v-model=\"inputText\" \r\n          :placeholder=\"currentPlaceholder\"\r\n          :disabled=\"isLoading\"\r\n          @confirm=\"sendMessage\"\r\n        />\r\n        <button \r\n          class=\"send-btn\" \r\n          :class=\"{ disabled: !inputText.trim() || isLoading }\" \r\n          @tap=\"sendMessage\"\r\n        >\r\n          {{ isLoading ? '查询中' : '查询' }}\r\n        </button>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 快捷查询 -->\r\n    <view v-if=\"messages.length === 0\" class=\"quick-queries\">\r\n      <text class=\"quick-title\">{{ currentCategory.icon }} {{ currentCategory.examples.title }}</text>\r\n      <view class=\"query-list\">\r\n        <view \r\n          v-for=\"(query, index) in currentCategory.examples.list\" \r\n          :key=\"index\" \r\n          class=\"query-item\" \r\n          @tap=\"askQuery(query)\"\r\n        >\r\n          <text class=\"query-text\">{{ query }}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      messages: [],\r\n      inputText: '',\r\n      isLoading: false,\r\n      isAiTyping: false,\r\n      scrollTop: 0,\r\n      userId: 123,\r\n      activeCategory: 'weather',\r\n      categories: [\r\n        {\r\n          key: 'weather',\r\n          name: '天气',\r\n          icon: '🌤️',\r\n          placeholder: '请输入城市名称查询天气...',\r\n          examples: {\r\n            title: '天气查询示例',\r\n            list: [\r\n              '北京今天的天气怎么样？',\r\n              '明天保定会下雨吗？',\r\n              '这周末的天气预报',\r\n              '上海的气温是多少？'\r\n            ]\r\n          }\r\n        },\r\n        {\r\n          key: 'phone',\r\n          name: '电话',\r\n          icon: '📞',\r\n          placeholder: '请输入要查询的联系人...',\r\n          examples: {\r\n            title: '电话查询示例',\r\n            list: [\r\n              '爸爸的电话号码',\r\n              '妈妈的手机号',\r\n              '老师的联系方式',\r\n              '紧急联系人电话'\r\n            ]\r\n          }\r\n        },\r\n        {\r\n          key: 'news',\r\n          name: '资讯',\r\n          icon: '📰',\r\n          placeholder: '请输入要查询的资讯内容...',\r\n          examples: {\r\n            title: '资讯查询示例',\r\n            list: [\r\n              '今天的新闻热点',\r\n              '最新科技资讯',\r\n              '教育政策新闻',\r\n              '健康生活资讯'\r\n            ]\r\n          }\r\n        },\r\n        {\r\n          key: 'other',\r\n          name: '其他',\r\n          icon: '🔎',\r\n          placeholder: '请输入要查询的信息...',\r\n          examples: {\r\n            title: '其他查询示例',\r\n            list: [\r\n              '附近的医院',\r\n              '公交路线查询',\r\n              '快递查询',\r\n              '节假日安排'\r\n            ]\r\n          }\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    currentCategory() {\r\n      return this.categories.find(cat => cat.key === this.activeCategory) || this.categories[0];\r\n    },\r\n    currentPlaceholder() {\r\n      return this.currentCategory.placeholder;\r\n    }\r\n  },\r\n  onLoad() {\r\n    this.loadUserInfo();\r\n    this.addWelcomeMessage();\r\n  },\r\n  methods: {\r\n    loadUserInfo() {\r\n      const userInfo = uni.getStorageSync('userInfo');\r\n      if (userInfo && userInfo.id) {\r\n        this.userId = userInfo.id;\r\n      }\r\n    },\r\n\r\n    addWelcomeMessage() {\r\n      this.messages.push({\r\n        type: 'ai',\r\n        content: '你好！我是信息查询助手，可以帮您查询天气、电话、资讯等各种信息。请选择查询类型或直接提问！',\r\n        time: this.getCurrentTime()\r\n      });\r\n      this.scrollToBottom();\r\n    },\r\n\r\n    switchCategory(categoryKey) {\r\n      this.activeCategory = categoryKey;\r\n      this.inputText = '';\r\n    },\r\n\r\n    askQuery(query) {\r\n      this.inputText = query;\r\n      this.sendMessage();\r\n    },\r\n\r\n    async sendMessage() {\r\n      if (!this.inputText.trim() || this.isLoading) return;\r\n\r\n      const userMessage = {\r\n        type: 'user',\r\n        content: this.inputText.trim(),\r\n        time: this.getCurrentTime()\r\n      };\r\n\r\n      this.messages.push(userMessage);\r\n      const query = this.inputText.trim();\r\n      this.inputText = '';\r\n      this.isLoading = true;\r\n      this.isAiTyping = true;\r\n      this.scrollToBottom();\r\n\r\n      try {\r\n        // 调用AI信息查询API\r\n        const response = await this.callSearchAPI(query);\r\n        \r\n        this.isAiTyping = false;\r\n        \r\n        if (response && response.success) {\r\n          this.messages.push({\r\n            type: 'ai',\r\n            content: response.response || '抱歉，没有找到相关信息。',\r\n            time: this.getCurrentTime()\r\n          });\r\n        } else {\r\n          this.messages.push({\r\n            type: 'ai',\r\n            content: '抱歉，查询服务暂时不可用，请稍后再试。',\r\n            time: this.getCurrentTime()\r\n          });\r\n        }\r\n      } catch (error) {\r\n        this.isAiTyping = false;\r\n        this.messages.push({\r\n          type: 'ai',\r\n          content: '网络连接失败，请检查网络后重试。',\r\n          time: this.getCurrentTime()\r\n        });\r\n        console.error('API调用失败:', error);\r\n      }\r\n\r\n      this.isLoading = false;\r\n      this.scrollToBottom();\r\n    },\r\n\r\n    async callSearchAPI(query) {\r\n      const apiUrl = 'http://localhost:8082/api/ai/miniprogram/info/query';\r\n      \r\n      const response = await uni.request({\r\n        url: apiUrl,\r\n        method: 'POST',\r\n        header: {\r\n          'Content-Type': 'application/json'\r\n        },\r\n        data: {\r\n          userId: this.userId,\r\n          query: query\r\n        }\r\n      });\r\n\r\n      if (response.statusCode === 200 && response.data.code === 200) {\r\n        return response.data.data;\r\n      } else {\r\n        throw new Error('API调用失败');\r\n      }\r\n    },\r\n\r\n    getCurrentTime() {\r\n      const now = new Date();\r\n      return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;\r\n    },\r\n\r\n    scrollToBottom() {\r\n      this.$nextTick(() => {\r\n        this.scrollTop = 999999;\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.container {\r\n  height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.header {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 30rpx;\r\n  background: #fff;\r\n  border-bottom: 1rpx solid #e9ecef;\r\n}\r\n\r\n.header-icon {\r\n  font-size: 48rpx;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.header-info {\r\n  flex: 1;\r\n}\r\n\r\n.header-title {\r\n  font-size: 36rpx;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  display: block;\r\n}\r\n\r\n.header-desc {\r\n  font-size: 24rpx;\r\n  color: #6c757d;\r\n  margin-top: 8rpx;\r\n  display: block;\r\n}\r\n\r\n.category-tabs {\r\n  display: flex;\r\n  background: #fff;\r\n  border-bottom: 1rpx solid #e9ecef;\r\n  padding: 0 20rpx;\r\n}\r\n\r\n.tab-item {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 20rpx 10rpx;\r\n  transition: all 0.3s ease;\r\n  border-bottom: 4rpx solid transparent;\r\n}\r\n\r\n.tab-item.active {\r\n  border-bottom-color: #007bff;\r\n}\r\n\r\n.tab-icon {\r\n  font-size: 32rpx;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.tab-text {\r\n  font-size: 24rpx;\r\n  color: #6c757d;\r\n}\r\n\r\n.tab-item.active .tab-text {\r\n  color: #007bff;\r\n  font-weight: bold;\r\n}\r\n\r\n.chat-area {\r\n  flex: 1;\r\n  padding: 20rpx;\r\n}\r\n\r\n.message-item {\r\n  display: flex;\r\n  margin-bottom: 30rpx;\r\n  animation: fadeInUp 0.3s ease;\r\n}\r\n\r\n.message-item.user {\r\n  flex-direction: row-reverse;\r\n}\r\n\r\n.message-avatar {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  border-radius: 50%;\r\n  background: #2c3e50;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 0 20rpx;\r\n}\r\n\r\n.message-item.user .message-avatar {\r\n  background: #007bff;\r\n}\r\n\r\n.avatar-text {\r\n  font-size: 24rpx;\r\n  color: #fff;\r\n  font-weight: bold;\r\n}\r\n\r\n.message-content {\r\n  flex: 1;\r\n  max-width: 70%;\r\n}\r\n\r\n.message-bubble {\r\n  background: #fff;\r\n  padding: 20rpx;\r\n  border-radius: 16rpx;\r\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);\r\n  position: relative;\r\n}\r\n\r\n.message-item.user .message-bubble {\r\n  background: #007bff;\r\n}\r\n\r\n.message-text {\r\n  font-size: 28rpx;\r\n  line-height: 1.5;\r\n  color: #333;\r\n  display: block;\r\n}\r\n\r\n.message-item.user .message-text {\r\n  color: #fff;\r\n}\r\n\r\n.message-time {\r\n  font-size: 20rpx;\r\n  color: #999;\r\n  margin-top: 10rpx;\r\n  display: block;\r\n}\r\n\r\n.message-item.user .message-time {\r\n  color: rgba(255,255,255,0.8);\r\n}\r\n\r\n.typing-indicator {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8rpx;\r\n}\r\n\r\n.dot {\r\n  width: 12rpx;\r\n  height: 12rpx;\r\n  border-radius: 50%;\r\n  background: #999;\r\n  animation: typing 1.4s infinite;\r\n}\r\n\r\n.dot:nth-child(2) { animation-delay: 0.2s; }\r\n.dot:nth-child(3) { animation-delay: 0.4s; }\r\n\r\n@keyframes typing {\r\n  0%, 60%, 100% { opacity: 0.3; }\r\n  30% { opacity: 1; }\r\n}\r\n\r\n@keyframes fadeInUp {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20rpx);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.input-area {\r\n  background: #fff;\r\n  padding: 20rpx;\r\n  border-top: 1rpx solid #e9ecef;\r\n}\r\n\r\n.input-container {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20rpx;\r\n}\r\n\r\n.input-field {\r\n  flex: 1;\r\n  height: 80rpx;\r\n  padding: 0 20rpx;\r\n  border: 1rpx solid #e9ecef;\r\n  border-radius: 40rpx;\r\n  font-size: 28rpx;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.send-btn {\r\n  width: 120rpx;\r\n  height: 80rpx;\r\n  background: #007bff;\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 40rpx;\r\n  font-size: 28rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.send-btn.disabled {\r\n  background: #ccc;\r\n  color: #999;\r\n}\r\n\r\n.quick-queries {\r\n  position: absolute;\r\n  bottom: 140rpx;\r\n  left: 20rpx;\r\n  right: 20rpx;\r\n  background: #fff;\r\n  border-radius: 16rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);\r\n}\r\n\r\n.quick-title {\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  margin-bottom: 20rpx;\r\n  display: block;\r\n}\r\n\r\n.query-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16rpx;\r\n}\r\n\r\n.query-item {\r\n  padding: 20rpx;\r\n  background: #f8f9fa;\r\n  border-radius: 12rpx;\r\n  border: 1rpx solid #e9ecef;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.query-item:active {\r\n  background: #e9ecef;\r\n  transform: scale(0.98);\r\n}\r\n\r\n.query-text {\r\n  font-size: 26rpx;\r\n  color: #495057;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/study/studyuProject/study-project/s-sai/pages/ai-search/ai-search.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAgGA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,UAAU,CAAE;AAAA,MACZ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,gBAAgB;AAAA,MAChB,YAAY;AAAA,QACV;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,YACR,OAAO;AAAA,YACP,MAAM;AAAA,cACJ;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,QACD;AAAA,QACD;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,YACR,OAAO;AAAA,YACP,MAAM;AAAA,cACJ;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,QACD;AAAA,QACD;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,YACR,OAAO;AAAA,YACP,MAAM;AAAA,cACJ;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,QACD;AAAA,QACD;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,YACR,OAAO;AAAA,YACP,MAAM;AAAA,cACJ;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,kBAAkB;AAChB,aAAO,KAAK,WAAW,KAAK,SAAO,IAAI,QAAQ,KAAK,cAAc,KAAK,KAAK,WAAW,CAAC;AAAA,IACzF;AAAA,IACD,qBAAqB;AACnB,aAAO,KAAK,gBAAgB;AAAA,IAC9B;AAAA,EACD;AAAA,EACD,SAAS;AACP,SAAK,aAAY;AACjB,SAAK,kBAAiB;AAAA,EACvB;AAAA,EACD,SAAS;AAAA,IACP,eAAe;AACb,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9C,UAAI,YAAY,SAAS,IAAI;AAC3B,aAAK,SAAS,SAAS;AAAA,MACzB;AAAA,IACD;AAAA,IAED,oBAAoB;AAClB,WAAK,SAAS,KAAK;AAAA,QACjB,MAAM;AAAA,QACN,SAAS;AAAA,QACT,MAAM,KAAK,eAAe;AAAA,MAC5B,CAAC;AACD,WAAK,eAAc;AAAA,IACpB;AAAA,IAED,eAAe,aAAa;AAC1B,WAAK,iBAAiB;AACtB,WAAK,YAAY;AAAA,IAClB;AAAA,IAED,SAAS,OAAO;AACd,WAAK,YAAY;AACjB,WAAK,YAAW;AAAA,IACjB;AAAA,IAED,MAAM,cAAc;AAClB,UAAI,CAAC,KAAK,UAAU,KAAI,KAAM,KAAK;AAAW;AAE9C,YAAM,cAAc;AAAA,QAClB,MAAM;AAAA,QACN,SAAS,KAAK,UAAU,KAAM;AAAA,QAC9B,MAAM,KAAK,eAAe;AAAA;AAG5B,WAAK,SAAS,KAAK,WAAW;AAC9B,YAAM,QAAQ,KAAK,UAAU,KAAI;AACjC,WAAK,YAAY;AACjB,WAAK,YAAY;AACjB,WAAK,aAAa;AAClB,WAAK,eAAc;AAEnB,UAAI;AAEF,cAAM,WAAW,MAAM,KAAK,cAAc,KAAK;AAE/C,aAAK,aAAa;AAElB,YAAI,YAAY,SAAS,SAAS;AAChC,eAAK,SAAS,KAAK;AAAA,YACjB,MAAM;AAAA,YACN,SAAS,SAAS,YAAY;AAAA,YAC9B,MAAM,KAAK,eAAe;AAAA,UAC5B,CAAC;AAAA,eACI;AACL,eAAK,SAAS,KAAK;AAAA,YACjB,MAAM;AAAA,YACN,SAAS;AAAA,YACT,MAAM,KAAK,eAAe;AAAA,UAC5B,CAAC;AAAA,QACH;AAAA,MACA,SAAO,OAAO;AACd,aAAK,aAAa;AAClB,aAAK,SAAS,KAAK;AAAA,UACjB,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM,KAAK,eAAe;AAAA,QAC5B,CAAC;AACDA,sBAAc,MAAA,MAAA,SAAA,wCAAA,YAAY,KAAK;AAAA,MACjC;AAEA,WAAK,YAAY;AACjB,WAAK,eAAc;AAAA,IACpB;AAAA,IAED,MAAM,cAAc,OAAO;AACzB,YAAM,SAAS;AAEf,YAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,QACjC,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,gBAAgB;AAAA,QACjB;AAAA,QACD,MAAM;AAAA,UACJ,QAAQ,KAAK;AAAA,UACb;AAAA,QACF;AAAA,MACF,CAAC;AAED,UAAI,SAAS,eAAe,OAAO,SAAS,KAAK,SAAS,KAAK;AAC7D,eAAO,SAAS,KAAK;AAAA,aAChB;AACL,cAAM,IAAI,MAAM,SAAS;AAAA,MAC3B;AAAA,IACD;AAAA,IAED,iBAAiB;AACf,YAAM,MAAM,oBAAI;AAChB,aAAO,GAAG,IAAI,SAAU,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG,CAAC,IAAI,IAAI,WAAY,EAAC,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC;AAAA,IACrG;AAAA,IAED,iBAAiB;AACf,WAAK,UAAU,MAAM;AACnB,aAAK,YAAY;AAAA,MACnB,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClSA,GAAG,WAAW,eAAe;"}
<template>
  <view class="container">
    <!-- 用户信息卡片 -->
    <view class="profile-card">
      <view class="avatar-section">
        <view class="avatar">
          <text class="avatar-text">孙</text>
        </view>
        <view class="user-info">
          <text class="user-name">孙颖</text>
          <text class="user-level">🌟 学习达人</text>
        </view>
      </view>
      <view class="stats-row">
        <view class="stat-item">
          <text class="stat-number">15</text>
          <text class="stat-label">学习天数</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">128</text>
          <text class="stat-label">完成题目</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">85%</text>
          <text class="stat-label">正确率</text>
        </view>
      </view>
    </view>
    
    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-item">
        <view class="menu-icon">📊</view>
        <text class="menu-text">学习报告</text>
        <text class="menu-arrow">></text>
      </view>
      <view class="menu-item">
        <view class="menu-icon">⚙️</view>
        <text class="menu-text">设置</text>
        <text class="menu-arrow">></text>
      </view>
      <view class="menu-item">
        <view class="menu-icon">❓</view>
        <text class="menu-text">帮助与反馈</text>
        <text class="menu-arrow">></text>
      </view>
      <view class="menu-item">
        <view class="menu-icon">ℹ️</view>
        <text class="menu-text">关于我们</text>
        <text class="menu-arrow">></text>
      </view>
    </view>
    
    <!-- 退出登录 -->
    <view class="logout-section">
      <button class="logout-btn" @click="logout">
        <text class="logout-icon">🚪</text>
        <text class="logout-text">退出登录</text>
      </button>
    </view>
    
    <!-- 底部导航栏 -->
    <view class="bottom-navigation">
      <view class="nav-item" @tap="switchTab('home')">
        <text class="nav-icon">🏠</text>
        <text class="nav-text">首页</text>
      </view>
      <view class="nav-item" @tap="switchTab('study')">
        <text class="nav-icon">📚</text>
        <text class="nav-text">学习路线</text>
      </view>

      <!-- 中间的拍照搜题按钮 -->
      <view class="camera-btn" @tap="takePhoto">
        <view class="camera-icon">📷</view>
      </view>

      <view class="nav-item" @tap="switchTab('tools')">
        <text class="nav-icon">💎</text>
        <text class="nav-text">精选单词</text>
      </view>
      <view class="nav-item active" @tap="switchTab('profile')">
        <text class="nav-icon">👤</text>
        <text class="nav-text">主页</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      
    }
  },
  methods: {
    switchTab(tab) {
      switch(tab) {
        case 'home':
          uni.reLaunch({
            url: '/pages/index/index'
          });
          break;
        case 'study':
          uni.reLaunch({
            url: '/pages/study-route/study-route'
          });
          break;
        case 'tools':
          uni.reLaunch({
            url: '/pages/tools/tools'
          });
          break;
        case 'profile':
          // 当前页面，不需要跳转
          break;
      }
    },

    // 拍照搜题功能
    takePhoto() {
      uni.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'],
        sourceType: ['camera'],
        success: (res) => {
          uni.showToast({
            title: '拍照成功！正在识别题目...',
            icon: 'success',
            duration: 2000
          });
        },
        fail: (err) => {
          uni.showToast({
            title: '拍照失败，请重试',
            icon: 'none'
          });
        }
      });
    },
    
    logout() {
      uni.showModal({
        title: '确认退出',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            uni.reLaunch({
              url: '/pages/index/index'
            });
          }
        }
      });
    }
  }
}
</script>

<style>
.container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 20rpx;
  padding-bottom: 140rpx;
}

.profile-card {
  background: white;
  border-radius: 15rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
}

.avatar-section {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background: linear-gradient(135deg, #007AFF, #5856D6);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
}

.avatar-text {
  font-size: 40rpx;
  color: white;
  font-weight: bold;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  display: block;
  margin-bottom: 8rpx;
}

.user-level {
  font-size: 24rpx;
  color: #6c757d;
}

.stats-row {
  display: flex;
  justify-content: space-around;
  padding-top: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 28rpx;
  font-weight: bold;
  color: #007AFF;
  display: block;
  margin-bottom: 5rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #6c757d;
}

.menu-section {
  background: white;
  border-radius: 15rpx;
  margin-bottom: 30rpx;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 35rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  font-size: 35rpx;
  margin-right: 25rpx;
}

.menu-text {
  flex: 1;
  font-size: 28rpx;
  color: #2c3e50;
}

.menu-arrow {
  font-size: 24rpx;
  color: #6c757d;
}

.logout-section {
  text-align: center;
  margin-bottom: 30rpx;
}

.logout-btn {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 25rpx 50rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
}

.logout-icon {
  font-size: 24rpx;
}

.logout-text {
  font-size: 26rpx;
  font-weight: 500;
}

/* 底部导航栏样式 */
.bottom-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: white;
  display: flex;
  align-items: center;
  justify-content: space-around;
  border-top: 1rpx solid #e5e5e5;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 10rpx 0;
  transition: all 0.3s ease;
}

.nav-item.active .nav-icon {
  color: #007AFF;
  transform: scale(1.1);
}

.nav-item.active .nav-text {
  color: #007AFF;
  font-weight: 600;
}

.nav-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
  color: #666;
  transition: all 0.3s ease;
}

.nav-text {
  font-size: 20rpx;
  color: #666;
  transition: all 0.3s ease;
}

/* 拍照搜题按钮 */
.camera-btn {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 15rpx rgba(255, 107, 107, 0.4);
  transform: translateY(-10rpx);
  transition: all 0.3s ease;
}

.camera-btn:active {
  transform: translateY(-8rpx) scale(0.95);
  box-shadow: 0 2rpx 10rpx rgba(255, 107, 107, 0.6);
}

.camera-icon {
  font-size: 45rpx;
  color: white;
}
</style>

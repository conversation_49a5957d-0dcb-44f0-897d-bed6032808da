"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      promptText: "",
      generatedText: "",
      isGenerating: false,
      userId: 123,
      activeType: "essay",
      writingTypes: [
        {
          key: "essay",
          name: "作文",
          icon: "📝",
          placeholder: "请输入作文题目或要求，例如：写一篇关于春天的作文...",
          examples: [
            "写一篇关于春天的作文",
            "我的理想职业",
            "难忘的一件事",
            "保护环境的重要性"
          ]
        },
        {
          key: "story",
          name: "故事",
          icon: "📚",
          placeholder: "请输入故事主题或情节，例如：写一个关于友谊的故事...",
          examples: [
            "写一个关于友谊的故事",
            "小动物的冒险故事",
            "未来世界的科幻故事",
            "童话故事：勇敢的小公主"
          ]
        },
        {
          key: "poem",
          name: "诗歌",
          icon: "🎭",
          placeholder: "请输入诗歌主题或风格，例如：写一首关于月亮的诗...",
          examples: [
            "写一首关于月亮的诗",
            "赞美老师的诗歌",
            "描写秋天的现代诗",
            "关于母爱的诗歌"
          ]
        },
        {
          key: "general",
          name: "其他",
          icon: "📄",
          placeholder: "请输入您想要创作的文本类型和要求...",
          examples: [
            "写一段产品介绍",
            "编写一份活动策划",
            "写一封感谢信",
            "创作一段演讲稿"
          ]
        }
      ]
    };
  },
  computed: {
    currentType() {
      return this.writingTypes.find((type) => type.key === this.activeType) || this.writingTypes[0];
    }
  },
  onLoad() {
    this.loadUserInfo();
  },
  methods: {
    loadUserInfo() {
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      if (userInfo && userInfo.id) {
        this.userId = userInfo.id;
      }
    },
    switchType(typeKey) {
      this.activeType = typeKey;
      this.promptText = "";
      this.generatedText = "";
    },
    useExample(example) {
      this.promptText = example;
    },
    async generateText() {
      if (!this.promptText.trim() || this.isGenerating)
        return;
      this.isGenerating = true;
      this.generatedText = "";
      try {
        const response = await this.callWritingAPI(this.promptText.trim(), this.activeType);
        if (response && response.success) {
          this.generatedText = response.response || "抱歉，生成失败，请重试。";
        } else {
          this.generatedText = "抱歉，创作服务暂时不可用，请稍后再试。";
        }
      } catch (error) {
        this.generatedText = "网络连接失败，请检查网络后重试。";
        common_vendor.index.__f__("error", "at pages/ai-writing/ai-writing.vue:201", "API调用失败:", error);
      }
      this.isGenerating = false;
    },
    async regenerateText() {
      if (!this.promptText.trim())
        return;
      this.generateText();
    },
    async callWritingAPI(prompt, type) {
      const apiUrl = "http://localhost:8082/api/ai/miniprogram/text/generate";
      const response = await common_vendor.index.request({
        url: apiUrl,
        method: "POST",
        header: {
          "Content-Type": "application/json"
        },
        data: {
          userId: this.userId,
          prompt,
          type
        }
      });
      if (response.statusCode === 200 && response.data.code === 200) {
        return response.data.data;
      } else {
        throw new Error("API调用失败");
      }
    },
    copyText() {
      if (!this.generatedText)
        return;
      common_vendor.index.setClipboardData({
        data: this.generatedText,
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "复制失败",
            icon: "none"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.writingTypes, (type, index, i0) => {
      return {
        a: common_vendor.t(type.icon),
        b: common_vendor.t(type.name),
        c: index,
        d: $data.activeType === type.key ? 1 : "",
        e: common_vendor.o(($event) => $options.switchType(type.key), index)
      };
    }),
    b: common_vendor.t($options.currentType.icon),
    c: common_vendor.t($options.currentType.name),
    d: $options.currentType.placeholder,
    e: $data.promptText,
    f: common_vendor.o(($event) => $data.promptText = $event.detail.value),
    g: common_vendor.t($data.promptText.length),
    h: common_vendor.t($data.isGenerating ? "⏳" : "✨"),
    i: common_vendor.t($data.isGenerating ? "创作中..." : "开始创作"),
    j: !$data.promptText.trim() || $data.isGenerating ? 1 : "",
    k: common_vendor.o((...args) => $options.generateText && $options.generateText(...args)),
    l: $data.generatedText || $data.isGenerating
  }, $data.generatedText || $data.isGenerating ? common_vendor.e({
    m: common_vendor.t($options.currentType.icon),
    n: $data.generatedText && !$data.isGenerating
  }, $data.generatedText && !$data.isGenerating ? {
    o: common_vendor.o((...args) => $options.copyText && $options.copyText(...args)),
    p: common_vendor.o((...args) => $options.regenerateText && $options.regenerateText(...args))
  } : {}, {
    q: $data.isGenerating
  }, $data.isGenerating ? {} : {}, {
    r: $data.generatedText
  }, $data.generatedText ? {
    s: common_vendor.t($data.generatedText)
  } : {}) : {}, {
    t: !$data.generatedText && !$data.isGenerating
  }, !$data.generatedText && !$data.isGenerating ? {
    v: common_vendor.t($options.currentType.name),
    w: common_vendor.f($options.currentType.examples, (example, index, i0) => {
      return {
        a: common_vendor.t(example),
        b: index,
        c: common_vendor.o(($event) => $options.useExample(example), index)
      };
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/ai-writing/ai-writing.js.map

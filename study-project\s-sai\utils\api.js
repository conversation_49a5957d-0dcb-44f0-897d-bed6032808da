/**
 * API配置文件
 * 统一管理所有API接口地址和请求方法
 */

// API基础配置
const API_CONFIG = {
  // 开发环境
  development: {
    baseUrl: 'http://localhost:8082',
    timeout: 30000
  },
  // 生产环境
  production: {
    baseUrl: 'https://your-production-domain.com',
    timeout: 30000
  }
};

// 当前环境配置
const currentEnv = 'development'; // 可以根据实际情况动态设置
const config = API_CONFIG[currentEnv];

// API接口地址
const API_URLS = {
  // AI功能接口
  AI: {
    // 知识问答
    KNOWLEDGE_QA: '/api/ai/miniprogram/knowledge/qa',
    KNOWLEDGE_QA_STREAM: '/api/ai/miniprogram/knowledge/qa/stream',
    
    // 信息查询
    INFO_QUERY: '/api/ai/miniprogram/info/query',
    INFO_QUERY_STREAM: '/api/ai/miniprogram/info/query/stream',
    
    // 文本生成
    TEXT_GENERATE: '/api/ai/miniprogram/text/generate',
    TEXT_GENERATE_STREAM: '/api/ai/miniprogram/text/generate/stream',
    
    // 语言翻译
    TRANSLATE: '/api/ai/miniprogram/translate',
    TRANSLATE_STREAM: '/api/ai/miniprogram/translate/stream',
    
    // 情感陪伴
    EMOTION_COMPANION: '/api/ai/miniprogram/emotion/companion',
    EMOTION_COMPANION_STREAM: '/api/ai/miniprogram/emotion/companion/stream',
    
    // 智能推荐
    RECOMMEND: '/api/ai/miniprogram/recommend',
    RECOMMEND_STREAM: '/api/ai/miniprogram/recommend/stream',
    
    // 会话管理
    SESSION_CLEAR: '/api/ai/miniprogram/session',
    SESSION_STATS: '/api/ai/miniprogram/session'
  },
  
  // 测试接口
  TEST: {
    HEALTH: '/api/ai/test/health',
    CHECK: '/api/ai/test/check'
  }
};

/**
 * 统一请求方法
 * @param {string} url 请求地址
 * @param {object} options 请求选项
 * @returns {Promise} 请求结果
 */
function request(url, options = {}) {
  const {
    method = 'GET',
    data = {},
    header = {},
    timeout = config.timeout
  } = options;

  // 完整的请求地址
  const fullUrl = url.startsWith('http') ? url : config.baseUrl + url;

  return new Promise((resolve, reject) => {
    uni.request({
      url: fullUrl,
      method,
      data,
      header: {
        'Content-Type': 'application/json',
        ...header
      },
      timeout,
      success: (response) => {
        const { statusCode, data: responseData } = response;
        
        if (statusCode === 200) {
          // 检查业务状态码
          if (responseData.code === 200) {
            resolve(responseData);
          } else {
            reject(new Error(responseData.message || '请求失败'));
          }
        } else {
          reject(new Error(`HTTP ${statusCode}: 请求失败`));
        }
      },
      fail: (error) => {
        console.error('请求失败:', error);
        reject(new Error('网络连接失败，请检查网络后重试'));
      }
    });
  });
}

/**
 * GET请求
 */
function get(url, params = {}, options = {}) {
  // 将参数拼接到URL中
  if (Object.keys(params).length > 0) {
    const queryString = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');
    url += (url.includes('?') ? '&' : '?') + queryString;
  }
  
  return request(url, { ...options, method: 'GET' });
}

/**
 * POST请求
 */
function post(url, data = {}, options = {}) {
  return request(url, { ...options, method: 'POST', data });
}

/**
 * PUT请求
 */
function put(url, data = {}, options = {}) {
  return request(url, { ...options, method: 'PUT', data });
}

/**
 * DELETE请求
 */
function del(url, options = {}) {
  return request(url, { ...options, method: 'DELETE' });
}

// AI功能API封装
const aiApi = {
  // 知识问答
  knowledgeQA: (userId, question) => {
    return post(API_URLS.AI.KNOWLEDGE_QA, { userId, question });
  },
  
  // 信息查询
  infoQuery: (userId, query) => {
    return post(API_URLS.AI.INFO_QUERY, { userId, query });
  },
  
  // 文本生成
  textGenerate: (userId, prompt, type = 'general') => {
    return post(API_URLS.AI.TEXT_GENERATE, { userId, prompt, type });
  },
  
  // 语言翻译
  translate: (userId, text, fromLang, toLang) => {
    return post(API_URLS.AI.TRANSLATE, { userId, text, fromLang, toLang });
  },
  
  // 情感陪伴
  emotionCompanion: (userId, message) => {
    return post(API_URLS.AI.EMOTION_COMPANION, { userId, message });
  },
  
  // 智能推荐
  recommend: (userId, preferences, category) => {
    return post(API_URLS.AI.RECOMMEND, { userId, preferences, category });
  },
  
  // 清除用户会话
  clearSession: (userId) => {
    return del(`${API_URLS.AI.SESSION_CLEAR}/${userId}`);
  },
  
  // 获取会话统计
  getSessionStats: (userId) => {
    return get(`${API_URLS.AI.SESSION_STATS}/${userId}/stats`);
  }
};

// 测试API封装
const testApi = {
  // 健康检查
  health: () => {
    return get(API_URLS.TEST.HEALTH);
  },
  
  // AI服务检查
  check: () => {
    return get(API_URLS.TEST.CHECK);
  }
};

/**
 * 获取用户ID
 * 从本地存储中获取用户信息
 */
function getUserId() {
  const userInfo = uni.getStorageSync('userInfo');
  return userInfo && userInfo.id ? userInfo.id : 123; // 默认用户ID
}

/**
 * 错误处理
 * 统一处理API错误
 */
function handleApiError(error, showToast = true) {
  console.error('API错误:', error);
  
  let message = '请求失败，请重试';
  
  if (error.message) {
    if (error.message.includes('网络')) {
      message = '网络连接失败，请检查网络';
    } else if (error.message.includes('超时')) {
      message = '请求超时，请重试';
    } else {
      message = error.message;
    }
  }
  
  if (showToast) {
    uni.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  }
  
  return message;
}

// 导出API
export {
  config,
  API_URLS,
  request,
  get,
  post,
  put,
  del,
  aiApi,
  testApi,
  getUserId,
  handleApiError
};

// 默认导出
export default {
  config,
  API_URLS,
  request,
  get,
  post,
  put,
  del,
  ai: aiApi,
  test: testApi,
  getUserId,
  handleApiError
};

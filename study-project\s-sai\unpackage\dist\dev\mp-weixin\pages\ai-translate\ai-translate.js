"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      inputText: "",
      translatedText: "",
      isTranslating: false,
      userId: 123,
      fromLangIndex: 1,
      // 默认中文
      toLangIndex: 0,
      // 默认英文
      languages: [
        { name: "英文", code: "en", flag: "🇺🇸" },
        { name: "中文", code: "zh", flag: "🇨🇳" },
        { name: "日文", code: "ja", flag: "🇯🇵" },
        { name: "韩文", code: "ko", flag: "🇰🇷" },
        { name: "法文", code: "fr", flag: "🇫🇷" },
        { name: "德文", code: "de", flag: "🇩🇪" },
        { name: "西班牙文", code: "es", flag: "🇪🇸" },
        { name: "俄文", code: "ru", flag: "🇷🇺" }
      ],
      quickTranslations: [
        "Hello, how are you?",
        "我喜欢学习编程",
        "Thank you very much",
        "今天天气很好",
        "Good morning",
        "很高兴认识你"
      ]
    };
  },
  onLoad() {
    this.loadUserInfo();
  },
  methods: {
    loadUserInfo() {
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      if (userInfo && userInfo.id) {
        this.userId = userInfo.id;
      }
    },
    onFromLangChange(e) {
      this.fromLangIndex = e.detail.value;
      this.clearResults();
    },
    onToLangChange(e) {
      this.toLangIndex = e.detail.value;
      this.clearResults();
    },
    swapLanguages() {
      const temp = this.fromLangIndex;
      this.fromLangIndex = this.toLangIndex;
      this.toLangIndex = temp;
      const tempText = this.inputText;
      this.inputText = this.translatedText;
      this.translatedText = tempText;
    },
    onInputChange() {
      this.translatedText = "";
    },
    clearInput() {
      this.inputText = "";
      this.translatedText = "";
    },
    clearResults() {
      this.translatedText = "";
    },
    async pasteText() {
      try {
        const clipboardData = await common_vendor.index.getClipboardData();
        if (clipboardData.data) {
          this.inputText = clipboardData.data;
          common_vendor.index.showToast({
            title: "粘贴成功",
            icon: "success"
          });
        }
      } catch (error) {
        common_vendor.index.showToast({
          title: "粘贴失败",
          icon: "none"
        });
      }
    },
    useQuickTranslation(text) {
      this.inputText = text;
      this.translateText();
    },
    async translateText() {
      if (!this.inputText.trim() || this.isTranslating)
        return;
      this.isTranslating = true;
      this.translatedText = "";
      try {
        const fromLang = this.languages[this.fromLangIndex].name;
        const toLang = this.languages[this.toLangIndex].name;
        const response = await this.callTranslateAPI(this.inputText.trim(), fromLang, toLang);
        if (response && response.success) {
          this.translatedText = response.response || "翻译失败，请重试。";
        } else {
          this.translatedText = "翻译服务暂时不可用，请稍后再试。";
        }
      } catch (error) {
        this.translatedText = "网络连接失败，请检查网络后重试。";
        common_vendor.index.__f__("error", "at pages/ai-translate/ai-translate.vue:247", "API调用失败:", error);
      }
      this.isTranslating = false;
    },
    async callTranslateAPI(text, fromLang, toLang) {
      const apiUrl = "http://localhost:8082/api/ai/miniprogram/translate";
      const response = await common_vendor.index.request({
        url: apiUrl,
        method: "POST",
        header: {
          "Content-Type": "application/json"
        },
        data: {
          userId: this.userId,
          text,
          fromLang,
          toLang
        }
      });
      if (response.statusCode === 200 && response.data.code === 200) {
        return response.data.data;
      } else {
        throw new Error("API调用失败");
      }
    },
    copyResult() {
      if (!this.translatedText)
        return;
      common_vendor.index.setClipboardData({
        data: this.translatedText,
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "复制失败",
            icon: "none"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.languages[$data.fromLangIndex].name),
    b: common_vendor.o((...args) => $options.onFromLangChange && $options.onFromLangChange(...args)),
    c: $data.fromLangIndex,
    d: $data.languages,
    e: common_vendor.o((...args) => $options.swapLanguages && $options.swapLanguages(...args)),
    f: common_vendor.t($data.languages[$data.toLangIndex].name),
    g: common_vendor.o((...args) => $options.onToLangChange && $options.onToLangChange(...args)),
    h: $data.toLangIndex,
    i: $data.languages,
    j: common_vendor.t($data.languages[$data.fromLangIndex].flag),
    k: common_vendor.t($data.inputText.length),
    l: `请输入要翻译的${$data.languages[$data.fromLangIndex].name}文本...`,
    m: common_vendor.o([($event) => $data.inputText = $event.detail.value, (...args) => $options.onInputChange && $options.onInputChange(...args)]),
    n: $data.inputText,
    o: $data.inputText
  }, $data.inputText ? {
    p: common_vendor.o((...args) => $options.clearInput && $options.clearInput(...args))
  } : {}, {
    q: common_vendor.o((...args) => $options.pasteText && $options.pasteText(...args)),
    r: common_vendor.t($data.isTranslating ? "⏳" : "🔄"),
    s: common_vendor.t($data.isTranslating ? "翻译中..." : "开始翻译"),
    t: !$data.inputText.trim() || $data.isTranslating ? 1 : "",
    v: common_vendor.o((...args) => $options.translateText && $options.translateText(...args)),
    w: common_vendor.t($data.languages[$data.toLangIndex].flag),
    x: $data.translatedText && !$data.isTranslating
  }, $data.translatedText && !$data.isTranslating ? {
    y: common_vendor.o((...args) => $options.copyResult && $options.copyResult(...args))
  } : {}, {
    z: $data.isTranslating
  }, $data.isTranslating ? {} : $data.translatedText ? {
    B: common_vendor.t($data.translatedText)
  } : {}, {
    A: $data.translatedText,
    C: !$data.inputText && !$data.translatedText
  }, !$data.inputText && !$data.translatedText ? {
    D: common_vendor.f($data.quickTranslations, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: index,
        c: common_vendor.o(($event) => $options.useQuickTranslation(item), index)
      };
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/ai-translate/ai-translate.js.map

package cn.zhentao.service.impl;

import cn.zhentao.mapper.*;
import cn.zhentao.pojo.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.zhentao.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【user(用户信息表)】的数据库操作Service实现
* @createDate 2025-07-28 16:25:23
*/
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService, UserDetailsService {
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private UserRoleMapper userRoleMapper;
    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private RolePermissionMapper rolePermiaaionMapper;
    @Autowired
    private PermissionMapper permissionMapper;


    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        QueryWrapper<User> wrapper=new QueryWrapper<>();
        wrapper.eq("username",username);
        User user = userMapper.selectOne(wrapper);
        if(user!=null){
            Long id = user.getId();
            QueryWrapper<UserRole> wrapper1=new QueryWrapper<>();
            wrapper1.eq("id",id);
            List<UserRole> userRoles = userRoleMapper.selectList(wrapper1);


            List<Integer> rids=new ArrayList<>();
            for(UserRole userRole:userRoles){
                rids.add(userRole.getRoleId());
            }
            List<Role> roles = roleMapper.selectBatchIds(rids);
            List<String> list=new ArrayList<>();
            for(Role role:roles){
                list.add(role.getRoleName());
            }
            user.setRnames(list);


            QueryWrapper<RolePermission> wrapper2 = new QueryWrapper<>();
            wrapper2.in("roleId", rids);
            wrapper2.select("permissionId");
            List<Object> pids = rolePermiaaionMapper.selectObjs(wrapper2);

            List<Integer> idList = pids.stream()
                    .map(obj -> (Integer) obj)
                    .collect(Collectors.toList());
            List<Permission> permissions = permissionMapper.selectBatchIds(idList);

            List<String> list1 = new ArrayList<>();
            for (Permission permission : permissions) {
                list1.add(permission.getPermissionName());
            }
            user.setPnames(list1);
            return user;
        }

        return null;
    }
}





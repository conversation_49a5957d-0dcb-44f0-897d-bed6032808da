{"name": "@dcloudio/uni-mp-vite", "version": "3.0.0-3081220230817001", "description": "uni-mp-vite", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/dcloudio/uni-app.git", "directory": "packages/uni-mp-vite"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "license": "Apache-2.0", "dependencies": {"@dcloudio/uni-cli-shared": "3.0.0-3081220230817001", "@dcloudio/uni-i18n": "3.0.0-3081220230817001", "@dcloudio/uni-mp-compiler": "3.0.0-3081220230817001", "@dcloudio/uni-mp-vue": "3.0.0-3081220230817001", "@dcloudio/uni-shared": "3.0.0-3081220230817001", "@vue/compiler-sfc": "3.2.47", "@vue/shared": "3.2.47", "debug": "^4.3.3"}, "gitHead": "33e807d66e1fe47e2ee08ad9c59247e37b8884da", "devDependencies": {"@types/debug": "^4.1.7"}}
"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/register/register.js";
  "./pages/login/login.js";
  "./pages/home/<USER>";
  "./pages/ai-home/ai-home.js";
  "./pages/ai-knowledge/ai-knowledge.js";
  "./pages/ai-search/ai-search.js";
  "./pages/ai-writing/ai-writing.js";
  "./pages/ai-translate/ai-translate.js";
  "./pages/ai-emotion/ai-emotion.js";
  "./pages/ai-recommend/ai-recommend.js";
}
const _sfc_main = {
  onLaunch: function() {
    common_vendor.index.__f__("log", "at App.vue:4", "App Launch");
  },
  onShow: function() {
    common_vendor.index.__f__("log", "at App.vue:7", "App Show");
  },
  onHide: function() {
    common_vendor.index.__f__("log", "at App.vue:10", "App Hide");
  }
};
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map

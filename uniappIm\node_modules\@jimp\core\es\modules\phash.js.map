{"version": 3, "sources": ["../../src/modules/phash.js"], "names": ["ImagePHash", "size", "smallerSize", "initCoefficients", "prototype", "distance", "s1", "s2", "counter", "k", "length", "getHash", "img", "clone", "resize", "grayscale", "vals", "x", "bitmap", "width", "y", "height", "intToRGBA", "getPixelColor", "b", "dctVals", "applyDCT", "total", "avg", "hash", "i", "rgba", "r", "Math", "floor", "pow", "g", "a", "c", "sqrt", "f", "N", "F", "u", "v", "sum", "j", "cos", "PI", "module", "exports"], "mappings": ";;AAAA;;;;;;;;;;;;;;;;;;;;;AAsBA;;AAEA;;;;;AAMA,SAASA,UAAT,CAAoBC,IAApB,EAA0BC,WAA1B,EAAuC;AACrC,OAAKD,IAAL,GAAY,KAAKA,IAAL,IAAaA,IAAzB;AACA,OAAKC,WAAL,GAAmB,KAAKA,WAAL,IAAoBA,WAAvC;AACAC,EAAAA,gBAAgB,CAAC,KAAKF,IAAN,CAAhB;AACD;;AAEDD,UAAU,CAACI,SAAX,CAAqBH,IAArB,GAA4B,EAA5B;AACAD,UAAU,CAACI,SAAX,CAAqBF,WAArB,GAAmC,CAAnC;;AAEAF,UAAU,CAACI,SAAX,CAAqBC,QAArB,GAAgC,UAASC,EAAT,EAAaC,EAAb,EAAiB;AAC/C,MAAIC,OAAO,GAAG,CAAd;;AAEA,OAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,EAAE,CAACI,MAAvB,EAA+BD,CAAC,EAAhC,EAAoC;AAClC,QAAIH,EAAE,CAACG,CAAD,CAAF,KAAUF,EAAE,CAACE,CAAD,CAAhB,EAAqB;AACnBD,MAAAA,OAAO;AACR;AACF;;AAED,SAAOA,OAAO,GAAGF,EAAE,CAACI,MAApB;AACD,CAVD,C,CAYA;;;AACAV,UAAU,CAACI,SAAX,CAAqBO,OAArB,GAA+B,UAASC,GAAT,EAAc;AAC3C;;;;;;AAMAA,EAAAA,GAAG,GAAGA,GAAG,CAACC,KAAJ,GAAYC,MAAZ,CAAmB,KAAKb,IAAxB,EAA8B,KAAKA,IAAnC,CAAN;AAEA;;;;;AAIAW,EAAAA,GAAG,CAACG,SAAJ;AAEA,MAAMC,IAAI,GAAG,EAAb;;AAEA,OAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGL,GAAG,CAACM,MAAJ,CAAWC,KAA/B,EAAsCF,CAAC,EAAvC,EAA2C;AACzCD,IAAAA,IAAI,CAACC,CAAD,CAAJ,GAAU,EAAV;;AACA,SAAK,IAAIG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGR,GAAG,CAACM,MAAJ,CAAWG,MAA/B,EAAuCD,CAAC,EAAxC,EAA4C;AAC1CJ,MAAAA,IAAI,CAACC,CAAD,CAAJ,CAAQG,CAAR,IAAaE,SAAS,CAACV,GAAG,CAACW,aAAJ,CAAkBN,CAAlB,EAAqBG,CAArB,CAAD,CAAT,CAAmCI,CAAhD;AACD;AACF;AAED;;;;;;;AAKA,MAAMC,OAAO,GAAGC,QAAQ,CAACV,IAAD,EAAO,KAAKf,IAAZ,CAAxB;AAEA;;;;;;AAKA;;;;;;;AAMA,MAAI0B,KAAK,GAAG,CAAZ;;AAEA,OAAK,IAAIV,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG,KAAKf,WAAzB,EAAsCe,EAAC,EAAvC,EAA2C;AACzC,SAAK,IAAIG,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG,KAAKlB,WAAzB,EAAsCkB,EAAC,EAAvC,EAA2C;AACzCO,MAAAA,KAAK,IAAIF,OAAO,CAACR,EAAD,CAAP,CAAWG,EAAX,CAAT;AACD;AACF;;AAED,MAAMQ,GAAG,GAAGD,KAAK,IAAI,KAAKzB,WAAL,GAAmB,KAAKA,WAA5B,CAAjB;AAEA;;;;;;;;;;;AAUA,MAAI2B,IAAI,GAAG,EAAX;;AAEA,OAAK,IAAIZ,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAG,KAAKf,WAAzB,EAAsCe,GAAC,EAAvC,EAA2C;AACzC,SAAK,IAAIG,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAG,KAAKlB,WAAzB,EAAsCkB,GAAC,EAAvC,EAA2C;AACzCS,MAAAA,IAAI,IAAIJ,OAAO,CAACR,GAAD,CAAP,CAAWG,GAAX,IAAgBQ,GAAhB,GAAsB,GAAtB,GAA4B,GAApC;AACD;AACF;;AAED,SAAOC,IAAP;AACD,CAvED,C,CAyEA;;;AAEA,SAASP,SAAT,CAAmBQ,CAAnB,EAAsB;AACpB,MAAMC,IAAI,GAAG,EAAb;AAEAA,EAAAA,IAAI,CAACC,CAAL,GAASC,IAAI,CAACC,KAAL,CAAWJ,CAAC,GAAGG,IAAI,CAACE,GAAL,CAAS,GAAT,EAAc,CAAd,CAAf,CAAT;AACAJ,EAAAA,IAAI,CAACK,CAAL,GAASH,IAAI,CAACC,KAAL,CAAW,CAACJ,CAAC,GAAGC,IAAI,CAACC,CAAL,GAASC,IAAI,CAACE,GAAL,CAAS,GAAT,EAAc,CAAd,CAAd,IAAkCF,IAAI,CAACE,GAAL,CAAS,GAAT,EAAc,CAAd,CAA7C,CAAT;AACAJ,EAAAA,IAAI,CAACP,CAAL,GAASS,IAAI,CAACC,KAAL,CACP,CAACJ,CAAC,GAAGC,IAAI,CAACC,CAAL,GAASC,IAAI,CAACE,GAAL,CAAS,GAAT,EAAc,CAAd,CAAb,GAAgCJ,IAAI,CAACK,CAAL,GAASH,IAAI,CAACE,GAAL,CAAS,GAAT,EAAc,CAAd,CAA1C,IACEF,IAAI,CAACE,GAAL,CAAS,GAAT,EAAc,CAAd,CAFK,CAAT;AAIAJ,EAAAA,IAAI,CAACM,CAAL,GAASJ,IAAI,CAACC,KAAL,CACP,CAACJ,CAAC,GACAC,IAAI,CAACC,CAAL,GAASC,IAAI,CAACE,GAAL,CAAS,GAAT,EAAc,CAAd,CADV,GAECJ,IAAI,CAACK,CAAL,GAASH,IAAI,CAACE,GAAL,CAAS,GAAT,EAAc,CAAd,CAFV,GAGCJ,IAAI,CAACP,CAAL,GAASS,IAAI,CAACE,GAAL,CAAS,GAAT,EAAc,CAAd,CAHX,IAIEF,IAAI,CAACE,GAAL,CAAS,GAAT,EAAc,CAAd,CALK,CAAT;AAQA,SAAOJ,IAAP;AACD;;AAED,IAAMO,CAAC,GAAG,EAAV;;AACA,SAASnC,gBAAT,CAA0BF,IAA1B,EAAgC;AAC9B,OAAK,IAAI6B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG7B,IAApB,EAA0B6B,CAAC,EAA3B,EAA+B;AAC7BQ,IAAAA,CAAC,CAACR,CAAD,CAAD,GAAO,CAAP;AACD;;AAEDQ,EAAAA,CAAC,CAAC,CAAD,CAAD,GAAO,IAAIL,IAAI,CAACM,IAAL,CAAU,GAAV,CAAX;AACD;;AAED,SAASb,QAAT,CAAkBc,CAAlB,EAAqBvC,IAArB,EAA2B;AACzB,MAAMwC,CAAC,GAAGxC,IAAV;AACA,MAAMyC,CAAC,GAAG,EAAV;;AAEA,OAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,CAApB,EAAuBE,CAAC,EAAxB,EAA4B;AAC1BD,IAAAA,CAAC,CAACC,CAAD,CAAD,GAAO,EAAP;;AACA,SAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,CAApB,EAAuBG,CAAC,EAAxB,EAA4B;AAC1B,UAAIC,GAAG,GAAG,CAAV;;AACA,WAAK,IAAIf,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGW,CAApB,EAAuBX,CAAC,EAAxB,EAA4B;AAC1B,aAAK,IAAIgB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGL,CAApB,EAAuBK,CAAC,EAAxB,EAA4B;AAC1BD,UAAAA,GAAG,IACDZ,IAAI,CAACc,GAAL,CAAU,CAAC,IAAIjB,CAAJ,GAAQ,CAAT,KAAe,MAAMW,CAArB,CAAD,GAA4BE,CAA5B,GAAgCV,IAAI,CAACe,EAA9C,IACAf,IAAI,CAACc,GAAL,CAAU,CAAC,IAAID,CAAJ,GAAQ,CAAT,KAAe,MAAML,CAArB,CAAD,GAA4BG,CAA5B,GAAgCX,IAAI,CAACe,EAA9C,CADA,GAEAR,CAAC,CAACV,CAAD,CAAD,CAAKgB,CAAL,CAHF;AAID;AACF;;AAEDD,MAAAA,GAAG,IAAKP,CAAC,CAACK,CAAD,CAAD,GAAOL,CAAC,CAACM,CAAD,CAAT,GAAgB,CAAvB;AACAF,MAAAA,CAAC,CAACC,CAAD,CAAD,CAAKC,CAAL,IAAUC,GAAV;AACD;AACF;;AAED,SAAOH,CAAP;AACD;;AAEDO,MAAM,CAACC,OAAP,GAAiBlD,UAAjB", "sourcesContent": ["/*\nCopyright (c) 2011 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\n// https://code.google.com/p/ironchef-team21/source/browse/ironchef_team21/src/ImagePHash.java\n\n/*\n * pHash-like image hash.\n * Author: Elliot Shepherd (<EMAIL>\n * Based On: http://www.hackerfactor.com/blog/index.php?/archives/432-Looks-Like-It.html\n */\n\nfunction ImagePHash(size, smallerSize) {\n  this.size = this.size || size;\n  this.smallerSize = this.smallerSize || smallerSize;\n  initCoefficients(this.size);\n}\n\nImagePHash.prototype.size = 32;\nImagePHash.prototype.smallerSize = 8;\n\nImagePHash.prototype.distance = function(s1, s2) {\n  let counter = 0;\n\n  for (let k = 0; k < s1.length; k++) {\n    if (s1[k] !== s2[k]) {\n      counter++;\n    }\n  }\n\n  return counter / s1.length;\n};\n\n// Returns a 'binary string' (like. 001010111011100010) which is easy to do a hamming distance on.\nImagePHash.prototype.getHash = function(img) {\n  /* 1. Reduce size.\n   * Like Average Hash, pHash starts with a small image.\n   * However, the image is larger than 8x8; 32x32 is a good size.\n   * This is really done to simplify the DCT computation and not\n   * because it is needed to reduce the high frequencies.\n   */\n  img = img.clone().resize(this.size, this.size);\n\n  /* 2. Reduce color.\n   * The image is reduced to a grayscale just to further simplify\n   * the number of computations.\n   */\n  img.grayscale();\n\n  const vals = [];\n\n  for (let x = 0; x < img.bitmap.width; x++) {\n    vals[x] = [];\n    for (let y = 0; y < img.bitmap.height; y++) {\n      vals[x][y] = intToRGBA(img.getPixelColor(x, y)).b;\n    }\n  }\n\n  /* 3. Compute the DCT.\n   * The DCT separates the image into a collection of frequencies\n   * and scalars. While JPEG uses an 8x8 DCT, this algorithm uses\n   * a 32x32 DCT.\n   */\n  const dctVals = applyDCT(vals, this.size);\n\n  /* 4. Reduce the DCT.\n   * This is the magic step. While the DCT is 32x32, just keep the\n   * top-left 8x8. Those represent the lowest frequencies in the\n   * picture.\n   */\n  /* 5. Compute the average value.\n   * Like the Average Hash, compute the mean DCT value (using only\n   * the 8x8 DCT low-frequency values and excluding the first term\n   * since the DC coefficient can be significantly different from\n   * the other values and will throw off the average).\n   */\n  let total = 0;\n\n  for (let x = 0; x < this.smallerSize; x++) {\n    for (let y = 0; y < this.smallerSize; y++) {\n      total += dctVals[x][y];\n    }\n  }\n\n  const avg = total / (this.smallerSize * this.smallerSize);\n\n  /* 6. Further reduce the DCT.\n   * This is the magic step. Set the 64 hash bits to 0 or 1\n   * depending on whether each of the 64 DCT values is above or\n   * below the average value. The result doesn't tell us the\n   * actual low frequencies; it just tells us the very-rough\n   * relative scale of the frequencies to the mean. The result\n   * will not vary as long as the overall structure of the image\n   * remains the same; this can survive gamma and color histogram\n   * adjustments without a problem.\n   */\n  let hash = '';\n\n  for (let x = 0; x < this.smallerSize; x++) {\n    for (let y = 0; y < this.smallerSize; y++) {\n      hash += dctVals[x][y] > avg ? '1' : '0';\n    }\n  }\n\n  return hash;\n};\n\n// DCT function stolen from http://stackoverflow.com/questions/4240490/problems-with-dct-and-idct-algorithm-in-java\n\nfunction intToRGBA(i) {\n  const rgba = {};\n\n  rgba.r = Math.floor(i / Math.pow(256, 3));\n  rgba.g = Math.floor((i - rgba.r * Math.pow(256, 3)) / Math.pow(256, 2));\n  rgba.b = Math.floor(\n    (i - rgba.r * Math.pow(256, 3) - rgba.g * Math.pow(256, 2)) /\n      Math.pow(256, 1)\n  );\n  rgba.a = Math.floor(\n    (i -\n      rgba.r * Math.pow(256, 3) -\n      rgba.g * Math.pow(256, 2) -\n      rgba.b * Math.pow(256, 1)) /\n      Math.pow(256, 0)\n  );\n\n  return rgba;\n}\n\nconst c = [];\nfunction initCoefficients(size) {\n  for (let i = 1; i < size; i++) {\n    c[i] = 1;\n  }\n\n  c[0] = 1 / Math.sqrt(2.0);\n}\n\nfunction applyDCT(f, size) {\n  const N = size;\n  const F = [];\n\n  for (let u = 0; u < N; u++) {\n    F[u] = [];\n    for (let v = 0; v < N; v++) {\n      let sum = 0;\n      for (let i = 0; i < N; i++) {\n        for (let j = 0; j < N; j++) {\n          sum +=\n            Math.cos(((2 * i + 1) / (2.0 * N)) * u * Math.PI) *\n            Math.cos(((2 * j + 1) / (2.0 * N)) * v * Math.PI) *\n            f[i][j];\n        }\n      }\n\n      sum *= (c[u] * c[v]) / 4;\n      F[u][v] = sum;\n    }\n  }\n\n  return F;\n}\n\nmodule.exports = ImagePHash;\n"], "file": "phash.js"}
package cn.zhentao.config;

import cn.zhentao.service.AiConversationService;
import cn.zhentao.util.DashScopeAiUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * AI服务配置类
 * 明确注册AI相关的Bean
 */
@Configuration
public class AiServiceConfig {

    /**
     * 注册AI对话服务Bean
     */
    @Bean
    public AiConversationService aiConversationService(DashScopeAiUtil dashScopeAiUtil) {
        AiConversationService service = new AiConversationService();
        // 手动设置依赖，因为我们不使用@Autowired
        try {
            java.lang.reflect.Field field = AiConversationService.class.getDeclaredField("dashScopeAiUtil");
            field.setAccessible(true);
            field.set(service, dashScopeAiUtil);
        } catch (Exception e) {
            throw new RuntimeException("Failed to inject Dash<PERSON><PERSON><PERSON><PERSON>Util", e);
        }
        return service;
    }
}

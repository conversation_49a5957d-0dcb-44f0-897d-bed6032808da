<template>
  <view class="container">
    <!-- 自定义状态栏 -->
    <view class="custom-status-bar">
      <view class="status-content">
        <text class="app-title">AI智能家教</text>
      </view>
    </view>

    <!-- 顶部用户信息区域 -->
    <view class="header-section">
      <view class="user-info">
        <view class="avatar">
          <text class="avatar-text">{{ userInfo.name ? userInfo.name.charAt(0) : 'A' }}</text>
        </view>
        <view class="user-details">
          <text class="welcome-text">欢迎回来</text>
          <text class="user-name">{{ userInfo.name || 'AI学习者' }}</text>
        </view>
      </view>
      <view class="header-actions">
        <view class="ai-status">
          <view class="status-indicator" :class="{ active: aiActive }"></view>
          <text class="status-text">{{ aiActive ? 'AI在线' : 'AI离线' }}</text>
        </view>
        <view class="settings-btn" @tap="openSettings">
          <text class="settings-icon">⚙️</text>
        </view>
      </view>
    </view>



    <!-- 学习监控区域 -->
    <view class="monitor-section">
      <view class="section-title">
        <text class="title-icon">👁️</text>
        <text class="title-text">实时监控学习</text>
      </view>
      <view class="monitor-card">
        <view class="monitor-status">
          <view class="status-item">
            <text class="status-label">监控状态</text>
            <text class="status-value" :class="{ active: monitorActive }">
              {{ monitorActive ? '监控中' : '未开启' }}
            </text>
          </view>
          <view class="monitor-toggle" @tap="toggleMonitor">
            <view class="toggle-btn" :class="{ active: monitorActive }">
              <view class="toggle-slider"></view>
            </view>
          </view>
        </view>
        <view class="monitor-features">
          <view class="feature-tag">专注度分析</view>
          <view class="feature-tag">姿势监测</view>
          <view class="feature-tag">环境检测</view>
          <view class="feature-tag">学习报告</view>
        </view>
      </view>
    </view>

    <!-- 退出按钮 -->
    <view class="logout-section">
      <button class="logout-btn" @click="logout">
        <text class="logout-icon">🚪</text>
        <text class="logout-text">退出登录</text>
      </button>
    </view>

    <!-- 底部导航提示 -->
    <view class="bottom-tip">
      <text class="tip-text">💡 长按功能图标可查看详细说明</text>
    </view>

    <!-- 底部导航栏 -->
    <view class="bottom-navigation">
      <view class="nav-item" @tap="goToHome">
        <text class="nav-icon">🏠</text>
        <text class="nav-text">首页</text>
      </view>

      <!-- 美化的拍照搜题按钮 -->
      <view class="camera-section">
        <view class="camera-btn" @tap="showCameraInfo">
          <view class="camera-icon">📷</view>
        </view>
        <text class="camera-text">拍照搜题</text>
      </view>

      <view class="nav-item active" @tap="switchTab('profile')">
        <text class="nav-icon">👤</text>
        <text class="nav-text">我的</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      userInfo: {},
      aiActive: true,
      monitorActive: false,
      pageLoaded: false
    }
  },
  onLoad() {
    this.loadUserInfo();
    this.initAIStatus();
    // 页面加载动画
    setTimeout(() => {
      this.pageLoaded = true;
    }, 300);
  },
  onPullDownRefresh() {
    this.refreshData();
  },
  methods: {
    loadUserInfo() {
      const userInfo = uni.getStorageSync('userInfo');
      if (userInfo) {
        this.userInfo = userInfo;
      }
    },
    
    initAIStatus() {
      // 模拟AI状态检测
      this.aiActive = true;
    },
    
    refreshData() {
      setTimeout(() => {
        this.loadUserInfo();
        this.initAIStatus();
        uni.stopPullDownRefresh();
        uni.showToast({
          title: '刷新成功',
          icon: 'success'
        });
      }, 1000);
    },
    
    startVoiceChat() {
      uni.showToast({
        title: '正在启动AI对话...',
        icon: 'loading'
      });
      // TODO: 实现语音对话功能
    },
    
    openFunction(type) {
      const functionRoutes = {
        'knowledge': '/pages/ai-knowledge/ai-knowledge',
        'search': '/pages/ai-search/ai-search',
        'writing': '/pages/ai-writing/ai-writing',
        'translate': '/pages/ai-translate/ai-translate',
        'emotion': '/pages/ai-emotion/ai-emotion',
        'recommend': '/pages/ai-recommend/ai-recommend',
        'reminder': '/pages/ai-reminder/ai-reminder',
        'game': '/pages/ai-game/ai-game',
        'health': '/pages/ai-health/ai-health',
        'photo-search': '/pages/ai-photo/ai-photo',
        'video-call': '/pages/ai-video/ai-video',
        'exam': '/pages/ai-exam/ai-exam',
        'textbook': '/pages/ai-textbook/ai-textbook'
      };

      const route = functionRoutes[type];
      if (route) {
        // 检查页面是否存在
        const availablePages = ['knowledge', 'search', 'writing', 'translate', 'emotion', 'recommend'];
        if (availablePages.includes(type)) {
          uni.navigateTo({
            url: route,
            fail: (err) => {
              console.error('页面跳转失败:', err);
              uni.showToast({
                title: '页面跳转失败',
                icon: 'none'
              });
            }
          });
        } else {
          uni.showToast({
            title: '功能开发中，敬请期待',
            icon: 'none'
          });
        }
      } else {
        uni.showToast({
          title: '功能开发中，敬请期待',
          icon: 'none'
        });
      }
    },
    
    toggleMonitor() {
      this.monitorActive = !this.monitorActive;
      uni.showToast({
        title: this.monitorActive ? '监控已开启' : '监控已关闭',
        icon: 'success'
      });
      // TODO: 实现监控开关功能
    },

    showFunctionDetail(type) {
      const functionDetails = {
        'knowledge': {
          title: '知识问答',
          icon: '📚',
          description: '让AI能够回答各种问题，如历史、科学、技术、文化等方面的问题，就像一个知识渊博的助手。',
          examples: [
            '地球的直径是多少？',
            '中国古代四大发明是什么？',
            '光的传播速度是多少？'
          ]
        },
        'search': {
          title: '信息查询',
          icon: '🔍',
          description: '帮助学生查询各类信息，如天气、父母电话等实用信息。',
          examples: [
            '查询明天保定的天气',
            '查询爸爸的电话号码',
            '今天的新闻热点'
          ]
        },
        'writing': {
          title: '文本生成',
          icon: '✍️',
          description: '包括写作文章、故事、诗歌、摘要等各类文本创作。',
          examples: [
            '写一首关于春天的诗',
            '写一个关于友谊的小故事',
            '帮我写一篇关于环保的作文'
          ]
        },
        'translate': {
          title: '语言翻译',
          icon: '🌐',
          description: '实现不同语言之间的即时翻译，支持多种主流语言。',
          examples: [
            '把"我喜欢振涛"翻译成英语',
            '这句日语是什么意思？',
            '帮我翻译这段法语'
          ]
        },
        'emotion': {
          title: '情感陪伴',
          icon: '💝',
          description: '识别用户的情感状态，如高兴、生气、悲伤等，并给予相应的情感回应和安慰。',
          examples: [
            '我今天很难过',
            '我考试考得不好',
            '我很开心今天学会了新知识'
          ]
        },
        'recommend': {
          title: '智能推荐',
          icon: '🎯',
          description: '根据用户的兴趣、历史记录等，推荐相关的服务、音乐、书籍等。',
          examples: [
            '推荐一些适合我的书籍',
            '推荐一些好听的音乐',
            '推荐一些学习资源'
          ]
        },
        'reminder': {
          title: '任务提醒',
          icon: '⏰',
          description: '帮助用户设置提醒事项，如生日、闹钟、节日、老师安排的重要事情等。',
          examples: [
            '提醒我明天下午六点给妈妈洗脚',
            '提醒我下周一交作业',
            '设置每天早上7点的闹钟'
          ]
        },
        'game': {
          title: '游戏娱乐',
          icon: '🎮',
          description: '提供各种语音游戏，如猜谜语、成语接龙、问答游戏等，为学生带来娱乐。',
          examples: [
            '我们来玩成语接龙吧',
            '给我出个谜语',
            '我们来玩问答游戏'
          ]
        },
        'health': {
          title: '健康管理',
          icon: '💪',
          description: '记录用户日常运动、饮食、睡眠数据，评估健康状况，提供个性化健康建议。',
          examples: [
            '记录我今天走了8000步',
            '我今天吃了什么',
            '提醒我要多喝水'
          ]
        },
        'photo-search': {
          title: '拍照搜题与辅导',
          icon: '📷',
          description: '支持高清拍照搜题，AI老师语音讲解，关联相似题目拓展训练，错题收集与分类。',
          examples: [
            '拍照识别数学题目',
            '拍照翻译英文文章',
            '错题自动收集整理'
          ]
        },
        'video-call': {
          title: '视频通话',
          icon: '📹',
          description: '家长可以通过外部设备与孩子进行实时对话，高清视频通话，清晰语音通话。',
          examples: [
            '与家长视频通话',
            '摄像头控制调节',
            '通话记录回放'
          ]
        },
        'exam': {
          title: '模拟考试',
          icon: '📝',
          description: 'AI智能组卷、监考、判卷，限时2小时，根据成绩定制提升计划。',
          examples: [
            'AI智能组卷考试',
            '自动监考判卷',
            '生成学习报告'
          ]
        },
        'textbook': {
          title: '课本学习',
          icon: '📖',
          description: '配套人教版/部编版教材，AI逐句讲解，口语训练跟读。',
          examples: [
            'AI逐句讲解课文',
            '跟AI练习口语',
            '课本内容同步学习'
          ]
        }
      };

      const detail = functionDetails[type];
      if (detail) {
        uni.showModal({
          title: `${detail.icon} ${detail.title}`,
          content: `${detail.description}\n\n示例：\n${detail.examples.join('\n')}`,
          showCancel: true,
          cancelText: '了解了',
          confirmText: '立即体验',
          success: (res) => {
            if (res.confirm) {
              this.openFunction(type);
            }
          }
        });
      }
    },

    openSettings() {
      uni.showActionSheet({
        itemList: ['个性化设置', '学习报告', '家长中心', '会员权益', '帮助反馈'],
        success: (res) => {
          const actions = [
            () => this.showPersonalSettings(),
            () => this.showLearningReport(),
            () => this.showParentCenter(),
            () => this.showMemberBenefits(),
            () => this.showHelpFeedback()
          ];
          actions[res.tapIndex]();
        }
      });
    },

    showPersonalSettings() {
      uni.showModal({
        title: '⚙️ 个性化设置',
        content: '• AI语音设置（语速、音色、风格）\n• 简易指令模式\n• 学习提醒设置\n• 界面主题选择',
        showCancel: true,
        cancelText: '取消',
        confirmText: '进入设置',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '功能开发中...',
              icon: 'none'
            });
          }
        }
      });
    },

    showLearningReport() {
      uni.showModal({
        title: '📊 学习报告',
        content: '• 学习时间统计\n• 专注度分析\n• 错题本查看\n• 学习进度报告\n• 家长同步通知',
        showCancel: true,
        cancelText: '取消',
        confirmText: '查看报告',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '功能开发中...',
              icon: 'none'
            });
          }
        }
      });
    },

    showParentCenter() {
      uni.showModal({
        title: '👨‍👩‍👧‍👦 家长中心',
        content: '• 实时监控学习状态\n• 视频通话功能\n• 学习报告查看\n• 账号管理（最多5个孩子）\n• 心理健康导师',
        showCancel: true,
        cancelText: '取消',
        confirmText: '进入中心',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '功能开发中...',
              icon: 'none'
            });
          }
        }
      });
    },

    showMemberBenefits() {
      uni.showModal({
        title: '💎 会员权益',
        content: '• AI监控学习功能\n• 错题本导出\n• 专属学习报告\n• 名校试卷库\n• 优惠券福利\n\n🎁 新用户注册送3个月VIP',
        showCancel: true,
        cancelText: '取消',
        confirmText: '了解详情',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '功能开发中...',
              icon: 'none'
            });
          }
        }
      });
    },

    showHelpFeedback() {
      uni.showModal({
        title: '💬 帮助反馈',
        content: '• 功能使用指南\n• 常见问题解答\n• 提交功能建议\n• 问题反馈\n• 联系客服',
        showCancel: true,
        cancelText: '取消',
        confirmText: '获取帮助',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '功能开发中...',
              icon: 'none'
            });
          }
        }
      });
    },

    logout() {
      uni.showModal({
        title: '退出登录',
        content: '确定要退出登录吗？',
        showCancel: true,
        cancelText: '取消',
        confirmText: '确定',
        success: (res) => {
          if (res.confirm) {
            // 清除用户信息
            uni.removeStorageSync('userInfo');
            uni.removeStorageSync('token');

            // 显示退出成功提示
            uni.showToast({
              title: '退出成功',
              icon: 'success',
              duration: 1500
            });

            // 延迟跳转到首页（选择登录方式页面）
            setTimeout(() => {
              uni.reLaunch({
                url: '/pages/index/index'
              });
            }, 1500);
          }
        }
      });
    },

    // 跳转到首页
    goToHome() {
      uni.switchTab({
        url: '/pages/home/<USER>'
      });
    },

    switchTab(tab) {
      switch(tab) {
        case 'profile':
          // 跳转到个人中心页面（如果存在的话）
          uni.showToast({
            title: '个人中心功能开发中',
            icon: 'none'
          });
          break;
      }
    },

    // 简化的拍照按钮提示
    showCameraInfo() {
      uni.showToast({
        title: '拍照功能开发中',
        icon: 'none',
        duration: 2000
      });
    }
  }
}
</script>

<style>
/* 自定义状态栏 */
.custom-status-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 88rpx; /* 状态栏高度 */
  background: #2c3e50;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.app-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
}

.container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 20rpx;
  padding-top: 108rpx; /* 为自定义状态栏留出空间 */
  padding-bottom: 0; /* 移除底部padding，因为现在由main页面管理 */
  animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 顶部用户信息 */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 20rpx;
  background: white;
  border-radius: 15rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: #007AFF;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.avatar-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.welcome-text {
  font-size: 24rpx;
  color: #6c757d;
  margin-bottom: 8rpx;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.ai-status {
  display: flex;
  align-items: center;
}

.settings-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: 1rpx solid #e9ecef;
}

.settings-btn:active {
  transform: scale(0.9);
  background: #e9ecef;
}

.settings-icon {
  font-size: 28rpx;
}

.status-indicator {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #ff4757;
  margin-right: 12rpx;
  animation: pulse 2s infinite;
}

.status-indicator.active {
  background: #2ed573;
}

.status-text {
  font-size: 24rpx;
  color: #6c757d;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* AI对话区域 */
.ai-chat-section {
  margin-bottom: 30rpx;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}

.chat-card {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.chat-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.voice-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10rpx); }
  60% { transform: translateY(-5rpx); }
}

.chat-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.chat-desc {
  font-size: 24rpx;
  color: #666;
}

.chat-arrow {
  font-size: 32rpx;
  color: #2c3e50;
  font-weight: bold;
}

/* 功能网格 */
.functions-section {
  margin-bottom: 30rpx;
}

.functions-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.function-item {
  background: white;
  border-radius: 12rpx;
  padding: 24rpx 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  animation: slideInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.function-item:nth-child(1) { animation-delay: 0.1s; }
.function-item:nth-child(2) { animation-delay: 0.2s; }
.function-item:nth-child(3) { animation-delay: 0.3s; }
.function-item:nth-child(4) { animation-delay: 0.4s; }
.function-item:nth-child(5) { animation-delay: 0.5s; }
.function-item:nth-child(6) { animation-delay: 0.6s; }
.function-item:nth-child(7) { animation-delay: 0.7s; }
.function-item:nth-child(8) { animation-delay: 0.8s; }
.function-item:nth-child(9) { animation-delay: 0.9s; }

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.function-item:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  border-color: rgba(255, 255, 255, 0.5);
}

.function-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  margin-bottom: 12rpx;
}

.function-icon.knowledge { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.function-icon.search { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.function-icon.writing { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.function-icon.translate { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
.function-icon.emotion { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
.function-icon.recommend { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
.function-icon.reminder { background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%); }
.function-icon.game { background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%); }
.function-icon.health { background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%); }

.function-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 6rpx;
}

.function-desc {
  font-size: 20rpx;
  color: #6c757d;
  line-height: 1.3;
}

/* 监控区域 */
.monitor-section {
  margin-bottom: 30rpx;
}

.monitor-card {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.monitor-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.status-item {
  display: flex;
  flex-direction: column;
}

.status-label {
  font-size: 24rpx;
  color: #6c757d;
  margin-bottom: 8rpx;
}

.status-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #ff4757;
}

.status-value.active {
  color: #2ed573;
}

.monitor-toggle {
  display: flex;
  align-items: center;
}

.toggle-btn {
  width: 80rpx;
  height: 40rpx;
  border-radius: 20rpx;
  background: #dee2e6;
  position: relative;
  transition: all 0.3s ease;
}

.toggle-btn.active {
  background: #27ae60;
}

.toggle-slider {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: #fff;
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.toggle-btn.active .toggle-slider {
  left: 44rpx;
}

.monitor-features {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.feature-tag {
  background: #2c3e50;
  color: #fff;
  font-size: 20rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

/* 快捷功能 */
.quick-actions {
  display: flex;
  justify-content: space-around;
  background: white;
  border-radius: 15rpx;
  padding: 30rpx 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
}

.action-icon {
  font-size: 40rpx;
  margin-bottom: 12rpx;
  padding: 16rpx;
  border-radius: 50%;
  background: #007AFF;
}

.action-text {
  font-size: 24rpx;
  color: #2c3e50;
  font-weight: 500;
}

/* 退出按钮 */
.logout-section {
  padding: 30rpx 20rpx;
  margin-bottom: 20rpx;
}

.logout-btn {
  width: 100%;
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 15rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 10rpx rgba(255, 71, 87, 0.3);
  transition: all 0.3s ease;
}

.logout-btn:active {
  transform: scale(0.98);
  background: #ff3742;
}

.logout-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.logout-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 底部提示 */
.bottom-tip {
  text-align: center;
  padding: 20rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #6c757d;
}

/* 底部导航栏 */
.bottom-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: white;
  display: flex;
  align-items: center;
  justify-content: space-around;
  border-top: 1rpx solid #e5e5e5;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 拍照搜题按钮区域 */
.camera-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.camera-btn {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 15rpx rgba(255, 107, 107, 0.4);
  transform: translateY(-10rpx); /* 稍微向上突出 */
  transition: all 0.3s ease;
  margin-bottom: 8rpx;
}

.camera-btn:active {
  transform: translateY(-8rpx) scale(0.95);
  box-shadow: 0 2rpx 10rpx rgba(255, 107, 107, 0.6);
}

.camera-icon {
  font-size: 45rpx;
  color: white;
}

.camera-text {
  font-size: 20rpx;
  color: #666;
  font-weight: 500;
  text-align: center;
  transform: translateY(-10rpx); /* 与按钮保持一致的位置 */
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 10rpx 0;
  transition: all 0.3s ease;
}

.nav-item.active .nav-icon {
  color: #007AFF;
  transform: scale(1.1);
}

.nav-item.active .nav-text {
  color: #007AFF;
  font-weight: 600;
}

.nav-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
  color: #666;
  transition: all 0.3s ease;
}

.nav-text {
  font-size: 20rpx;
  color: #666;
  transition: all 0.3s ease;
}

/* 为了给底部导航栏留出空间，调整容器底部padding */
.container {
  padding-bottom: 140rpx; /* 给底部导航栏留出空间 */
}
</style>

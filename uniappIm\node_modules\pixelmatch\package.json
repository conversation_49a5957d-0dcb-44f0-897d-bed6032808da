{"name": "pixelmatch", "version": "4.0.2", "description": "The smallest and fastest pixel-level image comparison library.", "main": "index.js", "bin": {"pixelmatch": "bin/pixelmatch"}, "dependencies": {"pngjs": "^3.0.0"}, "devDependencies": {"eslint": "^3.2.2", "eslint-config-mourner": "^2.0.1", "tap": "^6.3.0"}, "scripts": {"pretest": "eslint index.js bin/pixelmatch test/test.js", "test": "tap test/test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mapbox/pixelmatch.git"}, "keywords": ["image", "comparison", "diff"], "eslintConfig": {"extends": "mourner"}, "author": "<PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/mapbox/pixelmatch/issues"}, "homepage": "https://github.com/mapbox/pixelmatch#readme"}
{"version": 3, "file": "index.js", "sources": ["../src/errorlevel.js", "../src/formatinf.js", "../src/bitmat.js", "../src/version.js", "../src/alignpat.js", "../src/grid.js", "../src/findpat.js", "../src/detector.js", "../src/gf256poly.js", "../src/gf256.js", "../src/rsdecoder.js", "../src/datamask.js", "../src/bmparser.js", "../src/datablock.js", "../src/databr.js", "../src/decoder.js", "../src/qrcode.js"], "sourcesContent": ["/*\n  Ported to JavaScript by <PERSON><PERSON> 2011\n\n  <EMAIL>, www.lazarsoft.info\n\n*/\n\n/*\n*\n* Copyright 2007 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n\n\nexport default function ErrorCorrectionLevel(ordinal,  bits, name) {\n  this.ordinal_Renamed_Field = ordinal;\n  this.bits = bits;\n  this.name = name;\n}\n\nErrorCorrectionLevel.prototype.ordinal = function() {\n  return this.ordinal_Renamed_Field;\n};\n\nErrorCorrectionLevel.forBits = function(bits) {\n  if (bits < 0 || bits >= FOR_BITS.length) {\n    throw \"ArgumentException\";\n  }\n  return FOR_BITS[bits];\n};\n\nvar FOR_BITS = [\n  new ErrorCorrectionLevel(1, 0x00, \"M\"),\n  new ErrorCorrectionLevel(0, 0x01, \"L\"),\n  new ErrorCorrectionLevel(3, 0x02, \"H\"),\n  new ErrorCorrectionLevel(2, 0x03, \"Q\"),\n];\n", "/*\n  Ported to JavaScript by <PERSON><PERSON> 2011\n\n  <EMAIL>, www.lazarsoft.info\n\n*/\n\n/*\n*\n* Copyright 2007 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n\nimport {URShift} from './qrcode';\nimport ErrorCorrectionLevel from './errorlevel';\n\nvar FORMAT_INFO_MASK_QR = 0x5412;\nvar FORMAT_INFO_DECODE_LOOKUP = [\n  [0x5412, 0x00],\n  [0x5125, 0x01],\n  [0x5E7C, 0x02],\n  [0x5B4B, 0x03],\n  [0x45F9, 0x04],\n  [0x40CE, 0x05],\n  [0x4F97, 0x06],\n  [0x4AA0, 0x07],\n  [0x77C4, 0x08],\n  [0x72F3, 0x09],\n  [0x7DAA, 0x0A],\n  [0x789D, 0x0B],\n  [0x662F, 0x0C],\n  [0x6318, 0x0D],\n  [0x6C41, 0x0E],\n  [0x6976, 0x0F],\n  [0x1689, 0x10],\n  [0x13BE, 0x11],\n  [0x1CE7, 0x12],\n  [0x19D0, 0x13],\n  [0x0762, 0x14],\n  [0x0255, 0x15],\n  [0x0D0C, 0x16],\n  [0x083B, 0x17],\n  [0x355F, 0x18],\n  [0x3068, 0x19],\n  [0x3F31, 0x1A],\n  [0x3A06, 0x1B],\n  [0x24B4, 0x1C],\n  [0x2183, 0x1D],\n  [0x2EDA, 0x1E],\n  [0x2BED, 0x1F],\n];\nvar BITS_SET_IN_HALF_BYTE = [0, 1, 1, 2, 1, 2, 2, 3, 1, 2, 2, 3, 2, 3, 3, 4];\n\n\nexport default function FormatInformation(formatInfo) {\n  this.errorCorrectionLevel = ErrorCorrectionLevel.forBits((formatInfo >> 3) & 0x03);\n  this.dataMask =  (formatInfo & 0x07);\n}\n\nFormatInformation.prototype.GetHashCode = function() {\n  return (this.errorCorrectionLevel.ordinal() << 3) |  this.dataMask;\n};\n\nFormatInformation.prototype.Equals = function(o) {\n  var other =  o;\n  return this.errorCorrectionLevel == other.errorCorrectionLevel && this.dataMask == other.dataMask;\n};\n\nFormatInformation.numBitsDiffering = function(a,  b) {\n  a ^= b; // a now has a 1 bit exactly where its bit differs with b's\n  // Count bits set quickly with a series of lookups:\n  return BITS_SET_IN_HALF_BYTE[a & 0x0F] + BITS_SET_IN_HALF_BYTE[(URShift(a, 4) & 0x0F)] + BITS_SET_IN_HALF_BYTE[(URShift(a, 8) & 0x0F)] + BITS_SET_IN_HALF_BYTE[(URShift(a, 12) & 0x0F)] + BITS_SET_IN_HALF_BYTE[(URShift(a, 16) & 0x0F)] + BITS_SET_IN_HALF_BYTE[(URShift(a, 20) & 0x0F)] + BITS_SET_IN_HALF_BYTE[(URShift(a, 24) & 0x0F)] + BITS_SET_IN_HALF_BYTE[(URShift(a, 28) & 0x0F)];\n};\n\nFormatInformation.decodeFormatInformation = function(maskedFormatInfo) {\n  var formatInfo = FormatInformation.doDecodeFormatInformation(maskedFormatInfo);\n  if (formatInfo != null) {\n    return formatInfo;\n  }\n  // Should return null, but, some QR codes apparently\n  // do not mask this info. Try again by actually masking the pattern\n  // first\n  return FormatInformation.doDecodeFormatInformation(maskedFormatInfo ^ FORMAT_INFO_MASK_QR);\n};\nFormatInformation.doDecodeFormatInformation = function(maskedFormatInfo) {\n  // Find the int in FORMAT_INFO_DECODE_LOOKUP with fewest bits differing\n  var bestDifference = 0xffffffff;\n  var bestFormatInfo = 0;\n  for (var i = 0; i < FORMAT_INFO_DECODE_LOOKUP.length; i++) {\n    var decodeInfo = FORMAT_INFO_DECODE_LOOKUP[i];\n    var targetInfo = decodeInfo[0];\n    if (targetInfo == maskedFormatInfo) {\n      // Found an exact match\n      return new FormatInformation(decodeInfo[1]);\n    }\n    var bitsDifference = this.numBitsDiffering(maskedFormatInfo, targetInfo);\n    if (bitsDifference < bestDifference) {\n      bestFormatInfo = decodeInfo[1];\n      bestDifference = bitsDifference;\n    }\n  }\n  // Hamming distance of the 32 masked codes is 7, by construction, so <= 3 bits\n  // differing means we found a match\n  if (bestDifference <= 3) {\n    return new FormatInformation(bestFormatInfo);\n  }\n  return null;\n};\n\n\n", "/*\n  Ported to JavaScript by <PERSON><PERSON> 2011\n\n  <EMAIL>, www.lazarsoft.info\n\n*/\n\n/*\n*\n* Copyright 2007 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n\nimport {URShift} from './qrcode';\n\nexport default function BitMatrix(width,  height) {\n  if (!height)\n    height = width;\n  if (width < 1 || height < 1) {\n    throw \"Both dimensions must be greater than 0\";\n  }\n  this.width = width;\n  this.height = height;\n  var rowSize = width >> 5;\n  if ((width & 0x1f) != 0) {\n    rowSize++;\n  }\n  this.rowSize = rowSize;\n  this.bits = new Array(rowSize * height);\n  for (var i = 0; i < this.bits.length; i++)\n    this.bits[i] = 0;\n}\n\nObject.defineProperty(BitMatrix.prototype, \"Dimension\", {\n  get: function() {\n    if (this.width != this.height) {\n      throw \"Can't call getDimension() on a non-square matrix\";\n    }\n    return this.width;\n  }\n});\n\nBitMatrix.prototype.get_Renamed = function(x, y) {\n  var offset = y * this.rowSize + (x >> 5);\n  return ((URShift(this.bits[offset], (x & 0x1f))) & 1) != 0;\n};\n\nBitMatrix.prototype.set_Renamed = function(x, y) {\n  var offset = y * this.rowSize + (x >> 5);\n  this.bits[offset] |= 1 << (x & 0x1f);\n};\n\nBitMatrix.prototype.flip = function(x, y) {\n  var offset = y * this.rowSize + (x >> 5);\n  this.bits[offset] ^= 1 << (x & 0x1f);\n};\n\nBitMatrix.prototype.clear = function() {\n  var max = this.bits.length;\n  for (var i = 0; i < max; i++) {\n    this.bits[i] = 0;\n  }\n};\n\nBitMatrix.prototype.setRegion = function(left, top, width, height) {\n  if (top < 0 || left < 0) {\n    throw \"Left and top must be nonnegative\";\n  }\n  if (height < 1 || width < 1) {\n    throw \"Height and width must be at least 1\";\n  }\n  var right = left + width;\n  var bottom = top + height;\n  if (bottom > this.height || right > this.width) {\n    throw \"The region must fit inside the matrix\";\n  }\n  for (var y = top; y < bottom; y++) {\n    var offset = y * this.rowSize;\n    for (var x = left; x < right; x++) {\n      this.bits[offset + (x >> 5)] |= 1 << (x & 0x1f);\n    }\n  }\n};\n", "/*\n  Ported to JavaScript by <PERSON><PERSON> 2011\n\n  <EMAIL>, www.lazarsoft.info\n\n*/\n\n/*\n*\n* Copyright 2007 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n\nimport FormatInformation from './formatinf';\nimport BitMatrix from './bitmat';\n\nfunction ECB(count,  dataCodewords) {\n  this.count = count;\n  this.dataCodewords = dataCodewords;\n}\n\nfunction ECBlocks(ecCodewordsPerBlock,  ecBlocks1,  ecBlocks2) {\n  this.ecCodewordsPerBlock = ecCodewordsPerBlock;\n  if (ecBlocks2)\n    this.ecBlocks = [ecBlocks1, ecBlocks2];\n  else\n    this.ecBlocks = [ecBlocks1];\n}\n\nObject.defineProperty(ECBlocks.prototype, \"TotalECCodewords\", {\n  get: function() {\n    return  this.ecCodewordsPerBlock * this.NumBlocks;\n  }\n});\n\nObject.defineProperty(ECBlocks.prototype, \"NumBlocks\", {\n  get: function() {\n    var total = 0;\n    for (var i = 0; i < this.ecBlocks.length; i++) {\n      total += this.ecBlocks[i].length;\n    }\n    return total;\n  }\n});\n\nECBlocks.prototype.getECBlocks = function() {\n  return this.ecBlocks;\n};\n\nexport default function Version(versionNumber,  alignmentPatternCenters,  ecBlocks1,  ecBlocks2,  ecBlocks3,  ecBlocks4) {\n  this.versionNumber = versionNumber;\n  this.alignmentPatternCenters = alignmentPatternCenters;\n  this.ecBlocks = [ecBlocks1, ecBlocks2, ecBlocks3, ecBlocks4];\n\n  var total = 0;\n  var ecCodewords = ecBlocks1.ecCodewordsPerBlock;\n  var ecbArray = ecBlocks1.getECBlocks();\n  for (var i = 0; i < ecbArray.length; i++) {\n    var ecBlock = ecbArray[i];\n    total += ecBlock.count * (ecBlock.dataCodewords + ecCodewords);\n  }\n  this.totalCodewords = total;\n}\n\nObject.defineProperty(Version.prototype, \"DimensionForVersion\", {\n  get: function() {\n    return  17 + 4 * this.versionNumber;\n  }\n});\n\nVersion.prototype.buildFunctionPattern = function() {\n  var dimension = this.DimensionForVersion;\n  var bitMatrix = new BitMatrix(dimension);\n\n  // Top left finder pattern + separator + format\n  bitMatrix.setRegion(0, 0, 9, 9);\n  // Top right finder pattern + separator + format\n  bitMatrix.setRegion(dimension - 8, 0, 8, 9);\n  // Bottom left finder pattern + separator + format\n  bitMatrix.setRegion(0, dimension - 8, 9, 8);\n\n  // Alignment patterns\n  var max = this.alignmentPatternCenters.length;\n  for (var x = 0; x < max; x++) {\n    var i = this.alignmentPatternCenters[x] - 2;\n    for (var y = 0; y < max; y++) {\n      if ((x == 0 && (y == 0 || y == max - 1)) || (x == max - 1 && y == 0)) {\n        // No alignment patterns near the three finder paterns\n        continue;\n      }\n      bitMatrix.setRegion(this.alignmentPatternCenters[y] - 2, i, 5, 5);\n    }\n  }\n\n  // Vertical timing pattern\n  bitMatrix.setRegion(6, 9, 1, dimension - 17);\n  // Horizontal timing pattern\n  bitMatrix.setRegion(9, 6, dimension - 17, 1);\n\n  if (this.versionNumber > 6) {\n    // Version info, top right\n    bitMatrix.setRegion(dimension - 11, 0, 3, 6);\n    // Version info, bottom left\n    bitMatrix.setRegion(0, dimension - 11, 6, 3);\n  }\n\n  return bitMatrix;\n};\n\nVersion.prototype.getECBlocksForLevel = function(ecLevel) {\n  return this.ecBlocks[ecLevel.ordinal()];\n};\n\nVersion.VERSION_DECODE_INFO = [\n  0x07C94,\n  0x085BC,\n  0x09A99,\n  0x0A4D3,\n  0x0BBF6,\n  0x0C762,\n  0x0D847,\n  0x0E60D,\n  0x0F928,\n  0x10B78,\n  0x1145D,\n  0x12A17,\n  0x13532,\n  0x149A6,\n  0x15683,\n  0x168C9,\n  0x177EC,\n  0x18EC4,\n  0x191E1,\n  0x1AFAB,\n  0x1B08E,\n  0x1CC1A,\n  0x1D33F,\n  0x1ED75,\n  0x1F250,\n  0x209D5,\n  0x216F0,\n  0x228BA,\n  0x2379F,\n  0x24B0B,\n  0x2542E,\n  0x26A64,\n  0x27541,\n  0x28C69\n];\n\nVersion.VERSIONS = buildVersions();\n\nVersion.getVersionForNumber = function(versionNumber) {\n  if (versionNumber < 1 || versionNumber > 40) {\n    throw \"ArgumentException\";\n  }\n  return Version.VERSIONS[versionNumber - 1];\n};\n\nVersion.getProvisionalVersionForDimension = function(dimension) {\n  if (dimension % 4 != 1) {\n    throw \"Error getProvisionalVersionForDimension\";\n  }\n  try {\n    return Version.getVersionForNumber((dimension - 17) >> 2);\n  } catch (iae) {\n    throw \"Error getVersionForNumber\";\n  }\n};\n\nVersion.decodeVersionInformation = function(versionBits) {\n  var bestDifference = 0xffffffff;\n  var bestVersion = 0;\n  for (var i = 0; i < Version.VERSION_DECODE_INFO.length; i++) {\n    var targetVersion = Version.VERSION_DECODE_INFO[i];\n    // Do the version info bits match exactly? done.\n    if (targetVersion == versionBits) {\n      return this.getVersionForNumber(i + 7);\n    }\n    // Otherwise see if this is the closest to a real version info bit string\n    // we have seen so far\n    var bitsDifference = FormatInformation.numBitsDiffering(versionBits, targetVersion);\n    if (bitsDifference < bestDifference) {\n      bestVersion = i + 7;\n      bestDifference = bitsDifference;\n    }\n  }\n  // We can tolerate up to 3 bits of error since no two version info codewords will\n  // differ in less than 4 bits.\n  if (bestDifference <= 3) {\n    return this.getVersionForNumber(bestVersion);\n  }\n  // If we didn't find a close enough match, fail\n  return null;\n};\n\nfunction buildVersions() {\n  return [\n    new Version(1, [], new ECBlocks(7, new ECB(1, 19)), new ECBlocks(10, new ECB(1, 16)), new ECBlocks(13, new ECB(1, 13)), new ECBlocks(17, new ECB(1, 9))),\n    new Version(2, [6, 18], new ECBlocks(10, new ECB(1, 34)), new ECBlocks(16, new ECB(1, 28)), new ECBlocks(22, new ECB(1, 22)), new ECBlocks(28, new ECB(1, 16))),\n    new Version(3, [6, 22], new ECBlocks(15, new ECB(1, 55)), new ECBlocks(26, new ECB(1, 44)), new ECBlocks(18, new ECB(2, 17)), new ECBlocks(22, new ECB(2, 13))),\n    new Version(4, [6, 26], new ECBlocks(20, new ECB(1, 80)), new ECBlocks(18, new ECB(2, 32)), new ECBlocks(26, new ECB(2, 24)), new ECBlocks(16, new ECB(4, 9))),\n    new Version(5, [6, 30], new ECBlocks(26, new ECB(1, 108)), new ECBlocks(24, new ECB(2, 43)), new ECBlocks(18, new ECB(2, 15), new ECB(2, 16)), new ECBlocks(22, new ECB(2, 11), new ECB(2, 12))),\n    new Version(6, [6, 34], new ECBlocks(18, new ECB(2, 68)), new ECBlocks(16, new ECB(4, 27)), new ECBlocks(24, new ECB(4, 19)), new ECBlocks(28, new ECB(4, 15))),\n    new Version(7, [6, 22, 38], new ECBlocks(20, new ECB(2, 78)), new ECBlocks(18, new ECB(4, 31)), new ECBlocks(18, new ECB(2, 14), new ECB(4, 15)), new ECBlocks(26, new ECB(4, 13), new ECB(1, 14))),\n    new Version(8, [6, 24, 42], new ECBlocks(24, new ECB(2, 97)), new ECBlocks(22, new ECB(2, 38), new ECB(2, 39)), new ECBlocks(22, new ECB(4, 18), new ECB(2, 19)), new ECBlocks(26, new ECB(4, 14), new ECB(2, 15))),\n    new Version(9, [6, 26, 46], new ECBlocks(30, new ECB(2, 116)), new ECBlocks(22, new ECB(3, 36), new ECB(2, 37)), new ECBlocks(20, new ECB(4, 16), new ECB(4, 17)), new ECBlocks(24, new ECB(4, 12), new ECB(4, 13))),\n    new Version(10, [6, 28, 50], new ECBlocks(18, new ECB(2, 68), new ECB(2, 69)), new ECBlocks(26, new ECB(4, 43), new ECB(1, 44)), new ECBlocks(24, new ECB(6, 19), new ECB(2, 20)), new ECBlocks(28, new ECB(6, 15), new ECB(2, 16))),\n    new Version(11, [6, 30, 54], new ECBlocks(20, new ECB(4, 81)), new ECBlocks(30, new ECB(1, 50), new ECB(4, 51)), new ECBlocks(28, new ECB(4, 22), new ECB(4, 23)), new ECBlocks(24, new ECB(3, 12), new ECB(8, 13))),\n    new Version(12, [6, 32, 58], new ECBlocks(24, new ECB(2, 92), new ECB(2, 93)), new ECBlocks(22, new ECB(6, 36), new ECB(2, 37)), new ECBlocks(26, new ECB(4, 20), new ECB(6, 21)), new ECBlocks(28, new ECB(7, 14), new ECB(4, 15))),\n    new Version(13, [6, 34, 62], new ECBlocks(26, new ECB(4, 107)), new ECBlocks(22, new ECB(8, 37), new ECB(1, 38)), new ECBlocks(24, new ECB(8, 20), new ECB(4, 21)), new ECBlocks(22, new ECB(12, 11), new ECB(4, 12))),\n    new Version(14, [6, 26, 46, 66], new ECBlocks(30, new ECB(3, 115), new ECB(1, 116)), new ECBlocks(24, new ECB(4, 40), new ECB(5, 41)), new ECBlocks(20, new ECB(11, 16), new ECB(5, 17)), new ECBlocks(24, new ECB(11, 12), new ECB(5, 13))),\n    new Version(15, [6, 26, 48, 70], new ECBlocks(22, new ECB(5, 87), new ECB(1, 88)), new ECBlocks(24, new ECB(5, 41), new ECB(5, 42)), new ECBlocks(30, new ECB(5, 24), new ECB(7, 25)), new ECBlocks(24, new ECB(11, 12), new ECB(7, 13))),\n    new Version(16, [6, 26, 50, 74], new ECBlocks(24, new ECB(5, 98), new ECB(1, 99)), new ECBlocks(28, new ECB(7, 45), new ECB(3, 46)), new ECBlocks(24, new ECB(15, 19), new ECB(2, 20)), new ECBlocks(30, new ECB(3, 15), new ECB(13, 16))),\n    new Version(17, [6, 30, 54, 78], new ECBlocks(28, new ECB(1, 107), new ECB(5, 108)), new ECBlocks(28, new ECB(10, 46), new ECB(1, 47)), new ECBlocks(28, new ECB(1, 22), new ECB(15, 23)), new ECBlocks(28, new ECB(2, 14), new ECB(17, 15))),\n    new Version(18, [6, 30, 56, 82], new ECBlocks(30, new ECB(5, 120), new ECB(1, 121)), new ECBlocks(26, new ECB(9, 43), new ECB(4, 44)), new ECBlocks(28, new ECB(17, 22), new ECB(1, 23)), new ECBlocks(28, new ECB(2, 14), new ECB(19, 15))),\n    new Version(19, [6, 30, 58, 86], new ECBlocks(28, new ECB(3, 113), new ECB(4, 114)), new ECBlocks(26, new ECB(3, 44), new ECB(11, 45)), new ECBlocks(26, new ECB(17, 21), new ECB(4, 22)), new ECBlocks(26, new ECB(9, 13), new ECB(16, 14))),\n    new Version(20, [6, 34, 62, 90], new ECBlocks(28, new ECB(3, 107), new ECB(5, 108)), new ECBlocks(26, new ECB(3, 41), new ECB(13, 42)), new ECBlocks(30, new ECB(15, 24), new ECB(5, 25)), new ECBlocks(28, new ECB(15, 15), new ECB(10, 16))),\n    new Version(21, [6, 28, 50, 72, 94], new ECBlocks(28, new ECB(4, 116), new ECB(4, 117)), new ECBlocks(26, new ECB(17, 42)), new ECBlocks(28, new ECB(17, 22), new ECB(6, 23)), new ECBlocks(30, new ECB(19, 16), new ECB(6, 17))),\n    new Version(22, [6, 26, 50, 74, 98], new ECBlocks(28, new ECB(2, 111), new ECB(7, 112)), new ECBlocks(28, new ECB(17, 46)), new ECBlocks(30, new ECB(7, 24), new ECB(16, 25)), new ECBlocks(24, new ECB(34, 13))),\n    new Version(23, [6, 30, 54, 74, 102], new ECBlocks(30, new ECB(4, 121), new ECB(5, 122)), new ECBlocks(28, new ECB(4, 47), new ECB(14, 48)), new ECBlocks(30, new ECB(11, 24), new ECB(14, 25)), new ECBlocks(30, new ECB(16, 15), new ECB(14, 16))),\n    new Version(24, [6, 28, 54, 80, 106], new ECBlocks(30, new ECB(6, 117), new ECB(4, 118)), new ECBlocks(28, new ECB(6, 45), new ECB(14, 46)), new ECBlocks(30, new ECB(11, 24), new ECB(16, 25)), new ECBlocks(30, new ECB(30, 16), new ECB(2, 17))),\n    new Version(25, [6, 32, 58, 84, 110], new ECBlocks(26, new ECB(8, 106), new ECB(4, 107)), new ECBlocks(28, new ECB(8, 47), new ECB(13, 48)), new ECBlocks(30, new ECB(7, 24), new ECB(22, 25)), new ECBlocks(30, new ECB(22, 15), new ECB(13, 16))),\n    new Version(26, [6, 30, 58, 86, 114], new ECBlocks(28, new ECB(10, 114), new ECB(2, 115)), new ECBlocks(28, new ECB(19, 46), new ECB(4, 47)), new ECBlocks(28, new ECB(28, 22), new ECB(6, 23)), new ECBlocks(30, new ECB(33, 16), new ECB(4, 17))),\n    new Version(27, [6, 34, 62, 90, 118], new ECBlocks(30, new ECB(8, 122), new ECB(4, 123)), new ECBlocks(28, new ECB(22, 45), new ECB(3, 46)), new ECBlocks(30, new ECB(8, 23), new ECB(26, 24)), new ECBlocks(30, new ECB(12, 15),     new ECB(28, 16))),\n    new Version(28, [6, 26, 50, 74, 98, 122], new ECBlocks(30, new ECB(3, 117), new ECB(10, 118)), new ECBlocks(28, new ECB(3, 45), new ECB(23, 46)), new ECBlocks(30, new ECB(4, 24), new ECB(31, 25)), new ECBlocks(30, new ECB(11, 15), new ECB(31, 16))),\n    new Version(29, [6, 30, 54, 78, 102, 126], new ECBlocks(30, new ECB(7, 116), new ECB(7, 117)), new ECBlocks(28, new ECB(21, 45), new ECB(7, 46)), new ECBlocks(30, new ECB(1, 23), new ECB(37, 24)), new ECBlocks(30, new ECB(19, 15), new ECB(26, 16))),\n    new Version(30, [6, 26, 52, 78, 104, 130], new ECBlocks(30, new ECB(5, 115), new ECB(10, 116)), new ECBlocks(28, new ECB(19, 47), new ECB(10, 48)), new ECBlocks(30, new ECB(15, 24), new ECB(25, 25)), new ECBlocks(30, new ECB(23, 15), new ECB(25, 16))),\n    new Version(31, [6, 30, 56, 82, 108, 134], new ECBlocks(30, new ECB(13, 115), new ECB(3, 116)), new ECBlocks(28, new ECB(2, 46), new ECB(29, 47)), new ECBlocks(30, new ECB(42, 24), new ECB(1, 25)), new ECBlocks(30, new ECB(23, 15), new ECB(28, 16))),\n    new Version(32, [6, 34, 60, 86, 112, 138], new ECBlocks(30, new ECB(17, 115)), new ECBlocks(28, new ECB(10, 46), new ECB(23, 47)), new ECBlocks(30, new ECB(10, 24), new ECB(35, 25)), new ECBlocks(30, new ECB(19, 15), new ECB(35, 16))),\n    new Version(33, [6, 30, 58, 86, 114, 142], new ECBlocks(30, new ECB(17, 115), new ECB(1, 116)), new ECBlocks(28, new ECB(14, 46), new ECB(21, 47)), new ECBlocks(30, new ECB(29, 24), new ECB(19, 25)), new ECBlocks(30, new ECB(11, 15), new ECB(46, 16))),\n    new Version(34, [6, 34, 62, 90, 118, 146], new ECBlocks(30, new ECB(13, 115), new ECB(6, 116)), new ECBlocks(28, new ECB(14, 46), new ECB(23, 47)), new ECBlocks(30, new ECB(44, 24), new ECB(7, 25)), new ECBlocks(30, new ECB(59, 16), new ECB(1, 17))),\n    new Version(35, [6, 30, 54, 78, 102, 126, 150], new ECBlocks(30, new ECB(12, 121), new ECB(7, 122)), new ECBlocks(28, new ECB(12, 47), new ECB(26, 48)), new ECBlocks(30, new ECB(39, 24), new ECB(14, 25)), new ECBlocks(30, new ECB(22, 15), new ECB(41, 16))),\n    new Version(36, [6, 24, 50, 76, 102, 128, 154], new ECBlocks(30, new ECB(6, 121), new ECB(14, 122)), new ECBlocks(28, new ECB(6, 47), new ECB(34, 48)), new ECBlocks(30, new ECB(46, 24), new ECB(10, 25)), new ECBlocks(30, new ECB(2, 15), new ECB(64, 16))),\n    new Version(37, [6, 28, 54, 80, 106, 132, 158], new ECBlocks(30, new ECB(17, 122), new ECB(4, 123)), new ECBlocks(28, new ECB(29, 46), new ECB(14, 47)), new ECBlocks(30, new ECB(49, 24), new ECB(10, 25)), new ECBlocks(30, new ECB(24, 15), new ECB(46, 16))),\n    new Version(38, [6, 32, 58, 84, 110, 136, 162], new ECBlocks(30, new ECB(4, 122), new ECB(18, 123)), new ECBlocks(28, new ECB(13, 46), new ECB(32, 47)), new ECBlocks(30, new ECB(48, 24), new ECB(14, 25)), new ECBlocks(30, new ECB(42, 15), new ECB(32, 16))),\n    new Version(39, [6, 26, 54, 82, 110, 138, 166], new ECBlocks(30, new ECB(20, 117), new ECB(4, 118)), new ECBlocks(28, new ECB(40, 47), new ECB(7, 48)), new ECBlocks(30, new ECB(43, 24), new ECB(22, 25)), new ECBlocks(30, new ECB(10, 15), new ECB(67, 16))),\n    new Version(40, [6, 30, 58, 86, 114, 142, 170], new ECBlocks(30, new ECB(19, 118), new ECB(6, 119)), new ECBlocks(28, new ECB(18, 47), new ECB(31, 48)), new ECBlocks(30, new ECB(34, 24), new ECB(34, 25)), new ECBlocks(30, new ECB(20, 15), new ECB(61, 16))),\n  ];\n}\n", "/*\n  Ported to JavaScript by <PERSON><PERSON> 2011\n\n  <EMAIL>, www.lazarsoft.info\n\n*/\n\n/*\n*\n* Copyright 2007 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n\n\nexport default function AlignmentPattern(posX, posY,  estimatedModuleSize) {\n  this.x = posX;\n  this.y = posY;\n  this.count = 1;\n  this.estimatedModuleSize = estimatedModuleSize;\n}\n\nObject.defineProperty(AlignmentPattern.prototype, \"X\", {\n  get: function() {\n    return Math.floor(this.x);\n  }\n});\n\nObject.defineProperty(AlignmentPattern.prototype, \"Y\", {\n  get: function() {\n    return Math.floor(this.y);\n  }\n});\n\nAlignmentPattern.prototype.incrementCount = function() {\n  this.count++;\n};\n\nAlignmentPattern.prototype.aboutEquals = function(moduleSize,  i,  j) {\n  if (Math.abs(i - this.y) <= moduleSize && Math.abs(j - this.x) <= moduleSize) {\n    var moduleSizeDiff = Math.abs(moduleSize - this.estimatedModuleSize);\n    return moduleSizeDiff <= 1.0 || moduleSizeDiff / this.estimatedModuleSize <= 1.0;\n  }\n  return false;\n};\n\nexport function AlignmentPatternFinder(image,  startX,  startY,  width,  height,  moduleSize,  resultPointCallback) {\n  this.image = image;\n  this.possibleCenters = [];\n  this.startX = startX;\n  this.startY = startY;\n  this.width = width;\n  this.height = height;\n  this.moduleSize = moduleSize;\n  this.crossCheckStateCount = [0, 0, 0];\n  this.resultPointCallback = resultPointCallback;\n}\n\nAlignmentPatternFinder.prototype.centerFromEnd = function(stateCount,  end) {\n  return  (end - stateCount[2]) - stateCount[1] / 2.0;\n};\n\nAlignmentPatternFinder.prototype.foundPatternCross = function(stateCount) {\n  var moduleSize = this.moduleSize;\n  var maxVariance = moduleSize / 2.0;\n  for (var i = 0; i < 3; i++) {\n    if (Math.abs(moduleSize - stateCount[i]) >= maxVariance) {\n      return false;\n    }\n  }\n  return true;\n};\n\nAlignmentPatternFinder.prototype.crossCheckVertical = function(startI,  centerJ,  maxCount,  originalStateCountTotal) {\n  var image = this.image;\n\n  var maxI = image.height;\n  var stateCount = this.crossCheckStateCount;\n  stateCount[0] = 0;\n  stateCount[1] = 0;\n  stateCount[2] = 0;\n\n  // Start counting up from center\n  var i = startI;\n  while (i >= 0 && image.data[centerJ + i * image.width] && stateCount[1] <= maxCount) {\n    stateCount[1]++;\n    i--;\n  }\n  // If already too many modules in this state or ran off the edge:\n  if (i < 0 || stateCount[1] > maxCount) {\n    return NaN;\n  }\n  while (i >= 0 && !image.data[centerJ + i * image.width] && stateCount[0] <= maxCount) {\n    stateCount[0]++;\n    i--;\n  }\n  if (stateCount[0] > maxCount) {\n    return NaN;\n  }\n\n  // Now also count down from center\n  i = startI + 1;\n  while (i < maxI && image.data[centerJ + i * image.width] && stateCount[1] <= maxCount) {\n    stateCount[1]++;\n    i++;\n  }\n  if (i == maxI || stateCount[1] > maxCount) {\n    return NaN;\n  }\n  while (i < maxI && !image.data[centerJ + i * image.width] && stateCount[2] <= maxCount) {\n    stateCount[2]++;\n    i++;\n  }\n  if (stateCount[2] > maxCount) {\n    return NaN;\n  }\n\n  var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2];\n  if (5 * Math.abs(stateCountTotal - originalStateCountTotal) >= 2 * originalStateCountTotal) {\n    return NaN;\n  }\n\n  return this.foundPatternCross(stateCount) ? this.centerFromEnd(stateCount, i) : NaN;\n};\n\nAlignmentPatternFinder.prototype.handlePossibleCenter = function(stateCount,  i,  j) {\n  var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2];\n  var centerJ = this.centerFromEnd(stateCount, j);\n  var centerI = this.crossCheckVertical(i, Math.floor(centerJ), 2 * stateCount[1], stateCountTotal);\n  if (!isNaN(centerI)) {\n    var estimatedModuleSize = (stateCount[0] + stateCount[1] + stateCount[2]) / 3.0;\n    var max = this.possibleCenters.length;\n    for (var index = 0; index < max; index++) {\n      var center =  this.possibleCenters[index];\n      // Look for about the same center and module size:\n      if (center.aboutEquals(estimatedModuleSize, centerI, centerJ)) {\n        return new AlignmentPattern(centerJ, centerI, estimatedModuleSize);\n      }\n    }\n    // Hadn't found this before; save it\n    var point = new AlignmentPattern(centerJ, centerI, estimatedModuleSize);\n    this.possibleCenters.push(point);\n    if (this.resultPointCallback != null) {\n      this.resultPointCallback.foundPossibleResultPoint(point);\n    }\n  }\n  return null;\n};\n\nAlignmentPatternFinder.prototype.find = function() {\n  var image = this.image;\n  var startX = this.startX;\n  var height = this.height;\n  var maxJ = startX + this.width;\n  var middleI = this.startY + (height >> 1);\n  // We are looking for black/white/black modules in 1:1:1 ratio;\n  // this tracks the number of black/white/black modules seen so far\n  var stateCount = [0, 0, 0];\n  for (var iGen = 0; iGen < height; iGen++) {\n    // Search from middle outwards\n    var i = middleI + ((iGen & 0x01) == 0 ? ((iGen + 1) >> 1) : -((iGen + 1) >> 1));\n    stateCount[0] = 0;\n    stateCount[1] = 0;\n    stateCount[2] = 0;\n    var j = startX;\n    // Burn off leading white pixels before anything else; if we start in the middle of\n    // a white run, it doesn't make sense to count its length, since we don't know if the\n    // white run continued to the left of the start point\n    while (j < maxJ && !image.data[j + image.width * i]) {\n      j++;\n    }\n    var currentState = 0;\n    while (j < maxJ) {\n      if (image.data[j + i * image.width]) {\n        // Black pixel\n        if (currentState == 1) {\n          // Counting black pixels\n          stateCount[currentState]++;\n        } else {\n          // Counting white pixels\n          if (currentState == 2) {\n            // A winner?\n            if (this.foundPatternCross(stateCount)) {\n              // Yes\n              var confirmed = this.handlePossibleCenter(stateCount, i, j);\n              if (confirmed != null) {\n                return confirmed;\n              }\n            }\n            stateCount[0] = stateCount[2];\n            stateCount[1] = 1;\n            stateCount[2] = 0;\n            currentState = 1;\n          } else {\n            stateCount[++currentState]++;\n          }\n        }\n      } else {\n        // White pixel\n        if (currentState == 1) {\n          // Counting black pixels\n          currentState++;\n        }\n        stateCount[currentState]++;\n      }\n      j++;\n    }\n    if (this.foundPatternCross(stateCount)) {\n      var confirmed = this.handlePossibleCenter(stateCount, i, maxJ);\n      if (confirmed != null) {\n        return confirmed;\n      }\n    }\n  }\n\n  // Hmm, nothing we saw was observed and confirmed twice. If we had\n  // any guess at all, return it.\n  if (!(this.possibleCenters.length == 0)) {\n    return  this.possibleCenters[0];\n  }\n\n  throw \"Couldn't find enough alignment patterns\";\n};\n", "/*\n  Ported to JavaScript by <PERSON><PERSON> 2011\n\n  <EMAIL>, www.lazarsoft.info\n\n*/\n\n/*\n*\n* Copyright 2007 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n\nimport BitMatrix from './bitmat';\n\nvar GridSampler = {};\n\nGridSampler.checkAndNudgePoints = function(image,  points) {\n  var width = image.width;\n  var height = image.height;\n  // Check and nudge points from start until we see some that are OK:\n  var nudged = true;\n  for (var offset = 0; offset < points.length && nudged; offset += 2) {\n    var x = Math.floor(points[offset]);\n    var y = Math.floor(points[offset + 1]);\n    if (x < -1 || x > width || y < -1 || y > height) {\n      throw \"Error.checkAndNudgePoints \";\n    }\n    nudged = false;\n    if (x == -1) {\n      points[offset] = 0.0;\n      nudged = true;\n    } else if (x == width) {\n      points[offset] = width - 1;\n      nudged = true;\n    }\n    if (y == -1) {\n      points[offset + 1] = 0.0;\n      nudged = true;\n    } else if (y == height) {\n      points[offset + 1] = height - 1;\n      nudged = true;\n    }\n  }\n  // Check and nudge points from end:\n  nudged = true;\n  for (var offset = points.length - 2; offset >= 0 && nudged; offset -= 2) {\n    var x = Math.floor(points[offset]);\n    var y = Math.floor(points[offset + 1]);\n    if (x < -1 || x > width || y < -1 || y > height) {\n      throw \"Error.checkAndNudgePoints \";\n    }\n    nudged = false;\n    if (x == -1) {\n      points[offset] = 0.0;\n      nudged = true;\n    } else if (x == width) {\n      points[offset] = width - 1;\n      nudged = true;\n    }\n    if (y == -1) {\n      points[offset + 1] = 0.0;\n      nudged = true;\n    } else if (y == height) {\n      points[offset + 1] = height - 1;\n      nudged = true;\n    }\n  }\n};\n\n\n\nGridSampler.sampleGrid3 = function(image,  dimension,  transform) {\n  var bits = new BitMatrix(dimension);\n  var points = new Array(dimension << 1);\n  for (var y = 0; y < dimension; y++) {\n    var max = points.length;\n    var iValue =  y + 0.5;\n    for (var x = 0; x < max; x += 2) {\n      points[x] =  (x >> 1) + 0.5;\n      points[x + 1] = iValue;\n    }\n    transform.transformPoints1(points);\n    // Quick check to see if points transformed to something inside the image\n    // sufficient to check the endpoints\n    GridSampler.checkAndNudgePoints(image, points);\n    try {\n      for (var x = 0; x < max; x += 2) {\n        var bit = image.data[Math.floor(points[x]) + image.width * Math.floor(points[x + 1])];\n        if (bit)\n          bits.set_Renamed(x >> 1, y);\n      }\n    } catch (aioobe) {\n      // This feels wrong, but, sometimes if the finder patterns are misidentified, the resulting\n      // transform gets \"twisted\" such that it maps a straight line of points to a set of points\n      // whose endpoints are in bounds, but others are not. There is probably some mathematical\n      // way to detect this about the transformation that I don't know yet.\n      // This results in an ugly runtime exception despite our clever checks above -- can't have\n      // that. We could check each point's coordinates but that feels duplicative. We settle for\n      // catching and wrapping ArrayIndexOutOfBoundsException.\n      throw \"Error.checkAndNudgePoints\";\n    }\n  }\n  return bits;\n};\n\nexport default GridSampler;\n", "/*\n  Ported to JavaScript by <PERSON><PERSON> 2011\n\n  <EMAIL>, www.lazarsoft.info\n\n*/\n\n/*\n*\n* Copyright 2007 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n\nvar MIN_SKIP = 3;\nvar MAX_MODULES = 57;\nvar INTEGER_MATH_SHIFT = 8;\nvar CENTER_QUORUM = 2;\n\nfunction orderBestPatterns(patterns) {\n\n  function distance(pattern1,  pattern2) {\n    var xDiff = pattern1.X - pattern2.X;\n    var yDiff = pattern1.Y - pattern2.Y;\n    return  Math.sqrt((xDiff * xDiff + yDiff * yDiff));\n  }\n\n  /// <summary> Returns the z component of the cross product between vectors BC and BA.</summary>\n  function crossProductZ(pointA,  pointB,  pointC) {\n    var bX = pointB.x;\n    var bY = pointB.y;\n    return ((pointC.x - bX) * (pointA.y - bY)) - ((pointC.y - bY) * (pointA.x - bX));\n  }\n\n\n  // Find distances between pattern centers\n  var zeroOneDistance = distance(patterns[0], patterns[1]);\n  var oneTwoDistance = distance(patterns[1], patterns[2]);\n  var zeroTwoDistance = distance(patterns[0], patterns[2]);\n\n  var pointA, pointB, pointC;\n  // Assume one closest to other two is B; A and C will just be guesses at first\n  if (oneTwoDistance >= zeroOneDistance && oneTwoDistance >= zeroTwoDistance) {\n    pointB = patterns[0];\n    pointA = patterns[1];\n    pointC = patterns[2];\n  } else if (zeroTwoDistance >= oneTwoDistance && zeroTwoDistance >= zeroOneDistance) {\n    pointB = patterns[1];\n    pointA = patterns[0];\n    pointC = patterns[2];\n  } else {\n    pointB = patterns[2];\n    pointA = patterns[0];\n    pointC = patterns[1];\n  }\n\n  // Use cross product to figure out whether A and C are correct or flipped.\n  // This asks whether BC x BA has a positive z component, which is the arrangement\n  // we want for A, B, C. If it's negative, then we've got it flipped around and\n  // should swap A and C.\n  if (crossProductZ(pointA, pointB, pointC) < 0.0) {\n    var temp = pointA;\n    pointA = pointC;\n    pointC = temp;\n  }\n\n  patterns[0] = pointA;\n  patterns[1] = pointB;\n  patterns[2] = pointC;\n}\n\n\nfunction FinderPattern(posX, posY,  estimatedModuleSize) {\n  this.x = posX;\n  this.y = posY;\n  this.count = 1;\n  this.estimatedModuleSize = estimatedModuleSize;\n}\n\nObject.defineProperty(FinderPattern.prototype, \"X\", {\n  get: function() {\n    return this.x;\n  }\n});\n\nObject.defineProperty(FinderPattern.prototype, \"Y\", {\n  get: function() {\n    return this.y;\n  }\n});\n\nFinderPattern.prototype.incrementCount = function() {\n  this.count++;\n};\n\nFinderPattern.prototype.aboutEquals = function(moduleSize, i, j) {\n  if (Math.abs(i - this.y) <= moduleSize && Math.abs(j - this.x) <= moduleSize) {\n    var moduleSizeDiff = Math.abs(moduleSize - this.estimatedModuleSize);\n    return moduleSizeDiff <= 1.0 || moduleSizeDiff / this.estimatedModuleSize <= 1.0;\n  }\n  return false;\n};\n\nfunction FinderPatternInfo(patternCenters) {\n  this.bottomLeft = patternCenters[0];\n  this.topLeft = patternCenters[1];\n  this.topRight = patternCenters[2];\n}\n\nexport function FinderPatternFinder() {\n  this.image = null;\n  this.possibleCenters = [];\n  this.hasSkipped = false;\n  this.crossCheckStateCount = [0, 0, 0, 0, 0];\n  this.resultPointCallback = null;\n}\n\nObject.defineProperty(FinderPatternFinder.prototype, \"CrossCheckStateCount\", {\n  get: function() {\n    this.crossCheckStateCount[0] = 0;\n    this.crossCheckStateCount[1] = 0;\n    this.crossCheckStateCount[2] = 0;\n    this.crossCheckStateCount[3] = 0;\n    this.crossCheckStateCount[4] = 0;\n    return this.crossCheckStateCount;\n  }\n});\n\nFinderPatternFinder.prototype.foundPatternCross = function(stateCount) {\n  var totalModuleSize = 0;\n  for (var i = 0; i < 5; i++) {\n    var count = stateCount[i];\n    if (count == 0) {\n      return false;\n    }\n    totalModuleSize += count;\n  }\n  if (totalModuleSize < 7) {\n    return false;\n  }\n  var moduleSize = Math.floor((totalModuleSize << INTEGER_MATH_SHIFT) / 7);\n  var maxVariance = Math.floor(moduleSize / 2);\n  // Allow less than 50% variance from 1-1-3-1-1 proportions\n  return Math.abs(moduleSize - (stateCount[0] << INTEGER_MATH_SHIFT)) < maxVariance && Math.abs(moduleSize - (stateCount[1] << INTEGER_MATH_SHIFT)) < maxVariance && Math.abs(3 * moduleSize - (stateCount[2] << INTEGER_MATH_SHIFT)) < 3 * maxVariance && Math.abs(moduleSize - (stateCount[3] << INTEGER_MATH_SHIFT)) < maxVariance && Math.abs(moduleSize - (stateCount[4] << INTEGER_MATH_SHIFT)) < maxVariance;\n};\n\nFinderPatternFinder.prototype.centerFromEnd = function(stateCount,  end) {\n  return  (end - stateCount[4] - stateCount[3]) - stateCount[2] / 2.0;\n};\n\nFinderPatternFinder.prototype.crossCheckVertical = function(startI,  centerJ,  maxCount,  originalStateCountTotal) {\n  var image = this.image;\n\n  var maxI = image.height;\n  var stateCount = this.CrossCheckStateCount;\n\n  // Start counting up from center\n  var i = startI;\n  while (i >= 0 && image.data[centerJ + i * image.width]) {\n    stateCount[2]++;\n    i--;\n  }\n  if (i < 0) {\n    return NaN;\n  }\n  while (i >= 0 && !image.data[centerJ + i * image.width] && stateCount[1] <= maxCount) {\n    stateCount[1]++;\n    i--;\n  }\n  // If already too many modules in this state or ran off the edge:\n  if (i < 0 || stateCount[1] > maxCount) {\n    return NaN;\n  }\n  while (i >= 0 && image.data[centerJ + i * image.width] && stateCount[0] <= maxCount) {\n    stateCount[0]++;\n    i--;\n  }\n  if (stateCount[0] > maxCount) {\n    return NaN;\n  }\n\n  // Now also count down from center\n  i = startI + 1;\n  while (i < maxI && image.data[centerJ + i * image.width]) {\n    stateCount[2]++;\n    i++;\n  }\n  if (i == maxI) {\n    return NaN;\n  }\n  while (i < maxI && !image.data[centerJ + i * image.width] && stateCount[3] < maxCount) {\n    stateCount[3]++;\n    i++;\n  }\n  if (i == maxI || stateCount[3] >= maxCount) {\n    return NaN;\n  }\n  while (i < maxI && image.data[centerJ + i * image.width] && stateCount[4] < maxCount) {\n    stateCount[4]++;\n    i++;\n  }\n  if (stateCount[4] >= maxCount) {\n    return NaN;\n  }\n\n  // If we found a finder-pattern-like section, but its size is more than 40% different than\n  // the original, assume it's a false positive\n  var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2] + stateCount[3] + stateCount[4];\n  if (5 * Math.abs(stateCountTotal - originalStateCountTotal) >= 2 * originalStateCountTotal) {\n    return NaN;\n  }\n\n  return this.foundPatternCross(stateCount) ? this.centerFromEnd(stateCount, i) : NaN;\n};\n\nFinderPatternFinder.prototype.crossCheckHorizontal = function(startJ,  centerI,  maxCount, originalStateCountTotal) {\n  var image = this.image;\n\n  var maxJ = image.width;\n  var stateCount = this.CrossCheckStateCount;\n\n  var j = startJ;\n  while (j >= 0 && image.data[j + centerI * image.width]) {\n    stateCount[2]++;\n    j--;\n  }\n  if (j < 0) {\n    return NaN;\n  }\n  while (j >= 0 && !image.data[j + centerI * image.width] && stateCount[1] <= maxCount) {\n    stateCount[1]++;\n    j--;\n  }\n  if (j < 0 || stateCount[1] > maxCount) {\n    return NaN;\n  }\n  while (j >= 0 && image.data[j + centerI * image.width] && stateCount[0] <= maxCount) {\n    stateCount[0]++;\n    j--;\n  }\n  if (stateCount[0] > maxCount) {\n    return NaN;\n  }\n\n  j = startJ + 1;\n  while (j < maxJ && image.data[j + centerI * image.width]) {\n    stateCount[2]++;\n    j++;\n  }\n  if (j == maxJ) {\n    return NaN;\n  }\n  while (j < maxJ && !image.data[j + centerI * image.width] && stateCount[3] < maxCount) {\n    stateCount[3]++;\n    j++;\n  }\n  if (j == maxJ || stateCount[3] >= maxCount) {\n    return NaN;\n  }\n  while (j < maxJ && image.data[j + centerI * image.width] && stateCount[4] < maxCount) {\n    stateCount[4]++;\n    j++;\n  }\n  if (stateCount[4] >= maxCount) {\n    return NaN;\n  }\n\n  // If we found a finder-pattern-like section, but its size is significantly different than\n  // the original, assume it's a false positive\n  var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2] + stateCount[3] + stateCount[4];\n  if (5 * Math.abs(stateCountTotal - originalStateCountTotal) >= originalStateCountTotal) {\n    return NaN;\n  }\n\n  return this.foundPatternCross(stateCount) ? this.centerFromEnd(stateCount, j) : NaN;\n};\n\nFinderPatternFinder.prototype.handlePossibleCenter = function(stateCount,  i,  j) {\n  var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2] + stateCount[3] + stateCount[4];\n  var centerJ = this.centerFromEnd(stateCount, j); //float\n  var centerI = this.crossCheckVertical(i, Math.floor(centerJ), stateCount[2], stateCountTotal); //float\n  if (!isNaN(centerI)) {\n    // Re-cross check\n    centerJ = this.crossCheckHorizontal(Math.floor(centerJ), Math.floor(centerI), stateCount[2], stateCountTotal);\n    if (!isNaN(centerJ)) {\n      var estimatedModuleSize =   stateCountTotal / 7.0;\n      var found = false;\n      var max = this.possibleCenters.length;\n      for (var index = 0; index < max; index++) {\n        var center = this.possibleCenters[index];\n        // Look for about the same center and module size:\n        if (center.aboutEquals(estimatedModuleSize, centerI, centerJ)) {\n          center.incrementCount();\n          found = true;\n          break;\n        }\n      }\n      if (!found) {\n        var point = new FinderPattern(centerJ, centerI, estimatedModuleSize);\n        this.possibleCenters.push(point);\n        if (this.resultPointCallback != null) {\n          this.resultPointCallback.foundPossibleResultPoint(point);\n        }\n      }\n      return true;\n    }\n  }\n  return false;\n};\n\nFinderPatternFinder.prototype.selectBestPatterns = function() {\n\n  var startSize = this.possibleCenters.length;\n  if (startSize < 3) {\n    // Couldn't find enough finder patterns\n    throw \"Couldn't find enough finder patterns:\" + startSize + \" patterns found\";\n  }\n\n  // Filter outlier possibilities whose module size is too different\n  if (startSize > 3) {\n    // But we can only afford to do so if we have at least 4 possibilities to choose from\n    var totalModuleSize = 0.0;\n    var square = 0.0;\n    for (var i = 0; i < startSize; i++) {\n      var  centerValue = this.possibleCenters[i].estimatedModuleSize;\n      totalModuleSize += centerValue;\n      square += (centerValue * centerValue);\n    }\n    var average = totalModuleSize /  startSize;\n    this.possibleCenters.sort(function(center1, center2) {\n      var dA = Math.abs(center2.estimatedModuleSize - average);\n      var dB = Math.abs(center1.estimatedModuleSize - average);\n      if (dA < dB) {\n        return (-1);\n      } else if (dA == dB) {\n        return 0;\n      } else {\n        return 1;\n      }\n    });\n\n    var stdDev = Math.sqrt(square / startSize - average * average);\n    var limit = Math.max(0.2 * average, stdDev);\n    for (var i = this.possibleCenters - 1; i >= 0; i--) {\n      var pattern =  this.possibleCenters[i];\n      if (Math.abs(pattern.estimatedModuleSize - average) > limit) {\n        this.possibleCenters.splice(i, 1);\n      }\n    }\n  }\n\n  if (this.possibleCenters.length > 3) {\n    // Throw away all but those first size candidate points we found.\n    this.possibleCenters.sort(function(a, b) {\n      if (a.count > b.count) return -1;\n      if (a.count < b.count) return 1;\n      return 0;\n    });\n  }\n\n  return [this.possibleCenters[0],  this.possibleCenters[1],  this.possibleCenters[2]];\n};\n\nFinderPatternFinder.prototype.findRowSkip = function() {\n  var max = this.possibleCenters.length;\n  if (max <= 1) {\n    return 0;\n  }\n  var firstConfirmedCenter = null;\n  for (var i = 0; i < max; i++) {\n    var center =  this.possibleCenters[i];\n    if (center.count >= CENTER_QUORUM) {\n      if (firstConfirmedCenter == null) {\n        firstConfirmedCenter = center;\n      } else {\n        // We have two confirmed centers\n        // How far down can we skip before resuming looking for the next\n        // pattern? In the worst case, only the difference between the\n        // difference in the x / y coordinates of the two centers.\n        // This is the case where you find top left last.\n        this.hasSkipped = true;\n        return Math.floor((Math.abs(firstConfirmedCenter.X - center.X) - Math.abs(firstConfirmedCenter.Y - center.Y)) / 2);\n      }\n    }\n  }\n  return 0;\n};\n\nFinderPatternFinder.prototype.haveMultiplyConfirmedCenters = function() {\n  var confirmedCount = 0;\n  var totalModuleSize = 0.0;\n  var max = this.possibleCenters.length;\n  for (var i = 0; i < max; i++) {\n    var pattern =  this.possibleCenters[i];\n    if (pattern.count >= CENTER_QUORUM) {\n      confirmedCount++;\n      totalModuleSize += pattern.estimatedModuleSize;\n    }\n  }\n  if (confirmedCount < 3) {\n    return false;\n  }\n  // OK, we have at least 3 confirmed centers, but, it's possible that one is a \"false positive\"\n  // and that we need to keep looking. We detect this by asking if the estimated module sizes\n  // vary too much. We arbitrarily say that when the total deviation from average exceeds\n  // 5% of the total module size estimates, it's too much.\n  var average = totalModuleSize / max;\n  var totalDeviation = 0.0;\n  for (var i = 0; i < max; i++) {\n    pattern = this.possibleCenters[i];\n    totalDeviation += Math.abs(pattern.estimatedModuleSize - average);\n  }\n  return totalDeviation <= 0.05 * totalModuleSize;\n};\n\nFinderPatternFinder.prototype.findFinderPattern = function(image) {\n  var tryHarder = false;\n  this.image = image;\n  var maxI = image.height;\n  var maxJ = image.width;\n  var iSkip = Math.floor((3 * maxI) / (4 * MAX_MODULES));\n  if (iSkip < MIN_SKIP || tryHarder) {\n    iSkip = MIN_SKIP;\n  }\n\n  var done = false;\n  var stateCount = new Array(5);\n  for (var i = iSkip - 1; i < maxI && !done; i += iSkip) {\n    // Get a row of black/white values\n    stateCount[0] = 0;\n    stateCount[1] = 0;\n    stateCount[2] = 0;\n    stateCount[3] = 0;\n    stateCount[4] = 0;\n    var currentState = 0;\n    for (var j = 0; j < maxJ; j++) {\n      if (image.data[j + i * image.width]) {\n        // Black pixel\n        if ((currentState & 1) == 1) {\n          // Counting white pixels\n          currentState++;\n        }\n        stateCount[currentState]++;\n      } else {\n        // White pixel\n        if ((currentState & 1) == 0) {\n          // Counting black pixels\n          if (currentState == 4) {\n            // A winner?\n            if (this.foundPatternCross(stateCount)) {\n              // Yes\n              var confirmed = this.handlePossibleCenter(stateCount, i, j);\n              if (confirmed) {\n                // Start examining every other line. Checking each line turned out to be too\n                // expensive and didn't improve performance.\n                iSkip = 2;\n                if (this.hasSkipped) {\n                  done = this.haveMultiplyConfirmedCenters();\n                } else {\n                  var rowSkip = this.findRowSkip();\n                  if (rowSkip > stateCount[2]) {\n                    // Skip rows between row of lower confirmed center\n                    // and top of presumed third confirmed center\n                    // but back up a bit to get a full chance of detecting\n                    // it, entire width of center of finder pattern\n\n                    // Skip by rowSkip, but back off by stateCount[2] (size of last center\n                    // of pattern we saw) to be conservative, and also back off by iSkip which\n                    // is about to be re-added\n                    i += rowSkip - stateCount[2] - iSkip;\n                    j = maxJ - 1;\n                  }\n                }\n              } else {\n                // Advance to next black pixel\n                do {\n                  j++;\n                } while (j < maxJ && !image.data[j + i * image.width]);\n                j--; // back up to that last white pixel\n              }\n              // Clear state to start looking again\n              currentState = 0;\n              stateCount[0] = 0;\n              stateCount[1] = 0;\n              stateCount[2] = 0;\n              stateCount[3] = 0;\n              stateCount[4] = 0;\n            } else {\n              // No, shift counts back by two\n              stateCount[0] = stateCount[2];\n              stateCount[1] = stateCount[3];\n              stateCount[2] = stateCount[4];\n              stateCount[3] = 1;\n              stateCount[4] = 0;\n              currentState = 3;\n            }\n          } else {\n            stateCount[++currentState]++;\n          }\n        } else {\n          // Counting white pixels\n          stateCount[currentState]++;\n        }\n      }\n    }\n    if (this.foundPatternCross(stateCount)) {\n      var confirmed = this.handlePossibleCenter(stateCount, i, maxJ);\n      if (confirmed) {\n        iSkip = stateCount[0];\n        if (this.hasSkipped) {\n          // Found a third one\n          done = this.haveMultiplyConfirmedCenters();\n        }\n      }\n    }\n  }\n\n  var patternInfo = this.selectBestPatterns();\n  orderBestPatterns(patternInfo);\n\n  return new FinderPatternInfo(patternInfo);\n};\n", "/*\n  Ported to JavaScript by <PERSON><PERSON> 2011\n\n  <EMAIL>, www.lazarsoft.info\n\n*/\n\n/*\n*\n* Copyright 2007 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n\nimport Version from './version';\nimport {AlignmentPatternFinder} from './alignpat';\nimport GridSampler from './grid';\nimport {FinderPatternFinder} from './findpat';\n\nfunction PerspectiveTransform(a11,  a21,  a31,  a12,  a22,  a32,  a13,  a23,  a33) {\n  this.a11 = a11;\n  this.a12 = a12;\n  this.a13 = a13;\n  this.a21 = a21;\n  this.a22 = a22;\n  this.a23 = a23;\n  this.a31 = a31;\n  this.a32 = a32;\n  this.a33 = a33;\n}\n\nPerspectiveTransform.prototype.transformPoints1 = function(points) {\n  var max = points.length;\n  var a11 = this.a11;\n  var a12 = this.a12;\n  var a13 = this.a13;\n  var a21 = this.a21;\n  var a22 = this.a22;\n  var a23 = this.a23;\n  var a31 = this.a31;\n  var a32 = this.a32;\n  var a33 = this.a33;\n  for (var i = 0; i < max; i += 2) {\n    var x = points[i];\n    var y = points[i + 1];\n    var denominator = a13 * x + a23 * y + a33;\n    points[i] = (a11 * x + a21 * y + a31) / denominator;\n    points[i + 1] = (a12 * x + a22 * y + a32) / denominator;\n  }\n};\n\nPerspectiveTransform.prototype.transformPoints2 = function(xValues, yValues) {\n  var n = xValues.length;\n  for (var i = 0; i < n; i++) {\n    var x = xValues[i];\n    var y = yValues[i];\n    var denominator = this.a13 * x + this.a23 * y + this.a33;\n    xValues[i] = (this.a11 * x + this.a21 * y + this.a31) / denominator;\n    yValues[i] = (this.a12 * x + this.a22 * y + this.a32) / denominator;\n  }\n};\n\nPerspectiveTransform.prototype.buildAdjoint = function() {\n  // Adjoint is the transpose of the cofactor matrix:\n  return new PerspectiveTransform(this.a22 * this.a33 - this.a23 * this.a32, this.a23 * this.a31 - this.a21 * this.a33, this.a21 * this.a32 - this.a22 * this.a31, this.a13 * this.a32 - this.a12 * this.a33, this.a11 * this.a33 - this.a13 * this.a31, this.a12 * this.a31 - this.a11 * this.a32, this.a12 * this.a23 - this.a13 * this.a22, this.a13 * this.a21 - this.a11 * this.a23, this.a11 * this.a22 - this.a12 * this.a21);\n};\n\nPerspectiveTransform.prototype.times = function(other) {\n  return new PerspectiveTransform(this.a11 * other.a11 + this.a21 * other.a12 + this.a31 * other.a13, this.a11 * other.a21 + this.a21 * other.a22 + this.a31 * other.a23, this.a11 * other.a31 + this.a21 * other.a32 + this.a31 * other.a33, this.a12 * other.a11 + this.a22 * other.a12 + this.a32 * other.a13, this.a12 * other.a21 + this.a22 * other.a22 + this.a32 * other.a23, this.a12 * other.a31 + this.a22 * other.a32 + this.a32 * other.a33, this.a13 * other.a11 + this.a23 * other.a12 + this.a33 * other.a13, this.a13 * other.a21 + this.a23 * other.a22 + this.a33 * other.a23, this.a13 * other.a31 + this.a23 * other.a32 + this.a33 * other.a33);\n};\n\nPerspectiveTransform.quadrilateralToQuadrilateral = function(x0,  y0,  x1,  y1,  x2,  y2,  x3,  y3,  x0p,  y0p,  x1p,  y1p,  x2p,  y2p,  x3p,  y3p) {\n\n  var qToS = this.quadrilateralToSquare(x0, y0, x1, y1, x2, y2, x3, y3);\n  var sToQ = this.squareToQuadrilateral(x0p, y0p, x1p, y1p, x2p, y2p, x3p, y3p);\n  return sToQ.times(qToS);\n};\n\nPerspectiveTransform.squareToQuadrilateral = function(x0,  y0,  x1,  y1,  x2,  y2,  x3,  y3) {\n  var dy2 = y3 - y2;\n  var dy3 = y0 - y1 + y2 - y3;\n  if (dy2 == 0.0 && dy3 == 0.0) {\n    return new PerspectiveTransform(x1 - x0, x2 - x1, x0, y1 - y0, y2 - y1, y0, 0.0, 0.0, 1.0);\n  } else {\n    var dx1 = x1 - x2;\n    var dx2 = x3 - x2;\n    var dx3 = x0 - x1 + x2 - x3;\n    var dy1 = y1 - y2;\n    var denominator = dx1 * dy2 - dx2 * dy1;\n    var a13 = (dx3 * dy2 - dx2 * dy3) / denominator;\n    var a23 = (dx1 * dy3 - dx3 * dy1) / denominator;\n    return new PerspectiveTransform(x1 - x0 + a13 * x1, x3 - x0 + a23 * x3, x0, y1 - y0 + a13 * y1, y3 - y0 + a23 * y3, y0, a13, a23, 1.0);\n  }\n};\n\nPerspectiveTransform.quadrilateralToSquare = function(x0,  y0,  x1,  y1,  x2,  y2,  x3,  y3) {\n  // Here, the adjoint serves as the inverse:\n  return this.squareToQuadrilateral(x0, y0, x1, y1, x2, y2, x3, y3).buildAdjoint();\n};\n\nfunction DetectorResult(bits,  points) {\n  this.bits = bits;\n  this.points = points;\n}\n\nexport default function Detector(image) {\n  this.image = image;\n  this.resultPointCallback = null;\n}\n\nDetector.prototype.sizeOfBlackWhiteBlackRun = function(fromX,  fromY,  toX,  toY) {\n  // Mild variant of Bresenham's algorithm;\n  // see http://en.wikipedia.org/wiki/Bresenham's_line_algorithm\n  var steep = Math.abs(toY - fromY) > Math.abs(toX - fromX);\n  if (steep) {\n    var temp = fromX;\n    fromX = fromY;\n    fromY = temp;\n    temp = toX;\n    toX = toY;\n    toY = temp;\n  }\n\n  var dx = Math.abs(toX - fromX);\n  var dy = Math.abs(toY - fromY);\n  var error = -dx >> 1;\n  var ystep = fromY < toY ? 1 : -1;\n  var xstep = fromX < toX ? 1 : -1;\n  var state = 0; // In black pixels, looking for white, first or second time\n  for (var x = fromX, y = fromY; x != toX; x += xstep) {\n\n    var realX = steep ? y : x;\n    var realY = steep ? x : y;\n    if (state == 1) {\n      // In white pixels, looking for black\n      if (this.image.data[realX + realY * this.image.width]) {\n        state++;\n      }\n    } else {\n      if (!this.image.data[realX + realY * this.image.width]) {\n        state++;\n      }\n    }\n\n    if (state == 3) {\n      // Found black, white, black, and stumbled back onto white; done\n      var diffX = x - fromX;\n      var diffY = y - fromY;\n      return  Math.sqrt((diffX * diffX + diffY * diffY));\n    }\n    error += dy;\n    if (error > 0) {\n      if (y == toY) {\n        break;\n      }\n      y += ystep;\n      error -= dx;\n    }\n  }\n  var diffX2 = toX - fromX;\n  var diffY2 = toY - fromY;\n  return  Math.sqrt((diffX2 * diffX2 + diffY2 * diffY2));\n};\n\nDetector.prototype.sizeOfBlackWhiteBlackRunBothWays = function(fromX,  fromY,  toX,  toY) {\n\n  var result = this.sizeOfBlackWhiteBlackRun(fromX, fromY, toX, toY);\n\n  // Now count other way -- don't run off image though of course\n  var scale = 1.0;\n  var otherToX = fromX - (toX - fromX);\n  if (otherToX < 0) {\n    scale =  fromX /  (fromX - otherToX);\n    otherToX = 0;\n  } else if (otherToX >= this.image.width) {\n    scale =  (this.image.width - 1 - fromX) /  (otherToX - fromX);\n    otherToX = this.image.width - 1;\n  }\n  var otherToY = Math.floor(fromY - (toY - fromY) * scale);\n\n  scale = 1.0;\n  if (otherToY < 0) {\n    scale =  fromY /  (fromY - otherToY);\n    otherToY = 0;\n  } else if (otherToY >= this.image.height) {\n    scale =  (this.image.height - 1 - fromY) /  (otherToY - fromY);\n    otherToY = this.image.height - 1;\n  }\n  otherToX = Math.floor(fromX + (otherToX - fromX) * scale);\n\n  result += this.sizeOfBlackWhiteBlackRun(fromX, fromY, otherToX, otherToY);\n  return result - 1.0; // -1 because we counted the middle pixel twice\n};\n\nDetector.prototype.calculateModuleSizeOneWay = function(pattern,  otherPattern) {\n  var moduleSizeEst1 = this.sizeOfBlackWhiteBlackRunBothWays(Math.floor(pattern.X), Math.floor(pattern.Y), Math.floor(otherPattern.X), Math.floor(otherPattern.Y));\n  var moduleSizeEst2 = this.sizeOfBlackWhiteBlackRunBothWays(Math.floor(otherPattern.X), Math.floor(otherPattern.Y), Math.floor(pattern.X), Math.floor(pattern.Y));\n  if (isNaN(moduleSizeEst1)) {\n    return moduleSizeEst2 / 7.0;\n  }\n  if (isNaN(moduleSizeEst2)) {\n    return moduleSizeEst1 / 7.0;\n  }\n  // Average them, and divide by 7 since we've counted the width of 3 black modules,\n  // and 1 white and 1 black module on either side. Ergo, divide sum by 14.\n  return (moduleSizeEst1 + moduleSizeEst2) / 14.0;\n};\n\nDetector.prototype.calculateModuleSize = function(topLeft,  topRight,  bottomLeft) {\n  // Take the average\n  return (this.calculateModuleSizeOneWay(topLeft, topRight) + this.calculateModuleSizeOneWay(topLeft, bottomLeft)) / 2.0;\n};\n\nDetector.prototype.distance = function(pattern1,  pattern2) {\n  var xDiff = pattern1.X - pattern2.X;\n  var yDiff = pattern1.Y - pattern2.Y;\n  return  Math.sqrt((xDiff * xDiff + yDiff * yDiff));\n};\n\nDetector.prototype.computeDimension = function(topLeft,  topRight,  bottomLeft,  moduleSize) {\n  var tltrCentersDimension = Math.round(this.distance(topLeft, topRight) / moduleSize);\n  var tlblCentersDimension = Math.round(this.distance(topLeft, bottomLeft) / moduleSize);\n  var dimension = ((tltrCentersDimension + tlblCentersDimension) >> 1) + 7;\n  switch (dimension & 0x03) {\n  // mod 4\n  case 0:\n    dimension++;\n    break;\n  // 1? do nothing\n\n  case 2:\n    dimension--;\n    break;\n\n  case 3:\n    throw \"Error\";\n  }\n  return dimension;\n};\n\nDetector.prototype.findAlignmentInRegion = function(overallEstModuleSize,  estAlignmentX,  estAlignmentY,  allowanceFactor) {\n  // Look for an alignment pattern (3 modules in size) around where it\n  // should be\n  var allowance = Math.floor(allowanceFactor * overallEstModuleSize);\n  var alignmentAreaLeftX = Math.max(0, estAlignmentX - allowance);\n  var alignmentAreaRightX = Math.min(this.image.width - 1, estAlignmentX + allowance);\n  if (alignmentAreaRightX - alignmentAreaLeftX < overallEstModuleSize * 3) {\n    throw \"Error\";\n  }\n\n  var alignmentAreaTopY = Math.max(0, estAlignmentY - allowance);\n  var alignmentAreaBottomY = Math.min(this.image.height - 1, estAlignmentY + allowance);\n\n  var alignmentFinder = new AlignmentPatternFinder(this.image, alignmentAreaLeftX, alignmentAreaTopY, alignmentAreaRightX - alignmentAreaLeftX, alignmentAreaBottomY - alignmentAreaTopY, overallEstModuleSize, this.resultPointCallback);\n  return alignmentFinder.find();\n};\n\nDetector.prototype.createTransform = function(topLeft,  topRight,  bottomLeft, alignmentPattern, dimension) {\n  var dimMinusThree =  dimension - 3.5;\n  var bottomRightX;\n  var bottomRightY;\n  var sourceBottomRightX;\n  var sourceBottomRightY;\n  if (alignmentPattern != null) {\n    bottomRightX = alignmentPattern.X;\n    bottomRightY = alignmentPattern.Y;\n    sourceBottomRightX = sourceBottomRightY = dimMinusThree - 3.0;\n  } else {\n    // Don't have an alignment pattern, just make up the bottom-right point\n    bottomRightX = (topRight.X - topLeft.X) + bottomLeft.X;\n    bottomRightY = (topRight.Y - topLeft.Y) + bottomLeft.Y;\n    sourceBottomRightX = sourceBottomRightY = dimMinusThree;\n  }\n\n  var transform = PerspectiveTransform.quadrilateralToQuadrilateral(3.5, 3.5, dimMinusThree, 3.5, sourceBottomRightX, sourceBottomRightY, 3.5, dimMinusThree, topLeft.X, topLeft.Y, topRight.X, topRight.Y, bottomRightX, bottomRightY, bottomLeft.X, bottomLeft.Y);\n\n  return transform;\n};\n\nDetector.prototype.sampleGrid = function(image,  transform,  dimension) {\n\n  var sampler = GridSampler;\n  return sampler.sampleGrid3(image, dimension, transform);\n};\n\nDetector.prototype.processFinderPatternInfo = function(info) {\n\n  var topLeft = info.topLeft;\n  var topRight = info.topRight;\n  var bottomLeft = info.bottomLeft;\n\n  var moduleSize = this.calculateModuleSize(topLeft, topRight, bottomLeft);\n  if (moduleSize < 1.0) {\n    throw \"Error\";\n  }\n  var dimension = this.computeDimension(topLeft, topRight, bottomLeft, moduleSize);\n  var provisionalVersion = Version.getProvisionalVersionForDimension(dimension);\n  var modulesBetweenFPCenters = provisionalVersion.DimensionForVersion - 7;\n\n  var alignmentPattern = null;\n  // Anything above version 1 has an alignment pattern\n  if (provisionalVersion.alignmentPatternCenters.length > 0) {\n\n    // Guess where a \"bottom right\" finder pattern would have been\n    var bottomRightX = topRight.X - topLeft.X + bottomLeft.X;\n    var bottomRightY = topRight.Y - topLeft.Y + bottomLeft.Y;\n\n    // Estimate that alignment pattern is closer by 3 modules\n    // from \"bottom right\" to known top left location\n    var correctionToTopLeft = 1.0 - 3.0 /  modulesBetweenFPCenters;\n    var estAlignmentX = Math.floor(topLeft.X + correctionToTopLeft * (bottomRightX - topLeft.X));\n    var estAlignmentY = Math.floor(topLeft.Y + correctionToTopLeft * (bottomRightY - topLeft.Y));\n\n    // Kind of arbitrary -- expand search radius before giving up\n    for (var i = 4; i <= 16; i <<= 1) {\n      //try\n      //{\n      alignmentPattern = this.findAlignmentInRegion(moduleSize, estAlignmentX, estAlignmentY,  i);\n      break;\n      //}\n      //catch (re)\n      //{\n      // try next round\n      //}\n    }\n    // If we didn't find alignment pattern... well try anyway without it\n  }\n\n  var transform = this.createTransform(topLeft, topRight, bottomLeft, alignmentPattern, dimension);\n\n  var bits = this.sampleGrid(this.image, transform, dimension);\n\n  var points;\n  if (alignmentPattern == null) {\n    points = [bottomLeft, topLeft, topRight];\n  } else {\n    points = [bottomLeft, topLeft, topRight, alignmentPattern];\n  }\n  return new DetectorResult(bits, points);\n};\n\nDetector.prototype.detect = function() {\n  var info =  new FinderPatternFinder().findFinderPattern(this.image);\n\n  return this.processFinderPatternInfo(info);\n};\n", "/*\n  Ported to JavaScript by <PERSON><PERSON> 2011\n\n  <EMAIL>, www.lazarsoft.info\n\n*/\n\n/*\n*\n* Copyright 2007 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n\nexport default function GF256Poly(field,  coefficients) {\n  if (coefficients == null || coefficients.length == 0) {\n    throw \"System.ArgumentException\";\n  }\n  this.field = field;\n  var coefficientsLength = coefficients.length;\n  if (coefficientsLength > 1 && coefficients[0] == 0) {\n    // Leading term must be non-zero for anything except the constant polynomial \"0\"\n    var firstNonZero = 1;\n    while (firstNonZero < coefficientsLength && coefficients[firstNonZero] == 0) {\n      firstNonZero++;\n    }\n    if (firstNonZero == coefficientsLength) {\n      this.coefficients = field.Zero.coefficients;\n    } else {\n      this.coefficients = new Array(coefficientsLength - firstNonZero);\n      for (var i = 0; i < this.coefficients.length; i++) this.coefficients[i] = 0;\n      for (var ci = 0; ci < this.coefficients.length; ci++) this.coefficients[ci] = coefficients[firstNonZero + ci];\n    }\n  } else {\n    this.coefficients = coefficients;\n  }\n}\n\nObject.defineProperty(GF256Poly.prototype, \"Zero\", {\n  get: function() {\n    return this.coefficients[0] == 0;\n  }\n});\n\nObject.defineProperty(GF256Poly.prototype, \"Degree\", {\n  get: function() {\n    return this.coefficients.length - 1;\n  }\n});\n\nGF256Poly.prototype.getCoefficient = function(degree) {\n  return this.coefficients[this.coefficients.length - 1 - degree];\n};\n\nGF256Poly.prototype.evaluateAt = function(a) {\n  if (a == 0) {\n    // Just return the x^0 coefficient\n    return this.getCoefficient(0);\n  }\n  var size = this.coefficients.length;\n  if (a == 1) {\n    // Just the sum of the coefficients\n    var result = 0;\n    for (var i = 0; i < size; i++) {\n      result = this.field.addOrSubtract(result, this.coefficients[i]);\n    }\n    return result;\n  }\n  var result2 = this.coefficients[0];\n  for (var i = 1; i < size; i++) {\n    result2 = this.field.addOrSubtract(this.field.multiply(a, result2), this.coefficients[i]);\n  }\n  return result2;\n};\n\nGF256Poly.prototype.addOrSubtract = function(other) {\n  if (this.field != other.field) {\n    throw \"GF256Polys do not have same GF256 field\";\n  }\n  if (this.Zero) {\n    return other;\n  }\n  if (other.Zero) {\n    return this;\n  }\n\n  var smallerCoefficients = this.coefficients;\n  var largerCoefficients = other.coefficients;\n  if (smallerCoefficients.length > largerCoefficients.length) {\n    var temp = smallerCoefficients;\n    smallerCoefficients = largerCoefficients;\n    largerCoefficients = temp;\n  }\n  var sumDiff = new Array(largerCoefficients.length);\n  var lengthDiff = largerCoefficients.length - smallerCoefficients.length;\n  // Copy high-order terms only found in higher-degree polynomial's coefficients\n  for (var ci = 0; ci < lengthDiff; ci++)sumDiff[ci] = largerCoefficients[ci];\n\n  for (var i = lengthDiff; i < largerCoefficients.length; i++) {\n    sumDiff[i] = this.field.addOrSubtract(smallerCoefficients[i - lengthDiff], largerCoefficients[i]);\n  }\n\n  return new GF256Poly(this.field, sumDiff);\n};\n\nGF256Poly.prototype.multiply1 = function(other) {\n  if (this.field != other.field) {\n    throw \"GF256Polys do not have same GF256 field\";\n  }\n  if (this.Zero || other.Zero) {\n    return this.field.Zero;\n  }\n  var aCoefficients = this.coefficients;\n  var aLength = aCoefficients.length;\n  var bCoefficients = other.coefficients;\n  var bLength = bCoefficients.length;\n  var product = new Array(aLength + bLength - 1);\n  for (var i = 0; i < aLength; i++) {\n    var aCoeff = aCoefficients[i];\n    for (var j = 0; j < bLength; j++) {\n      product[i + j] = this.field.addOrSubtract(product[i + j], this.field.multiply(aCoeff, bCoefficients[j]));\n    }\n  }\n  return new GF256Poly(this.field, product);\n};\n\nGF256Poly.prototype.multiply2 = function(scalar) {\n  if (scalar == 0) {\n    return this.field.Zero;\n  }\n  if (scalar == 1) {\n    return this;\n  }\n  var size = this.coefficients.length;\n  var product = new Array(size);\n  for (var i = 0; i < size; i++) {\n    product[i] = this.field.multiply(this.coefficients[i], scalar);\n  }\n  return new GF256Poly(this.field, product);\n};\n\nGF256Poly.prototype.multiplyByMonomial = function(degree,  coefficient) {\n  if (degree < 0) {\n    throw \"System.ArgumentException\";\n  }\n  if (coefficient == 0) {\n    return this.field.Zero;\n  }\n  var size = this.coefficients.length;\n  var product = new Array(size + degree);\n  for (var i = 0; i < product.length; i++)product[i] = 0;\n  for (var i = 0; i < size; i++) {\n    product[i] = this.field.multiply(this.coefficients[i], coefficient);\n  }\n  return new GF256Poly(this.field, product);\n};\n\nGF256Poly.prototype.divide = function(other) {\n  if (this.field != other.field) {\n    throw \"GF256Polys do not have same GF256 field\";\n  }\n  if (other.Zero) {\n    throw \"Divide by 0\";\n  }\n\n  var quotient = this.field.Zero;\n  var remainder = this;\n\n  var denominatorLeadingTerm = other.getCoefficient(other.Degree);\n  var inverseDenominatorLeadingTerm = this.field.inverse(denominatorLeadingTerm);\n\n  while (remainder.Degree >= other.Degree && !remainder.Zero) {\n    var degreeDifference = remainder.Degree - other.Degree;\n    var scale = this.field.multiply(remainder.getCoefficient(remainder.Degree), inverseDenominatorLeadingTerm);\n    var term = other.multiplyByMonomial(degreeDifference, scale);\n    var iterationQuotient = this.field.buildMonomial(degreeDifference, scale);\n    quotient = quotient.addOrSubtract(iterationQuotient);\n    remainder = remainder.addOrSubtract(term);\n  }\n\n  return [quotient, remainder];\n};\n", "/*\n  Ported to JavaScript by <PERSON><PERSON> 2011\n\n  <EMAIL>, www.lazarsoft.info\n\n*/\n\n/*\n*\n* Copyright 2007 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n\nimport GF256Poly from './gf256poly';\n\nexport default function GF256(primitive) {\n  this.expTable = new Array(256);\n  this.logTable = new Array(256);\n  var x = 1;\n  for (var i = 0; i < 256; i++) {\n    this.expTable[i] = x;\n    x <<= 1; // x = x * 2; we're assuming the generator alpha is 2\n    if (x >= 0x100) {\n      x ^= primitive;\n    }\n  }\n  for (var i = 0; i < 255; i++) {\n    this.logTable[this.expTable[i]] = i;\n  }\n  // logTable[0] == 0 but this should never be used\n  var at0 = new Array(1); at0[0] = 0;\n  this.zero = new GF256Poly(this, new Array(at0));\n  var at1 = new Array(1); at1[0] = 1;\n  this.one = new GF256Poly(this, new Array(at1));\n}\n\nObject.defineProperty(GF256.prototype, \"Zero\", {\n  get: function() {\n    return this.zero;\n  }\n});\n\nObject.defineProperty(GF256.prototype, \"One\", {\n  get: function() {\n    return this.one;\n  }\n});\n\nGF256.prototype.buildMonomial = function(degree,  coefficient) {\n  if (degree < 0) {\n    throw \"System.ArgumentException\";\n  }\n  if (coefficient == 0) {\n    return this.zero;\n  }\n  var coefficients = new Array(degree + 1);\n  for (var i = 0; i < coefficients.length; i++)coefficients[i] = 0;\n  coefficients[0] = coefficient;\n  return new GF256Poly(this, coefficients);\n};\n\nGF256.prototype.exp = function(a) {\n  return this.expTable[a];\n};\n\nGF256.prototype.log = function(a) {\n  if (a == 0) {\n    throw \"System.ArgumentException\";\n  }\n  return this.logTable[a];\n};\n\nGF256.prototype.inverse = function(a) {\n  if (a == 0) {\n    throw \"System.ArithmeticException\";\n  }\n  return this.expTable[255 - this.logTable[a]];\n};\n\nGF256.prototype.addOrSubtract = function(a,  b) {\n  return a ^ b;\n};\n\nGF256.prototype.multiply = function(a,  b) {\n  if (a == 0 || b == 0) {\n    return 0;\n  }\n  if (a == 1) {\n    return b;\n  }\n  if (b == 1) {\n    return a;\n  }\n  return this.expTable[(this.logTable[a] + this.logTable[b]) % 255];\n};\n\nGF256.QR_CODE_FIELD = new GF256(0x011D);\nGF256.DATA_MATRIX_FIELD = new GF256(0x012D);\n", "/*\n  Ported to JavaScript by <PERSON><PERSON> 2011\n\n  <EMAIL>, www.lazarsoft.info\n\n*/\n\n/*\n*\n* Copyright 2007 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n\nimport GF256 from './gf256';\nimport GF256Poly from './gf256poly';\n\nexport default function ReedSolomonDecoder(field) {\n  this.field = field;\n}\n\nReedSolomonDecoder.prototype.decode = function(received, twoS) {\n  var poly = new GF256Poly(this.field, received);\n  var syndromeCoefficients = new Array(twoS);\n  for (var i = 0; i < syndromeCoefficients.length; i++)syndromeCoefficients[i] = 0;\n  var dataMatrix = false;//this.field.Equals(GF256.DATA_MATRIX_FIELD);\n  var noError = true;\n  for (var i = 0; i < twoS; i++) {\n    // Thanks to sanfordsquires for this fix:\n    var _eval = poly.evaluateAt(this.field.exp(dataMatrix ? i + 1 : i));\n    syndromeCoefficients[syndromeCoefficients.length - 1 - i] = _eval;\n    if (_eval != 0) {\n      noError = false;\n    }\n  }\n  if (noError) {\n    return;\n  }\n  var syndrome = new GF256Poly(this.field, syndromeCoefficients);\n  var sigmaOmega = this.runEuclideanAlgorithm(this.field.buildMonomial(twoS, 1), syndrome, twoS);\n  var sigma = sigmaOmega[0];\n  var omega = sigmaOmega[1];\n  var errorLocations = this.findErrorLocations(sigma);\n  var errorMagnitudes = this.findErrorMagnitudes(omega, errorLocations, dataMatrix);\n  for (var i = 0; i < errorLocations.length; i++) {\n    var position = received.length - 1 - this.field.log(errorLocations[i]);\n    if (position < 0) {\n      throw \"ReedSolomonException Bad error location\";\n    }\n    received[position] = GF256.prototype.addOrSubtract(received[position], errorMagnitudes[i]);\n  }\n};\n\nReedSolomonDecoder.prototype.runEuclideanAlgorithm = function(a,  b,  R) {\n  // Assume a's degree is >= b's\n  if (a.Degree < b.Degree) {\n    var temp = a;\n    a = b;\n    b = temp;\n  }\n\n  var rLast = a;\n  var r = b;\n  var sLast = this.field.One;\n  var s = this.field.Zero;\n  var tLast = this.field.Zero;\n  var t = this.field.One;\n\n  // Run Euclidean algorithm until r's degree is less than R/2\n  while (r.Degree >= Math.floor(R / 2)) {\n    var rLastLast = rLast;\n    var sLastLast = sLast;\n    var tLastLast = tLast;\n    rLast = r;\n    sLast = s;\n    tLast = t;\n\n    // Divide rLastLast by rLast, with quotient in q and remainder in r\n    if (rLast.Zero) {\n      // Oops, Euclidean algorithm already terminated?\n      throw \"r_{i-1} was zero\";\n    }\n    r = rLastLast;\n    var q = this.field.Zero;\n    var denominatorLeadingTerm = rLast.getCoefficient(rLast.Degree);\n    var dltInverse = this.field.inverse(denominatorLeadingTerm);\n    while (r.Degree >= rLast.Degree && !r.Zero) {\n      var degreeDiff = r.Degree - rLast.Degree;\n      var scale = this.field.multiply(r.getCoefficient(r.Degree), dltInverse);\n      q = q.addOrSubtract(this.field.buildMonomial(degreeDiff, scale));\n      r = r.addOrSubtract(rLast.multiplyByMonomial(degreeDiff, scale));\n    }\n\n    s = q.multiply1(sLast).addOrSubtract(sLastLast);\n    t = q.multiply1(tLast).addOrSubtract(tLastLast);\n  }\n\n  var sigmaTildeAtZero = t.getCoefficient(0);\n  if (sigmaTildeAtZero == 0) {\n    throw \"ReedSolomonException sigmaTilde(0) was zero\";\n  }\n\n  var inverse = this.field.inverse(sigmaTildeAtZero);\n  var sigma = t.multiply2(inverse);\n  var omega = r.multiply2(inverse);\n  return [sigma, omega];\n};\n\nReedSolomonDecoder.prototype.findErrorLocations = function(errorLocator) {\n  // This is a direct application of Chien's search\n  var numErrors = errorLocator.Degree;\n  if (numErrors == 1) {\n    // shortcut\n    return new Array(errorLocator.getCoefficient(1));\n  }\n  var result = new Array(numErrors);\n  var e = 0;\n  for (var i = 1; i < 256 && e < numErrors; i++) {\n    if (errorLocator.evaluateAt(i) == 0) {\n      result[e] = this.field.inverse(i);\n      e++;\n    }\n  }\n  if (e != numErrors) {\n    throw \"Error locator degree does not match number of roots\";\n  }\n  return result;\n};\n\nReedSolomonDecoder.prototype.findErrorMagnitudes = function(errorEvaluator, errorLocations, dataMatrix) {\n  // This is directly applying Forney's Formula\n  var s = errorLocations.length;\n  var result = new Array(s);\n  for (var i = 0; i < s; i++) {\n    var xiInverse = this.field.inverse(errorLocations[i]);\n    var denominator = 1;\n    for (var j = 0; j < s; j++) {\n      if (i != j) {\n        denominator = this.field.multiply(denominator, GF256.prototype.addOrSubtract(1, this.field.multiply(errorLocations[j], xiInverse)));\n      }\n    }\n    result[i] = this.field.multiply(errorEvaluator.evaluateAt(xiInverse), this.field.inverse(denominator));\n    // Thanks to sanfordsquires for this fix:\n    if (dataMatrix) {\n      result[i] = this.field.multiply(result[i], xiInverse);\n    }\n  }\n  return result;\n};\n", "/*\n  Ported to JavaScript by <PERSON><PERSON> 2011\n\n  <EMAIL>, www.lazarsoft.info\n\n*/\n\n/*\n*\n* Copyright 2007 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n\nimport {URShift} from './qrcode';\n\nvar DataMask = {};\n\nDataMask.forReference = function(reference) {\n  if (reference < 0 || reference > 7) {\n    throw \"System.ArgumentException\";\n  }\n  return DataMask.DATA_MASKS[reference];\n};\n\nfunction DataMask000() {\n  this.unmaskBitMatrix = function(bits,  dimension) {\n    for (var i = 0; i < dimension; i++) {\n      for (var j = 0; j < dimension; j++) {\n        if (this.isMasked(i, j)) {\n          bits.flip(j, i);\n        }\n      }\n    }\n  };\n  this.isMasked = function(i,  j) {\n    return ((i + j) & 0x01) == 0;\n  };\n}\n\nfunction DataMask001() {\n  this.unmaskBitMatrix = function(bits,  dimension) {\n    for (var i = 0; i < dimension; i++) {\n      for (var j = 0; j < dimension; j++) {\n        if (this.isMasked(i, j)) {\n          bits.flip(j, i);\n        }\n      }\n    }\n  };\n  this.isMasked = function(i,  j) {\n    return (i & 0x01) == 0;\n  };\n}\n\nfunction DataMask010() {\n  this.unmaskBitMatrix = function(bits,  dimension) {\n    for (var i = 0; i < dimension; i++) {\n      for (var j = 0; j < dimension; j++) {\n        if (this.isMasked(i, j)) {\n          bits.flip(j, i);\n        }\n      }\n    }\n  };\n  this.isMasked = function(i,  j) {\n    return j % 3 == 0;\n  };\n}\n\nfunction DataMask011() {\n  this.unmaskBitMatrix = function(bits,  dimension) {\n    for (var i = 0; i < dimension; i++) {\n      for (var j = 0; j < dimension; j++) {\n        if (this.isMasked(i, j)) {\n          bits.flip(j, i);\n        }\n      }\n    }\n  };\n  this.isMasked = function(i,  j) {\n    return (i + j) % 3 == 0;\n  };\n}\n\nfunction DataMask100() {\n  this.unmaskBitMatrix = function(bits,  dimension) {\n    for (var i = 0; i < dimension; i++) {\n      for (var j = 0; j < dimension; j++) {\n        if (this.isMasked(i, j)) {\n          bits.flip(j, i);\n        }\n      }\n    }\n  };\n  this.isMasked = function(i,  j) {\n    return (((URShift(i, 1)) + (j / 3)) & 0x01) == 0;\n  };\n}\n\nfunction DataMask101() {\n  this.unmaskBitMatrix = function(bits,  dimension) {\n    for (var i = 0; i < dimension; i++) {\n      for (var j = 0; j < dimension; j++) {\n        if (this.isMasked(i, j)) {\n          bits.flip(j, i);\n        }\n      }\n    }\n  };\n  this.isMasked = function(i,  j) {\n    var temp = i * j;\n    return (temp & 0x01) + (temp % 3) == 0;\n  };\n}\n\nfunction DataMask110() {\n  this.unmaskBitMatrix = function(bits,  dimension) {\n    for (var i = 0; i < dimension; i++) {\n      for (var j = 0; j < dimension; j++) {\n        if (this.isMasked(i, j)) {\n          bits.flip(j, i);\n        }\n      }\n    }\n  };\n  this.isMasked = function(i,  j) {\n    var temp = i * j;\n    return (((temp & 0x01) + (temp % 3)) & 0x01) == 0;\n  };\n}\nfunction DataMask111() {\n  this.unmaskBitMatrix = function(bits,  dimension) {\n    for (var i = 0; i < dimension; i++) {\n      for (var j = 0; j < dimension; j++) {\n        if (this.isMasked(i, j)) {\n          bits.flip(j, i);\n        }\n      }\n    }\n  };\n  this.isMasked = function(i,  j) {\n    return ((((i + j) & 0x01) + ((i * j) % 3)) & 0x01) == 0;\n  };\n}\n\nDataMask.DATA_MASKS = [new DataMask000(), new DataMask001(), new DataMask010(), new DataMask011(), new DataMask100(), new DataMask101(), new DataMask110(), new DataMask111()];\n\nexport default DataMask;\n", "/*\n  Ported to JavaScript by <PERSON><PERSON> 2011\n\n  <EMAIL>, www.lazarsoft.info\n\n*/\n\n/*\n*\n* Copyright 2007 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n\nimport FormatInformation from './formatinf';\nimport Version from './version';\nimport DataMask from './datamask';\n\nexport default function BitMatrixParser(bitMatrix) {\n  var dimension = bitMatrix.Dimension;\n  if (dimension < 21 || (dimension & 0x03) != 1) {\n    throw \"Error BitMatrixParser\";\n  }\n  this.bitMatrix = bitMatrix;\n  this.parsedVersion = null;\n  this.parsedFormatInfo = null;\n}\n\nBitMatrixParser.prototype.copyBit = function(i,  j,  versionBits) {\n  return this.bitMatrix.get_Renamed(i, j) ? (versionBits << 1) | 0x1 : versionBits << 1;\n};\n\nBitMatrixParser.prototype.readFormatInformation = function() {\n  if (this.parsedFormatInfo != null) {\n    return this.parsedFormatInfo;\n  }\n\n  // Read top-left format info bits\n  var formatInfoBits = 0;\n  for (var i = 0; i < 6; i++) {\n    formatInfoBits = this.copyBit(i, 8, formatInfoBits);\n  }\n  // .. and skip a bit in the timing pattern ...\n  formatInfoBits = this.copyBit(7, 8, formatInfoBits);\n  formatInfoBits = this.copyBit(8, 8, formatInfoBits);\n  formatInfoBits = this.copyBit(8, 7, formatInfoBits);\n  // .. and skip a bit in the timing pattern ...\n  for (var j = 5; j >= 0; j--) {\n    formatInfoBits = this.copyBit(8, j, formatInfoBits);\n  }\n\n  this.parsedFormatInfo = FormatInformation.decodeFormatInformation(formatInfoBits);\n  if (this.parsedFormatInfo != null) {\n    return this.parsedFormatInfo;\n  }\n\n  // Hmm, failed. Try the top-right/bottom-left pattern\n  var dimension = this.bitMatrix.Dimension;\n  formatInfoBits = 0;\n  var iMin = dimension - 8;\n  for (var i = dimension - 1; i >= iMin; i--) {\n    formatInfoBits = this.copyBit(i, 8, formatInfoBits);\n  }\n  for (var j = dimension - 7; j < dimension; j++) {\n    formatInfoBits = this.copyBit(8, j, formatInfoBits);\n  }\n\n  this.parsedFormatInfo = FormatInformation.decodeFormatInformation(formatInfoBits);\n  if (this.parsedFormatInfo != null) {\n    return this.parsedFormatInfo;\n  }\n  throw \"Error readFormatInformation\";\n};\n\nBitMatrixParser.prototype.readVersion = function() {\n  if (this.parsedVersion != null) {\n    return this.parsedVersion;\n  }\n\n  var dimension = this.bitMatrix.Dimension;\n\n  var provisionalVersion = (dimension - 17) >> 2;\n  if (provisionalVersion <= 6) {\n    return Version.getVersionForNumber(provisionalVersion);\n  }\n\n  // Read top-right version info: 3 wide by 6 tall\n  var versionBits = 0;\n  var ijMin = dimension - 11;\n  for (var j = 5; j >= 0; j--) {\n    for (var i = dimension - 9; i >= ijMin; i--) {\n      versionBits = this.copyBit(i, j, versionBits);\n    }\n  }\n\n  this.parsedVersion = Version.decodeVersionInformation(versionBits);\n  if (this.parsedVersion != null && this.parsedVersion.DimensionForVersion == dimension) {\n    return this.parsedVersion;\n  }\n\n  // Hmm, failed. Try bottom left: 6 wide by 3 tall\n  versionBits = 0;\n  for (var i = 5; i >= 0; i--) {\n    for (var j = dimension - 9; j >= ijMin; j--) {\n      versionBits = this.copyBit(i, j, versionBits);\n    }\n  }\n\n  this.parsedVersion = Version.decodeVersionInformation(versionBits);\n  if (this.parsedVersion != null && this.parsedVersion.DimensionForVersion == dimension) {\n    return this.parsedVersion;\n  }\n  throw \"Error readVersion\";\n};\n\nBitMatrixParser.prototype.readCodewords = function() {\n  var formatInfo = this.readFormatInformation();\n  var version = this.readVersion();\n\n  // Get the data mask for the format used in this QR Code. This will exclude\n  // some bits from reading as we wind through the bit matrix.\n  var dataMask = DataMask.forReference(formatInfo.dataMask);\n  var dimension = this.bitMatrix.Dimension;\n  dataMask.unmaskBitMatrix(this.bitMatrix, dimension);\n\n  var functionPattern = version.buildFunctionPattern();\n\n  var readingUp = true;\n  var result = new Array(version.totalCodewords);\n  var resultOffset = 0;\n  var currentByte = 0;\n  var bitsRead = 0;\n  // Read columns in pairs, from right to left\n  for (var j = dimension - 1; j > 0; j -= 2) {\n    if (j == 6) {\n      // Skip whole column with vertical alignment pattern;\n      // saves time and makes the other code proceed more cleanly\n      j--;\n    }\n    // Read alternatingly from bottom to top then top to bottom\n    for (var count = 0; count < dimension; count++) {\n      var i = readingUp ? dimension - 1 - count : count;\n      for (var col = 0; col < 2; col++) {\n        // Ignore bits covered by the function pattern\n        if (!functionPattern.get_Renamed(j - col, i)) {\n          // Read a bit\n          bitsRead++;\n          currentByte <<= 1;\n          if (this.bitMatrix.get_Renamed(j - col, i)) {\n            currentByte |= 1;\n          }\n          // If we've made a whole byte, save it off\n          if (bitsRead == 8) {\n            result[resultOffset++] =  currentByte;\n            bitsRead = 0;\n            currentByte = 0;\n          }\n        }\n      }\n    }\n    readingUp ^= true; // readingUp = !readingUp; // switch directions\n  }\n  if (resultOffset != version.totalCodewords) {\n    throw \"Error readCodewords\";\n  }\n  return result;\n};\n", "/*\n  Ported to JavaScript by <PERSON><PERSON> 2011\n\n  <EMAIL>, www.lazarsoft.info\n\n*/\n\n/*\n*\n* Copyright 2007 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n\n\nexport default function DataBlock(numDataCodewords,  codewords) {\n  this.numDataCodewords = numDataCodewords;\n  this.codewords = codewords;\n}\n\nDataBlock.getDataBlocks = function(rawCodewords,  version,  ecLevel) {\n\n  if (rawCodewords.length != version.totalCodewords) {\n    throw \"ArgumentException\";\n  }\n\n  // Figure out the number and size of data blocks used by this version and\n  // error correction level\n  var ecBlocks = version.getECBlocksForLevel(ecLevel);\n\n  // First count the total number of data blocks\n  var totalBlocks = 0;\n  var ecBlockArray = ecBlocks.getECBlocks();\n  for (var i = 0; i < ecBlockArray.length; i++) {\n    totalBlocks += ecBlockArray[i].count;\n  }\n\n  // Now establish DataBlocks of the appropriate size and number of data codewords\n  var result = new Array(totalBlocks);\n  var numResultBlocks = 0;\n  for (var j = 0; j < ecBlockArray.length; j++) {\n    var ecBlock = ecBlockArray[j];\n    for (var i = 0; i < ecBlock.count; i++) {\n      var numDataCodewords = ecBlock.dataCodewords;\n      var numBlockCodewords = ecBlocks.ecCodewordsPerBlock + numDataCodewords;\n      result[numResultBlocks++] = new DataBlock(numDataCodewords, new Array(numBlockCodewords));\n    }\n  }\n\n  // All blocks have the same amount of data, except that the last n\n  // (where n may be 0) have 1 more byte. Figure out where these start.\n  var shorterBlocksTotalCodewords = result[0].codewords.length;\n  var longerBlocksStartAt = result.length - 1;\n  while (longerBlocksStartAt >= 0) {\n    var numCodewords = result[longerBlocksStartAt].codewords.length;\n    if (numCodewords == shorterBlocksTotalCodewords) {\n      break;\n    }\n    longerBlocksStartAt--;\n  }\n  longerBlocksStartAt++;\n\n  var shorterBlocksNumDataCodewords = shorterBlocksTotalCodewords - ecBlocks.ecCodewordsPerBlock;\n  // The last elements of result may be 1 element longer;\n  // first fill out as many elements as all of them have\n  var rawCodewordsOffset = 0;\n  for (var i = 0; i < shorterBlocksNumDataCodewords; i++) {\n    for (var j = 0; j < numResultBlocks; j++) {\n      result[j].codewords[i] = rawCodewords[rawCodewordsOffset++];\n    }\n  }\n  // Fill out the last data block in the longer ones\n  for (var j = longerBlocksStartAt; j < numResultBlocks; j++) {\n    result[j].codewords[shorterBlocksNumDataCodewords] = rawCodewords[rawCodewordsOffset++];\n  }\n  // Now add in error correction blocks\n  var max = result[0].codewords.length;\n  for (var i = shorterBlocksNumDataCodewords; i < max; i++) {\n    for (var j = 0; j < numResultBlocks; j++) {\n      var iOffset = j < longerBlocksStartAt ? i : i + 1;\n      result[j].codewords[iOffset] = rawCodewords[rawCodewordsOffset++];\n    }\n  }\n  return result;\n};\n", "/*\n  Ported to JavaScript by <PERSON><PERSON> 2011\n\n  <EMAIL>, www.lazarsoft.info\n\n*/\n\n/*\n*\n* Copyright 2007 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n\nimport {qrcode} from './qrcode';\n\nexport default function QRCodeDataBlockReader(blocks,  version,  numErrorCorrectionCode) {\n  this.blockPointer = 0;\n  this.bitPointer = 7;\n  this.dataLength = 0;\n  this.blocks = blocks;\n  this.numErrorCorrectionCode = numErrorCorrectionCode;\n  if (version <= 9)\n    this.dataLengthMode = 0;\n  else if (version >= 10 && version <= 26)\n    this.dataLengthMode = 1;\n  else if (version >= 27 && version <= 40)\n    this.dataLengthMode = 2;\n}\n\nQRCodeDataBlockReader.prototype.getNextBits = function(numBits) {\n  var bits = 0;\n  if (numBits < this.bitPointer + 1) {\n    // next word fits into current data block\n    var mask = 0;\n    for (var i = 0; i < numBits; i++) {\n      mask += (1 << i);\n    }\n    mask <<= (this.bitPointer - numBits + 1);\n\n    bits = (this.blocks[this.blockPointer] & mask) >> (this.bitPointer - numBits + 1);\n    this.bitPointer -= numBits;\n    return bits;\n  } else if (numBits < this.bitPointer + 1 + 8) {\n    // next word crosses 2 data blocks\n    var mask1 = 0;\n    for (var i = 0; i < this.bitPointer + 1; i++) {\n      mask1 += (1 << i);\n    }\n    bits = (this.blocks[this.blockPointer] & mask1) << (numBits - (this.bitPointer + 1));\n    this.blockPointer++;\n    bits += ((this.blocks[this.blockPointer]) >> (8 - (numBits - (this.bitPointer + 1))));\n\n    this.bitPointer = this.bitPointer - numBits % 8;\n    if (this.bitPointer < 0) {\n      this.bitPointer = 8 + this.bitPointer;\n    }\n    return bits;\n  } else if (numBits < this.bitPointer + 1 + 16) {\n    // next word crosses 3 data blocks\n    var mask1 = 0; // mask of first block\n    var mask3 = 0; // mask of 3rd block\n    //bitPointer + 1 : number of bits of the 1st block\n    //8 : number of the 2nd block (note that use already 8bits because next word uses 3 data blocks)\n    //numBits - (bitPointer + 1 + 8) : number of bits of the 3rd block\n    for (var i = 0; i < this.bitPointer + 1; i++) {\n      mask1 += (1 << i);\n    }\n    var bitsFirstBlock = (this.blocks[this.blockPointer] & mask1) << (numBits - (this.bitPointer + 1));\n    this.blockPointer++;\n\n    var bitsSecondBlock = this.blocks[this.blockPointer] << (numBits - (this.bitPointer + 1 + 8));\n    this.blockPointer++;\n\n    for (var i = 0; i < numBits - (this.bitPointer + 1 + 8); i++) {\n      mask3 += (1 << i);\n    }\n    mask3 <<= 8 - (numBits - (this.bitPointer + 1 + 8));\n    var bitsThirdBlock = (this.blocks[this.blockPointer] & mask3) >> (8 - (numBits - (this.bitPointer + 1 + 8)));\n\n    bits = bitsFirstBlock + bitsSecondBlock + bitsThirdBlock;\n    this.bitPointer = this.bitPointer - (numBits - 8) % 8;\n    if (this.bitPointer < 0) {\n      this.bitPointer = 8 + this.bitPointer;\n    }\n    return bits;\n  } else {\n    return 0;\n  }\n};\n\nQRCodeDataBlockReader.prototype.NextMode = function() {\n  if ((this.blockPointer > this.blocks.length - this.numErrorCorrectionCode - 2))\n    return 0;\n  else\n    return this.getNextBits(4);\n};\n\nQRCodeDataBlockReader.prototype.getDataLength = function(modeIndicator) {\n  var index = 0;\n  while (true) {\n    if ((modeIndicator >> index) == 1)\n      break;\n    index++;\n  }\n\n  return this.getNextBits(qrcode.sizeOfDataLengthInfo[this.dataLengthMode][index]);\n};\n\nQRCodeDataBlockReader.prototype.getRomanAndFigureString = function(dataLength) {\n  var length = dataLength;\n  var intData = 0;\n  var strData = \"\";\n  var tableRomanAndFigure = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', ' ', '$', '%', '*', '+', '-', '.', '/', ':'];\n  do {\n    if (length > 1) {\n      intData = this.getNextBits(11);\n      var firstLetter = Math.floor(intData / 45);\n      var secondLetter = intData % 45;\n      strData += tableRomanAndFigure[firstLetter];\n      strData += tableRomanAndFigure[secondLetter];\n      length -= 2;\n    } else if (length == 1) {\n      intData = this.getNextBits(6);\n      strData += tableRomanAndFigure[intData];\n      length -= 1;\n    }\n  }\n  while (length > 0);\n\n  return strData;\n};\n\nQRCodeDataBlockReader.prototype.getFigureString = function(dataLength) {\n  var length = dataLength;\n  var intData = 0;\n  var strData = \"\";\n  do {\n    if (length >= 3) {\n      intData = this.getNextBits(10);\n      if (intData < 100)\n        strData += \"0\";\n      if (intData < 10)\n        strData += \"0\";\n      length -= 3;\n    } else if (length == 2) {\n      intData = this.getNextBits(7);\n      if (intData < 10)\n        strData += \"0\";\n      length -= 2;\n    } else if (length == 1) {\n      intData = this.getNextBits(4);\n      length -= 1;\n    }\n    strData += intData;\n  }\n  while (length > 0);\n\n  return strData;\n};\n\nQRCodeDataBlockReader.prototype.get8bitByteArray = function(dataLength) {\n  var length = dataLength;\n  var intData = 0;\n  var output = [];\n\n  do {\n    intData = this.getNextBits(8);\n    output.push(intData);\n    length--;\n  }\n  while (length > 0);\n  return output;\n};\n\nQRCodeDataBlockReader.prototype.getKanjiString = function(dataLength) {\n  var length = dataLength;\n  var intData = 0;\n  var unicodeString = \"\";\n  do {\n    intData = this.getNextBits(13);\n    var lowerByte = intData % 0xC0;\n    var higherByte = intData / 0xC0;\n\n    var tempWord = (higherByte << 8) + lowerByte;\n    var shiftjisWord = 0;\n    if (tempWord + 0x8140 <= 0x9FFC) {\n      // between 8140 - 9FFC on Shift_JIS character set\n      shiftjisWord = tempWord + 0x8140;\n    } else {\n      // between E040 - EBBF on Shift_JIS character set\n      shiftjisWord = tempWord + 0xC140;\n    }\n\n    unicodeString += String.fromCharCode(shiftjisWord);\n    length--;\n  }\n  while (length > 0);\n\n\n  return unicodeString;\n};\n\nObject.defineProperty(QRCodeDataBlockReader.prototype, \"DataByte\", {\n  get: function() {\n    var output = [];\n    var MODE_NUMBER = 1;\n    var MODE_ROMAN_AND_NUMBER = 2;\n    var MODE_8BIT_BYTE = 4;\n    var MODE_KANJI = 8;\n    do {\n      var mode = this.NextMode();\n      if (mode == 0) {\n        if (output.length > 0)\n          break;\n        else\n          throw \"Empty data block\";\n      }\n      //if (mode != 1 && mode != 2 && mode != 4 && mode != 8)\n      //}\n      if (mode != MODE_NUMBER && mode != MODE_ROMAN_AND_NUMBER && mode != MODE_8BIT_BYTE && mode != MODE_KANJI && mode != 7) {\n        /*          canvas.println(\"Invalid mode: \" + mode);\n         mode = guessMode(mode);\n         canvas.println(\"Guessed mode: \" + mode); */\n        throw \"Invalid mode: \" + mode + \" in (block:\" + this.blockPointer + \" bit:\" + this.bitPointer + \")\";\n      }\n      var dataLength = this.getDataLength(mode);\n      if (dataLength < 1)\n        throw \"Invalid data length: \" + dataLength;\n      switch (mode) {\n\n      case MODE_NUMBER:\n        var temp_str = this.getFigureString(dataLength);\n        var ta = new Array(temp_str.length);\n        for (var j = 0; j < temp_str.length; j++)\n          ta[j] = temp_str.charCodeAt(j);\n        output.push(ta);\n        break;\n\n      case MODE_ROMAN_AND_NUMBER:\n        var temp_str = this.getRomanAndFigureString(dataLength);\n        var ta = new Array(temp_str.length);\n        for (var j = 0; j < temp_str.length; j++)\n          ta[j] = temp_str.charCodeAt(j);\n        output.push(ta);\n        break;\n\n      case MODE_8BIT_BYTE:\n        var temp_sbyteArray3 = this.get8bitByteArray(dataLength);\n        output.push(temp_sbyteArray3);\n        break;\n\n      case MODE_KANJI:\n        var temp_str = this.getKanjiString(dataLength);\n        output.push(temp_str);\n        break;\n      }\n      //\n    }\n    while (true);\n    return output;\n  }\n});\n", "/*\n  Ported to JavaScript by <PERSON><PERSON> 2011\n\n  <EMAIL>, www.lazarsoft.info\n\n*/\n\n/*\n*\n* Copyright 2007 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n\nimport ReedSolomonDecoder from './rsdecoder';\nimport GF256 from './gf256';\nimport BitMatrixParser from './bmparser';\nimport DataBlock from './datablock';\nimport QRCodeDataBlockReader from './databr';\n\nvar Decoder = {};\nDecoder.rsDecoder = new ReedSolomonDecoder(GF256.QR_CODE_FIELD);\n\nDecoder.correctErrors = function(codewordBytes,  numDataCodewords) {\n  var numCodewords = codewordBytes.length;\n  // First read into an array of ints\n  var codewordsInts = new Array(numCodewords);\n  for (var i = 0; i < numCodewords; i++) {\n    codewordsInts[i] = codewordBytes[i] & 0xFF;\n  }\n  var numECCodewords = codewordBytes.length - numDataCodewords;\n  try {\n    Decoder.rsDecoder.decode(codewordsInts, numECCodewords);\n  } catch (rse) {\n    throw rse;\n  }\n  // Copy back into array of bytes -- only need to worry about the bytes that were data\n  // We don't care about errors in the error-correction codewords\n  for (var i = 0; i < numDataCodewords; i++) {\n    codewordBytes[i] =  codewordsInts[i];\n  }\n};\n\nDecoder.decode = function(bits) {\n  var parser = new BitMatrixParser(bits);\n  var version = parser.readVersion();\n  var ecLevel = parser.readFormatInformation().errorCorrectionLevel;\n\n  // Read codewords\n  var codewords = parser.readCodewords();\n\n  // Separate into data blocks\n  var dataBlocks = DataBlock.getDataBlocks(codewords, version, ecLevel);\n\n  // Count total number of data bytes\n  var totalBytes = 0;\n  for (var i = 0; i < dataBlocks.length; i++) {\n    totalBytes += dataBlocks[i].numDataCodewords;\n  }\n  var resultBytes = new Array(totalBytes);\n  var resultOffset = 0;\n\n  // Error-correct and copy data blocks together into a stream of bytes\n  for (var j = 0; j < dataBlocks.length; j++) {\n    var dataBlock = dataBlocks[j];\n    var codewordBytes = dataBlock.codewords;\n    var numDataCodewords = dataBlock.numDataCodewords;\n    Decoder.correctErrors(codewordBytes, numDataCodewords);\n    for (var i = 0; i < numDataCodewords; i++) {\n      resultBytes[resultOffset++] = codewordBytes[i];\n    }\n  }\n\n  // Decode the contents of that stream of bytes\n  var reader = new QRCodeDataBlockReader(resultBytes, version.versionNumber, ecLevel.bits);\n  return reader;\n};\n\nexport default Decoder;\n", "/*\n   Copyright 2011 <PERSON><PERSON> (<EMAIL>, www.lazarsoft.info)\n\n   Licensed under the Apache License, Version 2.0 (the \"License\");\n   you may not use this file except in compliance with the License.\n   You may obtain a copy of the License at\n\n       http://www.apache.org/licenses/LICENSE-2.0\n\n   Unless required by applicable law or agreed to in writing, software\n   distributed under the License is distributed on an \"AS IS\" BASIS,\n   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n   See the License for the specific language governing permissions and\n   limitations under the License.\n*/\n\nimport Detector from './detector';\nimport Decoder from './decoder';\n\nexport var qrcode = {};\nqrcode.sizeOfDataLengthInfo =  [[10, 9, 8, 8], [12, 11, 16, 10], [14, 13, 16, 12]];\n\nexport default function QrCode() {\n\n  this.imagedata = null;\n  this.width = 0;\n  this.height = 0;\n  this.qrCodeSymbol = null;\n  this.debug = false;\n\n  this.callback = null;\n}\n\n\nQrCode.prototype.decode = function(src, data) {\n\n  var decode = (function() {\n\n    try {\n      this.error = undefined;\n      this.result = this.process(this.imagedata);\n    } catch (e) {\n      this.error = e;\n      this.result = undefined;\n    }\n\n    if (this.callback != null) {\n      this.callback(this.error, this.result);\n    }\n\n    return this.result;\n\n  }).bind(this);\n\n  if (src != undefined && src.width != undefined) {\n    /* decode from canvas canvas.context.getImageData */\n    this.width = src.width;\n    this.height = src.height;\n    this.imagedata = {\"data\": data || src.data};\n    this.imagedata.width = src.width;\n    this.imagedata.height = src.height;\n\n    decode();\n  } else {\n    if (typeof Image === \"undefined\") {\n      throw new Error(\"This source format is not supported in your environment, you need to pass an image buffer with width and height (see https://github.com/edi9999/jsqrcode/blob/master/test/qrcode.js)\");\n    }\n    /* decode from URL */\n\n    var image = new Image();\n    image.crossOrigin = \"Anonymous\";\n\n    image.onload = (function() {\n\n      var canvas_qr = document.createElement('canvas');\n      var context = canvas_qr.getContext('2d');\n      var canvas_out = document.getElementById(\"out-canvas\");\n\n      if (canvas_out != null) {\n\n        var outctx = canvas_out.getContext('2d');\n        outctx.clearRect(0, 0, 320, 240);\n        outctx.drawImage(image, 0, 0, 320, 240);\n      }\n\n      canvas_qr.width = image.width;\n      canvas_qr.height = image.height;\n      context.drawImage(image, 0, 0);\n      this.width = image.width;\n      this.height = image.height;\n\n      try {\n        this.imagedata = context.getImageData(0, 0, image.width, image.height);\n      } catch (e) {\n        this.result = \"Cross domain image reading not supported in your browser! Save it to your computer then drag and drop the file!\";\n        if (this.callback != null) return this.callback(null, this.result);\n      }\n\n      decode();\n\n    }).bind(this);\n\n    image.src = src;\n  }\n};\n\nQrCode.prototype.decode_utf8 = function(s) {\n\n  return decodeURIComponent(escape(s));\n};\n\nQrCode.prototype.process = function(imageData) {\n\n  var start = new Date().getTime();\n\n  var image = this.grayScaleToBitmap(this.grayscale(imageData));\n\n  var detector = new Detector(image);\n\n  var qRCodeMatrix = detector.detect();\n\n  /*for (var y = 0; y < qRCodeMatrix.bits.height; y++)\n   {\n   for (var x = 0; x < qRCodeMatrix.bits.width; x++)\n   {\n   var point = (x * 4*2) + (y*2 * imageData.width * 4);\n   imageData.data[point] = qRCodeMatrix.bits.get_Renamed(x,y)?0:0;\n   imageData.data[point+1] = qRCodeMatrix.bits.get_Renamed(x,y)?0:0;\n   imageData.data[point+2] = qRCodeMatrix.bits.get_Renamed(x,y)?255:0;\n   }\n   }*/\n\n  var reader = Decoder.decode(qRCodeMatrix.bits);\n  var data = reader.DataByte;\n  var str = \"\";\n  for (var i = 0; i < data.length; i++) {\n    for (var j = 0; j < data[i].length; j++)\n      str += String.fromCharCode(data[i][j]);\n  }\n\n  var end = new Date().getTime();\n  var time = end - start;\n  if (this.debug) {\n    console.log('QR Code processing time (ms): ' + time);\n  }\n\n  return {result: this.decode_utf8(str), points: qRCodeMatrix.points};\n};\n\nQrCode.prototype.getPixel = function(imageData, x, y) {\n  if (imageData.width < x) {\n    throw \"point error\";\n  }\n  if (imageData.height < y) {\n    throw \"point error\";\n  }\n  var point = (x * 4) + (y * imageData.width * 4);\n  return (imageData.data[point] * 33 + imageData.data[point + 1] * 34 + imageData.data[point + 2] * 33) / 100;\n};\n\nQrCode.prototype.binarize = function(th) {\n  var ret = new Array(this.width * this.height);\n  for (var y = 0; y < this.height; y++) {\n    for (var x = 0; x < this.width; x++) {\n      var gray = this.getPixel(x, y);\n\n      ret[x + y * this.width] = gray <= th;\n    }\n  }\n  return ret;\n};\n\nQrCode.prototype.getMiddleBrightnessPerArea = function(imageData) {\n  var numSqrtArea = 4;\n  //obtain middle brightness((min + max) / 2) per area\n  var areaWidth = Math.floor(imageData.width / numSqrtArea);\n  var areaHeight = Math.floor(imageData.height / numSqrtArea);\n  var minmax = new Array(numSqrtArea);\n  for (var i = 0; i < numSqrtArea; i++) {\n    minmax[i] = new Array(numSqrtArea);\n    for (var i2 = 0; i2 < numSqrtArea; i2++) {\n      minmax[i][i2] = [0, 0];\n    }\n  }\n  for (var ay = 0; ay < numSqrtArea; ay++) {\n    for (var ax = 0; ax < numSqrtArea; ax++) {\n      minmax[ax][ay][0] = 0xFF;\n      for (var dy = 0; dy < areaHeight; dy++) {\n        for (var dx = 0; dx < areaWidth; dx++) {\n          var target = imageData.data[areaWidth * ax + dx + (areaHeight * ay + dy) * imageData.width];\n          if (target < minmax[ax][ay][0])\n            minmax[ax][ay][0] = target;\n          if (target > minmax[ax][ay][1])\n            minmax[ax][ay][1] = target;\n        }\n      }\n    }\n  }\n  var middle = new Array(numSqrtArea);\n  for (var i3 = 0; i3 < numSqrtArea; i3++) {\n    middle[i3] = new Array(numSqrtArea);\n  }\n  for (var ay = 0; ay < numSqrtArea; ay++) {\n    for (var ax = 0; ax < numSqrtArea; ax++) {\n      middle[ax][ay] = Math.floor((minmax[ax][ay][0] + minmax[ax][ay][1]) / 2);\n    }\n  }\n\n  return middle;\n};\n\nQrCode.prototype.grayScaleToBitmap = function(grayScaleImageData) {\n  var middle = this.getMiddleBrightnessPerArea(grayScaleImageData);\n  var sqrtNumArea = middle.length;\n  var areaWidth = Math.floor(grayScaleImageData.width / sqrtNumArea);\n  var areaHeight = Math.floor(grayScaleImageData.height / sqrtNumArea);\n\n  for (var ay = 0; ay < sqrtNumArea; ay++) {\n    for (var ax = 0; ax < sqrtNumArea; ax++) {\n      for (var dy = 0; dy < areaHeight; dy++) {\n        for (var dx = 0; dx < areaWidth; dx++) {\n          grayScaleImageData.data[areaWidth * ax + dx + (areaHeight * ay + dy) * grayScaleImageData.width] = (grayScaleImageData.data[areaWidth * ax + dx + (areaHeight * ay + dy) * grayScaleImageData.width] < middle[ax][ay]);\n        }\n      }\n    }\n  }\n  return grayScaleImageData;\n};\n\nQrCode.prototype.grayscale = function(imageData) {\n  var ret = new Array(imageData.width * imageData.height);\n\n  for (var y = 0; y < imageData.height; y++) {\n    for (var x = 0; x < imageData.width; x++) {\n      var gray = this.getPixel(imageData, x, y);\n\n      ret[x + y * imageData.width] = gray;\n    }\n  }\n\n  return {\n    height: imageData.height,\n    width: imageData.width,\n    data: ret\n  };\n};\n\nexport function URShift(number,  bits) {\n  if (number >= 0)\n    return number >> bits;\n  else\n    return (number >> bits) + (2 << ~bits);\n}\n"], "names": ["QrCode"], "mappings": ";;;;;;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,AAAe,SAAS,oBAAoB,CAAC,OAAO,GAAG,IAAI,EAAE,IAAI,EAAE;EACjE,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC;EACrC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;EACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;CAClB;;AAED,oBAAoB,CAAC,SAAS,CAAC,OAAO,GAAG,WAAW;EAClD,OAAO,IAAI,CAAC,qBAAqB,CAAC;CACnC,CAAC;;AAEF,oBAAoB,CAAC,OAAO,GAAG,SAAS,IAAI,EAAE;EAC5C,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,QAAQ,CAAC,MAAM,EAAE;IACvC,MAAM,mBAAmB,CAAC;GAC3B;EACD,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC;CACvB,CAAC;;AAEF,IAAI,QAAQ,GAAG;EACb,IAAI,oBAAoB,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC;EACtC,IAAI,oBAAoB,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC;EACtC,IAAI,oBAAoB,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC;EACtC,IAAI,oBAAoB,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC;CACvC;;AC/CD;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,AAGA,IAAI,mBAAmB,GAAG,MAAM,CAAC;AACjC,IAAI,yBAAyB,GAAG;EAC9B,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;EACd,CAAC,MAAM,EAAE,IAAI,CAAC;CACf,CAAC;AACF,IAAI,qBAAqB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;;;AAG7E,AAAe,SAAS,iBAAiB,CAAC,UAAU,EAAE;EACpD,IAAI,CAAC,oBAAoB,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC,UAAU,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;EACnF,IAAI,CAAC,QAAQ,KAAK,UAAU,GAAG,IAAI,CAAC,CAAC;CACtC;;AAED,iBAAiB,CAAC,SAAS,CAAC,WAAW,GAAG,WAAW;EACnD,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC;CACpE,CAAC;;AAEF,iBAAiB,CAAC,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,EAAE;EAC/C,IAAI,KAAK,IAAI,CAAC,CAAC;EACf,OAAO,IAAI,CAAC,oBAAoB,IAAI,KAAK,CAAC,oBAAoB,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC;CACnG,CAAC;;AAEF,iBAAiB,CAAC,gBAAgB,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE;EACnD,CAAC,IAAI,CAAC,CAAC;;EAEP,OAAO,qBAAqB,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,qBAAqB,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,EAAE,GAAG,qBAAqB,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,EAAE,GAAG,qBAAqB,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,qBAAqB,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,qBAAqB,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,qBAAqB,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,qBAAqB,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC;CAC7X,CAAC;;AAEF,iBAAiB,CAAC,uBAAuB,GAAG,SAAS,gBAAgB,EAAE;EACrE,IAAI,UAAU,GAAG,iBAAiB,CAAC,yBAAyB,CAAC,gBAAgB,CAAC,CAAC;EAC/E,IAAI,UAAU,IAAI,IAAI,EAAE;IACtB,OAAO,UAAU,CAAC;GACnB;;;;EAID,OAAO,iBAAiB,CAAC,yBAAyB,CAAC,gBAAgB,GAAG,mBAAmB,CAAC,CAAC;CAC5F,CAAC;AACF,iBAAiB,CAAC,yBAAyB,GAAG,SAAS,gBAAgB,EAAE;;EAEvE,IAAI,cAAc,GAAG,UAAU,CAAC;EAChC,IAAI,cAAc,GAAG,CAAC,CAAC;EACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,yBAAyB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACzD,IAAI,UAAU,GAAG,yBAAyB,CAAC,CAAC,CAAC,CAAC;IAC9C,IAAI,UAAU,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;IAC/B,IAAI,UAAU,IAAI,gBAAgB,EAAE;;MAElC,OAAO,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;KAC7C;IACD,IAAI,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAC;IACzE,IAAI,cAAc,GAAG,cAAc,EAAE;MACnC,cAAc,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;MAC/B,cAAc,GAAG,cAAc,CAAC;KACjC;GACF;;;EAGD,IAAI,cAAc,IAAI,CAAC,EAAE;IACvB,OAAO,IAAI,iBAAiB,CAAC,cAAc,CAAC,CAAC;GAC9C;EACD,OAAO,IAAI,CAAC;CACb,CAAC;;ACtHF;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,AAEe,SAAS,SAAS,CAAC,KAAK,GAAG,MAAM,EAAE;EAChD,IAAI,CAAC,MAAM;IACT,MAAM,GAAG,KAAK,CAAC;EACjB,IAAI,KAAK,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,EAAE;IAC3B,MAAM,wCAAwC,CAAC;GAChD;EACD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;EACnB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;EACrB,IAAI,OAAO,GAAG,KAAK,IAAI,CAAC,CAAC;EACzB,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,EAAE;IACvB,OAAO,EAAE,CAAC;GACX;EACD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;EACvB,IAAI,CAAC,IAAI,GAAG,IAAI,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC;EACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE;IACvC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;CACpB;;AAED,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,SAAS,EAAE,WAAW,EAAE;EACtD,GAAG,EAAE,WAAW;IACd,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;MAC7B,MAAM,kDAAkD,CAAC;KAC1D;IACD,OAAO,IAAI,CAAC,KAAK,CAAC;GACnB;CACF,CAAC,CAAC;;AAEH,SAAS,CAAC,SAAS,CAAC,WAAW,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;EAC/C,IAAI,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;EACzC,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;CAC5D,CAAC;;AAEF,SAAS,CAAC,SAAS,CAAC,WAAW,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;EAC/C,IAAI,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;EACzC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;CACtC,CAAC;;AAEF,SAAS,CAAC,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;EACxC,IAAI,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;EACzC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;CACtC,CAAC;;AAEF,SAAS,CAAC,SAAS,CAAC,KAAK,GAAG,WAAW;EACrC,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;EAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;IAC5B,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;GAClB;CACF,CAAC;;AAEF,SAAS,CAAC,SAAS,CAAC,SAAS,GAAG,SAAS,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE;EACjE,IAAI,GAAG,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,EAAE;IACvB,MAAM,kCAAkC,CAAC;GAC1C;EACD,IAAI,MAAM,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE;IAC3B,MAAM,qCAAqC,CAAC;GAC7C;EACD,IAAI,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC;EACzB,IAAI,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC;EAC1B,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE;IAC9C,MAAM,uCAAuC,CAAC;GAC/C;EACD,KAAK,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;IACjC,IAAI,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;IAC9B,KAAK,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;MACjC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;KACjD;GACF;CACF,CAAC;;AC7FF;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,AAGA,SAAS,GAAG,CAAC,KAAK,GAAG,aAAa,EAAE;EAClC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;EACnB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;CACpC;;AAED,SAAS,QAAQ,CAAC,mBAAmB,GAAG,SAAS,GAAG,SAAS,EAAE;EAC7D,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;EAC/C,IAAI,SAAS;IACX,IAAI,CAAC,QAAQ,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;;IAEvC,IAAI,CAAC,QAAQ,GAAG,CAAC,SAAS,CAAC,CAAC;CAC/B;;AAED,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,EAAE,kBAAkB,EAAE;EAC5D,GAAG,EAAE,WAAW;IACd,QAAQ,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,SAAS,CAAC;GACnD;CACF,CAAC,CAAC;;AAEH,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,EAAE,WAAW,EAAE;EACrD,GAAG,EAAE,WAAW;IACd,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;MAC7C,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;KAClC;IACD,OAAO,KAAK,CAAC;GACd;CACF,CAAC,CAAC;;AAEH,QAAQ,CAAC,SAAS,CAAC,WAAW,GAAG,WAAW;EAC1C,OAAO,IAAI,CAAC,QAAQ,CAAC;CACtB,CAAC;;AAEF,AAAe,SAAS,OAAO,CAAC,aAAa,GAAG,uBAAuB,GAAG,SAAS,GAAG,SAAS,GAAG,SAAS,GAAG,SAAS,EAAE;EACvH,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;EACnC,IAAI,CAAC,uBAAuB,GAAG,uBAAuB,CAAC;EACvD,IAAI,CAAC,QAAQ,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;;EAE7D,IAAI,KAAK,GAAG,CAAC,CAAC;EACd,IAAI,WAAW,GAAG,SAAS,CAAC,mBAAmB,CAAC;EAChD,IAAI,QAAQ,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;EACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACxC,IAAI,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC1B,KAAK,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,aAAa,GAAG,WAAW,CAAC,CAAC;GAChE;EACD,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;CAC7B;;AAED,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,EAAE,qBAAqB,EAAE;EAC9D,GAAG,EAAE,WAAW;IACd,QAAQ,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;GACrC;CACF,CAAC,CAAC;;AAEH,OAAO,CAAC,SAAS,CAAC,oBAAoB,GAAG,WAAW;EAClD,IAAI,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC;EACzC,IAAI,SAAS,GAAG,IAAI,SAAS,CAAC,SAAS,CAAC,CAAC;;;EAGzC,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEhC,SAAS,CAAC,SAAS,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;;EAE5C,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;;;EAG5C,IAAI,GAAG,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC;EAC9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;IAC5B,IAAI,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;MAC5B,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;;QAEpE,SAAS;OACV;MACD,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;KACnE;GACF;;;EAGD,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,GAAG,EAAE,CAAC,CAAC;;EAE7C,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;;EAE7C,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE;;IAE1B,SAAS,CAAC,SAAS,CAAC,SAAS,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;;IAE7C,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;GAC9C;;EAED,OAAO,SAAS,CAAC;CAClB,CAAC;;AAEF,OAAO,CAAC,SAAS,CAAC,mBAAmB,GAAG,SAAS,OAAO,EAAE;EACxD,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;CACzC,CAAC;;AAEF,OAAO,CAAC,mBAAmB,GAAG;EAC5B,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;CACR,CAAC;;AAEF,OAAO,CAAC,QAAQ,GAAG,aAAa,EAAE,CAAC;;AAEnC,OAAO,CAAC,mBAAmB,GAAG,SAAS,aAAa,EAAE;EACpD,IAAI,aAAa,GAAG,CAAC,IAAI,aAAa,GAAG,EAAE,EAAE;IAC3C,MAAM,mBAAmB,CAAC;GAC3B;EACD,OAAO,OAAO,CAAC,QAAQ,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;CAC5C,CAAC;;AAEF,OAAO,CAAC,iCAAiC,GAAG,SAAS,SAAS,EAAE;EAC9D,IAAI,SAAS,GAAG,CAAC,IAAI,CAAC,EAAE;IACtB,MAAM,yCAAyC,CAAC;GACjD;EACD,IAAI;IACF,OAAO,OAAO,CAAC,mBAAmB,CAAC,CAAC,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;GAC3D,CAAC,OAAO,GAAG,EAAE;IACZ,MAAM,2BAA2B,CAAC;GACnC;CACF,CAAC;;AAEF,OAAO,CAAC,wBAAwB,GAAG,SAAS,WAAW,EAAE;EACvD,IAAI,cAAc,GAAG,UAAU,CAAC;EAChC,IAAI,WAAW,GAAG,CAAC,CAAC;EACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IAC3D,IAAI,aAAa,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;;IAEnD,IAAI,aAAa,IAAI,WAAW,EAAE;MAChC,OAAO,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KACxC;;;IAGD,IAAI,cAAc,GAAG,iBAAiB,CAAC,gBAAgB,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;IACpF,IAAI,cAAc,GAAG,cAAc,EAAE;MACnC,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;MACpB,cAAc,GAAG,cAAc,CAAC;KACjC;GACF;;;EAGD,IAAI,cAAc,IAAI,CAAC,EAAE;IACvB,OAAO,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;GAC9C;;EAED,OAAO,IAAI,CAAC;CACb,CAAC;;AAEF,SAAS,aAAa,GAAG;EACvB,OAAO;IACL,IAAI,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,QAAQ,CAAC,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACxJ,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAC/J,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAC/J,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC9J,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAChM,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAC/J,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACnM,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACnN,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACpN,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACpO,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACpN,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACpO,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACtN,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAC5O,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACzO,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IAC1O,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IAC7O,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IAC5O,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IAC7O,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IAC9O,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACjO,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IACjN,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IACpP,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACnP,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IACnP,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACnP,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,MAAM,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IACvP,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IACxP,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IACxP,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IAC3P,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IACzP,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IAC1O,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IAC3P,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACzP,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IAChQ,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IAC9P,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IAChQ,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IAChQ,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IAC/P,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;GACjQ,CAAC;CACH;;AC1PD;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,AAAe,SAAS,gBAAgB,CAAC,IAAI,EAAE,IAAI,GAAG,mBAAmB,EAAE;EACzE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;EACd,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;EACd,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;EACf,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;CAChD;;AAED,MAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,SAAS,EAAE,GAAG,EAAE;EACrD,GAAG,EAAE,WAAW;IACd,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;GAC3B;CACF,CAAC,CAAC;;AAEH,MAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,SAAS,EAAE,GAAG,EAAE;EACrD,GAAG,EAAE,WAAW;IACd,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;GAC3B;CACF,CAAC,CAAC;;AAEH,gBAAgB,CAAC,SAAS,CAAC,cAAc,GAAG,WAAW;EACrD,IAAI,CAAC,KAAK,EAAE,CAAC;CACd,CAAC;;AAEF,gBAAgB,CAAC,SAAS,CAAC,WAAW,GAAG,SAAS,UAAU,GAAG,CAAC,GAAG,CAAC,EAAE;EACpE,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,UAAU,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,UAAU,EAAE;IAC5E,IAAI,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC;IACrE,OAAO,cAAc,IAAI,GAAG,IAAI,cAAc,GAAG,IAAI,CAAC,mBAAmB,IAAI,GAAG,CAAC;GAClF;EACD,OAAO,KAAK,CAAC;CACd,CAAC;;AAEF,AAAO,SAAS,sBAAsB,CAAC,KAAK,GAAG,MAAM,GAAG,MAAM,GAAG,KAAK,GAAG,MAAM,GAAG,UAAU,GAAG,mBAAmB,EAAE;EAClH,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;EACnB,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;EAC1B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;EACrB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;EACrB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;EACnB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;EACrB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;EAC7B,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EACtC,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;CAChD;;AAED,sBAAsB,CAAC,SAAS,CAAC,aAAa,GAAG,SAAS,UAAU,GAAG,GAAG,EAAE;EAC1E,QAAQ,CAAC,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;CACrD,CAAC;;AAEF,sBAAsB,CAAC,SAAS,CAAC,iBAAiB,GAAG,SAAS,UAAU,EAAE;EACxE,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;EACjC,IAAI,WAAW,GAAG,UAAU,GAAG,GAAG,CAAC;EACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;IAC1B,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,WAAW,EAAE;MACvD,OAAO,KAAK,CAAC;KACd;GACF;EACD,OAAO,IAAI,CAAC;CACb,CAAC;;AAEF,sBAAsB,CAAC,SAAS,CAAC,kBAAkB,GAAG,SAAS,MAAM,GAAG,OAAO,GAAG,QAAQ,GAAG,uBAAuB,EAAE;EACpH,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;;EAEvB,IAAI,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;EACxB,IAAI,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC;EAC3C,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAClB,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAClB,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;;;EAGlB,IAAI,CAAC,GAAG,MAAM,CAAC;EACf,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE;IACnF,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB,CAAC,EAAE,CAAC;GACL;;EAED,IAAI,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,QAAQ,EAAE;IACrC,OAAO,GAAG,CAAC;GACZ;EACD,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE;IACpF,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB,CAAC,EAAE,CAAC;GACL;EACD,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,QAAQ,EAAE;IAC5B,OAAO,GAAG,CAAC;GACZ;;;EAGD,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC;EACf,OAAO,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE;IACrF,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB,CAAC,EAAE,CAAC;GACL;EACD,IAAI,CAAC,IAAI,IAAI,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,QAAQ,EAAE;IACzC,OAAO,GAAG,CAAC;GACZ;EACD,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE;IACtF,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB,CAAC,EAAE,CAAC;GACL;EACD,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,QAAQ,EAAE;IAC5B,OAAO,GAAG,CAAC;GACZ;;EAED,IAAI,eAAe,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;EACpE,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,GAAG,uBAAuB,CAAC,IAAI,CAAC,GAAG,uBAAuB,EAAE;IAC1F,OAAO,GAAG,CAAC;GACZ;;EAED,OAAO,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;CACrF,CAAC;;AAEF,sBAAsB,CAAC,SAAS,CAAC,oBAAoB,GAAG,SAAS,UAAU,GAAG,CAAC,GAAG,CAAC,EAAE;EACnF,IAAI,eAAe,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;EACpE,IAAI,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;EAChD,IAAI,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC;EAClG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;IACnB,IAAI,mBAAmB,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;IAChF,IAAI,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;IACtC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,GAAG,EAAE,KAAK,EAAE,EAAE;MACxC,IAAI,MAAM,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;;MAE1C,IAAI,MAAM,CAAC,WAAW,CAAC,mBAAmB,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE;QAC7D,OAAO,IAAI,gBAAgB,CAAC,OAAO,EAAE,OAAO,EAAE,mBAAmB,CAAC,CAAC;OACpE;KACF;;IAED,IAAI,KAAK,GAAG,IAAI,gBAAgB,CAAC,OAAO,EAAE,OAAO,EAAE,mBAAmB,CAAC,CAAC;IACxE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,EAAE;MACpC,IAAI,CAAC,mBAAmB,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;KAC1D;GACF;EACD,OAAO,IAAI,CAAC;CACb,CAAC;;AAEF,sBAAsB,CAAC,SAAS,CAAC,IAAI,GAAG,WAAW;EACjD,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;EACvB,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;EACzB,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;EACzB,IAAI,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC;EAC/B,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,IAAI,MAAM,IAAI,CAAC,CAAC,CAAC;;;EAG1C,IAAI,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EAC3B,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,MAAM,EAAE,IAAI,EAAE,EAAE;;IAExC,IAAI,CAAC,GAAG,OAAO,IAAI,CAAC,IAAI,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAChF,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAClB,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAClB,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAClB,IAAI,CAAC,GAAG,MAAM,CAAC;;;;IAIf,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE;MACnD,CAAC,EAAE,CAAC;KACL;IACD,IAAI,YAAY,GAAG,CAAC,CAAC;IACrB,OAAO,CAAC,GAAG,IAAI,EAAE;MACf,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE;;QAEnC,IAAI,YAAY,IAAI,CAAC,EAAE;;UAErB,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;SAC5B,MAAM;;UAEL,IAAI,YAAY,IAAI,CAAC,EAAE;;YAErB,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE;;cAEtC,IAAI,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;cAC5D,IAAI,SAAS,IAAI,IAAI,EAAE;gBACrB,OAAO,SAAS,CAAC;eAClB;aACF;YACD,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAC9B,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAClB,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAClB,YAAY,GAAG,CAAC,CAAC;WAClB,MAAM;YACL,UAAU,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC;WAC9B;SACF;OACF,MAAM;;QAEL,IAAI,YAAY,IAAI,CAAC,EAAE;;UAErB,YAAY,EAAE,CAAC;SAChB;QACD,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;OAC5B;MACD,CAAC,EAAE,CAAC;KACL;IACD,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE;MACtC,IAAI,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;MAC/D,IAAI,SAAS,IAAI,IAAI,EAAE;QACrB,OAAO,SAAS,CAAC;OAClB;KACF;GACF;;;;EAID,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,MAAM,IAAI,CAAC,CAAC,EAAE;IACvC,QAAQ,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;GACjC;;EAED,MAAM,yCAAyC,CAAC;CACjD,CAAC;;ACxOF;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,AAEA,IAAI,WAAW,GAAG,EAAE,CAAC;;AAErB,WAAW,CAAC,mBAAmB,GAAG,SAAS,KAAK,GAAG,MAAM,EAAE;EACzD,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;EACxB,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;;EAE1B,IAAI,MAAM,GAAG,IAAI,CAAC;EAClB,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,MAAM,EAAE,MAAM,IAAI,CAAC,EAAE;IAClE,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;IACnC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;IACvC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,MAAM,EAAE;MAC/C,MAAM,4BAA4B,CAAC;KACpC;IACD,MAAM,GAAG,KAAK,CAAC;IACf,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;MACX,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC;MACrB,MAAM,GAAG,IAAI,CAAC;KACf,MAAM,IAAI,CAAC,IAAI,KAAK,EAAE;MACrB,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;MAC3B,MAAM,GAAG,IAAI,CAAC;KACf;IACD,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;MACX,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;MACzB,MAAM,GAAG,IAAI,CAAC;KACf,MAAM,IAAI,CAAC,IAAI,MAAM,EAAE;MACtB,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC;MAChC,MAAM,GAAG,IAAI,CAAC;KACf;GACF;;EAED,MAAM,GAAG,IAAI,CAAC;EACd,KAAK,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,IAAI,CAAC,IAAI,MAAM,EAAE,MAAM,IAAI,CAAC,EAAE;IACvE,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;IACnC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;IACvC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,MAAM,EAAE;MAC/C,MAAM,4BAA4B,CAAC;KACpC;IACD,MAAM,GAAG,KAAK,CAAC;IACf,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;MACX,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC;MACrB,MAAM,GAAG,IAAI,CAAC;KACf,MAAM,IAAI,CAAC,IAAI,KAAK,EAAE;MACrB,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;MAC3B,MAAM,GAAG,IAAI,CAAC;KACf;IACD,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;MACX,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;MACzB,MAAM,GAAG,IAAI,CAAC;KACf,MAAM,IAAI,CAAC,IAAI,MAAM,EAAE;MACtB,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC;MAChC,MAAM,GAAG,IAAI,CAAC;KACf;GACF;CACF,CAAC;;;;AAIF,WAAW,CAAC,WAAW,GAAG,SAAS,KAAK,GAAG,SAAS,GAAG,SAAS,EAAE;EAChE,IAAI,IAAI,GAAG,IAAI,SAAS,CAAC,SAAS,CAAC,CAAC;EACpC,IAAI,MAAM,GAAG,IAAI,KAAK,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC;EACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;IAClC,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;IACxB,IAAI,MAAM,IAAI,CAAC,GAAG,GAAG,CAAC;IACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;MAC/B,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;MAC5B,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;KACxB;IACD,SAAS,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;;;IAGnC,WAAW,CAAC,mBAAmB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IAC/C,IAAI;MACF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;QAC/B,IAAI,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACtF,IAAI,GAAG;UACL,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;OAC/B;KACF,CAAC,OAAO,MAAM,EAAE;;;;;;;;MAQf,MAAM,2BAA2B,CAAC;KACnC;GACF;EACD,OAAO,IAAI,CAAC;CACb,CAAC;;ACnHF;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,IAAI,QAAQ,GAAG,CAAC,CAAC;AACjB,IAAI,WAAW,GAAG,EAAE,CAAC;AACrB,IAAI,kBAAkB,GAAG,CAAC,CAAC;AAC3B,IAAI,aAAa,GAAG,CAAC,CAAC;;AAEtB,SAAS,iBAAiB,CAAC,QAAQ,EAAE;;EAEnC,SAAS,QAAQ,CAAC,QAAQ,GAAG,QAAQ,EAAE;IACrC,IAAI,KAAK,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;IACpC,IAAI,KAAK,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;IACpC,QAAQ,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,EAAE,CAAC;GACpD;;;EAGD,SAAS,aAAa,CAAC,MAAM,GAAG,MAAM,GAAG,MAAM,EAAE;IAC/C,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC;IAClB,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC;IAClB,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;GAClF;;;;EAID,IAAI,eAAe,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACzD,IAAI,cAAc,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACxD,IAAI,eAAe,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEzD,IAAI,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;;EAE3B,IAAI,cAAc,IAAI,eAAe,IAAI,cAAc,IAAI,eAAe,EAAE;IAC1E,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACrB,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACrB,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;GACtB,MAAM,IAAI,eAAe,IAAI,cAAc,IAAI,eAAe,IAAI,eAAe,EAAE;IAClF,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACrB,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACrB,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;GACtB,MAAM;IACL,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACrB,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACrB,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;GACtB;;;;;;EAMD,IAAI,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,GAAG,GAAG,EAAE;IAC/C,IAAI,IAAI,GAAG,MAAM,CAAC;IAClB,MAAM,GAAG,MAAM,CAAC;IAChB,MAAM,GAAG,IAAI,CAAC;GACf;;EAED,QAAQ,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;EACrB,QAAQ,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;EACrB,QAAQ,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;CACtB;;;AAGD,SAAS,aAAa,CAAC,IAAI,EAAE,IAAI,GAAG,mBAAmB,EAAE;EACvD,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;EACd,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;EACd,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;EACf,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;CAChD;;AAED,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC,SAAS,EAAE,GAAG,EAAE;EAClD,GAAG,EAAE,WAAW;IACd,OAAO,IAAI,CAAC,CAAC,CAAC;GACf;CACF,CAAC,CAAC;;AAEH,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC,SAAS,EAAE,GAAG,EAAE;EAClD,GAAG,EAAE,WAAW;IACd,OAAO,IAAI,CAAC,CAAC,CAAC;GACf;CACF,CAAC,CAAC;;AAEH,aAAa,CAAC,SAAS,CAAC,cAAc,GAAG,WAAW;EAClD,IAAI,CAAC,KAAK,EAAE,CAAC;CACd,CAAC;;AAEF,aAAa,CAAC,SAAS,CAAC,WAAW,GAAG,SAAS,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE;EAC/D,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,UAAU,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,UAAU,EAAE;IAC5E,IAAI,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC;IACrE,OAAO,cAAc,IAAI,GAAG,IAAI,cAAc,GAAG,IAAI,CAAC,mBAAmB,IAAI,GAAG,CAAC;GAClF;EACD,OAAO,KAAK,CAAC;CACd,CAAC;;AAEF,SAAS,iBAAiB,CAAC,cAAc,EAAE;EACzC,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;EACpC,IAAI,CAAC,OAAO,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;EACjC,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;CACnC;;AAED,AAAO,SAAS,mBAAmB,GAAG;EACpC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;EAClB,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;EAC1B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;EACxB,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EAC5C,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;CACjC;;AAED,MAAM,CAAC,cAAc,CAAC,mBAAmB,CAAC,SAAS,EAAE,sBAAsB,EAAE;EAC3E,GAAG,EAAE,WAAW;IACd,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACjC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACjC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACjC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACjC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACjC,OAAO,IAAI,CAAC,oBAAoB,CAAC;GAClC;CACF,CAAC,CAAC;;AAEH,mBAAmB,CAAC,SAAS,CAAC,iBAAiB,GAAG,SAAS,UAAU,EAAE;EACrE,IAAI,eAAe,GAAG,CAAC,CAAC;EACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;IAC1B,IAAI,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;IAC1B,IAAI,KAAK,IAAI,CAAC,EAAE;MACd,OAAO,KAAK,CAAC;KACd;IACD,eAAe,IAAI,KAAK,CAAC;GAC1B;EACD,IAAI,eAAe,GAAG,CAAC,EAAE;IACvB,OAAO,KAAK,CAAC;GACd;EACD,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,eAAe,IAAI,kBAAkB,IAAI,CAAC,CAAC,CAAC;EACzE,IAAI,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;;EAE7C,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,kBAAkB,CAAC,CAAC,GAAG,WAAW,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,kBAAkB,CAAC,CAAC,GAAG,WAAW,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,kBAAkB,CAAC,CAAC,GAAG,CAAC,GAAG,WAAW,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,kBAAkB,CAAC,CAAC,GAAG,WAAW,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,kBAAkB,CAAC,CAAC,GAAG,WAAW,CAAC;CACnZ,CAAC;;AAEF,mBAAmB,CAAC,SAAS,CAAC,aAAa,GAAG,SAAS,UAAU,GAAG,GAAG,EAAE;EACvE,QAAQ,CAAC,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;CACrE,CAAC;;AAEF,mBAAmB,CAAC,SAAS,CAAC,kBAAkB,GAAG,SAAS,MAAM,GAAG,OAAO,GAAG,QAAQ,GAAG,uBAAuB,EAAE;EACjH,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;;EAEvB,IAAI,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;EACxB,IAAI,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC;;;EAG3C,IAAI,CAAC,GAAG,MAAM,CAAC;EACf,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE;IACtD,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB,CAAC,EAAE,CAAC;GACL;EACD,IAAI,CAAC,GAAG,CAAC,EAAE;IACT,OAAO,GAAG,CAAC;GACZ;EACD,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE;IACpF,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB,CAAC,EAAE,CAAC;GACL;;EAED,IAAI,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,QAAQ,EAAE;IACrC,OAAO,GAAG,CAAC;GACZ;EACD,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE;IACnF,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB,CAAC,EAAE,CAAC;GACL;EACD,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,QAAQ,EAAE;IAC5B,OAAO,GAAG,CAAC;GACZ;;;EAGD,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC;EACf,OAAO,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE;IACxD,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB,CAAC,EAAE,CAAC;GACL;EACD,IAAI,CAAC,IAAI,IAAI,EAAE;IACb,OAAO,GAAG,CAAC;GACZ;EACD,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,QAAQ,EAAE;IACrF,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB,CAAC,EAAE,CAAC;GACL;EACD,IAAI,CAAC,IAAI,IAAI,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE;IAC1C,OAAO,GAAG,CAAC;GACZ;EACD,OAAO,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,QAAQ,EAAE;IACpF,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB,CAAC,EAAE,CAAC;GACL;EACD,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE;IAC7B,OAAO,GAAG,CAAC;GACZ;;;;EAID,IAAI,eAAe,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;EACpG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,GAAG,uBAAuB,CAAC,IAAI,CAAC,GAAG,uBAAuB,EAAE;IAC1F,OAAO,GAAG,CAAC;GACZ;;EAED,OAAO,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;CACrF,CAAC;;AAEF,mBAAmB,CAAC,SAAS,CAAC,oBAAoB,GAAG,SAAS,MAAM,GAAG,OAAO,GAAG,QAAQ,EAAE,uBAAuB,EAAE;EAClH,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;;EAEvB,IAAI,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC;EACvB,IAAI,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC;;EAE3C,IAAI,CAAC,GAAG,MAAM,CAAC;EACf,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE;IACtD,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB,CAAC,EAAE,CAAC;GACL;EACD,IAAI,CAAC,GAAG,CAAC,EAAE;IACT,OAAO,GAAG,CAAC;GACZ;EACD,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE;IACpF,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB,CAAC,EAAE,CAAC;GACL;EACD,IAAI,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,QAAQ,EAAE;IACrC,OAAO,GAAG,CAAC;GACZ;EACD,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE;IACnF,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB,CAAC,EAAE,CAAC;GACL;EACD,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,QAAQ,EAAE;IAC5B,OAAO,GAAG,CAAC;GACZ;;EAED,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC;EACf,OAAO,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE;IACxD,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB,CAAC,EAAE,CAAC;GACL;EACD,IAAI,CAAC,IAAI,IAAI,EAAE;IACb,OAAO,GAAG,CAAC;GACZ;EACD,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,QAAQ,EAAE;IACrF,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB,CAAC,EAAE,CAAC;GACL;EACD,IAAI,CAAC,IAAI,IAAI,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE;IAC1C,OAAO,GAAG,CAAC;GACZ;EACD,OAAO,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,QAAQ,EAAE;IACpF,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB,CAAC,EAAE,CAAC;GACL;EACD,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE;IAC7B,OAAO,GAAG,CAAC;GACZ;;;;EAID,IAAI,eAAe,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;EACpG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,GAAG,uBAAuB,CAAC,IAAI,uBAAuB,EAAE;IACtF,OAAO,GAAG,CAAC;GACZ;;EAED,OAAO,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;CACrF,CAAC;;AAEF,mBAAmB,CAAC,SAAS,CAAC,oBAAoB,GAAG,SAAS,UAAU,GAAG,CAAC,GAAG,CAAC,EAAE;EAChF,IAAI,eAAe,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;EACpG,IAAI,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;EAChD,IAAI,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC;EAC9F,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;;IAEnB,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC;IAC9G,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;MACnB,IAAI,mBAAmB,KAAK,eAAe,GAAG,GAAG,CAAC;MAClD,IAAI,KAAK,GAAG,KAAK,CAAC;MAClB,IAAI,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;MACtC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,GAAG,EAAE,KAAK,EAAE,EAAE;QACxC,IAAI,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;;QAEzC,IAAI,MAAM,CAAC,WAAW,CAAC,mBAAmB,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE;UAC7D,MAAM,CAAC,cAAc,EAAE,CAAC;UACxB,KAAK,GAAG,IAAI,CAAC;UACb,MAAM;SACP;OACF;MACD,IAAI,CAAC,KAAK,EAAE;QACV,IAAI,KAAK,GAAG,IAAI,aAAa,CAAC,OAAO,EAAE,OAAO,EAAE,mBAAmB,CAAC,CAAC;QACrE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjC,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,EAAE;UACpC,IAAI,CAAC,mBAAmB,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;SAC1D;OACF;MACD,OAAO,IAAI,CAAC;KACb;GACF;EACD,OAAO,KAAK,CAAC;CACd,CAAC;;AAEF,mBAAmB,CAAC,SAAS,CAAC,kBAAkB,GAAG,WAAW;;EAE5D,IAAI,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;EAC5C,IAAI,SAAS,GAAG,CAAC,EAAE;;IAEjB,MAAM,uCAAuC,GAAG,SAAS,GAAG,iBAAiB,CAAC;GAC/E;;;EAGD,IAAI,SAAS,GAAG,CAAC,EAAE;;IAEjB,IAAI,eAAe,GAAG,GAAG,CAAC;IAC1B,IAAI,MAAM,GAAG,GAAG,CAAC;IACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;MAClC,KAAK,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC;MAC/D,eAAe,IAAI,WAAW,CAAC;MAC/B,MAAM,KAAK,WAAW,GAAG,WAAW,CAAC,CAAC;KACvC;IACD,IAAI,OAAO,GAAG,eAAe,IAAI,SAAS,CAAC;IAC3C,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,OAAO,EAAE,OAAO,EAAE;MACnD,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,mBAAmB,GAAG,OAAO,CAAC,CAAC;MACzD,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,mBAAmB,GAAG,OAAO,CAAC,CAAC;MACzD,IAAI,EAAE,GAAG,EAAE,EAAE;QACX,QAAQ,CAAC,CAAC,EAAE;OACb,MAAM,IAAI,EAAE,IAAI,EAAE,EAAE;QACnB,OAAO,CAAC,CAAC;OACV,MAAM;QACL,OAAO,CAAC,CAAC;OACV;KACF,CAAC,CAAC;;IAEH,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,SAAS,GAAG,OAAO,GAAG,OAAO,CAAC,CAAC;IAC/D,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,OAAO,EAAE,MAAM,CAAC,CAAC;IAC5C,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;MAClD,IAAI,OAAO,IAAI,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;MACvC,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,mBAAmB,GAAG,OAAO,CAAC,GAAG,KAAK,EAAE;QAC3D,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;OACnC;KACF;GACF;;EAED,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;;IAEnC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE;MACvC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;MACjC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;MAChC,OAAO,CAAC,CAAC;KACV,CAAC,CAAC;GACJ;;EAED,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;CACtF,CAAC;;AAEF,mBAAmB,CAAC,SAAS,CAAC,WAAW,GAAG,WAAW;EACrD,IAAI,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;EACtC,IAAI,GAAG,IAAI,CAAC,EAAE;IACZ,OAAO,CAAC,CAAC;GACV;EACD,IAAI,oBAAoB,GAAG,IAAI,CAAC;EAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;IAC5B,IAAI,MAAM,IAAI,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;IACtC,IAAI,MAAM,CAAC,KAAK,IAAI,aAAa,EAAE;MACjC,IAAI,oBAAoB,IAAI,IAAI,EAAE;QAChC,oBAAoB,GAAG,MAAM,CAAC;OAC/B,MAAM;;;;;;QAML,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;OACpH;KACF;GACF;EACD,OAAO,CAAC,CAAC;CACV,CAAC;;AAEF,mBAAmB,CAAC,SAAS,CAAC,4BAA4B,GAAG,WAAW;EACtE,IAAI,cAAc,GAAG,CAAC,CAAC;EACvB,IAAI,eAAe,GAAG,GAAG,CAAC;EAC1B,IAAI,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;EACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;IAC5B,IAAI,OAAO,IAAI,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;IACvC,IAAI,OAAO,CAAC,KAAK,IAAI,aAAa,EAAE;MAClC,cAAc,EAAE,CAAC;MACjB,eAAe,IAAI,OAAO,CAAC,mBAAmB,CAAC;KAChD;GACF;EACD,IAAI,cAAc,GAAG,CAAC,EAAE;IACtB,OAAO,KAAK,CAAC;GACd;;;;;EAKD,IAAI,OAAO,GAAG,eAAe,GAAG,GAAG,CAAC;EACpC,IAAI,cAAc,GAAG,GAAG,CAAC;EACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;IAC5B,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;IAClC,cAAc,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,mBAAmB,GAAG,OAAO,CAAC,CAAC;GACnE;EACD,OAAO,cAAc,IAAI,IAAI,GAAG,eAAe,CAAC;CACjD,CAAC;;AAEF,mBAAmB,CAAC,SAAS,CAAC,iBAAiB,GAAG,SAAS,KAAK,EAAE;EAChE,IAAI,SAAS,GAAG,KAAK,CAAC;EACtB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;EACnB,IAAI,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;EACxB,IAAI,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC;EACvB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;EACvD,IAAI,KAAK,GAAG,QAAQ,IAAI,SAAS,EAAE;IACjC,KAAK,GAAG,QAAQ,CAAC;GAClB;;EAED,IAAI,IAAI,GAAG,KAAK,CAAC;EACjB,IAAI,UAAU,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;EAC9B,KAAK,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,EAAE;;IAErD,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAClB,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAClB,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAClB,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAClB,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAClB,IAAI,YAAY,GAAG,CAAC,CAAC;IACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;MAC7B,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE;;QAEnC,IAAI,CAAC,YAAY,GAAG,CAAC,KAAK,CAAC,EAAE;;UAE3B,YAAY,EAAE,CAAC;SAChB;QACD,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;OAC5B,MAAM;;QAEL,IAAI,CAAC,YAAY,GAAG,CAAC,KAAK,CAAC,EAAE;;UAE3B,IAAI,YAAY,IAAI,CAAC,EAAE;;YAErB,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE;;cAEtC,IAAI,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;cAC5D,IAAI,SAAS,EAAE;;;gBAGb,KAAK,GAAG,CAAC,CAAC;gBACV,IAAI,IAAI,CAAC,UAAU,EAAE;kBACnB,IAAI,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;iBAC5C,MAAM;kBACL,IAAI,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;kBACjC,IAAI,OAAO,GAAG,UAAU,CAAC,CAAC,CAAC,EAAE;;;;;;;;;oBAS3B,CAAC,IAAI,OAAO,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;oBACrC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;mBACd;iBACF;eACF,MAAM;;gBAEL,GAAG;kBACD,CAAC,EAAE,CAAC;iBACL,QAAQ,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE;gBACvD,CAAC,EAAE,CAAC;eACL;;cAED,YAAY,GAAG,CAAC,CAAC;cACjB,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;cAClB,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;cAClB,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;cAClB,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;cAClB,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;aACnB,MAAM;;cAEL,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;cAC9B,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;cAC9B,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;cAC9B,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;cAClB,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;cAClB,YAAY,GAAG,CAAC,CAAC;aAClB;WACF,MAAM;YACL,UAAU,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC;WAC9B;SACF,MAAM;;UAEL,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;SAC5B;OACF;KACF;IACD,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE;MACtC,IAAI,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;MAC/D,IAAI,SAAS,EAAE;QACb,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;QACtB,IAAI,IAAI,CAAC,UAAU,EAAE;;UAEnB,IAAI,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;SAC5C;OACF;KACF;GACF;;EAED,IAAI,WAAW,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;EAC5C,iBAAiB,CAAC,WAAW,CAAC,CAAC;;EAE/B,OAAO,IAAI,iBAAiB,CAAC,WAAW,CAAC,CAAC;CAC3C,CAAC;;ACnhBF;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,AAKA,SAAS,oBAAoB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE;EACjF,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;EACf,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;EACf,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;EACf,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;EACf,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;EACf,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;EACf,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;EACf,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;EACf,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;CAChB;;AAED,oBAAoB,CAAC,SAAS,CAAC,gBAAgB,GAAG,SAAS,MAAM,EAAE;EACjE,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;EACxB,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;EACnB,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;EACnB,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;EACnB,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;EACnB,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;EACnB,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;EACnB,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;EACnB,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;EACnB,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;EACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;IAC/B,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAClB,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACtB,IAAI,WAAW,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IAC1C,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI,WAAW,CAAC;IACpD,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI,WAAW,CAAC;GACzD;CACF,CAAC;;AAEF,oBAAoB,CAAC,SAAS,CAAC,gBAAgB,GAAG,SAAS,OAAO,EAAE,OAAO,EAAE;EAC3E,IAAI,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;EACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;IAC1B,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;IACnB,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;IACnB,IAAI,WAAW,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;IACzD,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,WAAW,CAAC;IACpE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,WAAW,CAAC;GACrE;CACF,CAAC;;AAEF,oBAAoB,CAAC,SAAS,CAAC,YAAY,GAAG,WAAW;;EAEvD,OAAO,IAAI,oBAAoB,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;CACpa,CAAC;;AAEF,oBAAoB,CAAC,SAAS,CAAC,KAAK,GAAG,SAAS,KAAK,EAAE;EACrD,OAAO,IAAI,oBAAoB,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;CACroB,CAAC;;AAEF,oBAAoB,CAAC,4BAA4B,GAAG,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE;;EAElJ,IAAI,IAAI,GAAG,IAAI,CAAC,qBAAqB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;EACtE,IAAI,IAAI,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;EAC9E,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;CACzB,CAAC;;AAEF,oBAAoB,CAAC,qBAAqB,GAAG,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;EAC3F,IAAI,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;EAClB,IAAI,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;EAC5B,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE;IAC5B,OAAO,IAAI,oBAAoB,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;GAC5F,MAAM;IACL,IAAI,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;IAClB,IAAI,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;IAClB,IAAI,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IAC5B,IAAI,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;IAClB,IAAI,WAAW,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;IACxC,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,WAAW,CAAC;IAChD,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,WAAW,CAAC;IAChD,OAAO,IAAI,oBAAoB,CAAC,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;GACxI;CACF,CAAC;;AAEF,oBAAoB,CAAC,qBAAqB,GAAG,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;;EAE3F,OAAO,IAAI,CAAC,qBAAqB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,YAAY,EAAE,CAAC;CAClF,CAAC;;AAEF,SAAS,cAAc,CAAC,IAAI,GAAG,MAAM,EAAE;EACrC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;EACjB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;CACtB;;AAED,AAAe,SAAS,QAAQ,CAAC,KAAK,EAAE;EACtC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;EACnB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;CACjC;;AAED,QAAQ,CAAC,SAAS,CAAC,wBAAwB,GAAG,SAAS,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE;;;EAGhF,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;EAC1D,IAAI,KAAK,EAAE;IACT,IAAI,IAAI,GAAG,KAAK,CAAC;IACjB,KAAK,GAAG,KAAK,CAAC;IACd,KAAK,GAAG,IAAI,CAAC;IACb,IAAI,GAAG,GAAG,CAAC;IACX,GAAG,GAAG,GAAG,CAAC;IACV,GAAG,GAAG,IAAI,CAAC;GACZ;;EAED,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;EAC/B,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;EAC/B,IAAI,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;EACrB,IAAI,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EACjC,IAAI,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EACjC,IAAI,KAAK,GAAG,CAAC,CAAC;EACd,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,KAAK,EAAE;;IAEnD,IAAI,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;IAC1B,IAAI,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;IAC1B,IAAI,KAAK,IAAI,CAAC,EAAE;;MAEd,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;QACrD,KAAK,EAAE,CAAC;OACT;KACF,MAAM;MACL,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;QACtD,KAAK,EAAE,CAAC;OACT;KACF;;IAED,IAAI,KAAK,IAAI,CAAC,EAAE;;MAEd,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC;MACtB,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC;MACtB,QAAQ,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,EAAE,CAAC;KACpD;IACD,KAAK,IAAI,EAAE,CAAC;IACZ,IAAI,KAAK,GAAG,CAAC,EAAE;MACb,IAAI,CAAC,IAAI,GAAG,EAAE;QACZ,MAAM;OACP;MACD,CAAC,IAAI,KAAK,CAAC;MACX,KAAK,IAAI,EAAE,CAAC;KACb;GACF;EACD,IAAI,MAAM,GAAG,GAAG,GAAG,KAAK,CAAC;EACzB,IAAI,MAAM,GAAG,GAAG,GAAG,KAAK,CAAC;EACzB,QAAQ,IAAI,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,EAAE,CAAC;CACxD,CAAC;;AAEF,QAAQ,CAAC,SAAS,CAAC,gCAAgC,GAAG,SAAS,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE;;EAExF,IAAI,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;;;EAGnE,IAAI,KAAK,GAAG,GAAG,CAAC;EAChB,IAAI,QAAQ,GAAG,KAAK,IAAI,GAAG,GAAG,KAAK,CAAC,CAAC;EACrC,IAAI,QAAQ,GAAG,CAAC,EAAE;IAChB,KAAK,IAAI,KAAK,KAAK,KAAK,GAAG,QAAQ,CAAC,CAAC;IACrC,QAAQ,GAAG,CAAC,CAAC;GACd,MAAM,IAAI,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;IACvC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,KAAK,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC;IAC9D,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;GACjC;EACD,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,GAAG,KAAK,IAAI,KAAK,CAAC,CAAC;;EAEzD,KAAK,GAAG,GAAG,CAAC;EACZ,IAAI,QAAQ,GAAG,CAAC,EAAE;IAChB,KAAK,IAAI,KAAK,KAAK,KAAK,GAAG,QAAQ,CAAC,CAAC;IACrC,QAAQ,GAAG,CAAC,CAAC;GACd,MAAM,IAAI,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;IACxC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,KAAK,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC;IAC/D,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;GAClC;EACD,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,QAAQ,GAAG,KAAK,IAAI,KAAK,CAAC,CAAC;;EAE1D,MAAM,IAAI,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;EAC1E,OAAO,MAAM,GAAG,GAAG,CAAC;CACrB,CAAC;;AAEF,QAAQ,CAAC,SAAS,CAAC,yBAAyB,GAAG,SAAS,OAAO,GAAG,YAAY,EAAE;EAC9E,IAAI,cAAc,GAAG,IAAI,CAAC,gCAAgC,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;EACjK,IAAI,cAAc,GAAG,IAAI,CAAC,gCAAgC,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;EACjK,IAAI,KAAK,CAAC,cAAc,CAAC,EAAE;IACzB,OAAO,cAAc,GAAG,GAAG,CAAC;GAC7B;EACD,IAAI,KAAK,CAAC,cAAc,CAAC,EAAE;IACzB,OAAO,cAAc,GAAG,GAAG,CAAC;GAC7B;;;EAGD,OAAO,CAAC,cAAc,GAAG,cAAc,IAAI,IAAI,CAAC;CACjD,CAAC;;AAEF,QAAQ,CAAC,SAAS,CAAC,mBAAmB,GAAG,SAAS,OAAO,GAAG,QAAQ,GAAG,UAAU,EAAE;;EAEjF,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,OAAO,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAC,yBAAyB,CAAC,OAAO,EAAE,UAAU,CAAC,IAAI,GAAG,CAAC;CACxH,CAAC;;AAEF,QAAQ,CAAC,SAAS,CAAC,QAAQ,GAAG,SAAS,QAAQ,GAAG,QAAQ,EAAE;EAC1D,IAAI,KAAK,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;EACpC,IAAI,KAAK,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;EACpC,QAAQ,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,EAAE,CAAC;CACpD,CAAC;;AAEF,QAAQ,CAAC,SAAS,CAAC,gBAAgB,GAAG,SAAS,OAAO,GAAG,QAAQ,GAAG,UAAU,GAAG,UAAU,EAAE;EAC3F,IAAI,oBAAoB,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,GAAG,UAAU,CAAC,CAAC;EACrF,IAAI,oBAAoB,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC;EACvF,IAAI,SAAS,GAAG,CAAC,CAAC,oBAAoB,GAAG,oBAAoB,KAAK,CAAC,IAAI,CAAC,CAAC;EACzE,QAAQ,SAAS,GAAG,IAAI;;EAExB,KAAK,CAAC;IACJ,SAAS,EAAE,CAAC;IACZ,MAAM;;;EAGR,KAAK,CAAC;IACJ,SAAS,EAAE,CAAC;IACZ,MAAM;;EAER,KAAK,CAAC;IACJ,MAAM,OAAO,CAAC;GACf;EACD,OAAO,SAAS,CAAC;CAClB,CAAC;;AAEF,QAAQ,CAAC,SAAS,CAAC,qBAAqB,GAAG,SAAS,oBAAoB,GAAG,aAAa,GAAG,aAAa,GAAG,eAAe,EAAE;;;EAG1H,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,oBAAoB,CAAC,CAAC;EACnE,IAAI,kBAAkB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,aAAa,GAAG,SAAS,CAAC,CAAC;EAChE,IAAI,mBAAmB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,aAAa,GAAG,SAAS,CAAC,CAAC;EACpF,IAAI,mBAAmB,GAAG,kBAAkB,GAAG,oBAAoB,GAAG,CAAC,EAAE;IACvE,MAAM,OAAO,CAAC;GACf;;EAED,IAAI,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,aAAa,GAAG,SAAS,CAAC,CAAC;EAC/D,IAAI,oBAAoB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,aAAa,GAAG,SAAS,CAAC,CAAC;;EAEtF,IAAI,eAAe,GAAG,IAAI,sBAAsB,CAAC,IAAI,CAAC,KAAK,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,mBAAmB,GAAG,kBAAkB,EAAE,oBAAoB,GAAG,iBAAiB,EAAE,oBAAoB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;EACxO,OAAO,eAAe,CAAC,IAAI,EAAE,CAAC;CAC/B,CAAC;;AAEF,QAAQ,CAAC,SAAS,CAAC,eAAe,GAAG,SAAS,OAAO,GAAG,QAAQ,GAAG,UAAU,EAAE,gBAAgB,EAAE,SAAS,EAAE;EAC1G,IAAI,aAAa,IAAI,SAAS,GAAG,GAAG,CAAC;EACrC,IAAI,YAAY,CAAC;EACjB,IAAI,YAAY,CAAC;EACjB,IAAI,kBAAkB,CAAC;EACvB,IAAI,kBAAkB,CAAC;EACvB,IAAI,gBAAgB,IAAI,IAAI,EAAE;IAC5B,YAAY,GAAG,gBAAgB,CAAC,CAAC,CAAC;IAClC,YAAY,GAAG,gBAAgB,CAAC,CAAC,CAAC;IAClC,kBAAkB,GAAG,kBAAkB,GAAG,aAAa,GAAG,GAAG,CAAC;GAC/D,MAAM;;IAEL,YAAY,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC;IACvD,YAAY,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC;IACvD,kBAAkB,GAAG,kBAAkB,GAAG,aAAa,CAAC;GACzD;;EAED,IAAI,SAAS,GAAG,oBAAoB,CAAC,4BAA4B,CAAC,GAAG,EAAE,GAAG,EAAE,aAAa,EAAE,GAAG,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,GAAG,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,YAAY,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;;EAElQ,OAAO,SAAS,CAAC;CAClB,CAAC;;AAEF,QAAQ,CAAC,SAAS,CAAC,UAAU,GAAG,SAAS,KAAK,GAAG,SAAS,GAAG,SAAS,EAAE;;EAEtE,IAAI,OAAO,GAAG,WAAW,CAAC;EAC1B,OAAO,OAAO,CAAC,WAAW,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;CACzD,CAAC;;AAEF,QAAQ,CAAC,SAAS,CAAC,wBAAwB,GAAG,SAAS,IAAI,EAAE;;EAE3D,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;EAC3B,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;EAC7B,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;;EAEjC,IAAI,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;EACzE,IAAI,UAAU,GAAG,GAAG,EAAE;IACpB,MAAM,OAAO,CAAC;GACf;EACD,IAAI,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;EACjF,IAAI,kBAAkB,GAAG,OAAO,CAAC,iCAAiC,CAAC,SAAS,CAAC,CAAC;EAC9E,IAAI,uBAAuB,GAAG,kBAAkB,CAAC,mBAAmB,GAAG,CAAC,CAAC;;EAEzE,IAAI,gBAAgB,GAAG,IAAI,CAAC;;EAE5B,IAAI,kBAAkB,CAAC,uBAAuB,CAAC,MAAM,GAAG,CAAC,EAAE;;;IAGzD,IAAI,YAAY,GAAG,QAAQ,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC;IACzD,IAAI,YAAY,GAAG,QAAQ,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC;;;;IAIzD,IAAI,mBAAmB,GAAG,GAAG,GAAG,GAAG,IAAI,uBAAuB,CAAC;IAC/D,IAAI,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,mBAAmB,IAAI,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7F,IAAI,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,mBAAmB,IAAI,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;;;IAG7F,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,KAAK,CAAC,EAAE;;;MAGhC,gBAAgB,GAAG,IAAI,CAAC,qBAAqB,CAAC,UAAU,EAAE,aAAa,EAAE,aAAa,GAAG,CAAC,CAAC,CAAC;MAC5F,MAAM;;;;;;KAMP;;GAEF;;EAED,IAAI,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;;EAEjG,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;;EAE7D,IAAI,MAAM,CAAC;EACX,IAAI,gBAAgB,IAAI,IAAI,EAAE;IAC5B,MAAM,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;GAC1C,MAAM;IACL,MAAM,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,gBAAgB,CAAC,CAAC;GAC5D;EACD,OAAO,IAAI,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;CACzC,CAAC;;AAEF,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,WAAW;EACrC,IAAI,IAAI,IAAI,IAAI,mBAAmB,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;EAEpE,OAAO,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;CAC5C,CAAC;;ACnWF;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,AAAe,SAAS,SAAS,CAAC,KAAK,GAAG,YAAY,EAAE;EACtD,IAAI,YAAY,IAAI,IAAI,IAAI,YAAY,CAAC,MAAM,IAAI,CAAC,EAAE;IACpD,MAAM,0BAA0B,CAAC;GAClC;EACD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;EACnB,IAAI,kBAAkB,GAAG,YAAY,CAAC,MAAM,CAAC;EAC7C,IAAI,kBAAkB,GAAG,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;;IAElD,IAAI,YAAY,GAAG,CAAC,CAAC;IACrB,OAAO,YAAY,GAAG,kBAAkB,IAAI,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;MAC3E,YAAY,EAAE,CAAC;KAChB;IACD,IAAI,YAAY,IAAI,kBAAkB,EAAE;MACtC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC;KAC7C,MAAM;MACL,IAAI,CAAC,YAAY,GAAG,IAAI,KAAK,CAAC,kBAAkB,GAAG,YAAY,CAAC,CAAC;MACjE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MAC5E,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,YAAY,GAAG,EAAE,CAAC,CAAC;KAC/G;GACF,MAAM;IACL,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;GAClC;CACF;;AAED,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,EAAE;EACjD,GAAG,EAAE,WAAW;IACd,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;GAClC;CACF,CAAC,CAAC;;AAEH,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,SAAS,EAAE,QAAQ,EAAE;EACnD,GAAG,EAAE,WAAW;IACd,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;GACrC;CACF,CAAC,CAAC;;AAEH,SAAS,CAAC,SAAS,CAAC,cAAc,GAAG,SAAS,MAAM,EAAE;EACpD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC;CACjE,CAAC;;AAEF,SAAS,CAAC,SAAS,CAAC,UAAU,GAAG,SAAS,CAAC,EAAE;EAC3C,IAAI,CAAC,IAAI,CAAC,EAAE;;IAEV,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;GAC/B;EACD,IAAI,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;EACpC,IAAI,CAAC,IAAI,CAAC,EAAE;;IAEV,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;MAC7B,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;KACjE;IACD,OAAO,MAAM,CAAC;GACf;EACD,IAAI,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;EACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;IAC7B,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;GAC3F;EACD,OAAO,OAAO,CAAC;CAChB,CAAC;;AAEF,SAAS,CAAC,SAAS,CAAC,aAAa,GAAG,SAAS,KAAK,EAAE;EAClD,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;IAC7B,MAAM,yCAAyC,CAAC;GACjD;EACD,IAAI,IAAI,CAAC,IAAI,EAAE;IACb,OAAO,KAAK,CAAC;GACd;EACD,IAAI,KAAK,CAAC,IAAI,EAAE;IACd,OAAO,IAAI,CAAC;GACb;;EAED,IAAI,mBAAmB,GAAG,IAAI,CAAC,YAAY,CAAC;EAC5C,IAAI,kBAAkB,GAAG,KAAK,CAAC,YAAY,CAAC;EAC5C,IAAI,mBAAmB,CAAC,MAAM,GAAG,kBAAkB,CAAC,MAAM,EAAE;IAC1D,IAAI,IAAI,GAAG,mBAAmB,CAAC;IAC/B,mBAAmB,GAAG,kBAAkB,CAAC;IACzC,kBAAkB,GAAG,IAAI,CAAC;GAC3B;EACD,IAAI,OAAO,GAAG,IAAI,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;EACnD,IAAI,UAAU,GAAG,kBAAkB,CAAC,MAAM,GAAG,mBAAmB,CAAC,MAAM,CAAC;;EAExE,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,UAAU,EAAE,EAAE,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,kBAAkB,CAAC,EAAE,CAAC,CAAC;;EAE5E,KAAK,IAAI,CAAC,GAAG,UAAU,EAAE,CAAC,GAAG,kBAAkB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IAC3D,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;GACnG;;EAED,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;CAC3C,CAAC;;AAEF,SAAS,CAAC,SAAS,CAAC,SAAS,GAAG,SAAS,KAAK,EAAE;EAC9C,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;IAC7B,MAAM,yCAAyC,CAAC;GACjD;EACD,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,EAAE;IAC3B,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;GACxB;EACD,IAAI,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC;EACtC,IAAI,OAAO,GAAG,aAAa,CAAC,MAAM,CAAC;EACnC,IAAI,aAAa,GAAG,KAAK,CAAC,YAAY,CAAC;EACvC,IAAI,OAAO,GAAG,aAAa,CAAC,MAAM,CAAC;EACnC,IAAI,OAAO,GAAG,IAAI,KAAK,CAAC,OAAO,GAAG,OAAO,GAAG,CAAC,CAAC,CAAC;EAC/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE;IAChC,IAAI,MAAM,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;IAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE;MAChC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC1G;GACF;EACD,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;CAC3C,CAAC;;AAEF,SAAS,CAAC,SAAS,CAAC,SAAS,GAAG,SAAS,MAAM,EAAE;EAC/C,IAAI,MAAM,IAAI,CAAC,EAAE;IACf,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;GACxB;EACD,IAAI,MAAM,IAAI,CAAC,EAAE;IACf,OAAO,IAAI,CAAC;GACb;EACD,IAAI,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;EACpC,IAAI,OAAO,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC;EAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;IAC7B,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;GAChE;EACD,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;CAC3C,CAAC;;AAEF,SAAS,CAAC,SAAS,CAAC,kBAAkB,GAAG,SAAS,MAAM,GAAG,WAAW,EAAE;EACtE,IAAI,MAAM,GAAG,CAAC,EAAE;IACd,MAAM,0BAA0B,CAAC;GAClC;EACD,IAAI,WAAW,IAAI,CAAC,EAAE;IACpB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;GACxB;EACD,IAAI,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;EACpC,IAAI,OAAO,GAAG,IAAI,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC;EACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;IAC7B,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;GACrE;EACD,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;CAC3C,CAAC;;AAEF,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,SAAS,KAAK,EAAE;EAC3C,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;IAC7B,MAAM,yCAAyC,CAAC;GACjD;EACD,IAAI,KAAK,CAAC,IAAI,EAAE;IACd,MAAM,aAAa,CAAC;GACrB;;EAED,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;EAC/B,IAAI,SAAS,GAAG,IAAI,CAAC;;EAErB,IAAI,sBAAsB,GAAG,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;EAChE,IAAI,6BAA6B,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;;EAE/E,OAAO,SAAS,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;IAC1D,IAAI,gBAAgB,GAAG,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;IACvD,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,6BAA6B,CAAC,CAAC;IAC3G,IAAI,IAAI,GAAG,KAAK,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;IAC7D,IAAI,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;IAC1E,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;IACrD,SAAS,GAAG,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;GAC3C;;EAED,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;CAC9B,CAAC;;AC/LF;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,AAEe,SAAS,KAAK,CAAC,SAAS,EAAE;EACvC,IAAI,CAAC,QAAQ,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;EAC/B,IAAI,CAAC,QAAQ,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;EAC/B,IAAI,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;IAC5B,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACrB,CAAC,KAAK,CAAC,CAAC;IACR,IAAI,CAAC,IAAI,KAAK,EAAE;MACd,CAAC,IAAI,SAAS,CAAC;KAChB;GACF;EACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;IAC5B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;GACrC;;EAED,IAAI,GAAG,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACnC,IAAI,CAAC,IAAI,GAAG,IAAI,SAAS,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;EAChD,IAAI,GAAG,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACnC,IAAI,CAAC,GAAG,GAAG,IAAI,SAAS,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;CAChD;;AAED,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,MAAM,EAAE;EAC7C,GAAG,EAAE,WAAW;IACd,OAAO,IAAI,CAAC,IAAI,CAAC;GAClB;CACF,CAAC,CAAC;;AAEH,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,EAAE;EAC5C,GAAG,EAAE,WAAW;IACd,OAAO,IAAI,CAAC,GAAG,CAAC;GACjB;CACF,CAAC,CAAC;;AAEH,KAAK,CAAC,SAAS,CAAC,aAAa,GAAG,SAAS,MAAM,GAAG,WAAW,EAAE;EAC7D,IAAI,MAAM,GAAG,CAAC,EAAE;IACd,MAAM,0BAA0B,CAAC;GAClC;EACD,IAAI,WAAW,IAAI,CAAC,EAAE;IACpB,OAAO,IAAI,CAAC,IAAI,CAAC;GAClB;EACD,IAAI,YAAY,GAAG,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;EACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACjE,YAAY,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC;EAC9B,OAAO,IAAI,SAAS,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;CAC1C,CAAC;;AAEF,KAAK,CAAC,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,EAAE;EAChC,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;CACzB,CAAC;;AAEF,KAAK,CAAC,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,EAAE;EAChC,IAAI,CAAC,IAAI,CAAC,EAAE;IACV,MAAM,0BAA0B,CAAC;GAClC;EACD,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;CACzB,CAAC;;AAEF,KAAK,CAAC,SAAS,CAAC,OAAO,GAAG,SAAS,CAAC,EAAE;EACpC,IAAI,CAAC,IAAI,CAAC,EAAE;IACV,MAAM,4BAA4B,CAAC;GACpC;EACD,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;CAC9C,CAAC;;AAEF,KAAK,CAAC,SAAS,CAAC,aAAa,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE;EAC9C,OAAO,CAAC,GAAG,CAAC,CAAC;CACd,CAAC;;AAEF,KAAK,CAAC,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE;EACzC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;IACpB,OAAO,CAAC,CAAC;GACV;EACD,IAAI,CAAC,IAAI,CAAC,EAAE;IACV,OAAO,CAAC,CAAC;GACV;EACD,IAAI,CAAC,IAAI,CAAC,EAAE;IACV,OAAO,CAAC,CAAC;GACV;EACD,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;CACnE,CAAC;;AAEF,KAAK,CAAC,aAAa,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;AACxC,KAAK,CAAC,iBAAiB,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;;AC5G5C;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,AAGe,SAAS,kBAAkB,CAAC,KAAK,EAAE;EAChD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;CACpB;;AAED,kBAAkB,CAAC,SAAS,CAAC,MAAM,GAAG,SAAS,QAAQ,EAAE,IAAI,EAAE;EAC7D,IAAI,IAAI,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;EAC/C,IAAI,oBAAoB,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC;EAC3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,oBAAoB,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACjF,IAAI,UAAU,GAAG,KAAK,CAAC;EACvB,IAAI,OAAO,GAAG,IAAI,CAAC;EACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;;IAE7B,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACpE,oBAAoB,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;IAClE,IAAI,KAAK,IAAI,CAAC,EAAE;MACd,OAAO,GAAG,KAAK,CAAC;KACjB;GACF;EACD,IAAI,OAAO,EAAE;IACX,OAAO;GACR;EACD,IAAI,QAAQ,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,oBAAoB,CAAC,CAAC;EAC/D,IAAI,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;EAC/F,IAAI,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;EAC1B,IAAI,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;EAC1B,IAAI,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;EACpD,IAAI,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC;EAClF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IAC9C,IAAI,QAAQ,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IACvE,IAAI,QAAQ,GAAG,CAAC,EAAE;MAChB,MAAM,yCAAyC,CAAC;KACjD;IACD,QAAQ,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;GAC5F;CACF,CAAC;;AAEF,kBAAkB,CAAC,SAAS,CAAC,qBAAqB,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;;EAEvE,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,EAAE;IACvB,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,CAAC,GAAG,CAAC,CAAC;IACN,CAAC,GAAG,IAAI,CAAC;GACV;;EAED,IAAI,KAAK,GAAG,CAAC,CAAC;EACd,IAAI,CAAC,GAAG,CAAC,CAAC;EACV,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;EAC3B,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;EACxB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;EAC5B,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;;;EAGvB,OAAO,CAAC,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;IACpC,IAAI,SAAS,GAAG,KAAK,CAAC;IACtB,IAAI,SAAS,GAAG,KAAK,CAAC;IACtB,IAAI,SAAS,GAAG,KAAK,CAAC;IACtB,KAAK,GAAG,CAAC,CAAC;IACV,KAAK,GAAG,CAAC,CAAC;IACV,KAAK,GAAG,CAAC,CAAC;;;IAGV,IAAI,KAAK,CAAC,IAAI,EAAE;;MAEd,MAAM,kBAAkB,CAAC;KAC1B;IACD,CAAC,GAAG,SAAS,CAAC;IACd,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IACxB,IAAI,sBAAsB,GAAG,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAChE,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;IAC5D,OAAO,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;MAC1C,IAAI,UAAU,GAAG,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;MACzC,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,UAAU,CAAC,CAAC;MACxE,CAAC,GAAG,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC;MACjE,CAAC,GAAG,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,kBAAkB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC;KAClE;;IAED,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;IAChD,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;GACjD;;EAED,IAAI,gBAAgB,GAAG,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;EAC3C,IAAI,gBAAgB,IAAI,CAAC,EAAE;IACzB,MAAM,6CAA6C,CAAC;GACrD;;EAED,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;EACnD,IAAI,KAAK,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;EACjC,IAAI,KAAK,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;EACjC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;CACvB,CAAC;;AAEF,kBAAkB,CAAC,SAAS,CAAC,kBAAkB,GAAG,SAAS,YAAY,EAAE;;EAEvE,IAAI,SAAS,GAAG,YAAY,CAAC,MAAM,CAAC;EACpC,IAAI,SAAS,IAAI,CAAC,EAAE;;IAElB,OAAO,IAAI,KAAK,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;GAClD;EACD,IAAI,MAAM,GAAG,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC;EAClC,IAAI,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;IAC7C,IAAI,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;MACnC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,EAAE,CAAC;KACL;GACF;EACD,IAAI,CAAC,IAAI,SAAS,EAAE;IAClB,MAAM,qDAAqD,CAAC;GAC7D;EACD,OAAO,MAAM,CAAC;CACf,CAAC;;AAEF,kBAAkB,CAAC,SAAS,CAAC,mBAAmB,GAAG,SAAS,cAAc,EAAE,cAAc,EAAE,UAAU,EAAE;;EAEtG,IAAI,CAAC,GAAG,cAAc,CAAC,MAAM,CAAC;EAC9B,IAAI,MAAM,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;EAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;IAC1B,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IACtD,IAAI,WAAW,GAAG,CAAC,CAAC;IACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;MAC1B,IAAI,CAAC,IAAI,CAAC,EAAE;QACV,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;OACrI;KACF;IACD,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;;IAEvG,IAAI,UAAU,EAAE;MACd,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;KACvD;GACF;EACD,OAAO,MAAM,CAAC;CACf,CAAC;;AC9JF;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,AAEA,IAAI,QAAQ,GAAG,EAAE,CAAC;;AAElB,QAAQ,CAAC,YAAY,GAAG,SAAS,SAAS,EAAE;EAC1C,IAAI,SAAS,GAAG,CAAC,IAAI,SAAS,GAAG,CAAC,EAAE;IAClC,MAAM,0BAA0B,CAAC;GAClC;EACD,OAAO,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;CACvC,CAAC;;AAEF,SAAS,WAAW,GAAG;EACrB,IAAI,CAAC,eAAe,GAAG,SAAS,IAAI,GAAG,SAAS,EAAE;IAChD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;MAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;QAClC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UACvB,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACjB;OACF;KACF;GACF,CAAC;EACF,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE;IAC9B,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC;GAC9B,CAAC;CACH;;AAED,SAAS,WAAW,GAAG;EACrB,IAAI,CAAC,eAAe,GAAG,SAAS,IAAI,GAAG,SAAS,EAAE;IAChD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;MAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;QAClC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UACvB,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACjB;OACF;KACF;GACF,CAAC;EACF,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE;IAC9B,OAAO,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC;GACxB,CAAC;CACH;;AAED,SAAS,WAAW,GAAG;EACrB,IAAI,CAAC,eAAe,GAAG,SAAS,IAAI,GAAG,SAAS,EAAE;IAChD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;MAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;QAClC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UACvB,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACjB;OACF;KACF;GACF,CAAC;EACF,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE;IAC9B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;GACnB,CAAC;CACH;;AAED,SAAS,WAAW,GAAG;EACrB,IAAI,CAAC,eAAe,GAAG,SAAS,IAAI,GAAG,SAAS,EAAE;IAChD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;MAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;QAClC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UACvB,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACjB;OACF;KACF;GACF,CAAC;EACF,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE;IAC9B,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;GACzB,CAAC;CACH;;AAED,SAAS,WAAW,GAAG;EACrB,IAAI,CAAC,eAAe,GAAG,SAAS,IAAI,GAAG,SAAS,EAAE;IAChD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;MAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;QAClC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UACvB,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACjB;OACF;KACF;GACF,CAAC;EACF,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE;IAC9B,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC;GAClD,CAAC;CACH;;AAED,SAAS,WAAW,GAAG;EACrB,IAAI,CAAC,eAAe,GAAG,SAAS,IAAI,GAAG,SAAS,EAAE;IAChD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;MAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;QAClC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UACvB,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACjB;OACF;KACF;GACF,CAAC;EACF,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE;IAC9B,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IACjB,OAAO,CAAC,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;GACxC,CAAC;CACH;;AAED,SAAS,WAAW,GAAG;EACrB,IAAI,CAAC,eAAe,GAAG,SAAS,IAAI,GAAG,SAAS,EAAE;IAChD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;MAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;QAClC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UACvB,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACjB;OACF;KACF;GACF,CAAC;EACF,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE;IAC9B,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IACjB,OAAO,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC;GACnD,CAAC;CACH;AACD,SAAS,WAAW,GAAG;EACrB,IAAI,CAAC,eAAe,GAAG,SAAS,IAAI,GAAG,SAAS,EAAE;IAChD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;MAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;QAClC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UACvB,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACjB;OACF;KACF;GACF,CAAC;EACF,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE;IAC9B,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC;GACzD,CAAC;CACH;;AAED,QAAQ,CAAC,UAAU,GAAG,CAAC,IAAI,WAAW,EAAE,EAAE,IAAI,WAAW,EAAE,EAAE,IAAI,WAAW,EAAE,EAAE,IAAI,WAAW,EAAE,EAAE,IAAI,WAAW,EAAE,EAAE,IAAI,WAAW,EAAE,EAAE,IAAI,WAAW,EAAE,EAAE,IAAI,WAAW,EAAE,CAAC,CAAC;;AC5J/K;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,AAIe,SAAS,eAAe,CAAC,SAAS,EAAE;EACjD,IAAI,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;EACpC,IAAI,SAAS,GAAG,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,KAAK,CAAC,EAAE;IAC7C,MAAM,uBAAuB,CAAC;GAC/B;EACD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;EAC3B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;EAC1B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;CAC9B;;AAED,eAAe,CAAC,SAAS,CAAC,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE;EAChE,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,IAAI,GAAG,GAAG,WAAW,IAAI,CAAC,CAAC;CACvF,CAAC;;AAEF,eAAe,CAAC,SAAS,CAAC,qBAAqB,GAAG,WAAW;EAC3D,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,EAAE;IACjC,OAAO,IAAI,CAAC,gBAAgB,CAAC;GAC9B;;;EAGD,IAAI,cAAc,GAAG,CAAC,CAAC;EACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;IAC1B,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,cAAc,CAAC,CAAC;GACrD;;EAED,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,cAAc,CAAC,CAAC;EACpD,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,cAAc,CAAC,CAAC;EACpD,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,cAAc,CAAC,CAAC;;EAEpD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;IAC3B,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,cAAc,CAAC,CAAC;GACrD;;EAED,IAAI,CAAC,gBAAgB,GAAG,iBAAiB,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC;EAClF,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,EAAE;IACjC,OAAO,IAAI,CAAC,gBAAgB,CAAC;GAC9B;;;EAGD,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;EACzC,cAAc,GAAG,CAAC,CAAC;EACnB,IAAI,IAAI,GAAG,SAAS,GAAG,CAAC,CAAC;EACzB,KAAK,IAAI,CAAC,GAAG,SAAS,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,EAAE,EAAE;IAC1C,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,cAAc,CAAC,CAAC;GACrD;EACD,KAAK,IAAI,CAAC,GAAG,SAAS,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;IAC9C,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,cAAc,CAAC,CAAC;GACrD;;EAED,IAAI,CAAC,gBAAgB,GAAG,iBAAiB,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC;EAClF,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,EAAE;IACjC,OAAO,IAAI,CAAC,gBAAgB,CAAC;GAC9B;EACD,MAAM,6BAA6B,CAAC;CACrC,CAAC;;AAEF,eAAe,CAAC,SAAS,CAAC,WAAW,GAAG,WAAW;EACjD,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,EAAE;IAC9B,OAAO,IAAI,CAAC,aAAa,CAAC;GAC3B;;EAED,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;;EAEzC,IAAI,kBAAkB,GAAG,CAAC,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;EAC/C,IAAI,kBAAkB,IAAI,CAAC,EAAE;IAC3B,OAAO,OAAO,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,CAAC;GACxD;;;EAGD,IAAI,WAAW,GAAG,CAAC,CAAC;EACpB,IAAI,KAAK,GAAG,SAAS,GAAG,EAAE,CAAC;EAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;IAC3B,KAAK,IAAI,CAAC,GAAG,SAAS,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE;MAC3C,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;KAC/C;GACF;;EAED,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;EACnE,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,IAAI,IAAI,CAAC,aAAa,CAAC,mBAAmB,IAAI,SAAS,EAAE;IACrF,OAAO,IAAI,CAAC,aAAa,CAAC;GAC3B;;;EAGD,WAAW,GAAG,CAAC,CAAC;EAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;IAC3B,KAAK,IAAI,CAAC,GAAG,SAAS,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE;MAC3C,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;KAC/C;GACF;;EAED,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;EACnE,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,IAAI,IAAI,CAAC,aAAa,CAAC,mBAAmB,IAAI,SAAS,EAAE;IACrF,OAAO,IAAI,CAAC,aAAa,CAAC;GAC3B;EACD,MAAM,mBAAmB,CAAC;CAC3B,CAAC;;AAEF,eAAe,CAAC,SAAS,CAAC,aAAa,GAAG,WAAW;EACnD,IAAI,UAAU,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;EAC9C,IAAI,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;;;;EAIjC,IAAI,QAAQ,GAAG,QAAQ,CAAC,YAAY,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;EAC1D,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;EACzC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;;EAEpD,IAAI,eAAe,GAAG,OAAO,CAAC,oBAAoB,EAAE,CAAC;;EAErD,IAAI,SAAS,GAAG,IAAI,CAAC;EACrB,IAAI,MAAM,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;EAC/C,IAAI,YAAY,GAAG,CAAC,CAAC;EACrB,IAAI,WAAW,GAAG,CAAC,CAAC;EACpB,IAAI,QAAQ,GAAG,CAAC,CAAC;;EAEjB,KAAK,IAAI,CAAC,GAAG,SAAS,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;IACzC,IAAI,CAAC,IAAI,CAAC,EAAE;;;MAGV,CAAC,EAAE,CAAC;KACL;;IAED,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,SAAS,EAAE,KAAK,EAAE,EAAE;MAC9C,IAAI,CAAC,GAAG,SAAS,GAAG,SAAS,GAAG,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC;MAClD,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE;;QAEhC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,EAAE;;UAE5C,QAAQ,EAAE,CAAC;UACX,WAAW,KAAK,CAAC,CAAC;UAClB,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,EAAE;YAC1C,WAAW,IAAI,CAAC,CAAC;WAClB;;UAED,IAAI,QAAQ,IAAI,CAAC,EAAE;YACjB,MAAM,CAAC,YAAY,EAAE,CAAC,IAAI,WAAW,CAAC;YACtC,QAAQ,GAAG,CAAC,CAAC;YACb,WAAW,GAAG,CAAC,CAAC;WACjB;SACF;OACF;KACF;IACD,SAAS,IAAI,IAAI,CAAC;GACnB;EACD,IAAI,YAAY,IAAI,OAAO,CAAC,cAAc,EAAE;IAC1C,MAAM,qBAAqB,CAAC;GAC7B;EACD,OAAO,MAAM,CAAC;CACf,CAAC;;AChLF;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,AAAe,SAAS,SAAS,CAAC,gBAAgB,GAAG,SAAS,EAAE;EAC9D,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;EACzC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;CAC5B;;AAED,SAAS,CAAC,aAAa,GAAG,SAAS,YAAY,GAAG,OAAO,GAAG,OAAO,EAAE;;EAEnE,IAAI,YAAY,CAAC,MAAM,IAAI,OAAO,CAAC,cAAc,EAAE;IACjD,MAAM,mBAAmB,CAAC;GAC3B;;;;EAID,IAAI,QAAQ,GAAG,OAAO,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;;;EAGpD,IAAI,WAAW,GAAG,CAAC,CAAC;EACpB,IAAI,YAAY,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;EAC1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IAC5C,WAAW,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;GACtC;;;EAGD,IAAI,MAAM,GAAG,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC;EACpC,IAAI,eAAe,GAAG,CAAC,CAAC;EACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IAC5C,IAAI,OAAO,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;IAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;MACtC,IAAI,gBAAgB,GAAG,OAAO,CAAC,aAAa,CAAC;MAC7C,IAAI,iBAAiB,GAAG,QAAQ,CAAC,mBAAmB,GAAG,gBAAgB,CAAC;MACxE,MAAM,CAAC,eAAe,EAAE,CAAC,GAAG,IAAI,SAAS,CAAC,gBAAgB,EAAE,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC;KAC3F;GACF;;;;EAID,IAAI,2BAA2B,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC;EAC7D,IAAI,mBAAmB,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;EAC5C,OAAO,mBAAmB,IAAI,CAAC,EAAE;IAC/B,IAAI,YAAY,GAAG,MAAM,CAAC,mBAAmB,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC;IAChE,IAAI,YAAY,IAAI,2BAA2B,EAAE;MAC/C,MAAM;KACP;IACD,mBAAmB,EAAE,CAAC;GACvB;EACD,mBAAmB,EAAE,CAAC;;EAEtB,IAAI,6BAA6B,GAAG,2BAA2B,GAAG,QAAQ,CAAC,mBAAmB,CAAC;;;EAG/F,IAAI,kBAAkB,GAAG,CAAC,CAAC;EAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,6BAA6B,EAAE,CAAC,EAAE,EAAE;IACtD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,EAAE,CAAC,EAAE,EAAE;MACxC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,kBAAkB,EAAE,CAAC,CAAC;KAC7D;GACF;;EAED,KAAK,IAAI,CAAC,GAAG,mBAAmB,EAAE,CAAC,GAAG,eAAe,EAAE,CAAC,EAAE,EAAE;IAC1D,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,6BAA6B,CAAC,GAAG,YAAY,CAAC,kBAAkB,EAAE,CAAC,CAAC;GACzF;;EAED,IAAI,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC;EACrC,KAAK,IAAI,CAAC,GAAG,6BAA6B,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;IACxD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,EAAE,CAAC,EAAE,EAAE;MACxC,IAAI,OAAO,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MAClD,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,YAAY,CAAC,kBAAkB,EAAE,CAAC,CAAC;KACnE;GACF;EACD,OAAO,MAAM,CAAC;CACf,CAAC;;AC9FF;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,AAEe,SAAS,qBAAqB,CAAC,MAAM,GAAG,OAAO,GAAG,sBAAsB,EAAE;EACvF,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;EACtB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;EACpB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;EACpB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;EACrB,IAAI,CAAC,sBAAsB,GAAG,sBAAsB,CAAC;EACrD,IAAI,OAAO,IAAI,CAAC;IACd,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;OACrB,IAAI,OAAO,IAAI,EAAE,IAAI,OAAO,IAAI,EAAE;IACrC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;OACrB,IAAI,OAAO,IAAI,EAAE,IAAI,OAAO,IAAI,EAAE;IACrC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;CAC3B;;AAED,qBAAqB,CAAC,SAAS,CAAC,WAAW,GAAG,SAAS,OAAO,EAAE;EAC9D,IAAI,IAAI,GAAG,CAAC,CAAC;EACb,IAAI,OAAO,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE;;IAEjC,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE;MAChC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;KAClB;IACD,IAAI,MAAM,IAAI,CAAC,UAAU,GAAG,OAAO,GAAG,CAAC,CAAC,CAAC;;IAEzC,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,IAAI,MAAM,IAAI,CAAC,UAAU,GAAG,OAAO,GAAG,CAAC,CAAC,CAAC;IAClF,IAAI,CAAC,UAAU,IAAI,OAAO,CAAC;IAC3B,OAAO,IAAI,CAAC;GACb,MAAM,IAAI,OAAO,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,EAAE;;IAE5C,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;MAC5C,KAAK,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;KACnB;IACD,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;IACrF,IAAI,CAAC,YAAY,EAAE,CAAC;IACpB,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,OAAO,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEtF,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,GAAG,OAAO,GAAG,CAAC,CAAC;IAChD,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE;MACvB,IAAI,CAAC,UAAU,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC;KACvC;IACD,OAAO,IAAI,CAAC;GACb,MAAM,IAAI,OAAO,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,GAAG,EAAE,EAAE;;IAE7C,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,KAAK,GAAG,CAAC,CAAC;;;;IAId,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;MAC5C,KAAK,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;KACnB;IACD,IAAI,cAAc,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;IACnG,IAAI,CAAC,YAAY,EAAE,CAAC;;IAEpB,IAAI,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,OAAO,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC9F,IAAI,CAAC,YAAY,EAAE,CAAC;;IAEpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;MAC5D,KAAK,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;KACnB;IACD,KAAK,KAAK,CAAC,IAAI,OAAO,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACpD,IAAI,cAAc,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,KAAK,MAAM,CAAC,IAAI,OAAO,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE7G,IAAI,GAAG,cAAc,GAAG,eAAe,GAAG,cAAc,CAAC;IACzD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC;IACtD,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE;MACvB,IAAI,CAAC,UAAU,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC;KACvC;IACD,OAAO,IAAI,CAAC;GACb,MAAM;IACL,OAAO,CAAC,CAAC;GACV;CACF,CAAC;;AAEF,qBAAqB,CAAC,SAAS,CAAC,QAAQ,GAAG,WAAW;EACpD,KAAK,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,sBAAsB,GAAG,CAAC;IAC3E,OAAO,CAAC,CAAC;;IAET,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;CAC9B,CAAC;;AAEF,qBAAqB,CAAC,SAAS,CAAC,aAAa,GAAG,SAAS,aAAa,EAAE;EACtE,IAAI,KAAK,GAAG,CAAC,CAAC;EACd,OAAO,IAAI,EAAE;IACX,IAAI,CAAC,aAAa,IAAI,KAAK,KAAK,CAAC;MAC/B,MAAM;IACR,KAAK,EAAE,CAAC;GACT;;EAED,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;CAClF,CAAC;;AAEF,qBAAqB,CAAC,SAAS,CAAC,uBAAuB,GAAG,SAAS,UAAU,EAAE;EAC7E,IAAI,MAAM,GAAG,UAAU,CAAC;EACxB,IAAI,OAAO,GAAG,CAAC,CAAC;EAChB,IAAI,OAAO,GAAG,EAAE,CAAC;EACjB,IAAI,mBAAmB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;EAC5P,GAAG;IACD,IAAI,MAAM,GAAG,CAAC,EAAE;MACd,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;MAC/B,IAAI,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC;MAC3C,IAAI,YAAY,GAAG,OAAO,GAAG,EAAE,CAAC;MAChC,OAAO,IAAI,mBAAmB,CAAC,WAAW,CAAC,CAAC;MAC5C,OAAO,IAAI,mBAAmB,CAAC,YAAY,CAAC,CAAC;MAC7C,MAAM,IAAI,CAAC,CAAC;KACb,MAAM,IAAI,MAAM,IAAI,CAAC,EAAE;MACtB,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;MAC9B,OAAO,IAAI,mBAAmB,CAAC,OAAO,CAAC,CAAC;MACxC,MAAM,IAAI,CAAC,CAAC;KACb;GACF;SACM,MAAM,GAAG,CAAC,EAAE;;EAEnB,OAAO,OAAO,CAAC;CAChB,CAAC;;AAEF,qBAAqB,CAAC,SAAS,CAAC,eAAe,GAAG,SAAS,UAAU,EAAE;EACrE,IAAI,MAAM,GAAG,UAAU,CAAC;EACxB,IAAI,OAAO,GAAG,CAAC,CAAC;EAChB,IAAI,OAAO,GAAG,EAAE,CAAC;EACjB,GAAG;IACD,IAAI,MAAM,IAAI,CAAC,EAAE;MACf,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;MAC/B,IAAI,OAAO,GAAG,GAAG;QACf,OAAO,IAAI,GAAG,CAAC;MACjB,IAAI,OAAO,GAAG,EAAE;QACd,OAAO,IAAI,GAAG,CAAC;MACjB,MAAM,IAAI,CAAC,CAAC;KACb,MAAM,IAAI,MAAM,IAAI,CAAC,EAAE;MACtB,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;MAC9B,IAAI,OAAO,GAAG,EAAE;QACd,OAAO,IAAI,GAAG,CAAC;MACjB,MAAM,IAAI,CAAC,CAAC;KACb,MAAM,IAAI,MAAM,IAAI,CAAC,EAAE;MACtB,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;MAC9B,MAAM,IAAI,CAAC,CAAC;KACb;IACD,OAAO,IAAI,OAAO,CAAC;GACpB;SACM,MAAM,GAAG,CAAC,EAAE;;EAEnB,OAAO,OAAO,CAAC;CAChB,CAAC;;AAEF,qBAAqB,CAAC,SAAS,CAAC,gBAAgB,GAAG,SAAS,UAAU,EAAE;EACtE,IAAI,MAAM,GAAG,UAAU,CAAC;EACxB,IAAI,OAAO,GAAG,CAAC,CAAC;EAChB,IAAI,MAAM,GAAG,EAAE,CAAC;;EAEhB,GAAG;IACD,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAC9B,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACrB,MAAM,EAAE,CAAC;GACV;SACM,MAAM,GAAG,CAAC,EAAE;EACnB,OAAO,MAAM,CAAC;CACf,CAAC;;AAEF,qBAAqB,CAAC,SAAS,CAAC,cAAc,GAAG,SAAS,UAAU,EAAE;EACpE,IAAI,MAAM,GAAG,UAAU,CAAC;EACxB,IAAI,OAAO,GAAG,CAAC,CAAC;EAChB,IAAI,aAAa,GAAG,EAAE,CAAC;EACvB,GAAG;IACD,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;IAC/B,IAAI,SAAS,GAAG,OAAO,GAAG,IAAI,CAAC;IAC/B,IAAI,UAAU,GAAG,OAAO,GAAG,IAAI,CAAC;;IAEhC,IAAI,QAAQ,GAAG,CAAC,UAAU,IAAI,CAAC,IAAI,SAAS,CAAC;IAC7C,IAAI,YAAY,GAAG,CAAC,CAAC;IACrB,IAAI,QAAQ,GAAG,MAAM,IAAI,MAAM,EAAE;;MAE/B,YAAY,GAAG,QAAQ,GAAG,MAAM,CAAC;KAClC,MAAM;;MAEL,YAAY,GAAG,QAAQ,GAAG,MAAM,CAAC;KAClC;;IAED,aAAa,IAAI,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;IACnD,MAAM,EAAE,CAAC;GACV;SACM,MAAM,GAAG,CAAC,EAAE;;;EAGnB,OAAO,aAAa,CAAC;CACtB,CAAC;;AAEF,MAAM,CAAC,cAAc,CAAC,qBAAqB,CAAC,SAAS,EAAE,UAAU,EAAE;EACjE,GAAG,EAAE,WAAW;IACd,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAI,WAAW,GAAG,CAAC,CAAC;IACpB,IAAI,qBAAqB,GAAG,CAAC,CAAC;IAC9B,IAAI,cAAc,GAAG,CAAC,CAAC;IACvB,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,GAAG;MACD,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;MAC3B,IAAI,IAAI,IAAI,CAAC,EAAE;QACb,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC;UACnB,MAAM;;UAEN,MAAM,kBAAkB,CAAC;OAC5B;;;MAGD,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI,IAAI,qBAAqB,IAAI,IAAI,IAAI,cAAc,IAAI,IAAI,IAAI,UAAU,IAAI,IAAI,IAAI,CAAC,EAAE;;;;QAIrH,MAAM,gBAAgB,GAAG,IAAI,GAAG,aAAa,GAAG,IAAI,CAAC,YAAY,GAAG,OAAO,GAAG,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;OACrG;MACD,IAAI,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;MAC1C,IAAI,UAAU,GAAG,CAAC;QAChB,MAAM,uBAAuB,GAAG,UAAU,CAAC;MAC7C,QAAQ,IAAI;;MAEZ,KAAK,WAAW;QACd,IAAI,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,EAAE,GAAG,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE;UACtC,EAAE,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACjC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAChB,MAAM;;MAER,KAAK,qBAAqB;QACxB,IAAI,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;QACxD,IAAI,EAAE,GAAG,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE;UACtC,EAAE,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACjC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAChB,MAAM;;MAER,KAAK,cAAc;QACjB,IAAI,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;QACzD,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC9B,MAAM;;MAER,KAAK,UAAU;QACb,IAAI,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAC/C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtB,MAAM;OACP;;KAEF;WACM,IAAI,EAAE;IACb,OAAO,MAAM,CAAC;GACf;CACF,CAAC,CAAC;;AChRH;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,AAMA,IAAI,OAAO,GAAG,EAAE,CAAC;AACjB,OAAO,CAAC,SAAS,GAAG,IAAI,kBAAkB,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;;AAEhE,OAAO,CAAC,aAAa,GAAG,SAAS,aAAa,GAAG,gBAAgB,EAAE;EACjE,IAAI,YAAY,GAAG,aAAa,CAAC,MAAM,CAAC;;EAExC,IAAI,aAAa,GAAG,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;EAC5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;IACrC,aAAa,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;GAC5C;EACD,IAAI,cAAc,GAAG,aAAa,CAAC,MAAM,GAAG,gBAAgB,CAAC;EAC7D,IAAI;IACF,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;GACzD,CAAC,OAAO,GAAG,EAAE;IACZ,MAAM,GAAG,CAAC;GACX;;;EAGD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC,EAAE,EAAE;IACzC,aAAa,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC;GACtC;CACF,CAAC;;AAEF,OAAO,CAAC,MAAM,GAAG,SAAS,IAAI,EAAE;EAC9B,IAAI,MAAM,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,CAAC;EACvC,IAAI,OAAO,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;EACnC,IAAI,OAAO,GAAG,MAAM,CAAC,qBAAqB,EAAE,CAAC,oBAAoB,CAAC;;;EAGlE,IAAI,SAAS,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC;;;EAGvC,IAAI,UAAU,GAAG,SAAS,CAAC,aAAa,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;;;EAGtE,IAAI,UAAU,GAAG,CAAC,CAAC;EACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IAC1C,UAAU,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC;GAC9C;EACD,IAAI,WAAW,GAAG,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC;EACxC,IAAI,YAAY,GAAG,CAAC,CAAC;;;EAGrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IAC1C,IAAI,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;IAC9B,IAAI,aAAa,GAAG,SAAS,CAAC,SAAS,CAAC;IACxC,IAAI,gBAAgB,GAAG,SAAS,CAAC,gBAAgB,CAAC;IAClD,OAAO,CAAC,aAAa,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;IACvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC,EAAE,EAAE;MACzC,WAAW,CAAC,YAAY,EAAE,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;KAChD;GACF;;;EAGD,IAAI,MAAM,GAAG,IAAI,qBAAqB,CAAC,WAAW,EAAE,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;EACzF,OAAO,MAAM,CAAC;CACf,CAAC;;ACtFF;;;;;;;;;;;;;;;;AAgBA,AAGO,IAAI,MAAM,GAAG,EAAE,CAAC;AACvB,MAAM,CAAC,oBAAoB,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;;AAEnF,AAAe,SAASA,QAAM,GAAG;;EAE/B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;EACtB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;EACf,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;EAChB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;EACzB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;;EAEnB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;CACtB;;;AAGDA,QAAM,CAAC,SAAS,CAAC,MAAM,GAAG,SAAS,GAAG,EAAE,IAAI,EAAE;;EAE5C,IAAI,MAAM,GAAG,CAAC,WAAW;;IAEvB,IAAI;MACF,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;MACvB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KAC5C,CAAC,OAAO,CAAC,EAAE;MACV,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;MACf,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;KACzB;;IAED,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;MACzB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;KACxC;;IAED,OAAO,IAAI,CAAC,MAAM,CAAC;;GAEpB,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;;EAEd,IAAI,GAAG,IAAI,SAAS,IAAI,GAAG,CAAC,KAAK,IAAI,SAAS,EAAE;;IAE9C,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;IACvB,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;IACzB,IAAI,CAAC,SAAS,GAAG,CAAC,MAAM,EAAE,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;IAC5C,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;IACjC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;;IAEnC,MAAM,EAAE,CAAC;GACV,MAAM;IACL,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;MAChC,MAAM,IAAI,KAAK,CAAC,sLAAsL,CAAC,CAAC;KACzM;;;IAGD,IAAI,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;IACxB,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC;;IAEhC,KAAK,CAAC,MAAM,GAAG,CAAC,WAAW;;MAEzB,IAAI,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;MACjD,IAAI,OAAO,GAAG,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;MACzC,IAAI,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;;MAEvD,IAAI,UAAU,IAAI,IAAI,EAAE;;QAEtB,IAAI,MAAM,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACzC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACjC,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;OACzC;;MAED,SAAS,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;MAC9B,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;MAChC,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MAC/B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;MACzB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;;MAE3B,IAAI;QACF,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;OACxE,CAAC,OAAO,CAAC,EAAE;QACV,IAAI,CAAC,MAAM,GAAG,iHAAiH,CAAC;QAChI,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;OACpE;;MAED,MAAM,EAAE,CAAC;;KAEV,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;;IAEd,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;GACjB;CACF,CAAC;;AAEFA,QAAM,CAAC,SAAS,CAAC,WAAW,GAAG,SAAS,CAAC,EAAE;;EAEzC,OAAO,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;CACtC,CAAC;;AAEFA,QAAM,CAAC,SAAS,CAAC,OAAO,GAAG,SAAS,SAAS,EAAE;;EAE7C,IAAI,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;;EAEjC,IAAI,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC;;EAE9D,IAAI,QAAQ,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC;;EAEnC,IAAI,YAAY,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;;;;;;;;;;;;;EAarC,IAAI,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;EAC/C,IAAI,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC;EAC3B,IAAI,GAAG,GAAG,EAAE,CAAC;EACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE;MACrC,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GAC1C;;EAED,IAAI,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;EAC/B,IAAI,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC;EACvB,IAAI,IAAI,CAAC,KAAK,EAAE;IACd,OAAO,CAAC,GAAG,CAAC,gCAAgC,GAAG,IAAI,CAAC,CAAC;GACtD;;EAED,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;CACrE,CAAC;;AAEFA,QAAM,CAAC,SAAS,CAAC,QAAQ,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE;EACpD,IAAI,SAAS,CAAC,KAAK,GAAG,CAAC,EAAE;IACvB,MAAM,aAAa,CAAC;GACrB;EACD,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;IACxB,MAAM,aAAa,CAAC;GACrB;EACD,IAAI,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;EAChD,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC;CAC7G,CAAC;;AAEFA,QAAM,CAAC,SAAS,CAAC,QAAQ,GAAG,SAAS,EAAE,EAAE;EACvC,IAAI,GAAG,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;EAC9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;MACnC,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;MAE/B,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;KACtC;GACF;EACD,OAAO,GAAG,CAAC;CACZ,CAAC;;AAEFA,QAAM,CAAC,SAAS,CAAC,0BAA0B,GAAG,SAAS,SAAS,EAAE;EAChE,IAAI,WAAW,GAAG,CAAC,CAAC;;EAEpB,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,GAAG,WAAW,CAAC,CAAC;EAC1D,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC;EAC5D,IAAI,MAAM,GAAG,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC;EACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE;IACpC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC;IACnC,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,WAAW,EAAE,EAAE,EAAE,EAAE;MACvC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KACxB;GACF;EACD,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,WAAW,EAAE,EAAE,EAAE,EAAE;IACvC,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,WAAW,EAAE,EAAE,EAAE,EAAE;MACvC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;MACzB,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,UAAU,EAAE,EAAE,EAAE,EAAE;QACtC,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,EAAE,EAAE,EAAE,EAAE;UACrC,IAAI,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,SAAS,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,UAAU,GAAG,EAAE,GAAG,EAAE,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC;UAC5F,IAAI,MAAM,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC5B,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;UAC7B,IAAI,MAAM,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC5B,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;SAC9B;OACF;KACF;GACF;EACD,IAAI,MAAM,GAAG,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC;EACpC,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,WAAW,EAAE,EAAE,EAAE,EAAE;IACvC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC;GACrC;EACD,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,WAAW,EAAE,EAAE,EAAE,EAAE;IACvC,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,WAAW,EAAE,EAAE,EAAE,EAAE;MACvC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;KAC1E;GACF;;EAED,OAAO,MAAM,CAAC;CACf,CAAC;;AAEFA,QAAM,CAAC,SAAS,CAAC,iBAAiB,GAAG,SAAS,kBAAkB,EAAE;EAChE,IAAI,MAAM,GAAG,IAAI,CAAC,0BAA0B,CAAC,kBAAkB,CAAC,CAAC;EACjE,IAAI,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC;EAChC,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,KAAK,GAAG,WAAW,CAAC,CAAC;EACnE,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC;;EAErE,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,WAAW,EAAE,EAAE,EAAE,EAAE;IACvC,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,WAAW,EAAE,EAAE,EAAE,EAAE;MACvC,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,UAAU,EAAE,EAAE,EAAE,EAAE;QACtC,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,EAAE,EAAE,EAAE,EAAE;UACrC,kBAAkB,CAAC,IAAI,CAAC,SAAS,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,UAAU,GAAG,EAAE,GAAG,EAAE,IAAI,kBAAkB,CAAC,KAAK,CAAC,IAAI,kBAAkB,CAAC,IAAI,CAAC,SAAS,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,UAAU,GAAG,EAAE,GAAG,EAAE,IAAI,kBAAkB,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACxN;OACF;KACF;GACF;EACD,OAAO,kBAAkB,CAAC;CAC3B,CAAC;;AAEFA,QAAM,CAAC,SAAS,CAAC,SAAS,GAAG,SAAS,SAAS,EAAE;EAC/C,IAAI,GAAG,GAAG,IAAI,KAAK,CAAC,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;;EAExD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;MACxC,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;;MAE1C,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;KACrC;GACF;;EAED,OAAO;IACL,MAAM,EAAE,SAAS,CAAC,MAAM;IACxB,KAAK,EAAE,SAAS,CAAC,KAAK;IACtB,IAAI,EAAE,GAAG;GACV,CAAC;CACH,CAAC;;AAEF,AAAO,SAAS,OAAO,CAAC,MAAM,GAAG,IAAI,EAAE;EACrC,IAAI,MAAM,IAAI,CAAC;IACb,OAAO,MAAM,IAAI,IAAI,CAAC;;IAEtB,OAAO,CAAC,MAAM,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;CAC1C;;;;;;;;"}
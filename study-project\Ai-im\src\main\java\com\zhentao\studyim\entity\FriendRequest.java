package com.zhentao.studyim.entity;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 好友申请实体类
 */
@Data
@Entity
@Table(name = "friend_request")
public class FriendRequest {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "from_user_id", nullable = false)
    private Long fromUserId;                  // 申请人用户ID

    @Column(name = "to_user_id", nullable = false)
    private Long toUserId;                    // 被申请人用户ID

    @Column(name = "message", length = 200)
    private String message;                   // 申请消息

    @Enumerated(EnumType.STRING)
    @Column(name = "status", length = 20)
    private RequestStatus status = RequestStatus.PENDING;  // 申请状态

    @Column(name = "create_time")
    private LocalDateTime createTime = LocalDateTime.now();  // 创建时间

    @Column(name = "update_time")
    private LocalDateTime updateTime = LocalDateTime.now();  // 更新时间

    /**
     * 好友申请状态枚举
     */
    public enum RequestStatus {
        PENDING,    // 待处理
        ACCEPTED,   // 已同意
        REJECTED,   // 已拒绝
        EXPIRED     // 已过期
    }

    /**
     * 在保存前设置默认值
     */
    @PrePersist
    public void prePersist() {
        if (this.status == null) {
            this.status = RequestStatus.PENDING;
        }
        if (this.createTime == null) {
            this.createTime = LocalDateTime.now();
        }
        if (this.updateTime == null) {
            this.updateTime = LocalDateTime.now();
        }
    }

    /**
     * 在更新前设置更新时间
     */
    @PreUpdate
    public void preUpdate() {
        this.updateTime = LocalDateTime.now();
    }
}

package cn.zhentao.service;

import cn.zhentao.pojo.SysDictType;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.Map;

/**
 * <p>
 * 字典类型表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-16
 */
public interface SysDictTypeService extends IService<SysDictType> {
    Map<String, Object> getSysDictType();
    Map<String, Object> saveSysDictType(SysDictType sysDictType);
    Map<String, Object> updateSysDictType(SysDictType sysDictType);
    Map<String, Object> deleteSysDictType(String id);
}

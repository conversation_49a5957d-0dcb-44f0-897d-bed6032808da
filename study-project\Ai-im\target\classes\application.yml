# 服务器配置
server:
  port: 8080

spring:
  application:
    name: study-im

  # 数据库配置
  datasource:
    url: **********************************************************************************************************************************************************************************
    username: root
    password: Sunshuo0818
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000

  # SQL初始化配置
  sql:
    init:
      mode: never
      schema-locations: classpath:schema.sql
      continue-on-error: true

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect

  # Redis配置 (开发环境可选)
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

# Netty WebSocket配置
netty:
  server:
    port: 9999
    boss-threads: 1
    worker-threads: 4

# JWT配置
jwt:
  secret: study-im-secret-key-2024
  expiration: 86400000

# 日志配置
logging:
  level:
    com.zhentao.studyim: debug
    org.springframework.web: info
spring:
  application:
    name: ai-userlogin
  profiles:
    active: dev

  # 数据库配置
  datasource:
    url: jdbc:mysql://**************:3306/ai_tutor?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
    username: root
    password: Sunshuo0818
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # Redis配置（开发环境禁用）
  # redis:
  #   host: 127.0.0.1
  #   port: 6379
  #   password:
  #   database: 0
  #   timeout: 3000ms
  #   lettuce:
  #     pool:
  #       max-active: 8
  #       max-idle: 8
  #       min-idle: 0
  #       max-wait: -1ms



server:
  port: 8083

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:/mapper/**/*.xml

# JWT配置
jwt:
  secret: aiTutorSecretKey2024ForUserLoginService
  expiration: 86400000  # 24小时
  header: Authorization
  prefix: Bearer

# 人脸识别配置
face:
  recognition:
    model-path: classpath:models/
    threshold: 0.8
    max-faces: 10
    image-width: 640
    image-height: 480

# 日志配置
logging:
  level:
    cn.zhentao: debug
    org.springframework.security: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

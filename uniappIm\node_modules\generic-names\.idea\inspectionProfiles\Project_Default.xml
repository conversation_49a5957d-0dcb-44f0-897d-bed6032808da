<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="BreakStatementWithLabelJS" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="CheckEmptyScriptTag" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ContinueOrBreakFromFinallyBlockJS" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ContinueStatementWithLabelJS" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="CssInvalidCharsetRule" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="CssInvalidHtmlTagReference" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="CssInvalidImport" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="CssMissingSemicolon" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="CssNoGenericFontName" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="CssRedundantUnit" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="CssUnitlessNumber" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="DebuggerStatementJS" enabled="false" level="WARNING" enabled_by_default="false">
      <scope name="Production" level="WARNING" enabled="false" />
    </inspection_tool>
    <inspection_tool class="DefaultNotLastCaseInSwitchJS" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="DivideByZeroJS" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="DocumentWriteJS" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="DuplicateCaseLabelJS" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="DuplicateConditionJS" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="DuplicatePropertyInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="CURRENT_FILE" value="true" />
      <option name="MODULE_WITH_DEPENDENCIES" value="false" />
      <option name="CHECK_DUPLICATE_VALUES" value="true" />
      <option name="CHECK_DUPLICATE_KEYS" value="true" />
      <option name="CHECK_DUPLICATE_KEYS_WITH_DIFFERENT_VALUES" value="true" />
    </inspection_tool>
    <inspection_tool class="Duplicates" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="EmptyFinallyBlockJS" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="EmptyTryBlockJS" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="Eslint" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="HtmlDeprecatedTag" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="InconsistentLineSeparators" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="InnerHTMLJS" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="JSNonStrictModeUsed" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="LabeledStatementJS" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="LessUnresolvedMixin" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="LessUnresolvedVariable" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="LongLine" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="NonBlockStatementBodyJS" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="PhpCSValidationInspection" enabled="true" level="ERROR" enabled_by_default="true">
      <option name="CODING_STANDARD" value="Custom" />
      <option name="CUSTOM_RULESET_PATH" value="$PROJECT_DIR$/../insteria-theme-build/wp-content/themes/insteria" />
      <option name="WARNING_HIGHLIGHT_LEVEL_NAME" value="ERROR" />
      <option name="SHOW_SNIFF_NAMES" value="true" />
    </inspection_tool>
    <inspection_tool class="PhpDivisionByZeroInspection" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PhpMultipleClassesDeclarationsInOneFile" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="PhpTraditionalSyntaxArrayLiteralInspection" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="PhpUndefinedCallbackInspection" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PhpUsageOfSilenceOperatorInspection" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ProblematicWhitespace" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ReturnFromFinallyBlockJS" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ReuseOfLocalVariableJS" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="SassScssUnresolvedMixin" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="SassScssUnresolvedPlaceholderSelector" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="SassScssUnresolvedVariable" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="Stylelint" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="SwitchStatementWithNoDefaultBranchJS" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="TextLabelInSwitchStatementJS" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ThrowFromFinallyBlockJS" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="TodoComment" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryLocalVariableJS" enabled="true" level="ERROR" enabled_by_default="true">
      <option name="m_ignoreImmediatelyReturnedVariables" value="false" />
      <option name="m_ignoreAnnotatedVariables" value="false" />
    </inspection_tool>
    <inspection_tool class="VoidExpressionJS" enabled="true" level="WARNING" enabled_by_default="true" />
  </profile>
</component>
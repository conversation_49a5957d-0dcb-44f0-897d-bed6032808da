"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      loginImage: "",
      loginLoading: false,
      loginResult: ""
    };
  },
  methods: {
    chooseImage() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          this.loginImage = res.tempFilePaths[0];
          this.loginResult = "";
          this.submitLogin();
        }
      });
    },
    submitLogin() {
      if (!this.loginImage) {
        this.loginResult = "请拍照或上传人脸图片";
        return;
      }
      this.loginLoading = true;
      this.loginResult = "";
      common_vendor.index.getFileSystemManager().readFile({
        filePath: this.loginImage,
        encoding: "base64",
        success: (res) => {
          const base64 = "data:image/jpeg;base64," + res.data;
          common_vendor.index.request({
            url: "http://localhost:8081/fact-info/check",
            method: "POST",
            header: {
              "Content-Type": "application/json"
            },
            data: {
              imageData: base64
            },
            success: (r) => {
              const data = r.data;
              if (data.code === 200) {
                this.loginResult = "✅ 登录成功！欢迎回来，" + data.userInfo.name;
                common_vendor.index.setStorageSync("userInfo", data.userInfo);
                setTimeout(() => {
                  common_vendor.index.showToast({
                    title: "登录成功",
                    icon: "success",
                    duration: 1500
                  });
                  setTimeout(() => {
                    common_vendor.index.reLaunch({
                      url: "/pages/home/<USER>"
                    });
                  }, 1500);
                }, 500);
              } else {
                this.loginResult = "❌ 登录失败：" + data.message;
              }
            },
            fail: (err) => {
              this.loginResult = "❌ 登录失败：" + err.errMsg;
            },
            complete: () => {
              this.loginLoading = false;
            }
          });
        },
        fail: (err) => {
          this.loginResult = "❌ 图片读取失败：" + err.errMsg;
          this.loginLoading = false;
        }
      });
    },
    goToRegister() {
      common_vendor.index.navigateTo({
        url: "/pages/register/register"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.loginImage
  }, !$data.loginImage ? {} : {
    b: $data.loginImage
  }, {
    c: common_vendor.o((...args) => $options.chooseImage && $options.chooseImage(...args)),
    d: $data.loginLoading
  }, $data.loginLoading ? {} : {}, {
    e: $data.loginResult
  }, $data.loginResult ? {
    f: common_vendor.t($data.loginResult)
  } : {}, {
    g: common_vendor.o((...args) => $options.goToRegister && $options.goToRegister(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/login/login.js.map

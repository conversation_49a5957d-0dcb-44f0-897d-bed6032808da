<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单首页 - 预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            height: 100vh;
            overflow: hidden;
        }
        
        /* 自定义状态栏 */
        .custom-status-bar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 44px;
            background: #2c3e50;
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .app-title {
            font-size: 16px;
            font-weight: bold;
            color: #fff;
        }
        
        .container {
            min-height: 100vh;
            background: #f8f9fa;
            padding-top: 54px;
            padding-bottom: 70px;
        }
        
        /* 主要内容 */
        .main-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: calc(100vh - 124px);
            padding: 20px;
        }
        
        /* 欢迎区域 */
        .welcome-section {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .welcome-icon {
            font-size: 50px;
            margin-bottom: 10px;
        }

        .welcome-title {
            display: block;
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .welcome-desc {
            display: block;
            font-size: 13px;
            color: #6c757d;
        }
        
        /* 功能按钮 */
        .action-buttons {
            width: 100%;
            max-width: 200px;
        }
        
        .action-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
        }
        
        .action-btn:active {
            transform: scale(0.95);
        }
        
        .btn-icon {
            display: block;
            font-size: 30px;
            margin-bottom: 8px;
        }
        
        .btn-text {
            display: block;
            font-size: 16px;
            font-weight: bold;
            color: #fff;
        }
        
        /* 底部导航栏 */
        .bottom-navigation {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: white;
            display: flex;
            align-items: center;
            justify-content: space-around;
            box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        
        .camera-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        
        .camera-btn {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #FF6B6B, #FF8E53);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);
            transform: translateY(-5px);
            margin-bottom: 4px;
        }
        
        .camera-btn:hover {
            transform: translateY(-5px) scale(1.05);
        }
        
        .camera-icon {
            font-size: 22px;
            color: white;
        }
        
        .camera-text {
            font-size: 10px;
            color: #666;
            font-weight: 500;
            text-align: center;
            transform: translateY(-5px);
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            flex: 1;
            padding: 5px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .nav-item.active .nav-icon {
            color: #007AFF;
            transform: scale(1.1);
        }
        
        .nav-item.active .nav-text {
            color: #007AFF;
            font-weight: 600;
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
            color: #666;
            transition: all 0.3s ease;
        }
        
        .nav-text {
            font-size: 10px;
            color: #666;
            transition: all 0.3s ease;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 10px;
            margin: 20px;
            border: 1px solid #c3e6cb;
            position: absolute;
            top: 54px;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        /* 新增预览样式 */
        .main-content {
            padding: 20px;
            padding-bottom: 120px;
        }

        .demo-section {
            margin-bottom: 30px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .demo-section h4 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 18px;
        }

        .banner-preview {
            height: 150px;
            border-radius: 15px;
            overflow: hidden;
        }

        .banner-item-preview {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: bold;
        }

        .ai-chat-preview {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 20px;
            display: flex;
            align-items: center;
            color: white;
        }

        .chat-icon-preview {
            font-size: 40px;
            margin-right: 15px;
        }

        .functions-preview {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
        }

        .function-preview {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px 10px;
            text-align: center;
            font-size: 14px;
            color: #333;
            border: 2px solid #e9ecef;
        }
    </style>
</head>
<body>
    <!-- 自定义状态栏 -->
    <div class="custom-status-bar">
        <div class="app-title">首页</div>
    </div>
    
    <!-- 成功提示 -->
    <div class="success-message">
        <strong>✅ 首页功能组件添加完成！</strong><br>
        • 添加了精美的轮播图展示<br>
        • 集成了"开启AI模式与AI对话"功能<br>
        • 添加了完整的智能功能网格（6个功能）<br>
        • 实现了响应式布局和美观的UI设计<br>
        • 登录成功后跳转到功能丰富的首页
    </div>
    
    <div class="container">
        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 轮播图预览 -->
            <div class="demo-section">
                <h4>📸 轮播图</h4>
                <div class="banner-preview">
                    <div class="banner-item-preview" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                        <span>🎓 欢迎使用AI智能家教</span>
                    </div>
                </div>
            </div>

            <!-- AI对话预览 -->
            <div class="demo-section">
                <h4>🤖 AI智能对话</h4>
                <div class="ai-chat-preview">
                    <span class="chat-icon-preview">🎤</span>
                    <div>
                        <strong>开启AI模式与AI对话</strong><br>
                        <small>语音交互，智能回答各种问题</small>
                    </div>
                </div>
            </div>

            <!-- 智能功能预览 -->
            <div class="demo-section">
                <h4>⭐ 智能功能网格</h4>
                <div class="functions-preview">
                    <div class="function-preview">📚<br>知识问答</div>
                    <div class="function-preview">🔍<br>信息查询</div>
                    <div class="function-preview">☁️<br>文本生成</div>
                    <div class="function-preview">🌐<br>语言翻译</div>
                    <div class="function-preview">💝<br>情感陪伴</div>
                    <div class="function-preview">🎯<br>智能推荐</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部导航栏 -->
    <div class="bottom-navigation">
        <div class="nav-item active">
            <div class="nav-icon">🏠</div>
            <div class="nav-text">首页</div>
        </div>
        
        <!-- 美化的拍照搜题按钮 -->
        <div class="camera-section">
            <div class="camera-btn" onclick="showCameraInfo()">
                <div class="camera-icon">📷</div>
            </div>
            <div class="camera-text">拍照搜题</div>
        </div>
        
        <div class="nav-item" onclick="showProfileInfo()">
            <div class="nav-icon">👤</div>
            <div class="nav-text">我的</div>
        </div>
    </div>
    
    <script>
        function showInfo(feature) {
            alert(`点击了 ${feature} 功能`);
        }
        
        function showCameraInfo() {
            alert('拍照搜题功能开发中');
        }
        
        function showProfileInfo() {
            alert('个人中心功能开发中');
        }
        
        // 3秒后隐藏成功提示
        setTimeout(() => {
            const message = document.querySelector('.success-message');
            if (message) {
                message.style.display = 'none';
            }
        }, 3000);
    </script>
</body>
</html>

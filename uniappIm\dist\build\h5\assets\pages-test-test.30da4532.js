import{_ as s,o as t,c as a,w as e,i as o,b as d,d as i,n as c,t as n,h as l,r,F as g,f as u,g as h,S as f}from"./index-0fa1fc91.js";import{k as L,r as m,l as k,g as $,e as _,c as S,d as b}from"./api.44778c12.js";import{c as w}from"./websocket.6c3599e6.js";const y=s({data:()=>({apiStatus:!1,wsStatus:!1,logs:[]}),onLoad(){this.addLog("测试页面加载完成","info"),this.addLog("API地址: http://localhost:8080/api","info"),this.addLog("WebSocket地址: ws://localhost:9999/ws","info")},methods:{addLog(s,t="info"){const a=(new Date).toLocaleTimeString();this.logs.unshift({time:a,message:s,type:t}),this.logs.length>50&&(this.logs=this.logs.slice(0,50))},async testUserList(){this.addLog("开始测试获取用户列表...","info");try{const s=await L();200===s.code?(this.apiStatus=!0,this.addLog("✅ 获取用户列表成功","success"),this.addLog(`用户数量: ${s.data.length}`,"info"),s.data.forEach(s=>{this.addLog(`用户: ${s.username} (${s.nickname})`,"info")})):this.addLog(`❌ 获取用户列表失败: ${s.message}`,"error")}catch(s){this.apiStatus=!1,this.addLog(`❌ 获取用户列表失败: ${s.message}`,"error")}},async testRegister(){this.addLog("开始测试用户注册...","info");const s=Math.floor(1e4*Math.random()),t={username:`test${s}`,password:"test123",nickname:`测试用户${s}`};try{const s=await m(t);200===s.code?(this.addLog("✅ 用户注册成功","success"),this.addLog(`用户: ${t.username}`,"info"),this.addLog(`Token: ${s.data.token.substring(0,20)}...`,"info")):this.addLog(`❌ 用户注册失败: ${s.message}`,"error")}catch(a){this.addLog(`❌ 用户注册失败: ${a.message}`,"error")}},async testLogin(){this.addLog("开始测试用户登录...","info");try{const s=await k({username:"test",password:"test123"});200===s.code?(this.addLog("✅ 用户登录成功","success"),this.addLog(`用户: ${s.data.user.username}`,"info"),this.addLog(`Token: ${s.data.token.substring(0,20)}...`,"info")):this.addLog(`❌ 用户登录失败: ${s.message}`,"error")}catch(s){this.addLog(`❌ 用户登录失败: ${s.message}`,"error")}},testWebSocket(){this.addLog("开始测试WebSocket连接...","info");const s=uni.getStorageSync("token")||"test-token";try{w(s,s=>{this.addLog(`📨 收到WebSocket消息: ${JSON.stringify(s)}`,"success")}),setTimeout(()=>{this.wsStatus=!0,this.addLog("✅ WebSocket连接成功","success")},2e3)}catch(t){this.wsStatus=!1,this.addLog(`❌ WebSocket连接失败: ${t.message}`,"error")}},async testFriendAPI(){this.addLog("开始测试好友管理API...","info");try{const s=await $();200===s.code?(this.addLog(`✅ 获取好友列表成功，共${s.data.length}个好友`,"success"),s.data.forEach(s=>{this.addLog(`好友: ${s.username} (${s.nickname})`,"info")})):this.addLog(`❌ 获取好友列表失败: ${s.message}`,"error");const t=await _();200===t.code?this.addLog(`✅ 获取好友申请成功，共${t.data.length}个申请`,"success"):this.addLog(`❌ 获取好友申请失败: ${t.message}`,"error")}catch(s){this.addLog(`❌ 好友管理API测试失败: ${s.message}`,"error")}},async testSearchUsers(){this.addLog("开始测试搜索用户...","info");try{const s=await S("test");200===s.code?(this.addLog(`✅ 搜索用户成功，找到${s.data.length}个用户`,"success"),s.data.forEach(s=>{this.addLog(`用户: ${s.username} (${s.nickname})`,"info")})):this.addLog(`❌ 搜索用户失败: ${s.message}`,"error")}catch(s){this.addLog(`❌ 搜索用户失败: ${s.message}`,"error")}},async testFriendRequest(){this.addLog("开始测试好友申请...","info");try{const s=await L();if(200===s.code&&s.data.length>0){const t=s.data[0],a=await b({toUserId:t.userId,message:"测试好友申请"});200===a.code?this.addLog("✅ 发送好友申请成功","success"):this.addLog(`❌ 发送好友申请失败: ${a.message}`,"error")}else this.addLog("❌ 无法获取用户列表进行测试","error")}catch(s){this.addLog(`❌ 好友申请测试失败: ${s.message}`,"error")}}}},[["render",function(s,L,m,k,$,_){const S=u,b=o,w=h,y=f;return t(),a(b,{class:"test-container"},{default:e(()=>[d(b,{class:"test-header"},{default:e(()=>[d(S,{class:"title"},{default:e(()=>[i("前后端交互测试")]),_:1})]),_:1}),d(b,{class:"test-section"},{default:e(()=>[d(S,{class:"section-title"},{default:e(()=>[i("连接状态")]),_:1}),d(b,{class:"status-item"},{default:e(()=>[d(S,{class:"status-label"},{default:e(()=>[i("后端API:")]),_:1}),d(S,{class:c(["status-value",{success:$.apiStatus,error:!$.apiStatus}])},{default:e(()=>[i(n($.apiStatus?"连接正常":"连接失败"),1)]),_:1},8,["class"])]),_:1}),d(b,{class:"status-item"},{default:e(()=>[d(S,{class:"status-label"},{default:e(()=>[i("WebSocket:")]),_:1}),d(S,{class:c(["status-value",{success:$.wsStatus,error:!$.wsStatus}])},{default:e(()=>[i(n($.wsStatus?"连接正常":"连接失败"),1)]),_:1},8,["class"])]),_:1})]),_:1}),d(b,{class:"test-section"},{default:e(()=>[d(S,{class:"section-title"},{default:e(()=>[i("基础API测试")]),_:1}),d(w,{onClick:_.testUserList,class:"test-btn"},{default:e(()=>[i("测试获取用户列表")]),_:1},8,["onClick"]),d(w,{onClick:_.testRegister,class:"test-btn"},{default:e(()=>[i("测试用户注册")]),_:1},8,["onClick"]),d(w,{onClick:_.testLogin,class:"test-btn"},{default:e(()=>[i("测试用户登录")]),_:1},8,["onClick"]),d(w,{onClick:_.testWebSocket,class:"test-btn"},{default:e(()=>[i("测试WebSocket")]),_:1},8,["onClick"])]),_:1}),d(b,{class:"test-section"},{default:e(()=>[d(S,{class:"section-title"},{default:e(()=>[i("好友管理测试")]),_:1}),d(w,{onClick:_.testFriendAPI,class:"test-btn"},{default:e(()=>[i("测试好友管理API")]),_:1},8,["onClick"]),d(w,{onClick:_.testSearchUsers,class:"test-btn"},{default:e(()=>[i("测试搜索用户")]),_:1},8,["onClick"]),d(w,{onClick:_.testFriendRequest,class:"test-btn"},{default:e(()=>[i("测试好友申请")]),_:1},8,["onClick"])]),_:1}),d(b,{class:"test-section"},{default:e(()=>[d(S,{class:"section-title"},{default:e(()=>[i("测试结果")]),_:1}),d(y,{class:"log-container","scroll-y":""},{default:e(()=>[(t(!0),l(g,null,r($.logs,(s,o)=>(t(),a(b,{key:o,class:"log-item"},{default:e(()=>[d(S,{class:"log-time"},{default:e(()=>[i(n(s.time),1)]),_:2},1024),d(S,{class:c(["log-message",s.type])},{default:e(()=>[i(n(s.message),1)]),_:2},1032,["class"])]),_:2},1024))),128))]),_:1})]),_:1})]),_:1})}],["__scopeId","data-v-56f6cd83"]]);export{y as default};

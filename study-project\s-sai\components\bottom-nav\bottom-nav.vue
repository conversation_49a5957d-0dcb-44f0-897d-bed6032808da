<template>
  <view class="bottom-nav">
    <view
      class="nav-item"
      :class="{ active: activeTab === 'home' }"
      @click="switchTab('home')"
    >
      <view class="nav-icon">🏠</view>
      <text class="nav-text">首页</text>
    </view>

    <view
      class="nav-item"
      :class="{ active: activeTab === 'study' }"
      @click="switchTab('study')"
    >
      <view class="nav-icon">📚</view>
      <text class="nav-text">学习路线</text>
    </view>

    <view
      class="nav-item"
      :class="{ active: activeTab === 'tools' }"
      @click="switchTab('tools')"
    >
      <view class="nav-icon">🔧</view>
      <text class="nav-text">工具</text>
    </view>

    <view
      class="nav-item"
      :class="{ active: activeTab === 'profile' }"
      @click="switchTab('profile')"
    >
      <view class="nav-icon">👤</view>
      <text class="nav-text">我的</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'BottomNav',
  props: {
    activeTab: {
      type: String,
      default: 'home'
    }
  },
  methods: {
    switchTab(tab) {
      // 发出事件给父组件处理
      this.$emit('tab-change', tab);

      // 直接处理页面跳转
      switch(tab) {
        case 'home':
          uni.reLaunch({
            url: '/pages/index/index'
          });
          break;
        case 'study':
          uni.reLaunch({
            url: '/pages/study-route/study-route'
          });
          break;
        case 'tools':
          uni.reLaunch({
            url: '/pages/tools/tools'
          });
          break;
        case 'profile':
          uni.reLaunch({
            url: '/pages/profile/profile'
          });
          break;
      }
    }
  }
}
</script>

<style scoped>
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: white;
  display: flex;
  align-items: center;
  justify-content: space-around;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 10rpx 0;
  transition: all 0.3s ease;
}

.nav-item.active {
  color: #007AFF;
}

.nav-icon {
  font-size: 32rpx;
  margin-bottom: 4rpx;
}

.nav-text {
  font-size: 20rpx;
  color: #666;
}

.nav-item.active .nav-text {
  color: #007AFF;
  font-weight: 500;
}
</style>

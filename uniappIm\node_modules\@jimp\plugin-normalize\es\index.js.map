{"version": 3, "sources": ["../src/index.js"], "names": ["histogram", "r", "Array", "fill", "g", "b", "scanQuiet", "bitmap", "width", "height", "x", "y", "index", "data", "normalize", "value", "min", "max", "getBounds", "histogramChannel", "findIndex", "slice", "reverse", "cb", "h", "call", "bounds", "idx"], "mappings": ";;;;;;;AAAA;;AAEA;;;;AAIA,SAASA,SAAT,GAAqB;AACnB,MAAMA,SAAS,GAAG;AAChBC,IAAAA,CAAC,EAAE,IAAIC,KAAJ,CAAU,GAAV,EAAeC,IAAf,CAAoB,CAApB,CADa;AAEhBC,IAAAA,CAAC,EAAE,IAAIF,KAAJ,CAAU,GAAV,EAAeC,IAAf,CAAoB,CAApB,CAFa;AAGhBE,IAAAA,CAAC,EAAE,IAAIH,KAAJ,CAAU,GAAV,EAAeC,IAAf,CAAoB,CAApB;AAHa,GAAlB;AAMA,OAAKG,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,KAAKC,MAAL,CAAYC,KAAjC,EAAwC,KAAKD,MAAL,CAAYE,MAApD,EAA4D,UAC1DC,CAD0D,EAE1DC,CAF0D,EAG1DC,KAH0D,EAI1D;AACAZ,IAAAA,SAAS,CAACC,CAAV,CAAY,KAAKM,MAAL,CAAYM,IAAZ,CAAiBD,KAAK,GAAG,CAAzB,CAAZ;AACAZ,IAAAA,SAAS,CAACI,CAAV,CAAY,KAAKG,MAAL,CAAYM,IAAZ,CAAiBD,KAAK,GAAG,CAAzB,CAAZ;AACAZ,IAAAA,SAAS,CAACK,CAAV,CAAY,KAAKE,MAAL,CAAYM,IAAZ,CAAiBD,KAAK,GAAG,CAAzB,CAAZ;AACD,GARD;AAUA,SAAOZ,SAAP;AACD;AAED;;;;;;;;;AAOA,IAAMc,UAAS,GAAG,SAAZA,SAAY,CAASC,KAAT,EAAgBC,GAAhB,EAAqBC,GAArB,EAA0B;AAC1C,SAAQ,CAACF,KAAK,GAAGC,GAAT,IAAgB,GAAjB,IAAyBC,GAAG,GAAGD,GAA/B,CAAP;AACD,CAFD;;AAIA,IAAME,SAAS,GAAG,SAAZA,SAAY,CAASC,gBAAT,EAA2B;AAC3C,SAAO,CACLA,gBAAgB,CAACC,SAAjB,CAA2B,UAAAL,KAAK;AAAA,WAAIA,KAAK,GAAG,CAAZ;AAAA,GAAhC,CADK,EAEL,MACEI,gBAAgB,CACbE,KADH,GAEGC,OAFH,GAGGF,SAHH,CAGa,UAAAL,KAAK;AAAA,WAAIA,KAAK,GAAG,CAAZ;AAAA,GAHlB,CAHG,CAAP;AAQD,CATD;AAWA;;;;;;;eAKe;AAAA,SAAO;AACpBD,IAAAA,SADoB,qBACVS,EADU,EACN;AACZ,UAAMC,CAAC,GAAGxB,SAAS,CAACyB,IAAV,CAAe,IAAf,CAAV,CADY,CAGZ;;AACA,UAAMC,MAAM,GAAG;AACbzB,QAAAA,CAAC,EAAEiB,SAAS,CAACM,CAAC,CAACvB,CAAH,CADC;AAEbG,QAAAA,CAAC,EAAEc,SAAS,CAACM,CAAC,CAACpB,CAAH,CAFC;AAGbC,QAAAA,CAAC,EAAEa,SAAS,CAACM,CAAC,CAACnB,CAAH;AAHC,OAAf,CAJY,CAUZ;;AACA,WAAKC,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,KAAKC,MAAL,CAAYC,KAAjC,EAAwC,KAAKD,MAAL,CAAYE,MAApD,EAA4D,UAC1DC,CAD0D,EAE1DC,CAF0D,EAG1DgB,GAH0D,EAI1D;AACA,YAAM1B,CAAC,GAAG,KAAKM,MAAL,CAAYM,IAAZ,CAAiBc,GAAG,GAAG,CAAvB,CAAV;AACA,YAAMvB,CAAC,GAAG,KAAKG,MAAL,CAAYM,IAAZ,CAAiBc,GAAG,GAAG,CAAvB,CAAV;AACA,YAAMtB,CAAC,GAAG,KAAKE,MAAL,CAAYM,IAAZ,CAAiBc,GAAG,GAAG,CAAvB,CAAV;AAEA,aAAKpB,MAAL,CAAYM,IAAZ,CAAiBc,GAAG,GAAG,CAAvB,IAA4Bb,UAAS,CAACb,CAAD,EAAIyB,MAAM,CAACzB,CAAP,CAAS,CAAT,CAAJ,EAAiByB,MAAM,CAACzB,CAAP,CAAS,CAAT,CAAjB,CAArC;AACA,aAAKM,MAAL,CAAYM,IAAZ,CAAiBc,GAAG,GAAG,CAAvB,IAA4Bb,UAAS,CAACV,CAAD,EAAIsB,MAAM,CAACtB,CAAP,CAAS,CAAT,CAAJ,EAAiBsB,MAAM,CAACtB,CAAP,CAAS,CAAT,CAAjB,CAArC;AACA,aAAKG,MAAL,CAAYM,IAAZ,CAAiBc,GAAG,GAAG,CAAvB,IAA4Bb,UAAS,CAACT,CAAD,EAAIqB,MAAM,CAACrB,CAAP,CAAS,CAAT,CAAJ,EAAiBqB,MAAM,CAACrB,CAAP,CAAS,CAAT,CAAjB,CAArC;AACD,OAZD;;AAcA,UAAI,0BAAckB,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACE,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD;AA/BmB,GAAP;AAAA,C", "sourcesContent": ["import { isNodePattern } from '@jimp/utils';\n\n/**\n * Get an image's histogram\n * @return {object} An object with an array of color occurrence counts for each channel (r,g,b)\n */\nfunction histogram() {\n  const histogram = {\n    r: new Array(256).fill(0),\n    g: new Array(256).fill(0),\n    b: new Array(256).fill(0)\n  };\n\n  this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function(\n    x,\n    y,\n    index\n  ) {\n    histogram.r[this.bitmap.data[index + 0]]++;\n    histogram.g[this.bitmap.data[index + 1]]++;\n    histogram.b[this.bitmap.data[index + 2]]++;\n  });\n\n  return histogram;\n}\n\n/**\n * Normalize values\n * @param  {integer} value Pixel channel value.\n * @param  {integer} min   Minimum value for channel\n * @param  {integer} max   Maximum value for channel\n * @return {integer} normalized values\n */\nconst normalize = function(value, min, max) {\n  return ((value - min) * 255) / (max - min);\n};\n\nconst getBounds = function(histogramChannel) {\n  return [\n    histogramChannel.findIndex(value => value > 0),\n    255 -\n      histogramChannel\n        .slice()\n        .reverse()\n        .findIndex(value => value > 0)\n  ];\n};\n\n/**\n * Normalizes the image\n * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\nexport default () => ({\n  normalize(cb) {\n    const h = histogram.call(this);\n\n    // store bounds (minimum and maximum values)\n    const bounds = {\n      r: getBounds(h.r),\n      g: getBounds(h.g),\n      b: getBounds(h.b)\n    };\n\n    // apply value transformations\n    this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function(\n      x,\n      y,\n      idx\n    ) {\n      const r = this.bitmap.data[idx + 0];\n      const g = this.bitmap.data[idx + 1];\n      const b = this.bitmap.data[idx + 2];\n\n      this.bitmap.data[idx + 0] = normalize(r, bounds.r[0], bounds.r[1]);\n      this.bitmap.data[idx + 1] = normalize(g, bounds.g[0], bounds.g[1]);\n      this.bitmap.data[idx + 2] = normalize(b, bounds.b[0], bounds.b[1]);\n    });\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  }\n});\n"], "file": "index.js"}
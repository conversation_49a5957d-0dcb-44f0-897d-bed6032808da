<!DOCTYPE html>
<html>
    <head>
        <title><PERSON><PERSON> browser example 3</title>
    </head>
    <body>

        <h1> Demonstrates loading a local file using <PERSON><PERSON> on a WebWorker thread </h1>
        <p><input type="file" onchange="newFiles(this);" /></p>  
        <script>
            
        function newFiles(element){
            for (var i=0; i<element.files.length; i++) {
                readFileAndProcess(element.files[i]);
            }

            function readFileAndProcess(readfile){
                var reader = new FileReader();
                reader.addEventListener("load", function(){
                    var worker = new Worker("jimp-worker.js");
                    worker.onmessage = function (e) {
                        var img = document.createElement("img");
                        img.setAttribute("src", e.data);
                        document.body.appendChild(img);
                    };
                    worker.postMessage(this.result);
                });
                reader.readAsArrayBuffer(readfile);
            }
        }

        </script>
        
    </body>
</html>

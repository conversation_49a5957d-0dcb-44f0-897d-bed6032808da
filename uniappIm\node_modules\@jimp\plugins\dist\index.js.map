{"version": 3, "sources": ["../src/index.js"], "names": ["plugins", "blit", "blur", "circle", "color", "contain", "cover", "crop", "displace", "dither", "fisheye", "flip", "gaussian", "invert", "mask", "normalize", "print", "resize", "rotate", "scale", "shadow", "threshold", "jimp<PERSON>v<PERSON><PERSON><PERSON>", "initializedPlugins", "map", "pluginModule", "plugin", "constants", "mergeDeep"], "mappings": ";;;;;;;;;;;;;AAAA;;AAEA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AAEA,IAAMA,OAAO,GAAG,CACdC,sBADc,EAEdC,sBAFc,EAGdC,wBAHc,EAIdC,uBAJc,EAKdC,yBALc,EAMdC,uBANc,EAOdC,sBAPc,EAQdC,0BARc,EASdC,wBATc,EAUdC,yBAVc,EAWdC,sBAXc,EAYdC,0BAZc,EAadC,wBAbc,EAcdC,sBAdc,EAedC,2BAfc,EAgBdC,uBAhBc,EAiBdC,wBAjBc,EAkBdC,wBAlBc,EAmBdC,uBAnBc,EAoBdC,wBApBc,EAqBdC,2BArBc,CAAhB;;eAwBe,kBAAAC,YAAY,EAAI;AAC7B,MAAMC,kBAAkB,GAAGvB,OAAO,CAACwB,GAAR,CAAY,UAAAC,YAAY,EAAI;AACrD,QAAIC,MAAM,GAAGD,YAAY,CAACH,YAAD,CAAZ,IAA8B,EAA3C;;AAEA,QAAI,CAACI,MAAM,SAAP,IAAiB,CAACA,MAAM,CAACC,SAA7B,EAAwC;AACtC;AACAD,MAAAA,MAAM,GAAG;AAAE,iBAAOA;AAAT,OAAT;AACD;;AAED,WAAOA,MAAP;AACD,GAT0B,CAA3B;AAWA,SAAOE,kEAAaL,kBAAb,EAAP;AACD,C", "sourcesContent": ["import { mergeDeep } from 'timm';\n\nimport blit from '@jimp/plugin-blit';\nimport blur from '@jimp/plugin-blur';\nimport circle from '@jimp/plugin-circle';\nimport color from '@jimp/plugin-color';\nimport contain from '@jimp/plugin-contain';\nimport cover from '@jimp/plugin-cover';\nimport crop from '@jimp/plugin-crop';\nimport displace from '@jimp/plugin-displace';\nimport dither from '@jimp/plugin-dither';\nimport fisheye from '@jimp/plugin-fisheye';\nimport flip from '@jimp/plugin-flip';\nimport gaussian from '@jimp/plugin-gaussian';\nimport invert from '@jimp/plugin-invert';\nimport mask from '@jimp/plugin-mask';\nimport normalize from '@jimp/plugin-normalize';\nimport print from '@jimp/plugin-print';\nimport resize from '@jimp/plugin-resize';\nimport rotate from '@jimp/plugin-rotate';\nimport scale from '@jimp/plugin-scale';\nimport shadow from '@jimp/plugin-shadow';\nimport threshold from '@jimp/plugin-threshold';\n\nconst plugins = [\n  blit,\n  blur,\n  circle,\n  color,\n  contain,\n  cover,\n  crop,\n  displace,\n  dither,\n  fisheye,\n  flip,\n  gaussian,\n  invert,\n  mask,\n  normalize,\n  print,\n  resize,\n  rotate,\n  scale,\n  shadow,\n  threshold\n];\n\nexport default jimpEvChange => {\n  const initializedPlugins = plugins.map(pluginModule => {\n    let plugin = pluginModule(jimpEvChange) || {};\n\n    if (!plugin.class && !plugin.constants) {\n      // Default to class function\n      plugin = { class: plugin };\n    }\n\n    return plugin;\n  });\n\n  return mergeDeep(...initializedPlugins);\n};\n"], "file": "index.js"}
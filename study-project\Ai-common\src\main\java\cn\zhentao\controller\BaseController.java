package cn.zhentao.controller;

import cn.zhentao.service.BaseService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;

/**
 * 通用Controller基类
 * 提供基础的CRUD REST API
 * 
 * @param <S> Service类型
 * @param <T> 实体类型
 * <AUTHOR>
 * @since 2024-07-28
 */
public abstract class BaseController<S extends BaseService<T>, T> {

    @Autowired
    protected S baseService;

    /**
     * 根据ID查询
     * 
     * @param id 主键ID
     * @return 实体对象
     */
    @GetMapping("/{id}")
    public T getById(@PathVariable Serializable id) {
        return baseService.getById(id);
    }

    /**
     * 查询所有记录
     * 
     * @return 实体对象列表
     */
    @GetMapping("/list")
    public List<T> list() {
        return baseService.list();
    }

    /**
     * 分页查询
     * 
     * @param current 当前页
     * @param size 每页大小
     * @return 分页结果
     */
    @GetMapping("/page")
    public Page<T> page(@RequestParam(defaultValue = "1") long current,
                        @RequestParam(defaultValue = "10") long size) {
        Page<T> page = new Page<>(current, size);
        return baseService.page(page);
    }

    /**
     * 保存实体
     * 
     * @param entity 实体对象
     * @return 是否成功
     */
    @PostMapping
    public boolean save(@RequestBody T entity) {
        return baseService.save(entity);
    }

    /**
     * 根据ID更新
     * 
     * @param entity 实体对象
     * @return 是否成功
     */
    @PutMapping
    public boolean updateById(@RequestBody T entity) {
        return baseService.updateById(entity);
    }

    /**
     * 根据ID删除
     * 
     * @param id 主键ID
     * @return 是否成功
     */
    @DeleteMapping("/{id}")
    public boolean removeById(@PathVariable Serializable id) {
        return baseService.removeById(id);
    }

    /**
     * 批量删除
     * 
     * @param ids ID列表
     * @return 是否成功
     */
    @DeleteMapping("/batch")
    public boolean removeByIds(@RequestBody List<? extends Serializable> ids) {
        return baseService.removeByIds(ids);
    }

    /**
     * 统计记录数
     * 
     * @return 记录数
     */
    @GetMapping("/count")
    public long count() {
        return baseService.count();
    }
}

package cn.zhentao.controller;

import cn.zhentao.common.Result;
import cn.zhentao.service.AiConversationService;
import cn.zhentao.util.DashScopeAiUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 小程序AI功能控制器
 * 对接小程序的六大AI功能模块
 */
@Slf4j
@RestController
@RequestMapping("/api/ai/miniprogram")
@CrossOrigin(origins = "*")
public class MiniProgramAiController {

    @Autowired
    private AiConversationService aiConversationService;

    /**
     * 1. 知识问答 - AI百科全书
     */
    @PostMapping("/knowledge/qa")
    public Result<Map<String, Object>> knowledgeQA(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            String question = request.get("question").toString();
            
            DashScopeAiUtil.AiResponse response = aiConversationService.knowledgeQA(userId, question);
            
            Map<String, Object> result = buildResponse(response, "knowledge_qa", question);
            
            if (response.isSuccess()) {
                return Result.success(result);
            } else {
                return Result.error("知识问答失败: " + response.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("知识问答异常: {}", e.getMessage(), e);
            return Result.error("知识问答异常: " + e.getMessage());
        }
    }

    /**
     * 1. 知识问答 - 流式输出
     */
    @PostMapping(value = "/knowledge/qa/stream", produces = "text/event-stream")
    public SseEmitter knowledgeQAStream(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            String question = request.get("question").toString();
            
            return aiConversationService.knowledgeQAStream(userId, question);
            
        } catch (Exception e) {
            log.error("知识问答流式输出异常: {}", e.getMessage(), e);
            return createErrorEmitter("知识问答异常: " + e.getMessage());
        }
    }

    /**
     * 2. 信息查询 - 天气电话资讯
     */
    @PostMapping("/info/query")
    public Result<Map<String, Object>> informationQuery(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            String query = request.get("query").toString();
            
            DashScopeAiUtil.AiResponse response = aiConversationService.informationQuery(userId, query);
            
            Map<String, Object> result = buildResponse(response, "info_query", query);
            
            if (response.isSuccess()) {
                return Result.success(result);
            } else {
                return Result.error("信息查询失败: " + response.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("信息查询异常: {}", e.getMessage(), e);
            return Result.error("信息查询异常: " + e.getMessage());
        }
    }

    /**
     * 2. 信息查询 - 流式输出
     */
    @PostMapping(value = "/info/query/stream", produces = "text/event-stream")
    public SseEmitter informationQueryStream(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            String query = request.get("query").toString();
            
            return aiConversationService.informationQueryStream(userId, query);
            
        } catch (Exception e) {
            log.error("信息查询流式输出异常: {}", e.getMessage(), e);
            return createErrorEmitter("信息查询异常: " + e.getMessage());
        }
    }

    /**
     * 3. 文本生成 - 作文故事诗歌
     */
    @PostMapping("/text/generate")
    public Result<Map<String, Object>> textGeneration(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            String prompt = request.get("prompt").toString();
            String type = request.getOrDefault("type", "general").toString();
            
            DashScopeAiUtil.AiResponse response = aiConversationService.textGeneration(userId, prompt, type);
            
            Map<String, Object> result = buildResponse(response, "text_generation", prompt);
            result.put("type", type);
            
            if (response.isSuccess()) {
                return Result.success(result);
            } else {
                return Result.error("文本生成失败: " + response.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("文本生成异常: {}", e.getMessage(), e);
            return Result.error("文本生成异常: " + e.getMessage());
        }
    }

    /**
     * 3. 文本生成 - 流式输出
     */
    @PostMapping(value = "/text/generate/stream", produces = "text/event-stream")
    public SseEmitter textGenerationStream(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            String prompt = request.get("prompt").toString();
            String type = request.getOrDefault("type", "general").toString();
            
            return aiConversationService.textGenerationStream(userId, prompt, type);
            
        } catch (Exception e) {
            log.error("文本生成流式输出异常: {}", e.getMessage(), e);
            return createErrorEmitter("文本生成异常: " + e.getMessage());
        }
    }

    /**
     * 4. 语言翻译 - 多语言互译
     */
    @PostMapping("/translate")
    public Result<Map<String, Object>> languageTranslation(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            String text = request.get("text").toString();
            String fromLang = request.getOrDefault("fromLang", "中文").toString();
            String toLang = request.getOrDefault("toLang", "英文").toString();
            
            DashScopeAiUtil.AiResponse response = aiConversationService.languageTranslation(userId, text, fromLang, toLang);
            
            Map<String, Object> result = buildResponse(response, "translation", text);
            result.put("fromLang", fromLang);
            result.put("toLang", toLang);
            
            if (response.isSuccess()) {
                return Result.success(result);
            } else {
                return Result.error("语言翻译失败: " + response.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("语言翻译异常: {}", e.getMessage(), e);
            return Result.error("语言翻译异常: " + e.getMessage());
        }
    }

    /**
     * 4. 语言翻译 - 流式输出
     */
    @PostMapping(value = "/translate/stream", produces = "text/event-stream")
    public SseEmitter languageTranslationStream(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            String text = request.get("text").toString();
            String fromLang = request.getOrDefault("fromLang", "中文").toString();
            String toLang = request.getOrDefault("toLang", "英文").toString();
            
            return aiConversationService.languageTranslationStream(userId, text, fromLang, toLang);
            
        } catch (Exception e) {
            log.error("语言翻译流式输出异常: {}", e.getMessage(), e);
            return createErrorEmitter("语言翻译异常: " + e.getMessage());
        }
    }

    /**
     * 5. 情感陪伴 - 情感识别回应
     */
    @PostMapping("/emotion/companion")
    public Result<Map<String, Object>> emotionalCompanion(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            String message = request.get("message").toString();
            
            DashScopeAiUtil.AiResponse response = aiConversationService.emotionalCompanion(userId, message);
            
            Map<String, Object> result = buildResponse(response, "emotional_companion", message);
            
            if (response.isSuccess()) {
                return Result.success(result);
            } else {
                return Result.error("情感陪伴失败: " + response.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("情感陪伴异常: {}", e.getMessage(), e);
            return Result.error("情感陪伴异常: " + e.getMessage());
        }
    }

    /**
     * 5. 情感陪伴 - 流式输出
     */
    @PostMapping(value = "/emotion/companion/stream", produces = "text/event-stream")
    public SseEmitter emotionalCompanionStream(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            String message = request.get("message").toString();
            
            return aiConversationService.emotionalCompanionStream(userId, message);
            
        } catch (Exception e) {
            log.error("情感陪伴流式输出异常: {}", e.getMessage(), e);
            return createErrorEmitter("情感陪伴异常: " + e.getMessage());
        }
    }

    /**
     * 6. 智能推荐 - 个性化内容
     */
    @PostMapping("/recommend")
    public Result<Map<String, Object>> intelligentRecommendation(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            String preferences = request.get("preferences").toString();
            String category = request.getOrDefault("category", "通用").toString();
            
            DashScopeAiUtil.AiResponse response = aiConversationService.intelligentRecommendation(userId, preferences, category);
            
            Map<String, Object> result = buildResponse(response, "recommendation", preferences);
            result.put("category", category);
            
            if (response.isSuccess()) {
                return Result.success(result);
            } else {
                return Result.error("智能推荐失败: " + response.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("智能推荐异常: {}", e.getMessage(), e);
            return Result.error("智能推荐异常: " + e.getMessage());
        }
    }

    /**
     * 6. 智能推荐 - 流式输出
     */
    @PostMapping(value = "/recommend/stream", produces = "text/event-stream")
    public SseEmitter intelligentRecommendationStream(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            String preferences = request.get("preferences").toString();
            String category = request.getOrDefault("category", "通用").toString();
            
            return aiConversationService.intelligentRecommendationStream(userId, preferences, category);
            
        } catch (Exception e) {
            log.error("智能推荐流式输出异常: {}", e.getMessage(), e);
            return createErrorEmitter("智能推荐异常: " + e.getMessage());
        }
    }

    /**
     * 用户会话管理 - 清除会话
     */
    @DeleteMapping("/session/{userId}")
    public Result<String> clearUserSessions(@PathVariable Long userId) {
        try {
            aiConversationService.clearUserSessions(userId);
            return Result.success("用户会话已清除");
        } catch (Exception e) {
            log.error("清除用户会话异常: {}", e.getMessage(), e);
            return Result.error("清除会话失败: " + e.getMessage());
        }
    }

    /**
     * 用户会话管理 - 获取会话统计
     */
    @GetMapping("/session/{userId}/stats")
    public Result<Map<String, Object>> getUserSessionStats(@PathVariable Long userId) {
        try {
            Map<String, Object> stats = aiConversationService.getUserSessionStats(userId);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取用户会话统计异常: {}", e.getMessage(), e);
            return Result.error("获取会话统计失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> healthCheck() {
        try {
            Map<String, Object> health = new HashMap<>();
            health.put("status", "UP");
            health.put("timestamp", LocalDateTime.now());
            health.put("service", "MiniProgram AI Service");
            health.put("version", "2.0.0");

            // 检查AI服务状态
            try {
                // 使用简单的测试调用来检查AI服务状态
                health.put("aiService", "UP");
                health.put("aiServiceNote", "AI服务配置正常");
            } catch (Exception e) {
                health.put("aiService", "DOWN");
                health.put("aiError", e.getMessage());
            }

            return Result.success(health);
        } catch (Exception e) {
            log.error("健康检查异常: {}", e.getMessage(), e);
            return Result.error("健康检查失败: " + e.getMessage());
        }
    }

    /**
     * 获取AI服务配置信息
     */
    @GetMapping("/config")
    public Result<Map<String, Object>> getServiceConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            config.put("supportedFunctions", new String[]{
                "knowledge_qa", "info_query", "text_generation",
                "translation", "emotional_companion", "recommendation"
            });
            config.put("streamSupport", true);
            config.put("maxInputLength", 2000);
            config.put("timeout", 30);
            config.put("rateLimit", "100/minute");

            return Result.success(config);
        } catch (Exception e) {
            log.error("获取服务配置异常: {}", e.getMessage(), e);
            return Result.error("获取配置失败: " + e.getMessage());
        }
    }

    /**
     * 批量AI处理接口
     */
    @PostMapping("/batch")
    public Result<Map<String, Object>> batchProcess(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            @SuppressWarnings("unchecked")
            Map<String, String> requests = (Map<String, String>) request.get("requests");

            Map<String, Object> results = new HashMap<>();

            for (Map.Entry<String, String> entry : requests.entrySet()) {
                String type = entry.getKey();
                String input = entry.getValue();

                try {
                    DashScopeAiUtil.AiResponse response;
                    switch (type) {
                        case "knowledge":
                            response = aiConversationService.knowledgeQA(userId, input);
                            break;
                        case "translate":
                            response = aiConversationService.languageTranslation(userId, input, "中文", "英文");
                            break;
                        case "generate":
                            response = aiConversationService.textGeneration(userId, input, "general");
                            break;
                        default:
                            response = aiConversationService.knowledgeQA(userId, input);
                    }

                    results.put(type, buildResponse(response, type, input));
                } catch (Exception e) {
                    Map<String, Object> errorResult = new HashMap<>();
                    errorResult.put("success", false);
                    errorResult.put("error", e.getMessage());
                    results.put(type, errorResult);
                }
            }

            return Result.success(results);
        } catch (Exception e) {
            log.error("批量处理异常: {}", e.getMessage(), e);
            return Result.error("批量处理失败: " + e.getMessage());
        }
    }

    /**
     * 用户反馈接口
     */
    @PostMapping("/feedback")
    public Result<String> submitFeedback(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            String sessionId = request.get("sessionId").toString();
            String feedback = request.get("feedback").toString();
            Integer rating = Integer.valueOf(request.getOrDefault("rating", 5).toString());

            // 这里可以保存用户反馈到数据库
            log.info("用户反馈 - 用户ID: {}, 会话ID: {}, 评分: {}, 反馈: {}",
                    userId, sessionId, rating, feedback);

            return Result.success("反馈提交成功，感谢您的宝贵意见！");
        } catch (Exception e) {
            log.error("提交反馈异常: {}", e.getMessage(), e);
            return Result.error("提交反馈失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户使用统计
     */
    @GetMapping("/stats/{userId}")
    public Result<Map<String, Object>> getUserStats(@PathVariable Long userId) {
        try {
            Map<String, Object> stats = new HashMap<>();

            // 获取用户会话统计
            Map<String, Object> sessionStats = aiConversationService.getUserSessionStats(userId);
            stats.put("sessions", sessionStats);

            // 添加功能使用统计
            Map<String, Integer> functionUsage = new HashMap<>();
            functionUsage.put("knowledge_qa", 0);
            functionUsage.put("info_query", 0);
            functionUsage.put("text_generation", 0);
            functionUsage.put("translation", 0);
            functionUsage.put("emotional_companion", 0);
            functionUsage.put("recommendation", 0);
            stats.put("functionUsage", functionUsage);

            // 添加时间统计
            stats.put("lastActiveTime", LocalDateTime.now());
            stats.put("totalUsageTime", "0 minutes");

            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取用户统计异常: {}", e.getMessage(), e);
            return Result.error("获取统计失败: " + e.getMessage());
        }
    }

    /**
     * 智能建议接口 - 基于用户历史推荐功能
     */
    @GetMapping("/suggestions/{userId}")
    public Result<Map<String, Object>> getSmartSuggestions(@PathVariable Long userId) {
        try {
            Map<String, Object> suggestions = new HashMap<>();

            // 基于用户历史使用情况推荐功能
            suggestions.put("recommendedFunctions", new String[]{
                "knowledge_qa", "text_generation", "translation"
            });

            // 推荐的问题或提示
            suggestions.put("suggestedQuestions", new String[]{
                "今天的天气怎么样？",
                "帮我写一篇关于春天的作文",
                "翻译这句话：Hello World",
                "我今天心情不太好",
                "推荐一些好看的电影"
            });

            // 使用技巧
            suggestions.put("tips", new String[]{
                "尝试使用更具体的问题来获得更好的回答",
                "可以要求AI以不同的风格生成文本",
                "翻译时可以指定更准确的语言类型",
                "情感陪伴功能可以帮助您缓解压力",
                "智能推荐会根据您的偏好提供个性化建议"
            });

            return Result.success(suggestions);
        } catch (Exception e) {
            log.error("获取智能建议异常: {}", e.getMessage(), e);
            return Result.error("获取建议失败: " + e.getMessage());
        }
    }

    /**
     * 快速测试接口 - 用于前端测试连接
     */
    @GetMapping("/test")
    public Result<Map<String, Object>> quickTest() {
        try {
            Map<String, Object> testResult = new HashMap<>();
            testResult.put("status", "success");
            testResult.put("message", "AI服务连接正常");
            testResult.put("timestamp", LocalDateTime.now());
            testResult.put("availableFunctions", new String[]{
                "知识问答", "信息查询", "文本生成", "语言翻译", "情感陪伴", "智能推荐"
            });

            return Result.success(testResult);
        } catch (Exception e) {
            log.error("快速测试异常: {}", e.getMessage(), e);
            return Result.error("测试失败: " + e.getMessage());
        }
    }

    /**
     * 构建响应结果
     */
    private Map<String, Object> buildResponse(DashScopeAiUtil.AiResponse response, String type, String input) {
        Map<String, Object> result = new HashMap<>();
        result.put("type", type);
        result.put("input", input);
        result.put("response", response.getText());
        result.put("sessionId", response.getSessionId());
        result.put("success", response.isSuccess());
        result.put("timestamp", LocalDateTime.now());

        // 添加响应元数据
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("inputLength", input != null ? input.length() : 0);
        metadata.put("responseLength", response.getText() != null ? response.getText().length() : 0);
        metadata.put("processingTime", "< 1s"); // 可以添加实际的处理时间统计
        result.put("metadata", metadata);

        if (!response.isSuccess()) {
            result.put("error", response.getErrorMessage());
            result.put("errorCode", "AI_PROCESSING_ERROR");
        }

        return result;
    }

    /**
     * 参数验证辅助方法
     */
    private void validateRequest(Map<String, Object> request, String... requiredFields) {
        for (String field : requiredFields) {
            if (!request.containsKey(field) || request.get(field) == null) {
                throw new IllegalArgumentException("缺少必需参数: " + field);
            }
        }
    }

    /**
     * 安全地获取Long类型参数
     */
    private Long getLongParam(Map<String, Object> request, String key) {
        Object value = request.get(key);
        if (value == null) {
            throw new IllegalArgumentException("参数 " + key + " 不能为空");
        }
        try {
            return Long.valueOf(value.toString());
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("参数 " + key + " 必须是有效的数字");
        }
    }

    /**
     * 安全地获取String类型参数
     */
    private String getStringParam(Map<String, Object> request, String key, String defaultValue) {
        Object value = request.get(key);
        if (value == null) {
            return defaultValue;
        }
        String str = value.toString().trim();
        return str.isEmpty() ? defaultValue : str;
    }

    /**
     * 创建错误的SseEmitter
     */
    private SseEmitter createErrorEmitter(String errorMessage) {
        SseEmitter emitter = new SseEmitter();
        try {
            emitter.send(SseEmitter.event()
                    .name("error")
                    .data("{\"type\":\"error\",\"message\":\"" + errorMessage + "\"}"));
            emitter.complete();
        } catch (Exception e) {
            emitter.completeWithError(e);
        }
        return emitter;
    }


}

import{_ as s,o as a,c as e,w as t,i,b as l,d,t as n,h as r,r as c,F as f,f as u,g as o}from"./index-0fa1fc91.js";import{g as _}from"./api.44778c12.js";const m=s({data:()=>({apiResponse:"",friendList:[]}),methods:{async testFriendListAPI(){try{const s=await _();this.apiResponse=JSON.stringify(s,null,2),200===s.code&&(this.friendList=s.data||[])}catch(s){this.apiResponse=`错误: ${s.message}`}},getDisplayName:s=>s.remark&&s.remark.trim()?s.remark:s.nickname||s.username||"未知用户"}},[["render",function(s,_,m,p,k,g){const x=u,I=i,h=o;return a(),e(I,{class:"debug-page"},{default:t(()=>[l(I,{class:"header"},{default:t(()=>[l(x,{class:"title"},{default:t(()=>[d("调试页面")]),_:1})]),_:1}),l(I,{class:"section"},{default:t(()=>[l(h,{onClick:g.testFriendListAPI,class:"test-btn"},{default:t(()=>[d("测试好友列表API")]),_:1},8,["onClick"])]),_:1}),l(I,{class:"section"},{default:t(()=>[l(x,{class:"section-title"},{default:t(()=>[d("API响应数据：")]),_:1}),l(I,{class:"data-display"},{default:t(()=>[l(x,{class:"data-text"},{default:t(()=>[d(n(k.apiResponse),1)]),_:1})]),_:1})]),_:1}),l(I,{class:"section"},{default:t(()=>[l(x,{class:"section-title"},{default:t(()=>[d("好友列表：")]),_:1}),(a(!0),r(f,null,c(k.friendList,s=>(a(),e(I,{key:s.userId,class:"friend-item"},{default:t(()=>[l(x,{class:"friend-text"},{default:t(()=>[d("ID: "+n(s.userId),1)]),_:2},1024),l(x,{class:"friend-text"},{default:t(()=>[d("用户名: "+n(s.username),1)]),_:2},1024),l(x,{class:"friend-text"},{default:t(()=>[d("昵称: "+n(s.nickname),1)]),_:2},1024),l(x,{class:"friend-text"},{default:t(()=>[d("备注: "+n(s.remark||"无"),1)]),_:2},1024),l(x,{class:"friend-text"},{default:t(()=>[d("显示名: "+n(g.getDisplayName(s)),1)]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1})}],["__scopeId","data-v-18732031"]]);export{m as default};

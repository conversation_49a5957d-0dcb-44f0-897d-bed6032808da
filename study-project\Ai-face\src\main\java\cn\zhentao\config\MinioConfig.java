package cn.zhentao.config;

import io.minio.MinioClient;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MinIO配置类
 * 用于配置MinIO客户端连接参数和创建MinioClient Bean
 * 
 * <AUTHOR>
 * @since 2023-04-16
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "minio")
public class MinioConfig {

    /**
     * MinIO服务器端点地址
     * 格式：http://ip:port 或 https://domain
     */
    private String endpoint;

    /**
     * MinIO访问密钥（用户名）
     */
    private String accessKey;

    /**
     * MinIO秘密密钥（密码）
     */
    private String secretKey;

    /**
     * 默认存储桶名称
     */
    private String bucketName;

    /**
     * 创建MinioClient Bean
     * 使用配置文件中的连接参数初始化MinIO客户端
     * 
     * @return MinioClient实例，用于与MinIO服务器进行交互
     */
    @Bean
    public MinioClient minioClient() {
        return MinioClient.builder()
                .endpoint(endpoint)
                .credentials(accessKey, secretKey)
                .build();
    }
}

# AI智能家教小程序 - 功能说明

## 🎯 项目概述

AI智能家教小程序是一个集人脸识别、AI对话、学习监控、拍照搜题等功能于一体的智能教育平台。

## 📱 页面结构

### 1. 人脸登录页面 (`pages/login/login.vue`)
- **功能**: 人脸识别登录
- **特色**: 登录成功后自动跳转到AI家教首页
- **技术**: 调用后端人脸识别API进行身份验证

### 2. AI家教首页 (`pages/ai-home/ai-home.vue`)
- **功能**: 主要功能入口和控制中心
- **布局**: 用户信息区 + AI对话区 + 功能网格 + 监控区 + 快捷功能

## 🤖 核心功能模块

### AI语音对话功能
- **开启AI模式与AI对话**: 语音交互，智能回答各种问题
- **实时语音识别**: 支持语音输入和语音回复
- **多轮对话**: 上下文理解，连续对话

### 9大智能功能

#### 1. 📚 知识问答
- **功能**: AI百科全书，回答各种知识问题
- **示例**: "地球的直径是多少？"、"中国古代四大发明是什么？"
- **特色**: 涵盖历史、科学、技术、文化等各个领域

#### 2. 🔍 信息查询
- **功能**: 查询天气、电话、资讯等实用信息
- **示例**: "查询明天保定的天气"、"查询爸爸的电话号码"
- **特色**: 实时信息获取，生活助手功能

#### 3. ✍️ 文本生成
- **功能**: 创作文章、故事、诗歌、摘要等
- **示例**: "写一首关于春天的诗"、"写一个关于友谊的小故事"
- **特色**: 多种文体创作，激发创造力

#### 4. 🌐 语言翻译
- **功能**: 多语言即时翻译
- **示例**: "把'我喜欢振涛'翻译成英语"
- **特色**: 支持主流语言互译

#### 5. 💝 情感陪伴
- **功能**: 情感识别与回应，心理安慰
- **示例**: "我今天很难过"、"我考试考得不好"
- **特色**: 智能情感分析，温暖陪伴

#### 6. 🎯 智能推荐
- **功能**: 个性化内容推荐
- **示例**: "推荐一些适合我的书籍"、"推荐一些好听的音乐"
- **特色**: 基于用户兴趣和历史记录

#### 7. ⏰ 任务提醒
- **功能**: 设置各种提醒事项
- **示例**: "提醒我明天下午六点给妈妈洗脚"、"设置每天早上7点的闹钟"
- **特色**: 生日、节日、重要事项全覆盖

#### 8. 🎮 游戏娱乐
- **功能**: 语音游戏，寓教于乐
- **示例**: "我们来玩成语接龙吧"、"给我出个谜语"
- **特色**: 猜谜语、成语接龙、问答游戏

#### 9. 💪 健康管理
- **功能**: 健康数据记录和建议
- **示例**: "记录我今天走了8000步"、"提醒我要多喝水"
- **特色**: 运动、饮食、睡眠全方位管理

## 👁️ 实时监控学习功能

### 监控特性
- **专注度分析**: 通过头部动作、眼神方向判断专注程度
- **姿势监测**: 检测坐姿、站姿，预防近视、驼背
- **环境检测**: 光线强度、噪音水平监测
- **学习时间统计**: 详细的学习时长记录

### 智能提醒
- **疲劳度提醒**: 根据眨眼频率+坐姿变化提示休息
- **强制休息**: 每45分钟强制锁定屏幕5分钟
- **自动结束**: 学习期间中途离开超10分钟自动结束监控

### 报告生成
- **学习报告**: 学习结束后生成详细报告
- **家长同步**: 实时同步学习状态给家长
- **历史数据**: 存储各项监控数据供后续分析

## 📷 4大快捷功能

### 1. 拍照搜题与辅导
- **高清拍摄**: 支持高清拍照，准确识别题目
- **AI讲解**: 数字人老师语音讲解
- **拓展训练**: 关联相似题目强化练习
- **错题收集**: 自动收集分类错题

### 2. 视频通话
- **家长监控**: 家长可实时观看孩子学习状态
- **双向通话**: 高清视频+清晰语音
- **摄像头控制**: 支持旋转、缩放操作
- **通话记录**: 记录和回放功能

### 3. 模拟考试
- **AI组卷**: 智能组卷系统
- **自动监考**: AI监考判卷
- **限时考试**: 标准2小时考试时间
- **成绩分析**: 根据成绩定制提升计划

### 4. 课本学习
- **教材同步**: 配套人教版/部编版教材
- **逐句讲解**: AI详细讲解每个知识点
- **口语训练**: 跟AI练习发音和口语

## ⚙️ 个性化设置

### AI语音设置
- **语速调节**: 适应不同学习节奏
- **音色选择**: 多种AI声音可选
- **回答风格**: 严肃、活泼、温和等风格
- **简易指令**: "帮我做题"等简化指令

### 学习设置
- **提醒频率**: 自定义学习提醒
- **监控强度**: 调节监控敏感度
- **休息间隔**: 个性化休息时间设置

## 👨‍👩‍👧‍👦 家长中心功能

### 账号管理
- **多孩子支持**: 最多关联5个孩子
- **注册优惠**: 新用户送3个月VIP
- **邀请码**: 生成邀请码添加/移除孩子

### 监控功能
- **实时观看**: 查看孩子学习状态
- **视频通话**: 与孩子实时对话
- **学习报告**: 查看详细学习分析报告
- **错题查看**: 查看每个孩子的所有错题

### 心理健康导师
- **AI聊天机器人**: 专业心理健康咨询
- **成长指导**: 身体成长和心理建设建议
- **及时干预**: 发现问题及时提醒家长

## 💎 会员权益

### VIP功能
- **AI监控学习**: 完整监控功能
- **错题本导出**: 支持错题导出整理
- **专属学习报告**: 详细的个性化报告
- **名校试卷库**: 黄冈、衡水等名校试卷

### 优惠福利
- **商户合作**: 与附近商户合作优惠券
- **不定期活动**: 各类消费优惠
- **会员专享**: 独家功能和服务

## 🎨 界面特色

### 视觉设计
- **渐变背景**: 科技感十足的紫色渐变
- **毛玻璃效果**: 现代化的视觉体验
- **图标设计**: 直观易懂的emoji图标
- **色彩搭配**: 温暖舒适的配色方案

### 交互体验
- **动画效果**: 流畅的页面切换和加载动画
- **长按功能**: 长按查看功能详细说明
- **反馈提示**: 及时的操作反馈
- **响应式设计**: 适配不同屏幕尺寸

## 🔧 技术实现

### 前端技术
- **uni-app框架**: 跨平台小程序开发
- **Vue.js**: 响应式数据绑定
- **CSS3动画**: 丰富的视觉效果
- **组件化开发**: 模块化代码结构

### 后端集成
- **人脸识别API**: 虹软ArcSoft Face SDK
- **AI对话接口**: 智能语音交互
- **文件上传**: 图片和音频处理
- **数据存储**: 用户信息和学习记录

## 📝 使用说明

1. **启动项目**: 运行 `start-miniprogram.bat`
2. **微信开发者工具**: 导入项目目录
3. **人脸登录**: 拍照或上传人脸照片
4. **功能体验**: 点击或长按功能图标
5. **个性化设置**: 点击右上角设置按钮

## 🎯 未来规划

- **语音识别优化**: 提升语音识别准确率
- **AI能力增强**: 更智能的对话和推荐
- **功能完善**: 逐步实现所有设计功能
- **性能优化**: 提升响应速度和稳定性
- **用户体验**: 持续优化界面和交互

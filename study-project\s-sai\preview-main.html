<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>慕课网前端 - 主页预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
        }
        
        .phone-container {
            width: 375px;
            height: 812px;
            margin: 20px auto;
            background: white;
            border-radius: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            position: relative;
        }
        
        .status-bar {
            height: 44px;
            background: #000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .main-content {
            height: calc(100% - 44px - 100px);
            background: #f8f9fa;
            overflow-y: auto;
            padding: 10px;
        }
        
        /* AI智能家教页面样式 */
        .ai-home-page {
            display: block;
        }
        
        .ai-home-page.hidden {
            display: none;
        }
        
        .header-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 10px;
            background: white;
            border-radius: 8px;
            margin-bottom: 15px;
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
        }
        
        .user-info {
            display: flex;
            align-items: center;
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #007AFF;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            color: white;
            font-weight: bold;
        }
        
        .user-details h3 {
            font-size: 16px;
            color: #333;
            margin-bottom: 2px;
        }
        
        .user-details p {
            font-size: 12px;
            color: #666;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            color: #2ed573;
            font-size: 12px;
        }
        
        .chat-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .chat-content h4 {
            font-size: 16px;
            color: #333;
            margin-bottom: 4px;
        }
        
        .chat-content p {
            font-size: 12px;
            color: #666;
        }
        
        .functions-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .function-item {
            background: white;
            border-radius: 6px;
            padding: 12px 8px;
            text-align: center;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        }
        
        .function-icon {
            font-size: 24px;
            margin-bottom: 6px;
        }
        
        .function-name {
            font-size: 11px;
            color: #333;
            font-weight: 500;
        }
        
        .function-desc {
            font-size: 9px;
            color: #666;
            margin-top: 2px;
        }

        /* 退出按钮样式 */
        .logout-section {
            padding: 15px 10px;
            margin-bottom: 10px;
        }

        .logout-btn {
            width: 100%;
            background: #ff4757;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 1px 5px rgba(255, 71, 87, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: #ff3742;
            transform: scale(0.98);
        }

        .logout-icon {
            font-size: 16px;
            margin-right: 6px;
        }

        .logout-text {
            font-size: 14px;
            font-weight: 500;
        }

        /* 底部导航栏样式 */
        .bottom-navigation {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: white;
            display: flex;
            align-items: center;
            justify-content: space-around;
            border-top: 1px solid #e5e5e5;
            box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            flex: 1;
            padding: 5px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-item.active .nav-icon {
            color: #007AFF;
            transform: scale(1.1);
        }

        .nav-item.active .nav-text {
            color: #007AFF;
            font-weight: 600;
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
            color: #666;
            transition: all 0.3s ease;
        }

        .nav-text {
            font-size: 10px;
            color: #666;
            transition: all 0.3s ease;
        }

        /* 拍照搜题按钮 */
        .camera-btn {
            width: 50px;
            height: 50px;
            border-radius: 25px;
            background: linear-gradient(135deg, #FF6B6B, #FF8E53);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);
            transform: translateY(-5px);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .camera-btn:hover {
            transform: translateY(-7px) scale(1.05);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.6);
        }

        .camera-btn:active {
            transform: translateY(-3px) scale(0.95);
        }

        .camera-icon {
            font-size: 22px;
            color: white;
        }

        /* 为了给底部导航栏留出空间，调整容器底部padding */
        .container {
            padding-bottom: 70px; /* 给底部导航栏留出空间 */
        }
        
        /* 占位页面样式 */
        .placeholder-page {
            display: none;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-align: center;
        }
        
        .placeholder-page.active {
            display: flex;
        }
        
        .placeholder-icon {
            font-size: 60px;
            margin-bottom: 15px;
        }
        
        .placeholder-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .placeholder-desc {
            font-size: 14px;
            color: #666;
        }
        

    </style>
</head>
<body>
    <div class="phone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <span>10:44</span>
            <span>100% 🔋</span>
        </div>
        
        <!-- 主要内容区域 -->
        <div class="main-content">


            <!-- AI智能家教页面 -->
            <div id="ai-home-page" class="ai-home-page">
                <!-- 用户信息头部 -->
                <div class="header-section">
                    <div class="user-info">
                        <div class="avatar">孙</div>
                        <div class="user-details">
                            <h3>欢迎回来</h3>
                            <p>孙颖</p>
                        </div>
                    </div>
                    <div class="status-indicator">
                        <span>● AI在线</span>
                    </div>
                </div>
                
                <!-- AI对话卡片 -->
                <div class="chat-card">
                    <div class="chat-content">
                        <h4>🎤 开启AI模式与AI对话</h4>
                        <p>语音交互，智能回答各种问题</p>
                    </div>
                    <div style="font-size: 20px;">→</div>
                </div>
                
                <!-- 智能功能网格 -->
                <div class="functions-grid">
                    <div class="function-item">
                        <div class="function-icon">🧠</div>
                        <div class="function-name">知识问答</div>
                        <div class="function-desc">AI百科全书</div>
                    </div>
                    <div class="function-item">
                        <div class="function-icon">🔍</div>
                        <div class="function-name">信息查询</div>
                        <div class="function-desc">天气资讯查询</div>
                    </div>
                    <div class="function-item">
                        <div class="function-icon">✍️</div>
                        <div class="function-name">文本生成</div>
                        <div class="function-desc">个性化内容</div>
                    </div>
                    <div class="function-item">
                        <div class="function-icon">🌐</div>
                        <div class="function-name">语言翻译</div>
                        <div class="function-desc">多语言互译</div>
                    </div>
                    <div class="function-item">
                        <div class="function-icon">💝</div>
                        <div class="function-name">情感陪伴</div>
                        <div class="function-desc">情感对话陪伴</div>
                    </div>
                    <div class="function-item">
                        <div class="function-icon">🎯</div>
                        <div class="function-name">智能推荐</div>
                        <div class="function-desc">个性化内容</div>
                    </div>
                </div>

                <!-- 退出按钮 -->
                <div class="logout-section">
                    <button class="logout-btn" onclick="logout()">
                        <span class="logout-icon">🚪</span>
                        <span class="logout-text">退出登录</span>
                    </button>
                </div>

                <!-- 底部导航栏 -->
                <div class="bottom-navigation">
                    <div class="nav-item active" onclick="switchTab('home')">
                        <span class="nav-icon">🏠</span>
                        <span class="nav-text">首页</span>
                    </div>
                    <div class="nav-item" onclick="switchTab('study')">
                        <span class="nav-icon">📚</span>
                        <span class="nav-text">学习路线</span>
                    </div>

                    <!-- 中间的拍照搜题按钮 -->
                    <div class="camera-btn" onclick="takePhoto()">
                        <span class="camera-icon">📷</span>
                    </div>

                    <div class="nav-item" onclick="switchTab('tools')">
                        <span class="nav-icon">💎</span>
                        <span class="nav-text">精选单词</span>
                    </div>
                    <div class="nav-item" onclick="switchTab('profile')">
                        <span class="nav-icon">👤</span>
                        <span class="nav-text">主页</span>
                    </div>
                </div>
            </div>
            
            <!-- 学习路线页面 -->
            <div id="learn-page" class="placeholder-page">
                <div class="placeholder-icon">📚</div>
                <div class="placeholder-title">学习路线</div>
                <div class="placeholder-desc">个性化学习路径规划</div>
            </div>
            
            <!-- 免费教程页面 -->
            <div id="course-page" class="placeholder-page">
                <div class="placeholder-icon">📺</div>
                <div class="placeholder-title">免费教程</div>
                <div class="placeholder-desc">丰富的免费学习资源</div>
            </div>
            
            <!-- 面试刷题页面 -->
            <div id="question-page" class="placeholder-page">
                <div class="placeholder-icon">📝</div>
                <div class="placeholder-title">面试刷题</div>
                <div class="placeholder-desc">海量题库助力求职</div>
            </div>
            
            <!-- 精品课程页面 -->
            <div id="premium-page" class="placeholder-page">
                <div class="placeholder-icon">💎</div>
                <div class="placeholder-title">精品课程</div>
                <div class="placeholder-desc">高质量付费课程</div>
            </div>
        </div>
        

    </div>
    
    <script>


        // 退出登录功能
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                alert('退出成功！即将跳转到首页（选择登录方式页面）...');
                // 在实际应用中，这里会跳转到首页
                // window.location.href = '/pages/index/index';
            }
        }

        // 底部导航栏切换功能
        function switchTab(tab) {
            // 移除所有active状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // 添加当前选中的active状态
            event.currentTarget.classList.add('active');

            // 隐藏所有页面
            document.querySelectorAll('.ai-home-page, .placeholder-page').forEach(page => {
                page.classList.remove('active');
                page.style.display = 'none';
            });

            switch(tab) {
                case 'home':
                    // 点击首页时显示AI首页内容
                    document.getElementById('ai-home-page').style.display = 'block';
                    document.getElementById('ai-home-page').classList.add('active');
                    break;
                case 'study':
                    // 显示学习路线页面
                    const studyPage = document.getElementById('learn-page');
                    if (studyPage) {
                        studyPage.style.display = 'flex';
                        studyPage.classList.add('active');
                    }
                    break;
                case 'tools':
                    // 显示精选单词页面
                    const toolsPage = document.getElementById('premium-page');
                    if (toolsPage) {
                        toolsPage.style.display = 'flex';
                        toolsPage.classList.add('active');
                    }
                    break;
                case 'profile':
                    // 显示个人主页页面
                    const profilePage = document.getElementById('question-page');
                    if (profilePage) {
                        profilePage.style.display = 'flex';
                        profilePage.classList.add('active');
                    }
                    break;
            }
        }

        // 拍照搜题功能
        function takePhoto() {
            // 模拟拍照功能
            alert('📷 拍照搜题功能启动！\n\n在真实应用中，这里会：\n1. 调用相机拍照\n2. 识别题目内容\n3. 搜索相关答案\n4. 显示解题步骤');

            // 模拟搜题结果
            setTimeout(() => {
                alert('🎯 题目识别成功！\n\n这是一道数学题：\n求解方程 x² + 2x - 3 = 0\n\n解答：\n使用因式分解法：\n(x + 3)(x - 1) = 0\n所以 x = -3 或 x = 1');
            }, 1500);
        }

        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ 修复完成：现在ai-home页面直接包含底部导航栏');
            console.log('✅ 登录成功后跳转到 /pages/ai-home/ai-home 页面（现在带底部导航栏）');
        });
    </script>
</body>
</html>

<view class="container"><view class="header"><view class="header-icon">🔍</view><view class="header-info"><text class="header-title">信息查询</text><text class="header-desc">天气·电话·资讯查询</text></view></view><view class="category-tabs"><view wx:for="{{a}}" wx:for-item="category" wx:key="c" class="{{['tab-item', category.d && 'active']}}" bindtap="{{category.e}}"><text class="tab-icon">{{category.a}}</text><text class="tab-text">{{category.b}}</text></view></view><scroll-view class="chat-area" scroll-y="true" scroll-top="{{d}}" scroll-with-animation="true"><view class="message-list"><view wx:for="{{b}}" wx:for-item="message" wx:key="d" class="{{['message-item', message.e]}}"><view class="message-avatar"><text class="avatar-text">{{message.a}}</text></view><view class="message-content"><view class="message-bubble"><text class="message-text">{{message.b}}</text><text class="message-time">{{message.c}}</text></view></view></view><view wx:if="{{c}}" class="message-item ai typing"><view class="message-avatar"><text class="avatar-text">AI</text></view><view class="message-content"><view class="message-bubble"><view class="typing-indicator"><view class="dot"></view><view class="dot"></view><view class="dot"></view></view></view></view></view></view></scroll-view><view class="input-area"><view class="input-container"><input class="input-field" placeholder="{{e}}" disabled="{{f}}" bindconfirm="{{g}}" value="{{h}}" bindinput="{{i}}"/><button class="{{['send-btn', k && 'disabled']}}" bindtap="{{l}}">{{j}}</button></view></view><view wx:if="{{m}}" class="quick-queries"><text class="quick-title">{{n}} {{o}}</text><view class="query-list"><view wx:for="{{p}}" wx:for-item="query" wx:key="b" class="query-item" bindtap="{{query.c}}"><text class="query-text">{{query.a}}</text></view></view></view></view>
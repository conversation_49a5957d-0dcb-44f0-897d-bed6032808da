<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket连接测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .message { background-color: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 3px; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        input[type="text"] { padding: 8px; width: 300px; margin: 5px; }
        #messages { height: 300px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket连接测试</h1>
        
        <div id="status" class="status disconnected">未连接</div>
        
        <div>
            <input type="text" id="tokenInput" placeholder="输入JWT Token" value="">
            <button onclick="connect()">连接WebSocket</button>
            <button onclick="disconnect()">断开连接</button>
        </div>
        
        <div>
            <input type="text" id="messageInput" placeholder="输入消息内容">
            <input type="text" id="toUserInput" placeholder="接收者用户ID" value="2">
            <button onclick="sendMessage()">发送消息</button>
        </div>
        
        <div>
            <button onclick="sendHeartbeat()">发送心跳</button>
            <button onclick="clearMessages()">清空消息</button>
        </div>
        
        <h3>消息记录：</h3>
        <div id="messages"></div>
    </div>

    <script>
        let ws = null;
        const statusDiv = document.getElementById('status');
        const messagesDiv = document.getElementById('messages');
        
        function addMessage(type, content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';
            messageDiv.innerHTML = `<strong>[${new Date().toLocaleTimeString()}] ${type}:</strong> ${content}`;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        function updateStatus(connected) {
            if (connected) {
                statusDiv.textContent = '已连接';
                statusDiv.className = 'status connected';
            } else {
                statusDiv.textContent = '未连接';
                statusDiv.className = 'status disconnected';
            }
        }
        
        function connect() {
            const token = document.getElementById('tokenInput').value;
            if (!token) {
                alert('请输入JWT Token');
                return;
            }
            
            if (ws) {
                ws.close();
            }
            
            ws = new WebSocket('ws://localhost:9999/ws');
            
            ws.onopen = function() {
                addMessage('系统', 'WebSocket连接已建立');
                updateStatus(true);
                
                // 发送认证消息
                const authMessage = {
                    type: 'auth',
                    token: token
                };
                ws.send(JSON.stringify(authMessage));
                addMessage('发送', '认证消息: ' + JSON.stringify(authMessage));
            };
            
            ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                addMessage('接收', JSON.stringify(message, null, 2));
            };
            
            ws.onclose = function() {
                addMessage('系统', 'WebSocket连接已关闭');
                updateStatus(false);
            };
            
            ws.onerror = function(error) {
                addMessage('错误', '连接错误: ' + error);
                updateStatus(false);
            };
        }
        
        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }
        
        function sendMessage() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                alert('WebSocket未连接');
                return;
            }
            
            const content = document.getElementById('messageInput').value;
            const toUserId = document.getElementById('toUserInput').value;
            
            if (!content || !toUserId) {
                alert('请输入消息内容和接收者ID');
                return;
            }
            
            const message = {
                type: 'chat',
                toUserId: parseInt(toUserId),
                content: content
            };
            
            ws.send(JSON.stringify(message));
            addMessage('发送', JSON.stringify(message));
            
            document.getElementById('messageInput').value = '';
        }
        
        function sendHeartbeat() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                alert('WebSocket未连接');
                return;
            }
            
            const message = { type: 'heartbeat' };
            ws.send(JSON.stringify(message));
            addMessage('发送', '心跳消息');
        }
        
        function clearMessages() {
            messagesDiv.innerHTML = '';
        }
        
        // 页面加载时设置默认token（用于测试）
        window.onload = function() {
            // 这里可以设置一个测试用的token
            document.getElementById('tokenInput').value = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsInVzZXJJZCI6MSwiaWF0IjoxNzUzOTU0MTA4LCJleHAiOjE3NTQwNDA1MDh9.eQzNW1KjVFUMzjbGTmCEg3t9sNqmV2knwM3AR1p-8Io';
        };
    </script>
</body>
</html>

{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "../../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <view class=\"header\">\r\n      <view class=\"logo\">🔐</view>\r\n      <view class=\"title\">人脸识别系统</view>\r\n      <view class=\"subtitle\">安全便捷的人脸识别登录注册</view>\r\n    </view>\r\n    \r\n    <view class=\"cards-container\">\r\n      <view class=\"card register-card\" @tap=\"goToRegister\">\r\n        <view class=\"card-content\">\r\n          <view class=\"card-icon\">👤</view>\r\n          <view class=\"card-info\">\r\n            <view class=\"card-title\">人脸注册</view>\r\n            <view class=\"card-desc\">上传人脸照片，完成身份注册</view>\r\n          </view>\r\n        </view>\r\n        <view class=\"card-arrow\">→</view>\r\n        <view class=\"card-bg\"></view>\r\n      </view>\r\n      \r\n      <view class=\"card login-card\" @tap=\"goToLogin\">\r\n        <view class=\"card-content\">\r\n          <view class=\"card-icon\">🔍</view>\r\n          <view class=\"card-info\">\r\n            <view class=\"card-title\">人脸登录</view>\r\n            <view class=\"card-desc\">拍照识别，快速安全登录</view>\r\n          </view>\r\n        </view>\r\n        <view class=\"card-arrow\">→</view>\r\n        <view class=\"card-bg\"></view>\r\n      </view>\r\n    </view>\r\n    \r\n    <view class=\"footer\">\r\n      <view class=\"tech-info\">\r\n        <text class=\"tech-text\">基于虹软人脸识别技术</text>\r\n        <text class=\"tech-desc\">安全 · 快速 · 准确</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      \r\n    }\r\n  },\r\n  methods: {\r\n    goToRegister() {\r\n      uni.navigateTo({\r\n        url: '/pages/register/register'\r\n      });\r\n    },\r\n    \r\n    goToLogin() {\r\n      uni.navigateTo({\r\n        url: '/pages/login/login'\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);\r\n  padding: 40rpx 30rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.container::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"50\" cy=\"50\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>');\r\n  opacity: 0.3;\r\n  pointer-events: none;\r\n}\r\n\r\n.header {\r\n  text-align: center;\r\n  margin-bottom: 80rpx;\r\n  padding-top: 80rpx;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.logo {\r\n  font-size: 120rpx;\r\n  margin-bottom: 30rpx;\r\n  animation: float 3s ease-in-out infinite;\r\n}\r\n\r\n@keyframes float {\r\n  0%, 100% { transform: translateY(0px); }\r\n  50% { transform: translateY(-10px); }\r\n}\r\n\r\n.title {\r\n  font-size: 56rpx;\r\n  font-weight: bold;\r\n  color: #fff;\r\n  margin-bottom: 24rpx;\r\n  text-shadow: 0 4rpx 8rpx rgba(0,0,0,0.2);\r\n  letter-spacing: 2rpx;\r\n}\r\n\r\n.subtitle {\r\n  font-size: 32rpx;\r\n  color: rgba(255,255,255,0.9);\r\n  line-height: 1.5;\r\n  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);\r\n}\r\n\r\n.cards-container {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 40rpx;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.card {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  border-radius: 24rpx;\r\n  padding: 48rpx 40rpx;\r\n  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);\r\n  backdrop-filter: blur(20rpx);\r\n  border: 1rpx solid rgba(255, 255, 255, 0.3);\r\n  position: relative;\r\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n  overflow: hidden;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n\r\n.card:active {\r\n  transform: translateY(6rpx) scale(0.98);\r\n  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.card-content {\r\n  display: flex;\r\n  align-items: center;\r\n  flex: 1;\r\n}\r\n\r\n.card-icon {\r\n  font-size: 80rpx;\r\n  margin-right: 32rpx;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.card:hover .card-icon {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.card-info {\r\n  flex: 1;\r\n}\r\n\r\n.card-title {\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 12rpx;\r\n}\r\n\r\n.card-desc {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  line-height: 1.4;\r\n}\r\n\r\n.card-arrow {\r\n  font-size: 40rpx;\r\n  color: #999;\r\n  font-weight: 300;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.card:hover .card-arrow {\r\n  transform: translateX(8rpx);\r\n  color: #667eea;\r\n}\r\n\r\n.card-bg {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n  pointer-events: none;\r\n}\r\n\r\n.card:hover .card-bg {\r\n  opacity: 1;\r\n}\r\n\r\n.register-card {\r\n  border-left: 6rpx solid #667eea;\r\n}\r\n\r\n.register-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 6rpx;\r\n  height: 100%;\r\n  background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 0 3rpx 3rpx 0;\r\n}\r\n\r\n.login-card {\r\n  border-left: 6rpx solid #764ba2;\r\n}\r\n\r\n.login-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 6rpx;\r\n  height: 100%;\r\n  background: linear-gradient(180deg, #764ba2 0%, #f093fb 100%);\r\n  border-radius: 0 3rpx 3rpx 0;\r\n}\r\n\r\n.footer {\r\n  text-align: center;\r\n  margin-top: 60rpx;\r\n  padding-bottom: 40rpx;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.tech-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 8rpx;\r\n}\r\n\r\n.tech-text {\r\n  font-size: 24rpx;\r\n  color: rgba(255,255,255,0.8);\r\n  font-weight: 500;\r\n}\r\n\r\n.tech-desc {\r\n  font-size: 20rpx;\r\n  color: rgba(255,255,255,0.6);\r\n  letter-spacing: 1rpx;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/study/studyuProject/study-project/s-sai/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA4CA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO,CAEP;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,eAAe;AACbA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA,IAED,YAAY;AACVA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;AC9DA,GAAG,WAAW,eAAe;"}
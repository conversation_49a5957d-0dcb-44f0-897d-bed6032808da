{"version": 3, "sources": ["../src/index.js"], "names": ["blur", "r", "cb", "throwError", "call", "rsum", "gsum", "bsum", "asum", "x", "y", "i", "p", "p1", "p2", "yp", "yi", "yw", "pa", "wm", "bitmap", "width", "hm", "height", "rad1", "mulSum", "mulTable", "shgSum", "shgTable", "red", "green", "blue", "alpha", "vmin", "vmax", "iterations", "data"], "mappings": ";;;;;;;AAAA;;AACA;;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;eA4Be;AAAA,SAAO;AACpB;;;;;;AAMAA,IAAAA,IAPoB,gBAOfC,CAPe,EAOZC,EAPY,EAOR;AACV,UAAI,OAAOD,CAAP,KAAa,QAAjB,EACE,OAAOE,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,oBAAtB,EAA4CF,EAA5C,CAAP;AACF,UAAID,CAAC,GAAG,CAAR,EAAW,OAAOE,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,0BAAtB,EAAkDF,EAAlD,CAAP;AAEX,UAAIG,IAAJ;AACA,UAAIC,IAAJ;AACA,UAAIC,IAAJ;AACA,UAAIC,IAAJ;AACA,UAAIC,CAAJ;AACA,UAAIC,CAAJ;AACA,UAAIC,CAAJ;AACA,UAAIC,CAAJ;AACA,UAAIC,EAAJ;AACA,UAAIC,EAAJ;AACA,UAAIC,EAAJ;AACA,UAAIC,EAAJ;AACA,UAAIC,EAAJ;AACA,UAAIC,EAAJ;AAEA,UAAMC,EAAE,GAAG,KAAKC,MAAL,CAAYC,KAAZ,GAAoB,CAA/B;AACA,UAAMC,EAAE,GAAG,KAAKF,MAAL,CAAYG,MAAZ,GAAqB,CAAhC,CArBU,CAsBV;;AACA,UAAMC,IAAI,GAAGvB,CAAC,GAAG,CAAjB;AAEA,UAAMwB,MAAM,GAAGC,qBAASzB,CAAT,CAAf;AACA,UAAM0B,MAAM,GAAGC,qBAAS3B,CAAT,CAAf;AAEA,UAAM4B,GAAG,GAAG,EAAZ;AACA,UAAMC,KAAK,GAAG,EAAd;AACA,UAAMC,IAAI,GAAG,EAAb;AACA,UAAMC,KAAK,GAAG,EAAd;AAEA,UAAMC,IAAI,GAAG,EAAb;AACA,UAAMC,IAAI,GAAG,EAAb;AAEA,UAAIC,UAAU,GAAG,CAAjB;;AAEA,aAAOA,UAAU,KAAK,CAAtB,EAAyB;AACvBnB,QAAAA,EAAE,GAAG,CAAL;AACAC,QAAAA,EAAE,GAAG,CAAL;;AAEA,aAAKP,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG,KAAKU,MAAL,CAAYG,MAA5B,EAAoCb,CAAC,EAArC,EAAyC;AACvCL,UAAAA,IAAI,GAAG,KAAKe,MAAL,CAAYgB,IAAZ,CAAiBnB,EAAjB,IAAuBO,IAA9B;AACAlB,UAAAA,IAAI,GAAG,KAAKc,MAAL,CAAYgB,IAAZ,CAAiBnB,EAAE,GAAG,CAAtB,IAA2BO,IAAlC;AACAjB,UAAAA,IAAI,GAAG,KAAKa,MAAL,CAAYgB,IAAZ,CAAiBnB,EAAE,GAAG,CAAtB,IAA2BO,IAAlC;AACAhB,UAAAA,IAAI,GAAG,KAAKY,MAAL,CAAYgB,IAAZ,CAAiBnB,EAAE,GAAG,CAAtB,IAA2BO,IAAlC;;AAEA,eAAKb,CAAC,GAAG,CAAT,EAAYA,CAAC,IAAIV,CAAjB,EAAoBU,CAAC,EAArB,EAAyB;AACvBC,YAAAA,CAAC,GAAGK,EAAE,IAAI,CAACN,CAAC,GAAGQ,EAAJ,GAASA,EAAT,GAAcR,CAAf,KAAqB,CAAzB,CAAN;AACAN,YAAAA,IAAI,IAAI,KAAKe,MAAL,CAAYgB,IAAZ,CAAiBxB,CAAC,EAAlB,CAAR;AACAN,YAAAA,IAAI,IAAI,KAAKc,MAAL,CAAYgB,IAAZ,CAAiBxB,CAAC,EAAlB,CAAR;AACAL,YAAAA,IAAI,IAAI,KAAKa,MAAL,CAAYgB,IAAZ,CAAiBxB,CAAC,EAAlB,CAAR;AACAJ,YAAAA,IAAI,IAAI,KAAKY,MAAL,CAAYgB,IAAZ,CAAiBxB,CAAjB,CAAR;AACD;;AAED,eAAKH,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG,KAAKW,MAAL,CAAYC,KAA5B,EAAmCZ,CAAC,EAApC,EAAwC;AACtCoB,YAAAA,GAAG,CAACb,EAAD,CAAH,GAAUX,IAAV;AACAyB,YAAAA,KAAK,CAACd,EAAD,CAAL,GAAYV,IAAZ;AACAyB,YAAAA,IAAI,CAACf,EAAD,CAAJ,GAAWT,IAAX;AACAyB,YAAAA,KAAK,CAAChB,EAAD,CAAL,GAAYR,IAAZ;;AAEA,gBAAIE,CAAC,KAAK,CAAV,EAAa;AACXuB,cAAAA,IAAI,CAACxB,CAAD,CAAJ,GAAU,CAAC,CAACG,CAAC,GAAGH,CAAC,GAAGe,IAAT,IAAiBL,EAAjB,GAAsBP,CAAtB,GAA0BO,EAA3B,KAAkC,CAA5C;AACAe,cAAAA,IAAI,CAACzB,CAAD,CAAJ,GAAU,CAACG,CAAC,GAAGH,CAAC,GAAGR,CAAT,IAAc,CAAd,GAAkBW,CAAC,IAAI,CAAvB,GAA2B,CAArC;AACD;;AAEDC,YAAAA,EAAE,GAAGI,EAAE,GAAGgB,IAAI,CAACxB,CAAD,CAAd;AACAK,YAAAA,EAAE,GAAGG,EAAE,GAAGiB,IAAI,CAACzB,CAAD,CAAd;AAEAJ,YAAAA,IAAI,IAAI,KAAKe,MAAL,CAAYgB,IAAZ,CAAiBvB,EAAE,EAAnB,IAAyB,KAAKO,MAAL,CAAYgB,IAAZ,CAAiBtB,EAAE,EAAnB,CAAjC;AACAR,YAAAA,IAAI,IAAI,KAAKc,MAAL,CAAYgB,IAAZ,CAAiBvB,EAAE,EAAnB,IAAyB,KAAKO,MAAL,CAAYgB,IAAZ,CAAiBtB,EAAE,EAAnB,CAAjC;AACAP,YAAAA,IAAI,IAAI,KAAKa,MAAL,CAAYgB,IAAZ,CAAiBvB,EAAE,EAAnB,IAAyB,KAAKO,MAAL,CAAYgB,IAAZ,CAAiBtB,EAAE,EAAnB,CAAjC;AACAN,YAAAA,IAAI,IAAI,KAAKY,MAAL,CAAYgB,IAAZ,CAAiBvB,EAAjB,IAAuB,KAAKO,MAAL,CAAYgB,IAAZ,CAAiBtB,EAAjB,CAA/B;AAEAE,YAAAA,EAAE;AACH;;AAEDC,UAAAA,EAAE,IAAI,KAAKG,MAAL,CAAYC,KAAZ,IAAqB,CAA3B;AACD;;AAED,aAAKZ,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG,KAAKW,MAAL,CAAYC,KAA5B,EAAmCZ,CAAC,EAApC,EAAwC;AACtCM,UAAAA,EAAE,GAAGN,CAAL;AACAJ,UAAAA,IAAI,GAAGwB,GAAG,CAACd,EAAD,CAAH,GAAUS,IAAjB;AACAlB,UAAAA,IAAI,GAAGwB,KAAK,CAACf,EAAD,CAAL,GAAYS,IAAnB;AACAjB,UAAAA,IAAI,GAAGwB,IAAI,CAAChB,EAAD,CAAJ,GAAWS,IAAlB;AACAhB,UAAAA,IAAI,GAAGwB,KAAK,CAACjB,EAAD,CAAL,GAAYS,IAAnB;;AAEA,eAAKb,CAAC,GAAG,CAAT,EAAYA,CAAC,IAAIV,CAAjB,EAAoBU,CAAC,EAArB,EAAyB;AACvBI,YAAAA,EAAE,IAAIJ,CAAC,GAAGW,EAAJ,GAAS,CAAT,GAAa,KAAKF,MAAL,CAAYC,KAA/B;AACAhB,YAAAA,IAAI,IAAIwB,GAAG,CAACd,EAAD,CAAX;AACAT,YAAAA,IAAI,IAAIwB,KAAK,CAACf,EAAD,CAAb;AACAR,YAAAA,IAAI,IAAIwB,IAAI,CAAChB,EAAD,CAAZ;AACAP,YAAAA,IAAI,IAAIwB,KAAK,CAACjB,EAAD,CAAb;AACD;;AAEDC,UAAAA,EAAE,GAAGP,CAAC,IAAI,CAAV;;AAEA,eAAKC,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG,KAAKU,MAAL,CAAYG,MAA5B,EAAoCb,CAAC,EAArC,EAAyC;AACvCQ,YAAAA,EAAE,GAAIV,IAAI,GAAGiB,MAAR,KAAoBE,MAAzB;AACA,iBAAKP,MAAL,CAAYgB,IAAZ,CAAiBpB,EAAE,GAAG,CAAtB,IAA2BE,EAA3B,CAFuC,CAIvC;;AACA,gBAAIA,EAAE,GAAG,GAAT,EAAc;AACZ,mBAAKE,MAAL,CAAYgB,IAAZ,CAAiBpB,EAAE,GAAG,CAAtB,IAA2B,GAA3B;AACD;;AAED,gBAAIE,EAAE,GAAG,CAAT,EAAY;AACVA,cAAAA,EAAE,GAAG,MAAMA,EAAX;AACA,mBAAKE,MAAL,CAAYgB,IAAZ,CAAiBpB,EAAjB,IAAuB,CAAEX,IAAI,GAAGoB,MAAR,KAAoBE,MAArB,IAA+BT,EAAtD;AACA,mBAAKE,MAAL,CAAYgB,IAAZ,CAAiBpB,EAAE,GAAG,CAAtB,IAA2B,CAAEV,IAAI,GAAGmB,MAAR,KAAoBE,MAArB,IAA+BT,EAA1D;AACA,mBAAKE,MAAL,CAAYgB,IAAZ,CAAiBpB,EAAE,GAAG,CAAtB,IAA2B,CAAET,IAAI,GAAGkB,MAAR,KAAoBE,MAArB,IAA+BT,EAA1D;AACD,aALD,MAKO;AACL,mBAAKE,MAAL,CAAYgB,IAAZ,CAAiBpB,EAAE,GAAG,CAAtB,IAA2B,CAA3B;AACA,mBAAKI,MAAL,CAAYgB,IAAZ,CAAiBpB,EAAE,GAAG,CAAtB,IAA2B,CAA3B;AACA,mBAAKI,MAAL,CAAYgB,IAAZ,CAAiBpB,EAAjB,IAAuB,CAAvB;AACD;;AAED,gBAAIP,CAAC,KAAK,CAAV,EAAa;AACXwB,cAAAA,IAAI,CAACvB,CAAD,CAAJ,GAAU,CAAC,CAACE,CAAC,GAAGF,CAAC,GAAGc,IAAT,IAAiBF,EAAjB,GAAsBV,CAAtB,GAA0BU,EAA3B,IAAiC,KAAKF,MAAL,CAAYC,KAAvD;AACAa,cAAAA,IAAI,CAACxB,CAAD,CAAJ,GAAU,CAACE,CAAC,GAAGF,CAAC,GAAGT,CAAT,IAAc,CAAd,GAAkBW,CAAC,GAAG,KAAKQ,MAAL,CAAYC,KAAlC,GAA0C,CAApD;AACD;;AAEDR,YAAAA,EAAE,GAAGJ,CAAC,GAAGwB,IAAI,CAACvB,CAAD,CAAb;AACAI,YAAAA,EAAE,GAAGL,CAAC,GAAGyB,IAAI,CAACxB,CAAD,CAAb;AAEAL,YAAAA,IAAI,IAAIwB,GAAG,CAAChB,EAAD,CAAH,GAAUgB,GAAG,CAACf,EAAD,CAArB;AACAR,YAAAA,IAAI,IAAIwB,KAAK,CAACjB,EAAD,CAAL,GAAYiB,KAAK,CAAChB,EAAD,CAAzB;AACAP,YAAAA,IAAI,IAAIwB,IAAI,CAAClB,EAAD,CAAJ,GAAWkB,IAAI,CAACjB,EAAD,CAAvB;AACAN,YAAAA,IAAI,IAAIwB,KAAK,CAACnB,EAAD,CAAL,GAAYmB,KAAK,CAAClB,EAAD,CAAzB;AAEAE,YAAAA,EAAE,IAAI,KAAKI,MAAL,CAAYC,KAAZ,IAAqB,CAA3B;AACD;AACF;AACF;;AAED,UAAI,0BAAcnB,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACE,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD;AApJmB,GAAP;AAAA,C", "sourcesContent": ["import { throwError, isNodePattern } from '@jimp/utils';\nimport { mulTable, shgTable } from './blur-tables';\n\n/*\n    Superfast Blur (0.5)\n    http://www.quasimondo.com/BoxBlurForCanvas/FastBlur.js\n\n    Copyright (c) 2011 <PERSON>\n\n    Permission is hereby granted, free of charge, to any person\n    obtaining a copy of this software and associated documentation\n    files (the \"Software\"), to deal in the Software without\n    restriction, including without limitation the rights to use,\n    copy, modify, merge, publish, distribute, sublicense, and/or sell\n    copies of the Software, and to permit persons to whom the\n    Software is furnished to do so, subject to the following\n    conditions:\n\n    The above copyright notice and this permission notice shall be\n    included in all copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n    EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES\n    OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n    NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\n    HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,\n    WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR\n    OTHER DEALINGS IN THE SOFTWARE.\n*/\n\nexport default () => ({\n  /**\n   * A fast blur algorithm that produces similar effect to a Gaussian blur - but MUCH quicker\n   * @param {number} r the pixel radius of the blur\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp} this for chaining of methods\n   */\n  blur(r, cb) {\n    if (typeof r !== 'number')\n      return throwError.call(this, 'r must be a number', cb);\n    if (r < 1) return throwError.call(this, 'r must be greater than 0', cb);\n\n    let rsum;\n    let gsum;\n    let bsum;\n    let asum;\n    let x;\n    let y;\n    let i;\n    let p;\n    let p1;\n    let p2;\n    let yp;\n    let yi;\n    let yw;\n    let pa;\n\n    const wm = this.bitmap.width - 1;\n    const hm = this.bitmap.height - 1;\n    // const wh = this.bitmap.width * this.bitmap.height;\n    const rad1 = r + 1;\n\n    const mulSum = mulTable[r];\n    const shgSum = shgTable[r];\n\n    const red = [];\n    const green = [];\n    const blue = [];\n    const alpha = [];\n\n    const vmin = [];\n    const vmax = [];\n\n    let iterations = 2;\n\n    while (iterations-- > 0) {\n      yi = 0;\n      yw = 0;\n\n      for (y = 0; y < this.bitmap.height; y++) {\n        rsum = this.bitmap.data[yw] * rad1;\n        gsum = this.bitmap.data[yw + 1] * rad1;\n        bsum = this.bitmap.data[yw + 2] * rad1;\n        asum = this.bitmap.data[yw + 3] * rad1;\n\n        for (i = 1; i <= r; i++) {\n          p = yw + ((i > wm ? wm : i) << 2);\n          rsum += this.bitmap.data[p++];\n          gsum += this.bitmap.data[p++];\n          bsum += this.bitmap.data[p++];\n          asum += this.bitmap.data[p];\n        }\n\n        for (x = 0; x < this.bitmap.width; x++) {\n          red[yi] = rsum;\n          green[yi] = gsum;\n          blue[yi] = bsum;\n          alpha[yi] = asum;\n\n          if (y === 0) {\n            vmin[x] = ((p = x + rad1) < wm ? p : wm) << 2;\n            vmax[x] = (p = x - r) > 0 ? p << 2 : 0;\n          }\n\n          p1 = yw + vmin[x];\n          p2 = yw + vmax[x];\n\n          rsum += this.bitmap.data[p1++] - this.bitmap.data[p2++];\n          gsum += this.bitmap.data[p1++] - this.bitmap.data[p2++];\n          bsum += this.bitmap.data[p1++] - this.bitmap.data[p2++];\n          asum += this.bitmap.data[p1] - this.bitmap.data[p2];\n\n          yi++;\n        }\n\n        yw += this.bitmap.width << 2;\n      }\n\n      for (x = 0; x < this.bitmap.width; x++) {\n        yp = x;\n        rsum = red[yp] * rad1;\n        gsum = green[yp] * rad1;\n        bsum = blue[yp] * rad1;\n        asum = alpha[yp] * rad1;\n\n        for (i = 1; i <= r; i++) {\n          yp += i > hm ? 0 : this.bitmap.width;\n          rsum += red[yp];\n          gsum += green[yp];\n          bsum += blue[yp];\n          asum += alpha[yp];\n        }\n\n        yi = x << 2;\n\n        for (y = 0; y < this.bitmap.height; y++) {\n          pa = (asum * mulSum) >>> shgSum;\n          this.bitmap.data[yi + 3] = pa;\n\n          // normalize alpha\n          if (pa > 255) {\n            this.bitmap.data[yi + 3] = 255;\n          }\n\n          if (pa > 0) {\n            pa = 255 / pa;\n            this.bitmap.data[yi] = ((rsum * mulSum) >>> shgSum) * pa;\n            this.bitmap.data[yi + 1] = ((gsum * mulSum) >>> shgSum) * pa;\n            this.bitmap.data[yi + 2] = ((bsum * mulSum) >>> shgSum) * pa;\n          } else {\n            this.bitmap.data[yi + 2] = 0;\n            this.bitmap.data[yi + 1] = 0;\n            this.bitmap.data[yi] = 0;\n          }\n\n          if (x === 0) {\n            vmin[y] = ((p = y + rad1) < hm ? p : hm) * this.bitmap.width;\n            vmax[y] = (p = y - r) > 0 ? p * this.bitmap.width : 0;\n          }\n\n          p1 = x + vmin[y];\n          p2 = x + vmax[y];\n\n          rsum += red[p1] - red[p2];\n          gsum += green[p1] - green[p2];\n          bsum += blue[p1] - blue[p2];\n          asum += alpha[p1] - alpha[p2];\n\n          yi += this.bitmap.width << 2;\n        }\n      }\n    }\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  }\n});\n"], "file": "index.js"}
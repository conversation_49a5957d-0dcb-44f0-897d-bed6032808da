{"version": 3, "sources": ["../src/index.js"], "names": ["alphabet", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "NaN", "i", "maxHash", "anyBase", "BIN", "slice", "Array", "join", "push", "length", "noop", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test", "Object", "prototype", "toString", "call", "toLowerCase", "indexOf", "bufferFromArrayBuffer", "arrayBuffer", "buffer", "<PERSON><PERSON><PERSON>", "alloc", "byteLength", "view", "Uint8Array", "loadFromURL", "options", "cb", "err", "response", "data", "headers", "url", "location", "<PERSON><PERSON><PERSON><PERSON>", "msg", "statusCode", "Error", "loadBufferFromPath", "src", "fs", "readFile", "match", "isRawRGBAData", "obj", "width", "height", "Uint8ClampedArray", "makeRGBABufferFromRGB", "rgbaBuffer", "allocUnsafe", "j", "emptyBitmap", "<PERSON><PERSON>", "args", "MIME_PNG", "path", "write", "mime", "getBase64", "<PERSON><PERSON><PERSON><PERSON>", "getBufferAsync", "getPixelColor", "setPixelColor", "jimpInstance", "finish", "evData", "methodName", "setTimeout", "emitError", "emitMulti", "parseInt", "w", "h", "_background", "cssColorToHex", "throwError", "bitmap", "writeUInt32BE", "parseBitmap", "original", "from", "_quality", "_deflateLevel", "_deflateStrategy", "_filterType", "_rgba", "_originalMime", "imageData", "isRGBA", "extraConstructor", "__extraConstructors", "find", "c", "Promise", "resolve", "reject", "run", "then", "bool", "eventName", "assign", "emit", "getMIME", "MIME", "getExtension", "createWriteStream", "getType", "pathObj", "Path", "parse", "dir", "mkdirp", "sync", "stream", "on", "end", "AUTO", "base", "hash", "pHash", "ImagePHash", "getHash", "compareHash", "currentHash", "distance", "x", "y", "edgeHandling", "xi", "yi", "EDGE_EXTEND", "Math", "round", "EDGE_WRAP", "idx", "getPixelIndex", "hex", "readUInt32BE", "yIndex", "xIndex", "alpha", "EventEmitter", "addConstants", "constants", "entries", "for<PERSON>ach", "name", "value", "addJimpMethods", "methods", "composite", "appendConstructorOption", "read", "image", "create", "rgbaToInt", "r", "g", "b", "a", "pow", "intToRGBA", "rgba", "floor", "cssColor", "Number", "toHex8", "limit255", "n", "max", "min", "diff", "img1", "img2", "threshold", "bmp1", "bmp2", "clone<PERSON>uiet", "resize", "numDiffPixels", "percent", "phash", "hash1", "hash2", "compareHashes", "colorDiff", "rgba1", "rgba2", "maxVal", "jimpEvMethod", "evName", "method", "evNameBefore", "evNameAfter", "replace", "wrappedCb", "apply", "result", "error", "clone", "jimp<PERSON>v<PERSON><PERSON><PERSON>", "f", "process", "env", "ENVIRONMENT", "gl", "window", "self"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AACA;;AACA;;AAEA;;AACA;;AACA;;AACA;;AACA;;AAEA;;AACA;;AAEA;;AACA;;AACA;;AACA;;AACA;;AAEA,IAAMA,QAAQ,GACZ,kEADF,C,CAGA;AACA;;AACA,IAAMC,aAAa,GAAG,CAACC,GAAD,EAAMA,GAAN,CAAtB;;AAEA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,EAApB,EAAwBA,CAAC,EAAzB,EAA6B;AAC3B,MAAMC,OAAO,GAAG,yBAAQC,oBAAQC,GAAhB,EAAqBN,QAAQ,CAACO,KAAT,CAAe,CAAf,EAAkBJ,CAAlB,CAArB,EACd,IAAIK,KAAJ,CAAU,KAAK,CAAf,EAAkBC,IAAlB,CAAuB,GAAvB,CADc,CAAhB;AAGAR,EAAAA,aAAa,CAACS,IAAd,CAAmBN,OAAO,CAACO,MAA3B;AACD,C,CAED;;;AACA,SAASC,IAAT,GAAgB,CAAE,C,CAElB;;;AAEA,SAASC,aAAT,CAAuBC,IAAvB,EAA6B;AAC3B,SACEC,MAAM,CAACC,SAAP,CAAiBC,QAAjB,CACGC,IADH,CACQJ,IADR,EAEGK,WAFH,GAGGC,OAHH,CAGW,aAHX,IAG4B,CAAC,CAJ/B;AAMD,C,CAED;AACA;;;AACA,SAASC,qBAAT,CAA+BC,WAA/B,EAA4C;AAC1C,MAAMC,MAAM,GAAGC,MAAM,CAACC,KAAP,CAAaH,WAAW,CAACI,UAAzB,CAAf;AACA,MAAMC,IAAI,GAAG,IAAIC,UAAJ,CAAeN,WAAf,CAAb;;AAEA,OAAK,IAAInB,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAGoB,MAAM,CAACZ,MAA3B,EAAmC,EAAER,EAArC,EAAwC;AACtCoB,IAAAA,MAAM,CAACpB,EAAD,CAAN,GAAYwB,IAAI,CAACxB,EAAD,CAAhB;AACD;;AAED,SAAOoB,MAAP;AACD;;AAED,SAASM,WAAT,CAAqBC,OAArB,EAA8BC,EAA9B,EAAkC;AAChC,2BAAQD,OAAR,EAAiB,UAACE,GAAD,EAAMC,QAAN,EAAgBC,IAAhB,EAAyB;AACxC,QAAIF,GAAJ,EAAS;AACP,aAAOD,EAAE,CAACC,GAAD,CAAT;AACD;;AAED,QAAI,aAAaC,QAAb,IAAyB,cAAcA,QAAQ,CAACE,OAApD,EAA6D;AAC3DL,MAAAA,OAAO,CAACM,GAAR,GAAcH,QAAQ,CAACE,OAAT,CAAiBE,QAA/B;AACA,aAAOR,WAAW,CAACC,OAAD,EAAUC,EAAV,CAAlB;AACD;;AAED,QAAI,yBAAOG,IAAP,MAAgB,QAAhB,IAA4BV,MAAM,CAACc,QAAP,CAAgBJ,IAAhB,CAAhC,EAAuD;AACrD,aAAOH,EAAE,CAAC,IAAD,EAAOG,IAAP,CAAT;AACD;;AAED,QAAMK,GAAG,GACP,iCACAT,OAAO,CAACM,GADR,GAEA,IAFA,GAGA,SAHA,GAIAH,QAAQ,CAACO,UAJT,GAKA,GANF;AAQA,WAAO,IAAIC,KAAJ,CAAUF,GAAV,CAAP;AACD,GAvBD;AAwBD;;AAED,SAASG,kBAAT,CAA4BC,GAA5B,EAAiCZ,EAAjC,EAAqC;AACnC,MACEa,kBACA,OAAOA,eAAGC,QAAV,KAAuB,UADvB,IAEA,CAACF,GAAG,CAACG,KAAJ,CAAU,qBAAV,CAHH,EAIE;AACAF,mBAAGC,QAAH,CAAYF,GAAZ,EAAiBZ,EAAjB;AACD,GAND,MAMO;AACLF,IAAAA,WAAW,CAAC;AAAEO,MAAAA,GAAG,EAAEO;AAAP,KAAD,EAAeZ,EAAf,CAAX;AACD;AACF;;AAED,SAASgB,aAAT,CAAuBC,GAAvB,EAA4B;AAC1B,SACEA,GAAG,IACH,yBAAOA,GAAP,MAAe,QADf,IAEA,OAAOA,GAAG,CAACC,KAAX,KAAqB,QAFrB,IAGA,OAAOD,GAAG,CAACE,MAAX,KAAsB,QAHtB,KAIC1B,MAAM,CAACc,QAAP,CAAgBU,GAAG,CAACd,IAApB,KACCc,GAAG,CAACd,IAAJ,YAAoBN,UADrB,IAEE,OAAOuB,iBAAP,KAA6B,UAA7B,IACCH,GAAG,CAACd,IAAJ,YAAoBiB,iBAPxB,MAQCH,GAAG,CAACd,IAAJ,CAASvB,MAAT,KAAoBqC,GAAG,CAACC,KAAJ,GAAYD,GAAG,CAACE,MAAhB,GAAyB,CAA7C,IACCF,GAAG,CAACd,IAAJ,CAASvB,MAAT,KAAoBqC,GAAG,CAACC,KAAJ,GAAYD,GAAG,CAACE,MAAhB,GAAyB,CAT/C,CADF;AAYD;;AAED,SAASE,qBAAT,CAA+B7B,MAA/B,EAAuC;AACrC,MAAIA,MAAM,CAACZ,MAAP,GAAgB,CAAhB,KAAsB,CAA1B,EAA6B;AAC3B,UAAM,IAAI8B,KAAJ,CAAU,4BAAV,CAAN;AACD;;AAED,MAAMY,UAAU,GAAG7B,MAAM,CAAC8B,WAAP,CAAoB/B,MAAM,CAACZ,MAAP,GAAgB,CAAjB,GAAsB,CAAzC,CAAnB;AACA,MAAI4C,CAAC,GAAG,CAAR;;AAEA,OAAK,IAAIpD,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAGoB,MAAM,CAACZ,MAA3B,EAAmCR,GAAC,EAApC,EAAwC;AACtCkD,IAAAA,UAAU,CAACE,CAAD,CAAV,GAAgBhC,MAAM,CAACpB,GAAD,CAAtB;;AAEA,QAAI,CAACA,GAAC,GAAG,CAAL,IAAU,CAAV,KAAgB,CAApB,EAAuB;AACrBkD,MAAAA,UAAU,CAAC,EAAEE,CAAH,CAAV,GAAkB,GAAlB;AACD;;AAEDA,IAAAA,CAAC;AACF;;AAED,SAAOF,UAAP;AACD;;AAED,IAAMG,WAAW,GAAG;AAClBtB,EAAAA,IAAI,EAAE,IADY;AAElBe,EAAAA,KAAK,EAAE,IAFW;AAGlBC,EAAAA,MAAM,EAAE;AAHU,CAApB;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;IAQMO,I;;;;;AACJ;AACA;AACA;AACA;AAGA;AAGA;AAGA;AAGA;AAGA,kBAAqB;AAAA;;AAAA,sCAANC,IAAM;AAANA,MAAAA,IAAM;AAAA;;AAAA;AACnB;AADmB,+FAdZF,WAcY;AAAA,oGAXP,UAWO;AAAA,sGARLC,IAAI,CAACE,QAQA;AAAA,8FALb,IAKa;AAAA,8FAFb,IAEa;AAAA,mGAyWR,UAAAC,IAAI;AAAA,aAAI,2BAAU,MAAKC,KAAf,kDAA4BD,IAA5B,CAAJ;AAAA,KAzWI;AAAA,uGA2YJ,UAAAE,IAAI;AAAA,aAAI,2BAAU,MAAKC,SAAf,kDAAgCD,IAAhC,CAAJ;AAAA,KA3YA;AAAA,kGAgdTE,sBAhdS;AAAA,uGAkdJC,2BAldI;AAAA,uGAujBJ,MAAKC,aAvjBD;AAAA,uGAulBJ,MAAKC,aAvlBD;AAGnB,QAAMC,YAAY,iDAAlB;AACA,QAAIrC,EAAE,GAAGnB,IAAT;;AAEA,QAAIC,aAAa,CAAC6C,IAAI,CAAC,CAAD,CAAL,CAAjB,EAA4B;AAC1BA,MAAAA,IAAI,CAAC,CAAD,CAAJ,GAAUrC,qBAAqB,CAACqC,IAAI,CAAC,CAAD,CAAL,CAA/B;AACD;;AAED,aAASW,MAAT,GAAyB;AAAA,yCAANX,IAAM;AAANA,QAAAA,IAAM;AAAA;;AAAA,UAChB1B,GADgB,GACT0B,IADS;AAEvB,UAAMY,MAAM,GAAGtC,GAAG,IAAI,EAAtB;AACAsC,MAAAA,MAAM,CAACC,UAAP,GAAoB,aAApB;AAEAC,MAAAA,UAAU,CAAC,YAAM;AAAA;;AACf;AACA,YAAIxC,GAAG,IAAID,EAAE,KAAKnB,IAAlB,EAAwB;AACtBwD,UAAAA,YAAY,CAACK,SAAb,CAAuB,aAAvB,EAAsCzC,GAAtC;AACD,SAFD,MAEO,IAAI,CAACA,GAAL,EAAU;AACfoC,UAAAA,YAAY,CAACM,SAAb,CAAuB,aAAvB,EAAsC,aAAtC;AACD;;AAED,eAAA3C,EAAE,EAACb,IAAH,aAAQkD,YAAR,SAAyBV,IAAzB;AACD,OATS,EASP,CATO,CAAV;AAUD;;AAED,QACG,OAAOA,IAAI,CAAC,CAAD,CAAX,KAAmB,QAAnB,IAA+B,OAAOA,IAAI,CAAC,CAAD,CAAX,KAAmB,QAAnD,IACCiB,QAAQ,CAACjB,IAAI,CAAC,CAAD,CAAL,EAAU,EAAV,CAAR,IAAyBiB,QAAQ,CAACjB,IAAI,CAAC,CAAD,CAAL,EAAU,EAAV,CAFpC,EAGE;AACA;AACA,UAAMkB,CAAC,GAAGD,QAAQ,CAACjB,IAAI,CAAC,CAAD,CAAL,EAAU,EAAV,CAAlB;AACA,UAAMmB,CAAC,GAAGF,QAAQ,CAACjB,IAAI,CAAC,CAAD,CAAL,EAAU,EAAV,CAAlB;AACA3B,MAAAA,EAAE,GAAG2B,IAAI,CAAC,CAAD,CAAT,CAJA,CAMA;;AACA,UAAI,OAAOA,IAAI,CAAC,CAAD,CAAX,KAAmB,QAAvB,EAAiC;AAC/B,cAAKoB,WAAL,GAAmBpB,IAAI,CAAC,CAAD,CAAvB;AACA3B,QAAAA,EAAE,GAAG2B,IAAI,CAAC,CAAD,CAAT;AACD,OAVD,CAYA;;;AACA,UAAI,OAAOA,IAAI,CAAC,CAAD,CAAX,KAAmB,QAAvB,EAAiC;AAC/B,cAAKoB,WAAL,GAAmBrB,IAAI,CAACsB,aAAL,CAAmBrB,IAAI,CAAC,CAAD,CAAvB,CAAnB;AACA3B,QAAAA,EAAE,GAAG2B,IAAI,CAAC,CAAD,CAAT;AACD;;AAED,UAAI,OAAO3B,EAAP,KAAc,WAAlB,EAA+B;AAC7BA,QAAAA,EAAE,GAAGnB,IAAL;AACD;;AAED,UAAI,OAAOmB,EAAP,KAAc,UAAlB,EAA8B;AAC5B,kEAAOiD,kBAAW9D,IAAX,iDAAsB,uBAAtB,EAA+CmD,MAA/C,CAAP;AACD;;AAED,YAAKY,MAAL,GAAc;AACZ/C,QAAAA,IAAI,EAAEV,MAAM,CAACC,KAAP,CAAamD,CAAC,GAAGC,CAAJ,GAAQ,CAArB,CADM;AAEZ5B,QAAAA,KAAK,EAAE2B,CAFK;AAGZ1B,QAAAA,MAAM,EAAE2B;AAHI,OAAd;;AAMA,WAAK,IAAI1E,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAG,MAAK8E,MAAL,CAAY/C,IAAZ,CAAiBvB,MAArC,EAA6CR,GAAC,IAAI,CAAlD,EAAqD;AACnD,cAAK8E,MAAL,CAAY/C,IAAZ,CAAiBgD,aAAjB,CAA+B,MAAKJ,WAApC,EAAiD3E,GAAjD;AACD;;AAEDkE,MAAAA,MAAM,CAAC,IAAD,iDAAN;AACD,KAxCD,MAwCO,IAAI,yBAAOX,IAAI,CAAC,CAAD,CAAX,MAAmB,QAAnB,IAA+BA,IAAI,CAAC,CAAD,CAAJ,CAAQtB,GAA3C,EAAgD;AACrDL,MAAAA,EAAE,GAAG2B,IAAI,CAAC,CAAD,CAAJ,IAAW9C,IAAhB;;AAEA,UAAI,OAAOmB,EAAP,KAAc,UAAlB,EAA8B;AAC5B,kEAAOiD,kBAAW9D,IAAX,iDAAsB,uBAAtB,EAA+CmD,MAA/C,CAAP;AACD;;AAEDxC,MAAAA,WAAW,CAAC6B,IAAI,CAAC,CAAD,CAAL,EAAU,UAAC1B,GAAD,EAAME,IAAN,EAAe;AAClC,YAAIF,GAAJ,EAAS;AACP,iBAAOgD,kBAAW9D,IAAX,iDAAsBc,GAAtB,EAA2BqC,MAA3B,CAAP;AACD;;AAED,cAAKc,WAAL,CAAiBjD,IAAjB,EAAuBwB,IAAI,CAAC,CAAD,CAAJ,CAAQtB,GAA/B,EAAoCiC,MAApC;AACD,OANU,CAAX;AAOD,KAdM,MAcA,IAAIX,IAAI,CAAC,CAAD,CAAJ,YAAmBD,IAAvB,EAA6B;AAClC;AADkC,UAE3B2B,QAF2B,GAEf1B,IAFe;AAGlC3B,MAAAA,EAAE,GAAG2B,IAAI,CAAC,CAAD,CAAT;;AAEA,UAAI,OAAO3B,EAAP,KAAc,WAAlB,EAA+B;AAC7BA,QAAAA,EAAE,GAAGnB,IAAL;AACD;;AAED,UAAI,OAAOmB,EAAP,KAAc,UAAlB,EAA8B;AAC5B,kEAAOiD,kBAAW9D,IAAX,iDAAsB,uBAAtB,EAA+CmD,MAA/C,CAAP;AACD;;AAED,YAAKY,MAAL,GAAc;AACZ/C,QAAAA,IAAI,EAAEV,MAAM,CAAC6D,IAAP,CAAYD,QAAQ,CAACH,MAAT,CAAgB/C,IAA5B,CADM;AAEZe,QAAAA,KAAK,EAAEmC,QAAQ,CAACH,MAAT,CAAgBhC,KAFX;AAGZC,QAAAA,MAAM,EAAEkC,QAAQ,CAACH,MAAT,CAAgB/B;AAHZ,OAAd;AAMA,YAAKoC,QAAL,GAAgBF,QAAQ,CAACE,QAAzB;AACA,YAAKC,aAAL,GAAqBH,QAAQ,CAACG,aAA9B;AACA,YAAKC,gBAAL,GAAwBJ,QAAQ,CAACI,gBAAjC;AACA,YAAKC,WAAL,GAAmBL,QAAQ,CAACK,WAA5B;AACA,YAAKC,KAAL,GAAaN,QAAQ,CAACM,KAAtB;AACA,YAAKZ,WAAL,GAAmBM,QAAQ,CAACN,WAA5B;AACA,YAAKa,aAAL,GAAqBP,QAAQ,CAACO,aAA9B;AAEAtB,MAAAA,MAAM,CAAC,IAAD,iDAAN;AACD,KA5BM,MA4BA,IAAItB,aAAa,CAACW,IAAI,CAAC,CAAD,CAAL,CAAjB,EAA4B;AAAA,UAC1BkC,SAD0B,GACblC,IADa;AAEjC3B,MAAAA,EAAE,GAAG2B,IAAI,CAAC,CAAD,CAAJ,IAAW9C,IAAhB;AAEA,UAAMiF,MAAM,GACVD,SAAS,CAAC3C,KAAV,GAAkB2C,SAAS,CAAC1C,MAA5B,GAAqC,CAArC,KAA2C0C,SAAS,CAAC1D,IAAV,CAAevB,MAD5D;AAEA,UAAMY,MAAM,GAAGsE,MAAM,GACjBrE,MAAM,CAAC6D,IAAP,CAAYO,SAAS,CAAC1D,IAAtB,CADiB,GAEjBkB,qBAAqB,CAACwC,SAAS,CAAC1D,IAAX,CAFzB;AAIA,YAAK+C,MAAL,GAAc;AACZ/C,QAAAA,IAAI,EAAEX,MADM;AAEZ0B,QAAAA,KAAK,EAAE2C,SAAS,CAAC3C,KAFL;AAGZC,QAAAA,MAAM,EAAE0C,SAAS,CAAC1C;AAHN,OAAd;AAMAmB,MAAAA,MAAM,CAAC,IAAD,iDAAN;AACD,KAjBM,MAiBA,IAAI,OAAOX,IAAI,CAAC,CAAD,CAAX,KAAmB,QAAvB,EAAiC;AACtC;AACA,UAAME,IAAI,GAAGF,IAAI,CAAC,CAAD,CAAjB;AACA3B,MAAAA,EAAE,GAAG2B,IAAI,CAAC,CAAD,CAAT;;AAEA,UAAI,OAAO3B,EAAP,KAAc,WAAlB,EAA+B;AAC7BA,QAAAA,EAAE,GAAGnB,IAAL;AACD;;AAED,UAAI,OAAOmB,EAAP,KAAc,UAAlB,EAA8B;AAC5B,kEAAOiD,kBAAW9D,IAAX,iDAAsB,uBAAtB,EAA+CmD,MAA/C,CAAP;AACD;;AAED3B,MAAAA,kBAAkB,CAACkB,IAAD,EAAO,UAAC5B,GAAD,EAAME,IAAN,EAAe;AACtC,YAAIF,GAAJ,EAAS;AACP,iBAAOgD,kBAAW9D,IAAX,iDAAsBc,GAAtB,EAA2BqC,MAA3B,CAAP;AACD;;AAED,cAAKc,WAAL,CAAiBjD,IAAjB,EAAuB0B,IAAvB,EAA6BS,MAA7B;AACD,OANiB,CAAlB;AAOD,KApBM,MAoBA,IAAI,yBAAOX,IAAI,CAAC,CAAD,CAAX,MAAmB,QAAnB,IAA+BlC,MAAM,CAACc,QAAP,CAAgBoB,IAAI,CAAC,CAAD,CAApB,CAAnC,EAA6D;AAClE;AACA,UAAMxB,IAAI,GAAGwB,IAAI,CAAC,CAAD,CAAjB;AACA3B,MAAAA,EAAE,GAAG2B,IAAI,CAAC,CAAD,CAAT;;AAEA,UAAI,OAAO3B,EAAP,KAAc,UAAlB,EAA8B;AAC5B,kEAAOiD,kBAAW9D,IAAX,iDAAsB,uBAAtB,EAA+CmD,MAA/C,CAAP;AACD;;AAED,YAAKc,WAAL,CAAiBjD,IAAjB,EAAuB,IAAvB,EAA6BmC,MAA7B;AACD,KAVM,MAUA;AACL;AACA;AACAtC,MAAAA,EAAE,GAAG2B,IAAI,CAACA,IAAI,CAAC/C,MAAL,GAAc,CAAf,CAAT;;AAEA,UAAI,OAAOoB,EAAP,KAAc,UAAlB,EAA8B;AAC5B;AACAA,QAAAA,EAAE,GAAG2B,IAAI,CAACA,IAAI,CAAC/C,MAAL,GAAc,CAAf,CAAT;;AAEA,YAAI,OAAOoB,EAAP,KAAc,UAAlB,EAA8B;AAC5BA,UAAAA,EAAE,GAAGnB,IAAL;AACD;AACF;;AAED,UAAMkF,gBAAgB,GAAGrC,IAAI,CAACsC,mBAAL,CAAyBC,IAAzB,CAA8B,UAAAC,CAAC;AAAA,eACtDA,CAAC,CAACnF,IAAF,OAAAmF,CAAC,EAASvC,IAAT,CADqD;AAAA,OAA/B,CAAzB;;AAIA,UAAIoC,gBAAJ,EAAsB;AACpB,YAAII,OAAJ,CAAY,UAACC,OAAD,EAAUC,MAAV;AAAA;;AAAA,iBACV,yBAAAN,gBAAgB,CAACO,GAAjB,EAAqBnF,IAArB,+EAAgCiF,OAAhC,EAAyCC,MAAzC,SAAoD1C,IAApD,EADU;AAAA,SAAZ,EAGG4C,IAHH,CAGQ;AAAA,iBAAMjC,MAAM,CAAC,IAAD,iDAAZ;AAAA,SAHR,WAISA,MAJT;AAKD,OAND,MAMO;AACL,kEAAOW,kBAAW9D,IAAX,iDAEL,oDACE,2DAHG,EAILmD,MAJK,CAAP;AAMD;AACF;;AA5LkB;AA6LpB;AAED;;;;;;;;;;;;gCAQYnC,I,EAAM0B,I,EAAMS,M,EAAQ;AAC9Bc,+BAAYjE,IAAZ,CAAiB,IAAjB,EAAuBgB,IAAvB,EAA6B,IAA7B,EAAmCmC,MAAnC;AACD;AAED;;;;;;;;;yBAMKkC,I,EAAMxE,E,EAAI;AACb,UAAI,OAAOwE,IAAP,KAAgB,SAApB,EAA+B;AAC7B,eAAOvB,kBAAW9D,IAAX,CACL,IADK,EAEL,wDAFK,EAGLa,EAHK,CAAP;AAKD;;AAED,WAAK2D,KAAL,GAAaa,IAAb;;AAEA,UAAI,0BAAcxE,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACb,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD;AAED;;;;;;;;;8BAMUqD,U,EAAYiC,S,EAAsB;AAAA,UAAXtE,IAAW,uEAAJ,EAAI;AAC1CA,MAAAA,IAAI,GAAGnB,MAAM,CAAC0F,MAAP,CAAcvE,IAAd,EAAoB;AAAEqC,QAAAA,UAAU,EAAVA,UAAF;AAAciC,QAAAA,SAAS,EAATA;AAAd,OAApB,CAAP;AACA,WAAKE,IAAL,CAAU,KAAV,EAAiBxE,IAAjB;;AAEA,UAAIqC,UAAJ,EAAgB;AACd,aAAKmC,IAAL,CAAUnC,UAAV,EAAsBrC,IAAtB;AACD;;AAED,WAAKwE,IAAL,CAAUF,SAAV,EAAqBtE,IAArB;AACD;;;8BAESqC,U,EAAYvC,G,EAAK;AACzB,WAAK0C,SAAL,CAAeH,UAAf,EAA2B,OAA3B,EAAoCvC,GAApC;AACD;AAED;;;;;;;gCAIY;AACV,aAAO,KAAKiD,MAAL,CAAY/B,MAAnB;AACD;AAED;;;;;;;+BAIW;AACT,aAAO,KAAK+B,MAAL,CAAYhC,KAAnB;AACD;AAED;;;;;;;8BAIU;AACR,aACE,YACC,KAAKgC,MAAL,KAAgBzB,WAAhB,GACG,YADH,GAEG,KAAKyB,MAAL,CAAYhC,KAAZ,GAAoB,GAApB,GAA0B,KAAKgC,MAAL,CAAY/B,MAH1C,IAIA,GALF;AAOD;AAED;;;;;;;+BAIW;AACT,aAAO,eAAP;AACD;AAED;;;;;;;8BAIU;AACR,UAAMY,IAAI,GAAG,KAAK6B,aAAL,IAAsBlC,IAAI,CAACE,QAAxC;AAEA,aAAOG,IAAP;AACD;AAED;;;;;;;mCAIe;AACb,UAAMA,IAAI,GAAG,KAAK6C,OAAL,EAAb;AAEA,aAAOC,IAAI,CAACC,YAAL,CAAkB/C,IAAlB,CAAP;AACD;AAED;;;;;;;;;0BAMMF,I,EAAM7B,E,EAAI;AAAA;;AACd,UAAI,CAACa,cAAD,IAAO,CAACA,eAAGkE,iBAAf,EAAkC;AAChC,cAAM,IAAIrE,KAAJ,CACJ,+DADI,CAAN;AAGD;;AAED,UAAI,OAAOmB,IAAP,KAAgB,QAApB,EAA8B;AAC5B,eAAOoB,kBAAW9D,IAAX,CAAgB,IAAhB,EAAsB,uBAAtB,EAA+Ca,EAA/C,CAAP;AACD;;AAED,UAAI,OAAOA,EAAP,KAAc,WAAlB,EAA+B;AAC7BA,QAAAA,EAAE,GAAGnB,IAAL;AACD;;AAED,UAAI,OAAOmB,EAAP,KAAc,UAAlB,EAA8B;AAC5B,eAAOiD,kBAAW9D,IAAX,CAAgB,IAAhB,EAAsB,uBAAtB,EAA+Ca,EAA/C,CAAP;AACD;;AAED,UAAM+B,IAAI,GAAG8C,IAAI,CAACG,OAAL,CAAanD,IAAb,KAAsB,KAAK+C,OAAL,EAAnC;;AACA,UAAMK,OAAO,GAAGC,iBAAKC,KAAL,CAAWtD,IAAX,CAAhB;;AAEA,UAAIoD,OAAO,CAACG,GAAZ,EAAiB;AACfC,2BAAOC,IAAP,CAAYL,OAAO,CAACG,GAApB;AACD;;AAED,WAAKnD,SAAL,CAAeF,IAAf,EAAqB,UAAC9B,GAAD,EAAMT,MAAN,EAAiB;AACpC,YAAIS,GAAJ,EAAS;AACP,iBAAOgD,kBAAW9D,IAAX,CAAgB,MAAhB,EAAsBc,GAAtB,EAA2BD,EAA3B,CAAP;AACD;;AAED,YAAMuF,MAAM,GAAG1E,eAAGkE,iBAAH,CAAqBlD,IAArB,CAAf;;AAEA0D,QAAAA,MAAM,CACHC,EADH,CACM,MADN,EACc,YAAM;AAChBD,UAAAA,MAAM,CAACzD,KAAP,CAAatC,MAAb;AACA+F,UAAAA,MAAM,CAACE,GAAP;AACD,SAJH,EAKGD,EALH,CAKM,OALN,EAKe,UAAAvF,GAAG,EAAI;AAClB,iBAAOgD,kBAAW9D,IAAX,CAAgB,MAAhB,EAAsBc,GAAtB,EAA2BD,EAA3B,CAAP;AACD,SAPH;AAQAuF,QAAAA,MAAM,CAACC,EAAP,CAAU,QAAV,EAAoB,YAAM;AACxBxF,UAAAA,EAAE,CAACb,IAAH,CAAQ,MAAR,EAAc,IAAd,EAAoB,MAApB;AACD,SAFD;AAGD,OAlBD;AAoBA,aAAO,IAAP;AACD;;;;AAID;;;;;;8BAMU4C,I,EAAM/B,E,EAAI;AAClB,UAAI+B,IAAI,KAAKL,IAAI,CAACgE,IAAlB,EAAwB;AACtB;AACA3D,QAAAA,IAAI,GAAG,KAAK6C,OAAL,EAAP;AACD;;AAED,UAAI,OAAO7C,IAAP,KAAgB,QAApB,EAA8B;AAC5B,eAAOkB,kBAAW9D,IAAX,CAAgB,IAAhB,EAAsB,uBAAtB,EAA+Ca,EAA/C,CAAP;AACD;;AAED,UAAI,OAAOA,EAAP,KAAc,UAAlB,EAA8B;AAC5B,eAAOiD,kBAAW9D,IAAX,CAAgB,IAAhB,EAAsB,uBAAtB,EAA+Ca,EAA/C,CAAP;AACD;;AAED,WAAKiC,SAAL,CAAeF,IAAf,EAAqB,UAAS9B,GAAT,EAAcE,IAAd,EAAoB;AACvC,YAAIF,GAAJ,EAAS;AACP,iBAAOgD,kBAAW9D,IAAX,CAAgB,IAAhB,EAAsBc,GAAtB,EAA2BD,EAA3B,CAAP;AACD;;AAED,YAAMY,GAAG,GAAG,UAAUmB,IAAV,GAAiB,UAAjB,GAA8B5B,IAAI,CAACjB,QAAL,CAAc,QAAd,CAA1C;AACAc,QAAAA,EAAE,CAACb,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoByB,GAApB;AACD,OAPD;AASA,aAAO,IAAP;AACD;;;;AAID;;;;;;yBAMK+E,I,EAAM3F,E,EAAI;AACb2F,MAAAA,IAAI,GAAGA,IAAI,IAAI,EAAf;;AAEA,UAAI,OAAOA,IAAP,KAAgB,UAApB,EAAgC;AAC9B3F,QAAAA,EAAE,GAAG2F,IAAL;AACAA,QAAAA,IAAI,GAAG,EAAP;AACD;;AAED,UAAI,OAAOA,IAAP,KAAgB,QAApB,EAA8B;AAC5B,eAAO1C,kBAAW9D,IAAX,CAAgB,IAAhB,EAAsB,uBAAtB,EAA+Ca,EAA/C,CAAP;AACD;;AAED,UAAI2F,IAAI,GAAG,CAAP,IAAYA,IAAI,GAAG,EAAvB,EAA2B;AACzB,eAAO1C,kBAAW9D,IAAX,CACL,IADK,EAEL,wCAFK,EAGLa,EAHK,CAAP;AAKD;;AAED,UAAI4F,IAAI,GAAG,KAAKC,KAAL,EAAX;AACAD,MAAAA,IAAI,GAAG,yBAAQtH,oBAAQC,GAAhB,EAAqBN,QAAQ,CAACO,KAAT,CAAe,CAAf,EAAkBmH,IAAlB,CAArB,EAA8CC,IAA9C,CAAP;;AAEA,aAAOA,IAAI,CAAChH,MAAL,GAAcV,aAAa,CAACyH,IAAD,CAAlC,EAA0C;AACxCC,QAAAA,IAAI,GAAG,MAAMA,IAAb,CADwC,CACrB;AACpB;;AAED,UAAI,0BAAc5F,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACb,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoByG,IAApB;AACD;;AAED,aAAOA,IAAP;AACD;AAED;;;;;;;4BAIQ;AACN,UAAMC,KAAK,GAAG,IAAIC,iBAAJ,EAAd;AACA,aAAOD,KAAK,CAACE,OAAN,CAAc,IAAd,CAAP;AACD;AAED;;;;;;;;qCAKiBC,W,EAAa;AAC5B,UAAMH,KAAK,GAAG,IAAIC,iBAAJ,EAAd;AACA,UAAMG,WAAW,GAAGJ,KAAK,CAACE,OAAN,CAAc,IAAd,CAApB;AAEA,aAAOF,KAAK,CAACK,QAAN,CAAeD,WAAf,EAA4BD,WAA5B,CAAP;AACD;AAED;;;;;;;;;;AAUA;;;;;;;;kCAQcG,C,EAAGC,C,EAAGC,Y,EAAcrG,E,EAAI;AACpC,UAAIsG,EAAJ;AACA,UAAIC,EAAJ;;AAEA,UAAI,OAAOF,YAAP,KAAwB,UAAxB,IAAsC,OAAOrG,EAAP,KAAc,WAAxD,EAAqE;AACnEA,QAAAA,EAAE,GAAGqG,YAAL;AACAA,QAAAA,YAAY,GAAG,IAAf;AACD;;AAED,UAAI,CAACA,YAAL,EAAmB;AACjBA,QAAAA,YAAY,GAAG3E,IAAI,CAAC8E,WAApB;AACD;;AAED,UAAI,OAAOL,CAAP,KAAa,QAAb,IAAyB,OAAOC,CAAP,KAAa,QAA1C,EAAoD;AAClD,eAAOnD,kBAAW9D,IAAX,CAAgB,IAAhB,EAAsB,yBAAtB,EAAiDa,EAAjD,CAAP;AACD,OAfmC,CAiBpC;;;AACAmG,MAAAA,CAAC,GAAGM,IAAI,CAACC,KAAL,CAAWP,CAAX,CAAJ;AACAC,MAAAA,CAAC,GAAGK,IAAI,CAACC,KAAL,CAAWN,CAAX,CAAJ;AACAE,MAAAA,EAAE,GAAGH,CAAL;AACAI,MAAAA,EAAE,GAAGH,CAAL;;AAEA,UAAIC,YAAY,KAAK3E,IAAI,CAAC8E,WAA1B,EAAuC;AACrC,YAAIL,CAAC,GAAG,CAAR,EAAWG,EAAE,GAAG,CAAL;AACX,YAAIH,CAAC,IAAI,KAAKjD,MAAL,CAAYhC,KAArB,EAA4BoF,EAAE,GAAG,KAAKpD,MAAL,CAAYhC,KAAZ,GAAoB,CAAzB;AAC5B,YAAIkF,CAAC,GAAG,CAAR,EAAWG,EAAE,GAAG,CAAL;AACX,YAAIH,CAAC,IAAI,KAAKlD,MAAL,CAAY/B,MAArB,EAA6BoF,EAAE,GAAG,KAAKrD,MAAL,CAAY/B,MAAZ,GAAqB,CAA1B;AAC9B;;AAED,UAAIkF,YAAY,KAAK3E,IAAI,CAACiF,SAA1B,EAAqC;AACnC,YAAIR,CAAC,GAAG,CAAR,EAAW;AACTG,UAAAA,EAAE,GAAG,KAAKpD,MAAL,CAAYhC,KAAZ,GAAoBiF,CAAzB;AACD;;AAED,YAAIA,CAAC,IAAI,KAAKjD,MAAL,CAAYhC,KAArB,EAA4B;AAC1BoF,UAAAA,EAAE,GAAGH,CAAC,GAAG,KAAKjD,MAAL,CAAYhC,KAArB;AACD;;AAED,YAAIkF,CAAC,GAAG,CAAR,EAAW;AACTE,UAAAA,EAAE,GAAG,KAAKpD,MAAL,CAAY/B,MAAZ,GAAqBiF,CAA1B;AACD;;AAED,YAAIA,CAAC,IAAI,KAAKlD,MAAL,CAAY/B,MAArB,EAA6B;AAC3BoF,UAAAA,EAAE,GAAGH,CAAC,GAAG,KAAKlD,MAAL,CAAY/B,MAArB;AACD;AACF;;AAED,UAAI/C,CAAC,GAAI,KAAK8E,MAAL,CAAYhC,KAAZ,GAAoBqF,EAApB,GAAyBD,EAA1B,IAAiC,CAAzC,CAhDoC,CAkDpC;;AACA,UAAIA,EAAE,GAAG,CAAL,IAAUA,EAAE,IAAI,KAAKpD,MAAL,CAAYhC,KAAhC,EAAuC;AACrC9C,QAAAA,CAAC,GAAG,CAAC,CAAL;AACD;;AAED,UAAImI,EAAE,GAAG,CAAL,IAAUA,EAAE,IAAI,KAAKrD,MAAL,CAAY/B,MAAhC,EAAwC;AACtC/C,QAAAA,CAAC,GAAG,CAAC,CAAL;AACD;;AAED,UAAI,0BAAc4B,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACb,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoBf,CAApB;AACD;;AAED,aAAOA,CAAP;AACD;AAED;;;;;;;;;;kCAOc+H,C,EAAGC,C,EAAGpG,E,EAAI;AACtB,UAAI,OAAOmG,CAAP,KAAa,QAAb,IAAyB,OAAOC,CAAP,KAAa,QAA1C,EACE,OAAOnD,kBAAW9D,IAAX,CAAgB,IAAhB,EAAsB,yBAAtB,EAAiDa,EAAjD,CAAP,CAFoB,CAItB;;AACAmG,MAAAA,CAAC,GAAGM,IAAI,CAACC,KAAL,CAAWP,CAAX,CAAJ;AACAC,MAAAA,CAAC,GAAGK,IAAI,CAACC,KAAL,CAAWN,CAAX,CAAJ;AAEA,UAAMQ,GAAG,GAAG,KAAKC,aAAL,CAAmBV,CAAnB,EAAsBC,CAAtB,CAAZ;AACA,UAAMU,GAAG,GAAG,KAAK5D,MAAL,CAAY/C,IAAZ,CAAiB4G,YAAjB,CAA8BH,GAA9B,CAAZ;;AAEA,UAAI,0BAAc5G,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACb,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB2H,GAApB;AACD;;AAED,aAAOA,GAAP;AACD;;;;AAID;;;;;;;;kCAQcA,G,EAAKX,C,EAAGC,C,EAAGpG,E,EAAI;AAC3B,UACE,OAAO8G,GAAP,KAAe,QAAf,IACA,OAAOX,CAAP,KAAa,QADb,IAEA,OAAOC,CAAP,KAAa,QAHf,EAKE,OAAOnD,kBAAW9D,IAAX,CAAgB,IAAhB,EAAsB,8BAAtB,EAAsDa,EAAtD,CAAP,CANyB,CAQ3B;;AACAmG,MAAAA,CAAC,GAAGM,IAAI,CAACC,KAAL,CAAWP,CAAX,CAAJ;AACAC,MAAAA,CAAC,GAAGK,IAAI,CAACC,KAAL,CAAWN,CAAX,CAAJ;AAEA,UAAMQ,GAAG,GAAG,KAAKC,aAAL,CAAmBV,CAAnB,EAAsBC,CAAtB,CAAZ;AACA,WAAKlD,MAAL,CAAY/C,IAAZ,CAAiBgD,aAAjB,CAA+B2D,GAA/B,EAAoCF,GAApC;;AAEA,UAAI,0BAAc5G,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACb,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD;;;;AAID;;;;+BAIW;AACT,WAAK,IAAI6H,MAAM,GAAG,CAAlB,EAAqBA,MAAM,GAAG,KAAK9D,MAAL,CAAY/B,MAA1C,EAAkD6F,MAAM,EAAxD,EAA4D;AAC1D,aAAK,IAAIC,MAAM,GAAG,CAAlB,EAAqBA,MAAM,GAAG,KAAK/D,MAAL,CAAYhC,KAA1C,EAAiD+F,MAAM,EAAvD,EAA2D;AACzD,cAAML,GAAG,GAAI,KAAK1D,MAAL,CAAYhC,KAAZ,GAAoB8F,MAApB,GAA6BC,MAA9B,IAAyC,CAArD;AACA,cAAMC,KAAK,GAAG,KAAKhE,MAAL,CAAY/C,IAAZ,CAAiByG,GAAG,GAAG,CAAvB,CAAd;;AAEA,cAAIM,KAAK,KAAK,IAAd,EAAoB;AAClB,mBAAO,IAAP;AACD;AACF;AACF;;AAED,aAAO,KAAP;AACD;AAED;;;;;;;;;;;iCAQaf,C,EAAGC,C,EAAGvD,C,EAAGC,C,EAAG;AACvB,UAAI,OAAOqD,CAAP,KAAa,QAAb,IAAyB,OAAOC,CAAP,KAAa,QAA1C,EAAoD;AAClD,eAAOnD,kBAAW9D,IAAX,CAAgB,IAAhB,EAAsB,yBAAtB,CAAP;AACD;;AAED,UAAI,OAAO0D,CAAP,KAAa,QAAb,IAAyB,OAAOC,CAAP,KAAa,QAA1C,EAAoD;AAClD,eAAOG,kBAAW9D,IAAX,CAAgB,IAAhB,EAAsB,yBAAtB,CAAP;AACD;;AAED,aAAO,yBAAa,IAAb,EAAmBgH,CAAnB,EAAsBC,CAAtB,EAAyBvD,CAAzB,EAA4BC,CAA5B,CAAP;AACD;;;EAjpBgBqE,kB;;AAopBZ,SAASC,YAAT,CAAsBC,SAAtB,EAAsD;AAAA,MAArBhF,YAAqB,uEAANX,IAAM;AAC3D1C,EAAAA,MAAM,CAACsI,OAAP,CAAeD,SAAf,EAA0BE,OAA1B,CAAkC,gBAAmB;AAAA;AAAA,QAAjBC,IAAiB;AAAA,QAAXC,KAAW;;AACnDpF,IAAAA,YAAY,CAACmF,IAAD,CAAZ,GAAqBC,KAArB;AACD,GAFD;AAGD;;AAEM,SAASC,cAAT,CAAwBC,OAAxB,EAAsD;AAAA,MAArBtF,YAAqB,uEAANX,IAAM;AAC3D1C,EAAAA,MAAM,CAACsI,OAAP,CAAeK,OAAf,EAAwBJ,OAAxB,CAAgC,iBAAmB;AAAA;AAAA,QAAjBC,IAAiB;AAAA,QAAXC,KAAW;;AACjDpF,IAAAA,YAAY,CAACpD,SAAb,CAAuBuI,IAAvB,IAA+BC,KAA/B;AACD,GAFD;AAGD;;AAEDL,YAAY,CAACC,SAAD,CAAZ;AACAK,cAAc,CAAC;AAAEE,EAAAA,SAAS,EAATA;AAAF,CAAD,CAAd;AAEAlG,IAAI,CAACsC,mBAAL,GAA2B,EAA3B;AAEA;;;;;;;AAMAtC,IAAI,CAACmG,uBAAL,GAA+B,UAASL,IAAT,EAAezI,IAAf,EAAqBuF,GAArB,EAA0B;AACvD5C,EAAAA,IAAI,CAACsC,mBAAL,CAAyBrF,IAAzB,CAA8B;AAAE6I,IAAAA,IAAI,EAAJA,IAAF;AAAQzI,IAAAA,IAAI,EAAJA,IAAR;AAAcuF,IAAAA,GAAG,EAAHA;AAAd,GAA9B;AACD,CAFD;AAIA;;;;;;AAIA5C,IAAI,CAACoG,IAAL,GAAY,YAAkB;AAAA,qCAANnG,IAAM;AAANA,IAAAA,IAAM;AAAA;;AAC5B,SAAO,IAAIwC,OAAJ,CAAY,UAACC,OAAD,EAAUC,MAAV,EAAqB;AACtC,gCAAI3C,IAAJ,EAAYC,IAAZ,SAAkB,UAAC1B,GAAD,EAAM8H,KAAN,EAAgB;AAChC,UAAI9H,GAAJ,EAASoE,MAAM,CAACpE,GAAD,CAAN,CAAT,KACKmE,OAAO,CAAC2D,KAAD,CAAP;AACN,KAHD;AAID,GALM,CAAP;AAMD,CAPD;;AASArG,IAAI,CAACsG,MAAL,GAActG,IAAI,CAACoG,IAAnB;AAEA;;;;;;;;;;AASApG,IAAI,CAACuG,SAAL,GAAiB,UAASC,CAAT,EAAYC,CAAZ,EAAeC,CAAf,EAAkBC,CAAlB,EAAqBrI,EAArB,EAAyB;AACxC,MACE,OAAOkI,CAAP,KAAa,QAAb,IACA,OAAOC,CAAP,KAAa,QADb,IAEA,OAAOC,CAAP,KAAa,QAFb,IAGA,OAAOC,CAAP,KAAa,QAJf,EAKE;AACA,WAAOpF,kBAAW9D,IAAX,CAAgB,IAAhB,EAAsB,+BAAtB,EAAuDa,EAAvD,CAAP;AACD;;AAED,MAAIkI,CAAC,GAAG,CAAJ,IAASA,CAAC,GAAG,GAAjB,EAAsB;AACpB,WAAOjF,kBAAW9D,IAAX,CAAgB,IAAhB,EAAsB,6BAAtB,EAAqDa,EAArD,CAAP;AACD;;AAED,MAAImI,CAAC,GAAG,CAAJ,IAASA,CAAC,GAAG,GAAjB,EAAsB;AACpBlF,sBAAW9D,IAAX,CAAgB,IAAhB,EAAsB,6BAAtB,EAAqDa,EAArD;AACD;;AAED,MAAIoI,CAAC,GAAG,CAAJ,IAASA,CAAC,GAAG,GAAjB,EAAsB;AACpB,WAAOnF,kBAAW9D,IAAX,CAAgB,IAAhB,EAAsB,6BAAtB,EAAqDa,EAArD,CAAP;AACD;;AAED,MAAIqI,CAAC,GAAG,CAAJ,IAASA,CAAC,GAAG,GAAjB,EAAsB;AACpB,WAAOpF,kBAAW9D,IAAX,CAAgB,IAAhB,EAAsB,6BAAtB,EAAqDa,EAArD,CAAP;AACD;;AAEDkI,EAAAA,CAAC,GAAGzB,IAAI,CAACC,KAAL,CAAWwB,CAAX,CAAJ;AACAE,EAAAA,CAAC,GAAG3B,IAAI,CAACC,KAAL,CAAW0B,CAAX,CAAJ;AACAD,EAAAA,CAAC,GAAG1B,IAAI,CAACC,KAAL,CAAWyB,CAAX,CAAJ;AACAE,EAAAA,CAAC,GAAG5B,IAAI,CAACC,KAAL,CAAW2B,CAAX,CAAJ;AAEA,MAAMjK,CAAC,GACL8J,CAAC,GAAGzB,IAAI,CAAC6B,GAAL,CAAS,GAAT,EAAc,CAAd,CAAJ,GACAH,CAAC,GAAG1B,IAAI,CAAC6B,GAAL,CAAS,GAAT,EAAc,CAAd,CADJ,GAEAF,CAAC,GAAG3B,IAAI,CAAC6B,GAAL,CAAS,GAAT,EAAc,CAAd,CAFJ,GAGAD,CAAC,GAAG5B,IAAI,CAAC6B,GAAL,CAAS,GAAT,EAAc,CAAd,CAJN;;AAMA,MAAI,0BAActI,EAAd,CAAJ,EAAuB;AACrBA,IAAAA,EAAE,CAACb,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoBf,CAApB;AACD;;AAED,SAAOA,CAAP;AACD,CA1CD;AA4CA;;;;;;;;AAMAsD,IAAI,CAAC6G,SAAL,GAAiB,UAASnK,CAAT,EAAY4B,EAAZ,EAAgB;AAC/B,MAAI,OAAO5B,CAAP,KAAa,QAAjB,EAA2B;AACzB,WAAO6E,kBAAW9D,IAAX,CAAgB,IAAhB,EAAsB,oBAAtB,EAA4Ca,EAA5C,CAAP;AACD;;AAED,MAAMwI,IAAI,GAAG,EAAb;AAEAA,EAAAA,IAAI,CAACN,CAAL,GAASzB,IAAI,CAACgC,KAAL,CAAWrK,CAAC,GAAGqI,IAAI,CAAC6B,GAAL,CAAS,GAAT,EAAc,CAAd,CAAf,CAAT;AACAE,EAAAA,IAAI,CAACL,CAAL,GAAS1B,IAAI,CAACgC,KAAL,CAAW,CAACrK,CAAC,GAAGoK,IAAI,CAACN,CAAL,GAASzB,IAAI,CAAC6B,GAAL,CAAS,GAAT,EAAc,CAAd,CAAd,IAAkC7B,IAAI,CAAC6B,GAAL,CAAS,GAAT,EAAc,CAAd,CAA7C,CAAT;AACAE,EAAAA,IAAI,CAACJ,CAAL,GAAS3B,IAAI,CAACgC,KAAL,CACP,CAACrK,CAAC,GAAGoK,IAAI,CAACN,CAAL,GAASzB,IAAI,CAAC6B,GAAL,CAAS,GAAT,EAAc,CAAd,CAAb,GAAgCE,IAAI,CAACL,CAAL,GAAS1B,IAAI,CAAC6B,GAAL,CAAS,GAAT,EAAc,CAAd,CAA1C,IACE7B,IAAI,CAAC6B,GAAL,CAAS,GAAT,EAAc,CAAd,CAFK,CAAT;AAIAE,EAAAA,IAAI,CAACH,CAAL,GAAS5B,IAAI,CAACgC,KAAL,CACP,CAACrK,CAAC,GACAoK,IAAI,CAACN,CAAL,GAASzB,IAAI,CAAC6B,GAAL,CAAS,GAAT,EAAc,CAAd,CADV,GAECE,IAAI,CAACL,CAAL,GAAS1B,IAAI,CAAC6B,GAAL,CAAS,GAAT,EAAc,CAAd,CAFV,GAGCE,IAAI,CAACJ,CAAL,GAAS3B,IAAI,CAAC6B,GAAL,CAAS,GAAT,EAAc,CAAd,CAHX,IAIE7B,IAAI,CAAC6B,GAAL,CAAS,GAAT,EAAc,CAAd,CALK,CAAT;;AAQA,MAAI,0BAActI,EAAd,CAAJ,EAAuB;AACrBA,IAAAA,EAAE,CAACb,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoBqJ,IAApB;AACD;;AAED,SAAOA,IAAP;AACD,CA1BD;AA4BA;;;;;;;AAKA9G,IAAI,CAACsB,aAAL,GAAqB,UAAS0F,QAAT,EAAmB;AACtCA,EAAAA,QAAQ,GAAGA,QAAQ,IAAI,CAAvB,CADsC,CACZ;;AAE1B,MAAI,OAAOA,QAAP,KAAoB,QAAxB,EAAkC,OAAOC,MAAM,CAACD,QAAD,CAAb;AAElC,SAAO9F,QAAQ,CAAC,2BAAU8F,QAAV,EAAoBE,MAApB,EAAD,EAA+B,EAA/B,CAAf;AACD,CAND;AAQA;;;;;;;AAKAlH,IAAI,CAACmH,QAAL,GAAgB,UAASC,CAAT,EAAY;AAC1BA,EAAAA,CAAC,GAAGrC,IAAI,CAACsC,GAAL,CAASD,CAAT,EAAY,CAAZ,CAAJ;AACAA,EAAAA,CAAC,GAAGrC,IAAI,CAACuC,GAAL,CAASF,CAAT,EAAY,GAAZ,CAAJ;AAEA,SAAOA,CAAP;AACD,CALD;AAOA;;;;;;;;;AAOApH,IAAI,CAACuH,IAAL,GAAY,UAASC,IAAT,EAAeC,IAAf,EAAsC;AAAA,MAAjBC,SAAiB,uEAAL,GAAK;AAChD,MAAI,EAAEF,IAAI,YAAYxH,IAAlB,KAA2B,EAAEyH,IAAI,YAAYzH,IAAlB,CAA/B,EACE,OAAOuB,kBAAW9D,IAAX,CAAgB,IAAhB,EAAsB,sCAAtB,CAAP;AAEF,MAAMkK,IAAI,GAAGH,IAAI,CAAChG,MAAlB;AACA,MAAMoG,IAAI,GAAGH,IAAI,CAACjG,MAAlB;;AAEA,MAAImG,IAAI,CAACnI,KAAL,KAAeoI,IAAI,CAACpI,KAApB,IAA6BmI,IAAI,CAAClI,MAAL,KAAgBmI,IAAI,CAACnI,MAAtD,EAA8D;AAC5D,QAAIkI,IAAI,CAACnI,KAAL,GAAamI,IAAI,CAAClI,MAAlB,GAA2BmI,IAAI,CAACpI,KAAL,GAAaoI,IAAI,CAACnI,MAAjD,EAAyD;AACvD;AACA+H,MAAAA,IAAI,GAAGA,IAAI,CAACK,UAAL,GAAkBC,MAAlB,CAAyBF,IAAI,CAACpI,KAA9B,EAAqCoI,IAAI,CAACnI,MAA1C,CAAP;AACD,KAHD,MAGO;AACL;AACAgI,MAAAA,IAAI,GAAGA,IAAI,CAACI,UAAL,GAAkBC,MAAlB,CAAyBH,IAAI,CAACnI,KAA9B,EAAqCmI,IAAI,CAAClI,MAA1C,CAAP;AACD;AACF;;AAED,MAAI,OAAOiI,SAAP,KAAqB,QAArB,IAAiCA,SAAS,GAAG,CAA7C,IAAkDA,SAAS,GAAG,CAAlE,EAAqE;AACnE,WAAOnG,kBAAW9D,IAAX,CAAgB,IAAhB,EAAsB,4CAAtB,CAAP;AACD;;AAED,MAAM8J,IAAI,GAAG,IAAIvH,IAAJ,CAAS2H,IAAI,CAACnI,KAAd,EAAqBmI,IAAI,CAAClI,MAA1B,EAAkC,UAAlC,CAAb;AAEA,MAAMsI,aAAa,GAAG,4BACpBJ,IAAI,CAAClJ,IADe,EAEpBmJ,IAAI,CAACnJ,IAFe,EAGpB8I,IAAI,CAAC/F,MAAL,CAAY/C,IAHQ,EAIpB8I,IAAI,CAAC/F,MAAL,CAAYhC,KAJQ,EAKpB+H,IAAI,CAAC/F,MAAL,CAAY/B,MALQ,EAMpB;AAAEiI,IAAAA,SAAS,EAATA;AAAF,GANoB,CAAtB;AASA,SAAO;AACLM,IAAAA,OAAO,EAAED,aAAa,IAAIR,IAAI,CAAC/F,MAAL,CAAYhC,KAAZ,GAAoB+H,IAAI,CAAC/F,MAAL,CAAY/B,MAApC,CADjB;AAEL4G,IAAAA,KAAK,EAAEkB;AAFF,GAAP;AAID,CApCD;AAsCA;;;;;;;;AAMAvH,IAAI,CAACwE,QAAL,GAAgB,UAASgD,IAAT,EAAeC,IAAf,EAAqB;AACnC,MAAMQ,KAAK,GAAG,IAAI7D,iBAAJ,EAAd;AACA,MAAM8D,KAAK,GAAGD,KAAK,CAAC5D,OAAN,CAAcmD,IAAd,CAAd;AACA,MAAMW,KAAK,GAAGF,KAAK,CAAC5D,OAAN,CAAcoD,IAAd,CAAd;AAEA,SAAOQ,KAAK,CAACzD,QAAN,CAAe0D,KAAf,EAAsBC,KAAtB,CAAP;AACD,CAND;AAQA;;;;;;;;AAMAnI,IAAI,CAACoI,aAAL,GAAqB,UAASF,KAAT,EAAgBC,KAAhB,EAAuB;AAC1C,MAAMF,KAAK,GAAG,IAAI7D,iBAAJ,EAAd;AAEA,SAAO6D,KAAK,CAACzD,QAAN,CAAe0D,KAAf,EAAsBC,KAAtB,CAAP;AACD,CAJD;AAMA;;;;;;;;;;;AASAnI,IAAI,CAACqI,SAAL,GAAiB,UAASC,KAAT,EAAgBC,KAAhB,EAAuB;AACtC,MAAM3B,GAAG,GAAG,SAANA,GAAM,CAAAQ,CAAC;AAAA,WAAIrC,IAAI,CAAC6B,GAAL,CAASQ,CAAT,EAAY,CAAZ,CAAJ;AAAA,GAAb;;AADsC,MAE9BC,GAF8B,GAEtBtC,IAFsB,CAE9BsC,GAF8B;AAGtC,MAAMmB,MAAM,GAAG,MAAM,GAAN,GAAY,CAA3B;;AAEA,MAAIF,KAAK,CAAC3B,CAAN,KAAY,CAAZ,IAAiB,CAAC2B,KAAK,CAAC3B,CAA5B,EAA+B;AAC7B2B,IAAAA,KAAK,CAAC3B,CAAN,GAAU,GAAV;AACD;;AAED,MAAI4B,KAAK,CAAC5B,CAAN,KAAY,CAAZ,IAAiB,CAAC4B,KAAK,CAAC5B,CAA5B,EAA+B;AAC7B4B,IAAAA,KAAK,CAAC5B,CAAN,GAAU,GAAV;AACD;;AAED,SACE,CAACU,GAAG,CAACT,GAAG,CAAC0B,KAAK,CAAC9B,CAAN,GAAU+B,KAAK,CAAC/B,CAAjB,CAAJ,EAAyBI,GAAG,CAAC0B,KAAK,CAAC9B,CAAN,GAAU+B,KAAK,CAAC/B,CAAhB,GAAoB8B,KAAK,CAAC3B,CAA1B,GAA8B4B,KAAK,CAAC5B,CAArC,CAA5B,CAAH,GACCU,GAAG,CAACT,GAAG,CAAC0B,KAAK,CAAC7B,CAAN,GAAU8B,KAAK,CAAC9B,CAAjB,CAAJ,EAAyBG,GAAG,CAAC0B,KAAK,CAAC7B,CAAN,GAAU8B,KAAK,CAAC9B,CAAhB,GAAoB6B,KAAK,CAAC3B,CAA1B,GAA8B4B,KAAK,CAAC5B,CAArC,CAA5B,CADJ,GAECU,GAAG,CAACT,GAAG,CAAC0B,KAAK,CAAC5B,CAAN,GAAU6B,KAAK,CAAC7B,CAAjB,CAAJ,EAAyBE,GAAG,CAAC0B,KAAK,CAAC5B,CAAN,GAAU6B,KAAK,CAAC7B,CAAhB,GAAoB4B,KAAK,CAAC3B,CAA1B,GAA8B4B,KAAK,CAAC5B,CAArC,CAA5B,CAFL,IAGA6B,MAJF;AAMD,CAnBD;AAqBA;;;;;;;;;;;;;;;;AAcO,SAASC,YAAT,CAAsB3H,UAAtB,EAAkC4H,MAAlC,EAA0CC,MAA1C,EAAkD;AACvD,MAAMC,YAAY,GAAG,YAAYF,MAAjC;AACA,MAAMG,WAAW,GAAGH,MAAM,CAACI,OAAP,CAAe,IAAf,EAAqB,EAArB,IAA2B,IAA/C;;AAEA9I,EAAAA,IAAI,CAACzC,SAAL,CAAeuD,UAAf,IAA6B,YAAkB;AAC7C,QAAIiI,SAAJ;;AAD6C,uCAAN9I,IAAM;AAANA,MAAAA,IAAM;AAAA;;AAE7C,QAAM3B,EAAE,GAAG2B,IAAI,CAAC0I,MAAM,CAACzL,MAAP,GAAgB,CAAjB,CAAf;AACA,QAAMyD,YAAY,GAAG,IAArB;;AAEA,QAAI,OAAOrC,EAAP,KAAc,UAAlB,EAA8B;AAC5ByK,MAAAA,SAAS,GAAG,qBAAkB;AAAA,2CAAN9I,IAAM;AAANA,UAAAA,IAAM;AAAA;;AAAA,YACrB1B,GADqB,GACR0B,IADQ;AAAA,YAChBxB,IADgB,GACRwB,IADQ;;AAG5B,YAAI1B,GAAJ,EAAS;AACPoC,UAAAA,YAAY,CAACK,SAAb,CAAuBF,UAAvB,EAAmCvC,GAAnC;AACD,SAFD,MAEO;AACLoC,UAAAA,YAAY,CAACM,SAAb,CAAuBH,UAAvB,EAAmC+H,WAAnC,uCACG/H,UADH,EACgBrC,IADhB;AAGD;;AAEDH,QAAAA,EAAE,CAAC0K,KAAH,CAAS,IAAT,EAAe/I,IAAf;AACD,OAZD;;AAcAA,MAAAA,IAAI,CAACA,IAAI,CAAC/C,MAAL,GAAc,CAAf,CAAJ,GAAwB6L,SAAxB;AACD,KAhBD,MAgBO;AACLA,MAAAA,SAAS,GAAG,KAAZ;AACD;;AAED,SAAK9H,SAAL,CAAeH,UAAf,EAA2B8H,YAA3B;AAEA,QAAIK,MAAJ;;AAEA,QAAI;AACFA,MAAAA,MAAM,GAAGN,MAAM,CAACK,KAAP,CAAa,IAAb,EAAmB/I,IAAnB,CAAT;;AAEA,UAAI,CAAC8I,SAAL,EAAgB;AACd,aAAK9H,SAAL,CAAeH,UAAf,EAA2B+H,WAA3B,uCACG/H,UADH,EACgBmI,MADhB;AAGD;AACF,KARD,CAQE,OAAOC,KAAP,EAAc;AACdA,MAAAA,KAAK,CAACpI,UAAN,GAAmBA,UAAnB;AACA,WAAKE,SAAL,CAAeF,UAAf,EAA2BoI,KAA3B;AACD;;AAED,WAAOD,MAAP;AACD,GA3CD;;AA6CAjJ,EAAAA,IAAI,CAACzC,SAAL,CAAeuD,UAAU,GAAG,OAA5B,IAAuC6H,MAAvC;AACD;AAED;;;;;;;AAKAF,YAAY,CAAC,OAAD,EAAU,OAAV,EAAmB,UAASnK,EAAT,EAAa;AAC1C,MAAM6K,KAAK,GAAG,IAAInJ,IAAJ,CAAS,IAAT,CAAd;;AAEA,MAAI,0BAAc1B,EAAd,CAAJ,EAAuB;AACrBA,IAAAA,EAAE,CAACb,IAAH,CAAQ0L,KAAR,EAAe,IAAf,EAAqBA,KAArB;AACD;;AAED,SAAOA,KAAP;AACD,CARW,CAAZ;AAUA;;;;;;AAKO,SAASC,YAAT,CAAsBtI,UAAtB,EAAkC6H,MAAlC,EAA0C;AAC/CF,EAAAA,YAAY,CAAC3H,UAAD,EAAa,QAAb,EAAuB6H,MAAvB,CAAZ;AACD;AAED;;;;;;;;AAMAS,YAAY,CAAC,YAAD,EAAe,UAAShE,GAAT,EAAc9G,EAAd,EAAkB;AAC3C,MAAI,OAAO8G,GAAP,KAAe,QAAnB,EAA6B;AAC3B,WAAO7D,kBAAW9D,IAAX,CAAgB,IAAhB,EAAsB,sCAAtB,EAA8Da,EAA9D,CAAP;AACD;;AAED,OAAK+C,WAAL,GAAmB+D,GAAnB;;AAEA,MAAI,0BAAc9G,EAAd,CAAJ,EAAuB;AACrBA,IAAAA,EAAE,CAACb,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,SAAO,IAAP;AACD,CAZW,CAAZ;AAcA;;;;;;;;;;;;AAWA2L,YAAY,CAAC,MAAD,EAAS,UAAS3E,CAAT,EAAYC,CAAZ,EAAevD,CAAf,EAAkBC,CAAlB,EAAqBiI,CAArB,EAAwB/K,EAAxB,EAA4B;AAC/C,MAAI,OAAOmG,CAAP,KAAa,QAAb,IAAyB,OAAOC,CAAP,KAAa,QAA1C,EAAoD;AAClD,WAAOnD,kBAAW9D,IAAX,CAAgB,IAAhB,EAAsB,yBAAtB,EAAiDa,EAAjD,CAAP;AACD;;AAED,MAAI,OAAO6C,CAAP,KAAa,QAAb,IAAyB,OAAOC,CAAP,KAAa,QAA1C,EAAoD;AAClD,WAAOG,kBAAW9D,IAAX,CAAgB,IAAhB,EAAsB,yBAAtB,EAAiDa,EAAjD,CAAP;AACD;;AAED,MAAI,OAAO+K,CAAP,KAAa,UAAjB,EAA6B;AAC3B,WAAO9H,kBAAW9D,IAAX,CAAgB,IAAhB,EAAsB,sBAAtB,EAA8Ca,EAA9C,CAAP;AACD;;AAED,MAAM2K,MAAM,GAAG,iBAAK,IAAL,EAAWxE,CAAX,EAAcC,CAAd,EAAiBvD,CAAjB,EAAoBC,CAApB,EAAuBiI,CAAvB,CAAf;;AAEA,MAAI,0BAAc/K,EAAd,CAAJ,EAAuB;AACrBA,IAAAA,EAAE,CAACb,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoBwL,MAApB;AACD;;AAED,SAAOA,MAAP;AACD,CApBW,CAAZ;;AAsBA,IAAIK,OAAO,CAACC,GAAR,CAAYC,WAAZ,KAA4B,SAAhC,EAA2C;AACzC;;AACA;AACA,MAAIC,EAAJ;;AAEA,MAAI,OAAOC,MAAP,KAAkB,WAAlB,IAAiC,QAAOA,MAAP,0DAAOA,MAAP,OAAkB,QAAvD,EAAiE;AAC/DD,IAAAA,EAAE,GAAGC,MAAL;AACD;;AAED,MAAI,OAAOC,IAAP,KAAgB,WAAhB,IAA+B,QAAOA,IAAP,0DAAOA,IAAP,OAAgB,QAAnD,EAA6D;AAC3DF,IAAAA,EAAE,GAAGE,IAAL;AACD;;AAEDF,EAAAA,EAAE,CAACzJ,IAAH,GAAUA,IAAV;AACAyJ,EAAAA,EAAE,CAAC1L,MAAH,GAAYA,MAAZ;AACD;;eAIciC,I", "sourcesContent": ["import fs from 'fs';\nimport Path from 'path';\nimport EventEmitter from 'events';\n\nimport { isNodePattern, throwError, scan, scanIterator } from '@jimp/utils';\nimport anyBase from 'any-base';\nimport mkdirp from 'mkdirp';\nimport pixelMatch from 'pixelmatch';\nimport tinyColor from 'tinycolor2';\n\nimport ImagePHash from './modules/phash';\nimport request from './request';\n\nimport composite from './composite';\nimport promisify from './utils/promisify';\nimport * as MIME from './utils/mime';\nimport { parseBitmap, getBuffer, getBufferAsync } from './utils/image-bitmap';\nimport * as constants from './constants';\n\nconst alphabet =\n  '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ$_';\n\n// an array storing the maximum string length of hashes at various bases\n// 0 and 1 do not exist as possible hash lengths\nconst maxHashLength = [NaN, NaN];\n\nfor (let i = 2; i < 65; i++) {\n  const maxHash = anyBase(anyBase.BIN, alphabet.slice(0, i))(\n    new Array(64 + 1).join('1')\n  );\n  maxHashLength.push(maxHash.length);\n}\n\n// no operation\nfunction noop() {}\n\n// error checking methods\n\nfunction isArrayBuffer(test) {\n  return (\n    Object.prototype.toString\n      .call(test)\n      .toLowerCase()\n      .indexOf('arraybuffer') > -1\n  );\n}\n\n// Prepare a Buffer object from the arrayBuffer. Necessary in the browser > node conversion,\n// But this function is not useful when running in node directly\nfunction bufferFromArrayBuffer(arrayBuffer) {\n  const buffer = Buffer.alloc(arrayBuffer.byteLength);\n  const view = new Uint8Array(arrayBuffer);\n\n  for (let i = 0; i < buffer.length; ++i) {\n    buffer[i] = view[i];\n  }\n\n  return buffer;\n}\n\nfunction loadFromURL(options, cb) {\n  request(options, (err, response, data) => {\n    if (err) {\n      return cb(err);\n    }\n\n    if ('headers' in response && 'location' in response.headers) {\n      options.url = response.headers.location;\n      return loadFromURL(options, cb);\n    }\n\n    if (typeof data === 'object' && Buffer.isBuffer(data)) {\n      return cb(null, data);\n    }\n\n    const msg =\n      'Could not load Buffer from <' +\n      options.url +\n      '> ' +\n      '(HTTP: ' +\n      response.statusCode +\n      ')';\n\n    return new Error(msg);\n  });\n}\n\nfunction loadBufferFromPath(src, cb) {\n  if (\n    fs &&\n    typeof fs.readFile === 'function' &&\n    !src.match(/^(http|ftp)s?:\\/\\/./)\n  ) {\n    fs.readFile(src, cb);\n  } else {\n    loadFromURL({ url: src }, cb);\n  }\n}\n\nfunction isRawRGBAData(obj) {\n  return (\n    obj &&\n    typeof obj === 'object' &&\n    typeof obj.width === 'number' &&\n    typeof obj.height === 'number' &&\n    (Buffer.isBuffer(obj.data) ||\n      obj.data instanceof Uint8Array ||\n      (typeof Uint8ClampedArray === 'function' &&\n        obj.data instanceof Uint8ClampedArray)) &&\n    (obj.data.length === obj.width * obj.height * 4 ||\n      obj.data.length === obj.width * obj.height * 3)\n  );\n}\n\nfunction makeRGBABufferFromRGB(buffer) {\n  if (buffer.length % 3 !== 0) {\n    throw new Error('Buffer length is incorrect');\n  }\n\n  const rgbaBuffer = Buffer.allocUnsafe((buffer.length / 3) * 4);\n  let j = 0;\n\n  for (let i = 0; i < buffer.length; i++) {\n    rgbaBuffer[j] = buffer[i];\n\n    if ((i + 1) % 3 === 0) {\n      rgbaBuffer[++j] = 255;\n    }\n\n    j++;\n  }\n\n  return rgbaBuffer;\n}\n\nconst emptyBitmap = {\n  data: null,\n  width: null,\n  height: null\n};\n\n/**\n * Jimp constructor (from a file)\n * @param path a path to the image\n * @param {function(Error, Jimp)} cb (optional) a function to call when the image is parsed to a bitmap\n */\n\n/**\n * Jimp constructor (from a url with options)\n * @param options { url, otherOptions}\n * @param {function(Error, Jimp)} cb (optional) a function to call when the image is parsed to a bitmap\n */\n\n/**\n * Jimp constructor (from another Jimp image or raw image data)\n * @param image a Jimp image to clone\n * @param {function(Error, Jimp)} cb a function to call when the image is parsed to a bitmap\n */\n\n/**\n * Jimp constructor (from a Buffer)\n * @param data a Buffer containing the image data\n * @param {function(Error, Jimp)} cb a function to call when the image is parsed to a bitmap\n */\n\n/**\n * Jimp constructor (to generate a new image)\n * @param w the width of the image\n * @param h the height of the image\n * @param {function(Error, Jimp)} cb (optional) a function to call when the image is parsed to a bitmap\n */\n\n/**\n * Jimp constructor (to generate a new image)\n * @param w the width of the image\n * @param h the height of the image\n * @param background color to fill the image with\n * @param {function(Error, Jimp)} cb (optional) a function to call when the image is parsed to a bitmap\n */\n\nclass Jimp extends EventEmitter {\n  // An object representing a bitmap in memory, comprising:\n  //  - data: a buffer of the bitmap data\n  //  - width: the width of the image in pixels\n  //  - height: the height of the image in pixels\n  bitmap = emptyBitmap;\n\n  // Default colour to use for new pixels\n  _background = 0x00000000;\n\n  // Default MIME is PNG\n  _originalMime = Jimp.MIME_PNG;\n\n  // Exif data for the image\n  _exif = null;\n\n  // Whether Transparency supporting formats will be exported as RGB or RGBA\n  _rgba = true;\n\n  constructor(...args) {\n    super();\n\n    const jimpInstance = this;\n    let cb = noop;\n\n    if (isArrayBuffer(args[0])) {\n      args[0] = bufferFromArrayBuffer(args[0]);\n    }\n\n    function finish(...args) {\n      const [err] = args;\n      const evData = err || {};\n      evData.methodName = 'constructor';\n\n      setTimeout(() => {\n        // run on next tick.\n        if (err && cb === noop) {\n          jimpInstance.emitError('constructor', err);\n        } else if (!err) {\n          jimpInstance.emitMulti('constructor', 'initialized');\n        }\n\n        cb.call(jimpInstance, ...args);\n      }, 1);\n    }\n\n    if (\n      (typeof args[0] === 'number' && typeof args[1] === 'number') ||\n      (parseInt(args[0], 10) && parseInt(args[1], 10))\n    ) {\n      // create a new image\n      const w = parseInt(args[0], 10);\n      const h = parseInt(args[1], 10);\n      cb = args[2];\n\n      // with a hex color\n      if (typeof args[2] === 'number') {\n        this._background = args[2];\n        cb = args[3];\n      }\n\n      // with a css color\n      if (typeof args[2] === 'string') {\n        this._background = Jimp.cssColorToHex(args[2]);\n        cb = args[3];\n      }\n\n      if (typeof cb === 'undefined') {\n        cb = noop;\n      }\n\n      if (typeof cb !== 'function') {\n        return throwError.call(this, 'cb must be a function', finish);\n      }\n\n      this.bitmap = {\n        data: Buffer.alloc(w * h * 4),\n        width: w,\n        height: h\n      };\n\n      for (let i = 0; i < this.bitmap.data.length; i += 4) {\n        this.bitmap.data.writeUInt32BE(this._background, i);\n      }\n\n      finish(null, this);\n    } else if (typeof args[0] === 'object' && args[0].url) {\n      cb = args[1] || noop;\n\n      if (typeof cb !== 'function') {\n        return throwError.call(this, 'cb must be a function', finish);\n      }\n\n      loadFromURL(args[0], (err, data) => {\n        if (err) {\n          return throwError.call(this, err, finish);\n        }\n\n        this.parseBitmap(data, args[0].url, finish);\n      });\n    } else if (args[0] instanceof Jimp) {\n      // clone an existing Jimp\n      const [original] = args;\n      cb = args[1];\n\n      if (typeof cb === 'undefined') {\n        cb = noop;\n      }\n\n      if (typeof cb !== 'function') {\n        return throwError.call(this, 'cb must be a function', finish);\n      }\n\n      this.bitmap = {\n        data: Buffer.from(original.bitmap.data),\n        width: original.bitmap.width,\n        height: original.bitmap.height\n      };\n\n      this._quality = original._quality;\n      this._deflateLevel = original._deflateLevel;\n      this._deflateStrategy = original._deflateStrategy;\n      this._filterType = original._filterType;\n      this._rgba = original._rgba;\n      this._background = original._background;\n      this._originalMime = original._originalMime;\n\n      finish(null, this);\n    } else if (isRawRGBAData(args[0])) {\n      const [imageData] = args;\n      cb = args[1] || noop;\n\n      const isRGBA =\n        imageData.width * imageData.height * 4 === imageData.data.length;\n      const buffer = isRGBA\n        ? Buffer.from(imageData.data)\n        : makeRGBABufferFromRGB(imageData.data);\n\n      this.bitmap = {\n        data: buffer,\n        width: imageData.width,\n        height: imageData.height\n      };\n\n      finish(null, this);\n    } else if (typeof args[0] === 'string') {\n      // read from a path\n      const path = args[0];\n      cb = args[1];\n\n      if (typeof cb === 'undefined') {\n        cb = noop;\n      }\n\n      if (typeof cb !== 'function') {\n        return throwError.call(this, 'cb must be a function', finish);\n      }\n\n      loadBufferFromPath(path, (err, data) => {\n        if (err) {\n          return throwError.call(this, err, finish);\n        }\n\n        this.parseBitmap(data, path, finish);\n      });\n    } else if (typeof args[0] === 'object' && Buffer.isBuffer(args[0])) {\n      // read from a buffer\n      const data = args[0];\n      cb = args[1];\n\n      if (typeof cb !== 'function') {\n        return throwError.call(this, 'cb must be a function', finish);\n      }\n\n      this.parseBitmap(data, null, finish);\n    } else {\n      // Allow client libs to add new ways to build a Jimp object.\n      // Extra constructors must be added by `Jimp.appendConstructorOption()`\n      cb = args[args.length - 1];\n\n      if (typeof cb !== 'function') {\n        // TODO: try to solve the args after cb problem.\n        cb = args[args.length - 2];\n\n        if (typeof cb !== 'function') {\n          cb = noop;\n        }\n      }\n\n      const extraConstructor = Jimp.__extraConstructors.find(c =>\n        c.test(...args)\n      );\n\n      if (extraConstructor) {\n        new Promise((resolve, reject) =>\n          extraConstructor.run.call(this, resolve, reject, ...args)\n        )\n          .then(() => finish(null, this))\n          .catch(finish);\n      } else {\n        return throwError.call(\n          this,\n          'No matching constructor overloading was found. ' +\n            'Please see the docs for how to call the Jimp constructor.',\n          finish\n        );\n      }\n    }\n  }\n\n  /**\n   * Parse a bitmap with the loaded image types.\n   *\n   * @param {Buffer} data raw image data\n   * @param {string} path optional path to file\n   * @param {function(Error, Jimp)} finish (optional) a callback for when complete\n   * @memberof Jimp\n   */\n  parseBitmap(data, path, finish) {\n    parseBitmap.call(this, data, null, finish);\n  }\n\n  /**\n   * Sets the type of the image (RGB or RGBA) when saving in a format that supports transparency (default is RGBA)\n   * @param {boolean} bool A Boolean, true to use RGBA or false to use RGB\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp} this for chaining of methods\n   */\n  rgba(bool, cb) {\n    if (typeof bool !== 'boolean') {\n      return throwError.call(\n        this,\n        'bool must be a boolean, true for RGBA or false for RGB',\n        cb\n      );\n    }\n\n    this._rgba = bool;\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  }\n\n  /**\n   * Emit for multiple listeners\n   * @param {string} methodName name of the method to emit an error for\n   * @param {string} eventName name of the eventName to emit an error for\n   * @param {object} data to emit\n   */\n  emitMulti(methodName, eventName, data = {}) {\n    data = Object.assign(data, { methodName, eventName });\n    this.emit('any', data);\n\n    if (methodName) {\n      this.emit(methodName, data);\n    }\n\n    this.emit(eventName, data);\n  }\n\n  emitError(methodName, err) {\n    this.emitMulti(methodName, 'error', err);\n  }\n\n  /**\n   * Get the current height of the image\n   * @return {number} height of the image\n   */\n  getHeight() {\n    return this.bitmap.height;\n  }\n\n  /**\n   * Get the current width of the image\n   * @return {number} width of the image\n   */\n  getWidth() {\n    return this.bitmap.width;\n  }\n\n  /**\n   * Nicely format Jimp object when sent to the console e.g. console.log(image)\n   * @returns {string} pretty printed\n   */\n  inspect() {\n    return (\n      '<Jimp ' +\n      (this.bitmap === emptyBitmap\n        ? 'pending...'\n        : this.bitmap.width + 'x' + this.bitmap.height) +\n      '>'\n    );\n  }\n\n  /**\n   * Nicely format Jimp object when converted to a string\n   * @returns {string} pretty printed\n   */\n  toString() {\n    return '[object Jimp]';\n  }\n\n  /**\n   * Returns the original MIME of the image (default: \"image/png\")\n   * @returns {string} the MIME\n   */\n  getMIME() {\n    const mime = this._originalMime || Jimp.MIME_PNG;\n\n    return mime;\n  }\n\n  /**\n   * Returns the appropriate file extension for the original MIME of the image (default: \"png\")\n   * @returns {string} the file extension\n   */\n  getExtension() {\n    const mime = this.getMIME();\n\n    return MIME.getExtension(mime);\n  }\n\n  /**\n   * Writes the image to a file\n   * @param {string} path a path to the destination file\n   * @param {function(Error, Jimp)} cb (optional) a function to call when the image is saved to disk\n   * @returns {Jimp} this for chaining of methods\n   */\n  write(path, cb) {\n    if (!fs || !fs.createWriteStream) {\n      throw new Error(\n        'Cant access the filesystem. You can use the getBase64 method.'\n      );\n    }\n\n    if (typeof path !== 'string') {\n      return throwError.call(this, 'path must be a string', cb);\n    }\n\n    if (typeof cb === 'undefined') {\n      cb = noop;\n    }\n\n    if (typeof cb !== 'function') {\n      return throwError.call(this, 'cb must be a function', cb);\n    }\n\n    const mime = MIME.getType(path) || this.getMIME();\n    const pathObj = Path.parse(path);\n\n    if (pathObj.dir) {\n      mkdirp.sync(pathObj.dir);\n    }\n\n    this.getBuffer(mime, (err, buffer) => {\n      if (err) {\n        return throwError.call(this, err, cb);\n      }\n\n      const stream = fs.createWriteStream(path);\n\n      stream\n        .on('open', () => {\n          stream.write(buffer);\n          stream.end();\n        })\n        .on('error', err => {\n          return throwError.call(this, err, cb);\n        });\n      stream.on('finish', () => {\n        cb.call(this, null, this);\n      });\n    });\n\n    return this;\n  }\n\n  writeAsync = path => promisify(this.write, this, path);\n\n  /**\n   * Converts the image to a base 64 string\n   * @param {string} mime the mime type of the image data to be created\n   * @param {function(Error, Jimp)} cb a Node-style function to call with the buffer as the second argument\n   * @returns {Jimp} this for chaining of methods\n   */\n  getBase64(mime, cb) {\n    if (mime === Jimp.AUTO) {\n      // allow auto MIME detection\n      mime = this.getMIME();\n    }\n\n    if (typeof mime !== 'string') {\n      return throwError.call(this, 'mime must be a string', cb);\n    }\n\n    if (typeof cb !== 'function') {\n      return throwError.call(this, 'cb must be a function', cb);\n    }\n\n    this.getBuffer(mime, function(err, data) {\n      if (err) {\n        return throwError.call(this, err, cb);\n      }\n\n      const src = 'data:' + mime + ';base64,' + data.toString('base64');\n      cb.call(this, null, src);\n    });\n\n    return this;\n  }\n\n  getBase64Async = mime => promisify(this.getBase64, this, mime);\n\n  /**\n   * Generates a perceptual hash of the image <https://en.wikipedia.org/wiki/Perceptual_hashing>. And pads the string. Can configure base.\n   * @param {number} base (optional) a number between 2 and 64 representing the base for the hash (e.g. 2 is binary, 10 is decimal, 16 is hex, 64 is base 64). Defaults to 64.\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {string} a string representing the hash\n   */\n  hash(base, cb) {\n    base = base || 64;\n\n    if (typeof base === 'function') {\n      cb = base;\n      base = 64;\n    }\n\n    if (typeof base !== 'number') {\n      return throwError.call(this, 'base must be a number', cb);\n    }\n\n    if (base < 2 || base > 64) {\n      return throwError.call(\n        this,\n        'base must be a number between 2 and 64',\n        cb\n      );\n    }\n\n    let hash = this.pHash();\n    hash = anyBase(anyBase.BIN, alphabet.slice(0, base))(hash);\n\n    while (hash.length < maxHashLength[base]) {\n      hash = '0' + hash; // pad out with leading zeros\n    }\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, hash);\n    }\n\n    return hash;\n  }\n\n  /**\n   * Calculates the perceptual hash\n   * @returns {number} the perceptual hash\n   */\n  pHash() {\n    const pHash = new ImagePHash();\n    return pHash.getHash(this);\n  }\n\n  /**\n   * Calculates the hamming distance of the current image and a hash based on their perceptual hash\n   * @param {hash} compareHash hash to compare to\n   * @returns {number} a number ranging from 0 to 1, 0 means they are believed to be identical\n   */\n  distanceFromHash(compareHash) {\n    const pHash = new ImagePHash();\n    const currentHash = pHash.getHash(this);\n\n    return pHash.distance(currentHash, compareHash);\n  }\n\n  /**\n   * Converts the image to a buffer\n   * @param {string} mime the mime type of the image buffer to be created\n   * @param {function(Error, Jimp)} cb a Node-style function to call with the buffer as the second argument\n   * @returns {Jimp} this for chaining of methods\n   */\n  getBuffer = getBuffer;\n\n  getBufferAsync = getBufferAsync;\n\n  /**\n   * Returns the offset of a pixel in the bitmap buffer\n   * @param {number} x the x coordinate\n   * @param {number} y the y coordinate\n   * @param {string} edgeHandling (optional) define how to sum pixels from outside the border\n   * @param {number} cb (optional) a callback for when complete\n   * @returns {number} the index of the pixel or -1 if not found\n   */\n  getPixelIndex(x, y, edgeHandling, cb) {\n    let xi;\n    let yi;\n\n    if (typeof edgeHandling === 'function' && typeof cb === 'undefined') {\n      cb = edgeHandling;\n      edgeHandling = null;\n    }\n\n    if (!edgeHandling) {\n      edgeHandling = Jimp.EDGE_EXTEND;\n    }\n\n    if (typeof x !== 'number' || typeof y !== 'number') {\n      return throwError.call(this, 'x and y must be numbers', cb);\n    }\n\n    // round input\n    x = Math.round(x);\n    y = Math.round(y);\n    xi = x;\n    yi = y;\n\n    if (edgeHandling === Jimp.EDGE_EXTEND) {\n      if (x < 0) xi = 0;\n      if (x >= this.bitmap.width) xi = this.bitmap.width - 1;\n      if (y < 0) yi = 0;\n      if (y >= this.bitmap.height) yi = this.bitmap.height - 1;\n    }\n\n    if (edgeHandling === Jimp.EDGE_WRAP) {\n      if (x < 0) {\n        xi = this.bitmap.width + x;\n      }\n\n      if (x >= this.bitmap.width) {\n        xi = x % this.bitmap.width;\n      }\n\n      if (y < 0) {\n        xi = this.bitmap.height + y;\n      }\n\n      if (y >= this.bitmap.height) {\n        yi = y % this.bitmap.height;\n      }\n    }\n\n    let i = (this.bitmap.width * yi + xi) << 2;\n\n    // if out of bounds index is -1\n    if (xi < 0 || xi >= this.bitmap.width) {\n      i = -1;\n    }\n\n    if (yi < 0 || yi >= this.bitmap.height) {\n      i = -1;\n    }\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, i);\n    }\n\n    return i;\n  }\n\n  /**\n   * Returns the hex colour value of a pixel\n   * @param {number} x the x coordinate\n   * @param {number} y the y coordinate\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {number} the color of the pixel\n   */\n  getPixelColor(x, y, cb) {\n    if (typeof x !== 'number' || typeof y !== 'number')\n      return throwError.call(this, 'x and y must be numbers', cb);\n\n    // round input\n    x = Math.round(x);\n    y = Math.round(y);\n\n    const idx = this.getPixelIndex(x, y);\n    const hex = this.bitmap.data.readUInt32BE(idx);\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, hex);\n    }\n\n    return hex;\n  }\n\n  getPixelColour = this.getPixelColor;\n\n  /**\n   * Returns the hex colour value of a pixel\n   * @param {number} hex color to set\n   * @param {number} x the x coordinate\n   * @param {number} y the y coordinate\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {number} the index of the pixel or -1 if not found\n   */\n  setPixelColor(hex, x, y, cb) {\n    if (\n      typeof hex !== 'number' ||\n      typeof x !== 'number' ||\n      typeof y !== 'number'\n    )\n      return throwError.call(this, 'hex, x and y must be numbers', cb);\n\n    // round input\n    x = Math.round(x);\n    y = Math.round(y);\n\n    const idx = this.getPixelIndex(x, y);\n    this.bitmap.data.writeUInt32BE(hex, idx);\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  }\n\n  setPixelColour = this.setPixelColor;\n\n  /**\n   * Determine if the image contains opaque pixels.\n   * @return {boolean} hasAlpha whether the image contains opaque pixels\n   */\n  hasAlpha() {\n    for (let yIndex = 0; yIndex < this.bitmap.height; yIndex++) {\n      for (let xIndex = 0; xIndex < this.bitmap.width; xIndex++) {\n        const idx = (this.bitmap.width * yIndex + xIndex) << 2;\n        const alpha = this.bitmap.data[idx + 3];\n\n        if (alpha !== 0xff) {\n          return true;\n        }\n      }\n    }\n\n    return false;\n  }\n\n  /**\n   * Iterate scan through a region of the bitmap\n   * @param {number} x the x coordinate to begin the scan at\n   * @param {number} y the y coordinate to begin the scan at\n   * @param w the width of the scan region\n   * @param h the height of the scan region\n   * @returns {IterableIterator<{x: number, y: number, idx: number, image: Jimp}>}\n   */\n  scanIterator(x, y, w, h) {\n    if (typeof x !== 'number' || typeof y !== 'number') {\n      return throwError.call(this, 'x and y must be numbers');\n    }\n\n    if (typeof w !== 'number' || typeof h !== 'number') {\n      return throwError.call(this, 'w and h must be numbers');\n    }\n\n    return scanIterator(this, x, y, w, h);\n  }\n}\n\nexport function addConstants(constants, jimpInstance = Jimp) {\n  Object.entries(constants).forEach(([name, value]) => {\n    jimpInstance[name] = value;\n  });\n}\n\nexport function addJimpMethods(methods, jimpInstance = Jimp) {\n  Object.entries(methods).forEach(([name, value]) => {\n    jimpInstance.prototype[name] = value;\n  });\n}\n\naddConstants(constants);\naddJimpMethods({ composite });\n\nJimp.__extraConstructors = [];\n\n/**\n * Allow client libs to add new ways to build a Jimp object.\n * @param {string} name identify the extra constructor.\n * @param {function} test a function that returns true when it accepts the arguments passed to the main constructor.\n * @param {function} run where the magic happens.\n */\nJimp.appendConstructorOption = function(name, test, run) {\n  Jimp.__extraConstructors.push({ name, test, run });\n};\n\n/**\n * Read an image from a file or a Buffer. Takes the same args as the constructor\n * @returns {Promise} a promise\n */\nJimp.read = function(...args) {\n  return new Promise((resolve, reject) => {\n    new Jimp(...args, (err, image) => {\n      if (err) reject(err);\n      else resolve(image);\n    });\n  });\n};\n\nJimp.create = Jimp.read;\n\n/**\n * A static helper method that converts RGBA values to a single integer value\n * @param {number} r the red value (0-255)\n * @param {number} g the green value (0-255)\n * @param {number} b the blue value (0-255)\n * @param {number} a the alpha value (0-255)\n * @param {function(Error, Jimp)} cb (optional) A callback for when complete\n * @returns {number} an single integer colour value\n */\nJimp.rgbaToInt = function(r, g, b, a, cb) {\n  if (\n    typeof r !== 'number' ||\n    typeof g !== 'number' ||\n    typeof b !== 'number' ||\n    typeof a !== 'number'\n  ) {\n    return throwError.call(this, 'r, g, b and a must be numbers', cb);\n  }\n\n  if (r < 0 || r > 255) {\n    return throwError.call(this, 'r must be between 0 and 255', cb);\n  }\n\n  if (g < 0 || g > 255) {\n    throwError.call(this, 'g must be between 0 and 255', cb);\n  }\n\n  if (b < 0 || b > 255) {\n    return throwError.call(this, 'b must be between 0 and 255', cb);\n  }\n\n  if (a < 0 || a > 255) {\n    return throwError.call(this, 'a must be between 0 and 255', cb);\n  }\n\n  r = Math.round(r);\n  b = Math.round(b);\n  g = Math.round(g);\n  a = Math.round(a);\n\n  const i =\n    r * Math.pow(256, 3) +\n    g * Math.pow(256, 2) +\n    b * Math.pow(256, 1) +\n    a * Math.pow(256, 0);\n\n  if (isNodePattern(cb)) {\n    cb.call(this, null, i);\n  }\n\n  return i;\n};\n\n/**\n * A static helper method that converts RGBA values to a single integer value\n * @param {number} i a single integer value representing an RGBA colour (e.g. 0xFF0000FF for red)\n * @param {function(Error, Jimp)} cb (optional) A callback for when complete\n * @returns {object} an object with the properties r, g, b and a representing RGBA values\n */\nJimp.intToRGBA = function(i, cb) {\n  if (typeof i !== 'number') {\n    return throwError.call(this, 'i must be a number', cb);\n  }\n\n  const rgba = {};\n\n  rgba.r = Math.floor(i / Math.pow(256, 3));\n  rgba.g = Math.floor((i - rgba.r * Math.pow(256, 3)) / Math.pow(256, 2));\n  rgba.b = Math.floor(\n    (i - rgba.r * Math.pow(256, 3) - rgba.g * Math.pow(256, 2)) /\n      Math.pow(256, 1)\n  );\n  rgba.a = Math.floor(\n    (i -\n      rgba.r * Math.pow(256, 3) -\n      rgba.g * Math.pow(256, 2) -\n      rgba.b * Math.pow(256, 1)) /\n      Math.pow(256, 0)\n  );\n\n  if (isNodePattern(cb)) {\n    cb.call(this, null, rgba);\n  }\n\n  return rgba;\n};\n\n/**\n * Converts a css color (Hex, 8-digit (RGBA) Hex, RGB, RGBA, HSL, HSLA, HSV, HSVA, Named) to a hex number\n * @param {string} cssColor a number\n * @returns {number} a hex number representing a color\n */\nJimp.cssColorToHex = function(cssColor) {\n  cssColor = cssColor || 0; // 0, null, undefined, NaN\n\n  if (typeof cssColor === 'number') return Number(cssColor);\n\n  return parseInt(tinyColor(cssColor).toHex8(), 16);\n};\n\n/**\n * Limits a number to between 0 or 255\n * @param {number} n a number\n * @returns {number} the number limited to between 0 or 255\n */\nJimp.limit255 = function(n) {\n  n = Math.max(n, 0);\n  n = Math.min(n, 255);\n\n  return n;\n};\n\n/**\n * Diffs two images and returns\n * @param {Jimp} img1 a Jimp image to compare\n * @param {Jimp} img2 a Jimp image to compare\n * @param {number} threshold (optional) a number, 0 to 1, the smaller the value the more sensitive the comparison (default: 0.1)\n * @returns {object} an object { percent: percent similar, diff: a Jimp image highlighting differences }\n */\nJimp.diff = function(img1, img2, threshold = 0.1) {\n  if (!(img1 instanceof Jimp) || !(img2 instanceof Jimp))\n    return throwError.call(this, 'img1 and img2 must be an Jimp images');\n\n  const bmp1 = img1.bitmap;\n  const bmp2 = img2.bitmap;\n\n  if (bmp1.width !== bmp2.width || bmp1.height !== bmp2.height) {\n    if (bmp1.width * bmp1.height > bmp2.width * bmp2.height) {\n      // img1 is bigger\n      img1 = img1.cloneQuiet().resize(bmp2.width, bmp2.height);\n    } else {\n      // img2 is bigger (or they are the same in area)\n      img2 = img2.cloneQuiet().resize(bmp1.width, bmp1.height);\n    }\n  }\n\n  if (typeof threshold !== 'number' || threshold < 0 || threshold > 1) {\n    return throwError.call(this, 'threshold must be a number between 0 and 1');\n  }\n\n  const diff = new Jimp(bmp1.width, bmp1.height, 0xffffffff);\n\n  const numDiffPixels = pixelMatch(\n    bmp1.data,\n    bmp2.data,\n    diff.bitmap.data,\n    diff.bitmap.width,\n    diff.bitmap.height,\n    { threshold }\n  );\n\n  return {\n    percent: numDiffPixels / (diff.bitmap.width * diff.bitmap.height),\n    image: diff\n  };\n};\n\n/**\n * Calculates the hamming distance of two images based on their perceptual hash\n * @param {Jimp} img1 a Jimp image to compare\n * @param {Jimp} img2 a Jimp image to compare\n * @returns {number} a number ranging from 0 to 1, 0 means they are believed to be identical\n */\nJimp.distance = function(img1, img2) {\n  const phash = new ImagePHash();\n  const hash1 = phash.getHash(img1);\n  const hash2 = phash.getHash(img2);\n\n  return phash.distance(hash1, hash2);\n};\n\n/**\n * Calculates the hamming distance of two images based on their perceptual hash\n * @param {hash} hash1 a pHash\n * @param {hash} hash2 a pHash\n * @returns {number} a number ranging from 0 to 1, 0 means they are believed to be identical\n */\nJimp.compareHashes = function(hash1, hash2) {\n  const phash = new ImagePHash();\n\n  return phash.distance(hash1, hash2);\n};\n\n/**\n * Compute color difference\n * 0 means no difference, 1 means maximum difference.\n * @param {number} rgba1:    first color to compare.\n * @param {number} rgba2:    second color to compare.\n * Both parameters must be an color object {r:val, g:val, b:val, a:val}\n * Where `a` is optional and `val` is an integer between 0 and 255.\n * @returns {number} float between 0 and 1.\n */\nJimp.colorDiff = function(rgba1, rgba2) {\n  const pow = n => Math.pow(n, 2);\n  const { max } = Math;\n  const maxVal = 255 * 255 * 3;\n\n  if (rgba1.a !== 0 && !rgba1.a) {\n    rgba1.a = 255;\n  }\n\n  if (rgba2.a !== 0 && !rgba2.a) {\n    rgba2.a = 255;\n  }\n\n  return (\n    (max(pow(rgba1.r - rgba2.r), pow(rgba1.r - rgba2.r - rgba1.a + rgba2.a)) +\n      max(pow(rgba1.g - rgba2.g), pow(rgba1.g - rgba2.g - rgba1.a + rgba2.a)) +\n      max(pow(rgba1.b - rgba2.b), pow(rgba1.b - rgba2.b - rgba1.a + rgba2.a))) /\n    maxVal\n  );\n};\n\n/**\n * Helper to create Jimp methods that emit events before and after its execution.\n * @param {string} methodName   The name to be appended to Jimp prototype.\n * @param {string} evName       The event name to be called.\n *                     It will be prefixed by `before-` and emitted when on method call.\n *                     It will be appended by `ed` and emitted after the method run.\n * @param {function} method       A function implementing the method itself.\n * It will also create a quiet version that will not emit events, to not\n * mess the user code with many `changed` event calls. You can call with\n * `methodName + \"Quiet\"`.\n *\n * The emitted event comes with a object parameter to the listener with the\n * `methodName` as one attribute.\n */\nexport function jimpEvMethod(methodName, evName, method) {\n  const evNameBefore = 'before-' + evName;\n  const evNameAfter = evName.replace(/e$/, '') + 'ed';\n\n  Jimp.prototype[methodName] = function(...args) {\n    let wrappedCb;\n    const cb = args[method.length - 1];\n    const jimpInstance = this;\n\n    if (typeof cb === 'function') {\n      wrappedCb = function(...args) {\n        const [err, data] = args;\n\n        if (err) {\n          jimpInstance.emitError(methodName, err);\n        } else {\n          jimpInstance.emitMulti(methodName, evNameAfter, {\n            [methodName]: data\n          });\n        }\n\n        cb.apply(this, args);\n      };\n\n      args[args.length - 1] = wrappedCb;\n    } else {\n      wrappedCb = false;\n    }\n\n    this.emitMulti(methodName, evNameBefore);\n\n    let result;\n\n    try {\n      result = method.apply(this, args);\n\n      if (!wrappedCb) {\n        this.emitMulti(methodName, evNameAfter, {\n          [methodName]: result\n        });\n      }\n    } catch (error) {\n      error.methodName = methodName;\n      this.emitError(methodName, error);\n    }\n\n    return result;\n  };\n\n  Jimp.prototype[methodName + 'Quiet'] = method;\n}\n\n/**\n * Creates a new image that is a clone of this one.\n * @param {function(Error, Jimp)} cb (optional) A callback for when complete\n * @returns the new image\n */\njimpEvMethod('clone', 'clone', function(cb) {\n  const clone = new Jimp(this);\n\n  if (isNodePattern(cb)) {\n    cb.call(clone, null, clone);\n  }\n\n  return clone;\n});\n\n/**\n * Simplify jimpEvMethod call for the common `change` evName.\n * @param {string} methodName name of the method\n * @param {function} method to watch changes for\n */\nexport function jimpEvChange(methodName, method) {\n  jimpEvMethod(methodName, 'change', method);\n}\n\n/**\n * Sets the type of the image (RGB or RGBA) when saving as PNG format (default is RGBA)\n * @param b A Boolean, true to use RGBA or false to use RGB\n * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\njimpEvChange('background', function(hex, cb) {\n  if (typeof hex !== 'number') {\n    return throwError.call(this, 'hex must be a hexadecimal rgba value', cb);\n  }\n\n  this._background = hex;\n\n  if (isNodePattern(cb)) {\n    cb.call(this, null, this);\n  }\n\n  return this;\n});\n\n/**\n * Scans through a region of the bitmap, calling a function for each pixel.\n * @param {number} x the x coordinate to begin the scan at\n * @param {number} y the y coordinate to begin the scan at\n * @param w the width of the scan region\n * @param h the height of the scan region\n * @param f a function to call on even pixel; the (x, y) position of the pixel\n * and the index of the pixel in the bitmap buffer are passed to the function\n * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\njimpEvChange('scan', function(x, y, w, h, f, cb) {\n  if (typeof x !== 'number' || typeof y !== 'number') {\n    return throwError.call(this, 'x and y must be numbers', cb);\n  }\n\n  if (typeof w !== 'number' || typeof h !== 'number') {\n    return throwError.call(this, 'w and h must be numbers', cb);\n  }\n\n  if (typeof f !== 'function') {\n    return throwError.call(this, 'f must be a function', cb);\n  }\n\n  const result = scan(this, x, y, w, h, f);\n\n  if (isNodePattern(cb)) {\n    cb.call(this, null, result);\n  }\n\n  return result;\n});\n\nif (process.env.ENVIRONMENT === 'BROWSER') {\n  // For use in a web browser or web worker\n  /* global self */\n  let gl;\n\n  if (typeof window !== 'undefined' && typeof window === 'object') {\n    gl = window;\n  }\n\n  if (typeof self !== 'undefined' && typeof self === 'object') {\n    gl = self;\n  }\n\n  gl.Jimp = Jimp;\n  gl.Buffer = Buffer;\n}\n\nexport { addType } from './utils/mime';\n\nexport default Jimp;\n"], "file": "index.js"}
{"version": 3, "file": "ai-recommend.js", "sources": ["pages/ai-recommend/ai-recommend.vue", "../../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYWktcmVjb21tZW5kL2FpLXJlY29tbWVuZC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <!-- 顶部标题 -->\r\n    <view class=\"header\">\r\n      <view class=\"header-icon\">🎯</view>\r\n      <view class=\"header-info\">\r\n        <text class=\"header-title\">智能推荐</text>\r\n        <text class=\"header-desc\">个性化内容推荐</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 推荐类别选择 -->\r\n    <view class=\"category-selector\">\r\n      <text class=\"selector-title\">🏷️ 推荐类别</text>\r\n      <scroll-view class=\"category-scroll\" scroll-x=\"true\" show-scrollbar=\"false\">\r\n        <view class=\"category-list\">\r\n          <view \r\n            v-for=\"(category, index) in categories\" \r\n            :key=\"index\"\r\n            class=\"category-item\"\r\n            :class=\"{ active: selectedCategory === category.key }\"\r\n            @tap=\"selectCategory(category.key)\"\r\n          >\r\n            <text class=\"category-icon\">{{ category.icon }}</text>\r\n            <text class=\"category-name\">{{ category.name }}</text>\r\n          </view>\r\n        </view>\r\n      </scroll-view>\r\n    </view>\r\n\r\n    <!-- 偏好输入区域 -->\r\n    <view class=\"preference-section\">\r\n      <text class=\"section-title\">💭 告诉我你的偏好</text>\r\n      <textarea \r\n        class=\"preference-input\" \r\n        v-model=\"preferences\" \r\n        :placeholder=\"currentCategory.placeholder\"\r\n        :maxlength=\"300\"\r\n        show-confirm-bar=\"false\"\r\n      />\r\n      <view class=\"char-count\">{{ preferences.length }}/300</view>\r\n      \r\n      <!-- 快捷偏好标签 -->\r\n      <view class=\"quick-tags\">\r\n        <text class=\"tags-title\">🏷️ 快捷标签</text>\r\n        <view class=\"tags-list\">\r\n          <view \r\n            v-for=\"(tag, index) in currentCategory.tags\" \r\n            :key=\"index\" \r\n            class=\"tag-item\"\r\n            @tap=\"addTag(tag)\"\r\n          >\r\n            <text class=\"tag-text\">{{ tag }}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 推荐按钮 -->\r\n    <view class=\"recommend-section\">\r\n      <button \r\n        class=\"recommend-btn\" \r\n        :class=\"{ disabled: !preferences.trim() || isRecommending }\" \r\n        @tap=\"getRecommendation\"\r\n      >\r\n        <text class=\"btn-icon\">{{ isRecommending ? '⏳' : '✨' }}</text>\r\n        <text class=\"btn-text\">{{ isRecommending ? '推荐中...' : '获取推荐' }}</text>\r\n      </button>\r\n    </view>\r\n\r\n    <!-- 推荐结果 -->\r\n    <view v-if=\"recommendationResult || isRecommending\" class=\"result-section\">\r\n      <view class=\"result-header\">\r\n        <text class=\"result-title\">{{ currentCategory.icon }} 为您推荐</text>\r\n        <view v-if=\"recommendationResult && !isRecommending\" class=\"result-actions\">\r\n          <button class=\"action-btn refresh\" @tap=\"getRecommendation\">🔄 重新推荐</button>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 推荐中的动画 -->\r\n      <view v-if=\"isRecommending\" class=\"recommending-animation\">\r\n        <view class=\"loading-circle\">\r\n          <view class=\"circle-dot\"></view>\r\n          <view class=\"circle-dot\"></view>\r\n          <view class=\"circle-dot\"></view>\r\n          <view class=\"circle-dot\"></view>\r\n        </view>\r\n        <text class=\"recommending-text\">AI正在为您精心挑选...</text>\r\n      </view>\r\n      \r\n      <!-- 推荐结果内容 -->\r\n      <view v-if=\"recommendationResult\" class=\"result-content\">\r\n        <text class=\"result-text\">{{ recommendationResult }}</text>\r\n        <view class=\"result-footer\">\r\n          <button class=\"footer-btn copy\" @tap=\"copyResult\">📋 复制推荐</button>\r\n          <button class=\"footer-btn share\" @tap=\"shareResult\">📤 分享</button>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 历史推荐 -->\r\n    <view v-if=\"!recommendationResult && !isRecommending && historyRecommendations.length > 0\" class=\"history-section\">\r\n      <text class=\"history-title\">📚 历史推荐</text>\r\n      <view class=\"history-list\">\r\n        <view \r\n          v-for=\"(item, index) in historyRecommendations\" \r\n          :key=\"index\" \r\n          class=\"history-item\"\r\n          @tap=\"viewHistory(item)\"\r\n        >\r\n          <view class=\"history-header\">\r\n            <text class=\"history-category\">{{ item.categoryIcon }} {{ item.categoryName }}</text>\r\n            <text class=\"history-time\">{{ item.time }}</text>\r\n          </view>\r\n          <text class=\"history-preference\">{{ item.preferences }}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      preferences: '',\r\n      recommendationResult: '',\r\n      isRecommending: false,\r\n      userId: 123,\r\n      selectedCategory: 'books',\r\n      categories: [\r\n        {\r\n          key: 'books',\r\n          name: '书籍',\r\n          icon: '📚',\r\n          placeholder: '请描述您喜欢的书籍类型、作者、主题等...\\n例如：我喜欢科幻小说，特别是关于人工智能的',\r\n          tags: ['科幻', '悬疑', '历史', '传记', '心理学', '编程', '文学', '哲学']\r\n        },\r\n        {\r\n          key: 'movies',\r\n          name: '电影',\r\n          icon: '🎬',\r\n          placeholder: '请描述您喜欢的电影类型、导演、演员等...\\n例如：我喜欢悬疑片和科幻片，特别是诺兰的作品',\r\n          tags: ['动作', '喜剧', '爱情', '科幻', '悬疑', '恐怖', '动画', '纪录片']\r\n        },\r\n        {\r\n          key: 'music',\r\n          name: '音乐',\r\n          icon: '🎵',\r\n          placeholder: '请描述您喜欢的音乐风格、歌手、乐器等...\\n例如：我喜欢流行音乐和古典音乐，特别是钢琴曲',\r\n          tags: ['流行', '摇滚', '古典', '爵士', '电子', '民谣', '说唱', '轻音乐']\r\n        },\r\n        {\r\n          key: 'food',\r\n          name: '美食',\r\n          icon: '🍽️',\r\n          placeholder: '请描述您喜欢的菜系、口味、食材等...\\n例如：我喜欢川菜和日料，偏爱辣味和清淡的食物',\r\n          tags: ['川菜', '粤菜', '日料', '韩料', '西餐', '甜品', '素食', '海鲜']\r\n        },\r\n        {\r\n          key: 'travel',\r\n          name: '旅行',\r\n          icon: '✈️',\r\n          placeholder: '请描述您喜欢的旅行方式、目的地类型等...\\n例如：我喜欢自然风光和历史文化，偏爱安静的小城',\r\n          tags: ['自然', '历史', '文化', '海滨', '山区', '城市', '乡村', '异国']\r\n        },\r\n        {\r\n          key: 'courses',\r\n          name: '课程',\r\n          icon: '🎓',\r\n          placeholder: '请描述您想学习的技能、知识领域等...\\n例如：我想学习编程和设计，特别是前端开发',\r\n          tags: ['编程', '设计', '语言', '音乐', '绘画', '摄影', '写作', '运动']\r\n        }\r\n      ],\r\n      historyRecommendations: []\r\n    }\r\n  },\r\n  computed: {\r\n    currentCategory() {\r\n      return this.categories.find(cat => cat.key === this.selectedCategory) || this.categories[0];\r\n    }\r\n  },\r\n  onLoad() {\r\n    this.loadUserInfo();\r\n    this.loadHistory();\r\n  },\r\n  methods: {\r\n    loadUserInfo() {\r\n      const userInfo = uni.getStorageSync('userInfo');\r\n      if (userInfo && userInfo.id) {\r\n        this.userId = userInfo.id;\r\n      }\r\n    },\r\n\r\n    loadHistory() {\r\n      const history = uni.getStorageSync('recommendHistory') || [];\r\n      this.historyRecommendations = history.slice(0, 5); // 只显示最近5条\r\n    },\r\n\r\n    saveHistory(recommendation) {\r\n      const history = uni.getStorageSync('recommendHistory') || [];\r\n      const newItem = {\r\n        categoryIcon: this.currentCategory.icon,\r\n        categoryName: this.currentCategory.name,\r\n        preferences: this.preferences,\r\n        result: recommendation,\r\n        time: this.getCurrentTime()\r\n      };\r\n      \r\n      history.unshift(newItem);\r\n      if (history.length > 20) {\r\n        history.splice(20); // 只保留最近20条\r\n      }\r\n      \r\n      uni.setStorageSync('recommendHistory', history);\r\n      this.loadHistory();\r\n    },\r\n\r\n    selectCategory(categoryKey) {\r\n      this.selectedCategory = categoryKey;\r\n      this.preferences = '';\r\n      this.recommendationResult = '';\r\n    },\r\n\r\n    addTag(tag) {\r\n      if (this.preferences) {\r\n        this.preferences += `，${tag}`;\r\n      } else {\r\n        this.preferences = tag;\r\n      }\r\n    },\r\n\r\n    async getRecommendation() {\r\n      if (!this.preferences.trim() || this.isRecommending) return;\r\n\r\n      this.isRecommending = true;\r\n      this.recommendationResult = '';\r\n\r\n      try {\r\n        const response = await this.callRecommendAPI(this.preferences.trim(), this.currentCategory.name);\r\n        \r\n        if (response && response.success) {\r\n          this.recommendationResult = response.response || '抱歉，暂时无法生成推荐，请重试。';\r\n          this.saveHistory(this.recommendationResult);\r\n        } else {\r\n          this.recommendationResult = '推荐服务暂时不可用，请稍后再试。';\r\n        }\r\n      } catch (error) {\r\n        this.recommendationResult = '网络连接失败，请检查网络后重试。';\r\n        console.error('API调用失败:', error);\r\n      }\r\n\r\n      this.isRecommending = false;\r\n    },\r\n\r\n    async callRecommendAPI(preferences, category) {\r\n      const apiUrl = 'http://localhost:8082/api/ai/miniprogram/recommend';\r\n      \r\n      const response = await uni.request({\r\n        url: apiUrl,\r\n        method: 'POST',\r\n        header: {\r\n          'Content-Type': 'application/json'\r\n        },\r\n        data: {\r\n          userId: this.userId,\r\n          preferences: preferences,\r\n          category: category\r\n        }\r\n      });\r\n\r\n      if (response.statusCode === 200 && response.data.code === 200) {\r\n        return response.data.data;\r\n      } else {\r\n        throw new Error('API调用失败');\r\n      }\r\n    },\r\n\r\n    copyResult() {\r\n      if (!this.recommendationResult) return;\r\n      \r\n      uni.setClipboardData({\r\n        data: this.recommendationResult,\r\n        success: () => {\r\n          uni.showToast({\r\n            title: '复制成功',\r\n            icon: 'success'\r\n          });\r\n        },\r\n        fail: () => {\r\n          uni.showToast({\r\n            title: '复制失败',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    shareResult() {\r\n      if (!this.recommendationResult) return;\r\n      \r\n      uni.showActionSheet({\r\n        itemList: ['分享给朋友', '保存到相册'],\r\n        success: (res) => {\r\n          if (res.tapIndex === 0) {\r\n            // 分享功能\r\n            uni.showToast({\r\n              title: '分享功能开发中',\r\n              icon: 'none'\r\n            });\r\n          } else if (res.tapIndex === 1) {\r\n            // 保存功能\r\n            uni.showToast({\r\n              title: '保存功能开发中',\r\n              icon: 'none'\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    viewHistory(item) {\r\n      this.preferences = item.preferences;\r\n      this.recommendationResult = item.result;\r\n      \r\n      // 切换到对应的类别\r\n      const category = this.categories.find(cat => cat.name === item.categoryName);\r\n      if (category) {\r\n        this.selectedCategory = category.key;\r\n      }\r\n    },\r\n\r\n    getCurrentTime() {\r\n      const now = new Date();\r\n      return `${now.getMonth() + 1}/${now.getDate()} ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.header {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 30rpx;\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(10rpx);\r\n}\r\n\r\n.header-icon {\r\n  font-size: 48rpx;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.header-info {\r\n  flex: 1;\r\n}\r\n\r\n.header-title {\r\n  font-size: 36rpx;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  display: block;\r\n}\r\n\r\n.header-desc {\r\n  font-size: 24rpx;\r\n  color: #6c757d;\r\n  margin-top: 8rpx;\r\n  display: block;\r\n}\r\n\r\n.category-selector {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(10rpx);\r\n  padding: 30rpx 0;\r\n}\r\n\r\n.selector-title {\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  margin: 0 30rpx 20rpx;\r\n  display: block;\r\n}\r\n\r\n.category-scroll {\r\n  white-space: nowrap;\r\n}\r\n\r\n.category-list {\r\n  display: inline-flex;\r\n  padding: 0 30rpx;\r\n  gap: 20rpx;\r\n}\r\n\r\n.category-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 20rpx;\r\n  min-width: 120rpx;\r\n  border-radius: 16rpx;\r\n  background: rgba(255, 255, 255, 0.8);\r\n  border: 2rpx solid transparent;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.category-item.active {\r\n  background: rgba(102, 126, 234, 0.1);\r\n  border-color: #667eea;\r\n  transform: scale(1.05);\r\n}\r\n\r\n.category-icon {\r\n  font-size: 32rpx;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.category-name {\r\n  font-size: 24rpx;\r\n  color: #495057;\r\n  font-weight: 500;\r\n  white-space: nowrap;\r\n}\r\n\r\n.category-item.active .category-name {\r\n  color: #667eea;\r\n  font-weight: bold;\r\n}\r\n\r\n.preference-section {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(10rpx);\r\n  margin: 20rpx;\r\n  border-radius: 16rpx;\r\n  padding: 30rpx;\r\n}\r\n\r\n.section-title {\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  margin-bottom: 20rpx;\r\n  display: block;\r\n}\r\n\r\n.preference-input {\r\n  width: 100%;\r\n  min-height: 200rpx;\r\n  padding: 20rpx;\r\n  border: 1rpx solid #e9ecef;\r\n  border-radius: 12rpx;\r\n  font-size: 28rpx;\r\n  line-height: 1.5;\r\n  background: #f8f9fa;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.char-count {\r\n  text-align: right;\r\n  font-size: 24rpx;\r\n  color: #6c757d;\r\n  margin-top: 10rpx;\r\n}\r\n\r\n.quick-tags {\r\n  margin-top: 30rpx;\r\n}\r\n\r\n.tags-title {\r\n  font-size: 24rpx;\r\n  color: #6c757d;\r\n  margin-bottom: 16rpx;\r\n  display: block;\r\n}\r\n\r\n.tags-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 12rpx;\r\n}\r\n\r\n.tag-item {\r\n  padding: 12rpx 20rpx;\r\n  background: #e9ecef;\r\n  border-radius: 20rpx;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.tag-item:active {\r\n  background: #667eea;\r\n  transform: scale(0.95);\r\n}\r\n\r\n.tag-item:active .tag-text {\r\n  color: #fff;\r\n}\r\n\r\n.tag-text {\r\n  font-size: 24rpx;\r\n  color: #495057;\r\n}\r\n\r\n.recommend-section {\r\n  padding: 30rpx;\r\n}\r\n\r\n.recommend-btn {\r\n  width: 100%;\r\n  height: 100rpx;\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 50rpx;\r\n  font-size: 32rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 16rpx;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.recommend-btn.disabled {\r\n  background: #ccc;\r\n  color: #999;\r\n  box-shadow: none;\r\n}\r\n\r\n.recommend-btn:not(.disabled):active {\r\n  transform: scale(0.98);\r\n}\r\n\r\n.result-section {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(10rpx);\r\n  margin: 0 20rpx 30rpx;\r\n  border-radius: 16rpx;\r\n  padding: 30rpx;\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.result-title {\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n}\r\n\r\n.result-actions {\r\n  display: flex;\r\n  gap: 16rpx;\r\n}\r\n\r\n.action-btn {\r\n  padding: 12rpx 20rpx;\r\n  border-radius: 20rpx;\r\n  font-size: 24rpx;\r\n  border: none;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-btn.refresh {\r\n  background: #ffc107;\r\n  color: #333;\r\n}\r\n\r\n.recommending-animation {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 60rpx 0;\r\n}\r\n\r\n.loading-circle {\r\n  position: relative;\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.circle-dot {\r\n  position: absolute;\r\n  width: 16rpx;\r\n  height: 16rpx;\r\n  border-radius: 50%;\r\n  background: #667eea;\r\n  animation: circleRotate 1.2s linear infinite;\r\n}\r\n\r\n.circle-dot:nth-child(1) { top: 0; left: 50%; margin-left: -8rpx; animation-delay: 0s; }\r\n.circle-dot:nth-child(2) { top: 50%; right: 0; margin-top: -8rpx; animation-delay: 0.3s; }\r\n.circle-dot:nth-child(3) { bottom: 0; left: 50%; margin-left: -8rpx; animation-delay: 0.6s; }\r\n.circle-dot:nth-child(4) { top: 50%; left: 0; margin-top: -8rpx; animation-delay: 0.9s; }\r\n\r\n@keyframes circleRotate {\r\n  0% { opacity: 1; transform: scale(1); }\r\n  50% { opacity: 0.3; transform: scale(0.5); }\r\n  100% { opacity: 1; transform: scale(1); }\r\n}\r\n\r\n.recommending-text {\r\n  font-size: 28rpx;\r\n  color: #6c757d;\r\n}\r\n\r\n.result-content {\r\n  border: 1rpx solid #e9ecef;\r\n  border-radius: 12rpx;\r\n  padding: 30rpx;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.result-text {\r\n  font-size: 28rpx;\r\n  line-height: 1.8;\r\n  color: #333;\r\n  white-space: pre-wrap;\r\n  display: block;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.result-footer {\r\n  display: flex;\r\n  gap: 16rpx;\r\n}\r\n\r\n.footer-btn {\r\n  flex: 1;\r\n  height: 70rpx;\r\n  border: none;\r\n  border-radius: 35rpx;\r\n  font-size: 26rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.footer-btn.copy {\r\n  background: #28a745;\r\n  color: #fff;\r\n}\r\n\r\n.footer-btn.share {\r\n  background: #17a2b8;\r\n  color: #fff;\r\n}\r\n\r\n.footer-btn:active {\r\n  transform: scale(0.95);\r\n}\r\n\r\n.history-section {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(10rpx);\r\n  margin: 0 20rpx 30rpx;\r\n  border-radius: 16rpx;\r\n  padding: 30rpx;\r\n}\r\n\r\n.history-title {\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  margin-bottom: 20rpx;\r\n  display: block;\r\n}\r\n\r\n.history-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16rpx;\r\n}\r\n\r\n.history-item {\r\n  padding: 20rpx;\r\n  background: #f8f9fa;\r\n  border-radius: 12rpx;\r\n  border: 1rpx solid #e9ecef;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.history-item:active {\r\n  background: #e9ecef;\r\n  transform: scale(0.98);\r\n}\r\n\r\n.history-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12rpx;\r\n}\r\n\r\n.history-category {\r\n  font-size: 24rpx;\r\n  color: #667eea;\r\n  font-weight: bold;\r\n}\r\n\r\n.history-time {\r\n  font-size: 20rpx;\r\n  color: #6c757d;\r\n}\r\n\r\n.history-preference {\r\n  font-size: 26rpx;\r\n  color: #495057;\r\n  line-height: 1.4;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/study/studyuProject/study-project/s-sai/pages/ai-recommend/ai-recommend.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA0HA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,aAAa;AAAA,MACb,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,MAChB,QAAQ;AAAA,MACR,kBAAkB;AAAA,MAClB,YAAY;AAAA,QACV;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM,CAAC,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,IAAI;AAAA,QACvD;AAAA,QACD;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,KAAK;AAAA,QACvD;AAAA,QACD;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,KAAK;AAAA,QACvD;AAAA,QACD;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,QACtD;AAAA,QACD;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,QACtD;AAAA,QACD;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,MAAM,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,QACvD;AAAA,MACD;AAAA,MACD,wBAAwB,CAAC;AAAA,IAC3B;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,kBAAkB;AAChB,aAAO,KAAK,WAAW,KAAK,SAAO,IAAI,QAAQ,KAAK,gBAAgB,KAAK,KAAK,WAAW,CAAC;AAAA,IAC5F;AAAA,EACD;AAAA,EACD,SAAS;AACP,SAAK,aAAY;AACjB,SAAK,YAAW;AAAA,EACjB;AAAA,EACD,SAAS;AAAA,IACP,eAAe;AACb,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9C,UAAI,YAAY,SAAS,IAAI;AAC3B,aAAK,SAAS,SAAS;AAAA,MACzB;AAAA,IACD;AAAA,IAED,cAAc;AACZ,YAAM,UAAUA,cAAG,MAAC,eAAe,kBAAkB,KAAK,CAAA;AAC1D,WAAK,yBAAyB,QAAQ,MAAM,GAAG,CAAC;AAAA,IACjD;AAAA,IAED,YAAY,gBAAgB;AAC1B,YAAM,UAAUA,cAAG,MAAC,eAAe,kBAAkB,KAAK,CAAA;AAC1D,YAAM,UAAU;AAAA,QACd,cAAc,KAAK,gBAAgB;AAAA,QACnC,cAAc,KAAK,gBAAgB;AAAA,QACnC,aAAa,KAAK;AAAA,QAClB,QAAQ;AAAA,QACR,MAAM,KAAK,eAAe;AAAA;AAG5B,cAAQ,QAAQ,OAAO;AACvB,UAAI,QAAQ,SAAS,IAAI;AACvB,gBAAQ,OAAO,EAAE;AAAA,MACnB;AAEAA,oBAAAA,MAAI,eAAe,oBAAoB,OAAO;AAC9C,WAAK,YAAW;AAAA,IACjB;AAAA,IAED,eAAe,aAAa;AAC1B,WAAK,mBAAmB;AACxB,WAAK,cAAc;AACnB,WAAK,uBAAuB;AAAA,IAC7B;AAAA,IAED,OAAO,KAAK;AACV,UAAI,KAAK,aAAa;AACpB,aAAK,eAAe,IAAI,GAAG;AAAA,aACtB;AACL,aAAK,cAAc;AAAA,MACrB;AAAA,IACD;AAAA,IAED,MAAM,oBAAoB;AACxB,UAAI,CAAC,KAAK,YAAY,KAAI,KAAM,KAAK;AAAgB;AAErD,WAAK,iBAAiB;AACtB,WAAK,uBAAuB;AAE5B,UAAI;AACF,cAAM,WAAW,MAAM,KAAK,iBAAiB,KAAK,YAAY,QAAQ,KAAK,gBAAgB,IAAI;AAE/F,YAAI,YAAY,SAAS,SAAS;AAChC,eAAK,uBAAuB,SAAS,YAAY;AACjD,eAAK,YAAY,KAAK,oBAAoB;AAAA,eACrC;AACL,eAAK,uBAAuB;AAAA,QAC9B;AAAA,MACA,SAAO,OAAO;AACd,aAAK,uBAAuB;AAC5BA,sBAAc,MAAA,MAAA,SAAA,8CAAA,YAAY,KAAK;AAAA,MACjC;AAEA,WAAK,iBAAiB;AAAA,IACvB;AAAA,IAED,MAAM,iBAAiB,aAAa,UAAU;AAC5C,YAAM,SAAS;AAEf,YAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,QACjC,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,gBAAgB;AAAA,QACjB;AAAA,QACD,MAAM;AAAA,UACJ,QAAQ,KAAK;AAAA,UACb;AAAA,UACA;AAAA,QACF;AAAA,MACF,CAAC;AAED,UAAI,SAAS,eAAe,OAAO,SAAS,KAAK,SAAS,KAAK;AAC7D,eAAO,SAAS,KAAK;AAAA,aAChB;AACL,cAAM,IAAI,MAAM,SAAS;AAAA,MAC3B;AAAA,IACD;AAAA,IAED,aAAa;AACX,UAAI,CAAC,KAAK;AAAsB;AAEhCA,oBAAAA,MAAI,iBAAiB;AAAA,QACnB,MAAM,KAAK;AAAA,QACX,SAAS,MAAM;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACF;AAAA,QACD,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,cAAc;AACZ,UAAI,CAAC,KAAK;AAAsB;AAEhCA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,SAAS,OAAO;AAAA,QAC3B,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,aAAa,GAAG;AAEtBA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,qBACQ,IAAI,aAAa,GAAG;AAE7BA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,YAAY,MAAM;AAChB,WAAK,cAAc,KAAK;AACxB,WAAK,uBAAuB,KAAK;AAGjC,YAAM,WAAW,KAAK,WAAW,KAAK,SAAO,IAAI,SAAS,KAAK,YAAY;AAC3E,UAAI,UAAU;AACZ,aAAK,mBAAmB,SAAS;AAAA,MACnC;AAAA,IACD;AAAA,IAED,iBAAiB;AACf,YAAM,MAAM,oBAAI;AAChB,aAAO,GAAG,IAAI,SAAW,IAAE,CAAC,IAAI,IAAI,QAAS,CAAA,IAAI,IAAI,SAAU,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG,CAAC,IAAI,IAAI,WAAU,EAAG,SAAQ,EAAG,SAAS,GAAG,GAAG,CAAC;AAAA,IAC7I;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChVA,GAAG,WAAW,eAAe;"}
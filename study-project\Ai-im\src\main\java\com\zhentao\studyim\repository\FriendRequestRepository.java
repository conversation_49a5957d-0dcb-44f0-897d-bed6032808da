package com.zhentao.studyim.repository;

import com.zhentao.studyim.entity.FriendRequest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 好友申请Repository接口
 */
@Repository
public interface FriendRequestRepository extends JpaRepository<FriendRequest, Long> {

    /**
     * 查找用户收到的好友申请
     * @param toUserId 被申请人用户ID
     * @param status 申请状态
     * @return 好友申请列表
     */
    List<FriendRequest> findByToUserIdAndStatus(Long toUserId, FriendRequest.RequestStatus status);

    /**
     * 查找用户发出的好友申请
     * @param fromUserId 申请人用户ID
     * @param status 申请状态
     * @return 好友申请列表
     */
    List<FriendRequest> findByFromUserIdAndStatus(Long fromUserId, FriendRequest.RequestStatus status);

    /**
     * 查找两个用户之间的好友申请
     * @param fromUserId 申请人用户ID
     * @param toUserId 被申请人用户ID
     * @return 好友申请
     */
    Optional<FriendRequest> findByFromUserIdAndToUserId(Long fromUserId, Long toUserId);

    /**
     * 检查是否存在待处理的好友申请
     * @param fromUserId 申请人用户ID
     * @param toUserId 被申请人用户ID
     * @param status 申请状态
     * @return 是否存在申请
     */
    boolean existsByFromUserIdAndToUserIdAndStatus(Long fromUserId, Long toUserId, FriendRequest.RequestStatus status);

    /**
     * 查找用户收到的待处理申请数量
     * @param toUserId 被申请人用户ID
     * @param status 申请状态
     * @return 申请数量
     */
    long countByToUserIdAndStatus(Long toUserId, FriendRequest.RequestStatus status);

    /**
     * 查找过期的好友申请
     * @param expireTime 过期时间
     * @param status 当前状态
     * @return 过期的申请列表
     */
    @Query("SELECT fr FROM FriendRequest fr WHERE fr.createTime < :expireTime AND fr.status = :status")
    List<FriendRequest> findExpiredRequests(@Param("expireTime") LocalDateTime expireTime, @Param("status") FriendRequest.RequestStatus status);

    /**
     * 删除两个用户之间的所有申请记录
     * @param fromUserId 申请人用户ID
     * @param toUserId 被申请人用户ID
     */
    void deleteByFromUserIdAndToUserId(Long fromUserId, Long toUserId);
}

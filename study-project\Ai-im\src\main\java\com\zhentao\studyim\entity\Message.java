package com.zhentao.studyim.entity;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 消息实体类
 */
@Data
@Entity
@Table(name = "message")
public class Message {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "from_user_id", nullable = false)
    private Long fromUserId;                  // 发送者ID

    @Column(name = "to_user_id", nullable = false)
    private Long toUserId;                    // 接收者ID

    @Column(nullable = false, columnDefinition = "TEXT")
    private String content;                   // 消息内容

    @Enumerated(EnumType.STRING)
    private MessageType type = MessageType.TEXT;  // 消息类型

    @Enumerated(EnumType.STRING)
    private MessageStatus status = MessageStatus.SENT;  // 消息状态

    @Column(name = "send_time")
    private LocalDateTime sendTime = LocalDateTime.now();  // 发送时间

    @Column(name = "read_time")
    private LocalDateTime readTime;           // 阅读时间

    /**
     * 消息类型枚举
     */
    public enum MessageType {
        TEXT,       // 文本消息
        IMAGE,      // 图片消息
        FILE,       // 文件消息
        SYSTEM      // 系统消息
    }

    /**
     * 消息状态枚举
     */
    public enum MessageStatus {
        SENT,       // 已发送
        DELIVERED,  // 已送达
        READ        // 已读
    }
}
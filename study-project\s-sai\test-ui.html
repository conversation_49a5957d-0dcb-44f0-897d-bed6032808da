<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>S-SAI 美化效果预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            padding-bottom: 80px;
        }
        
        .container {
            max-width: 375px;
            margin: 0 auto;
            animation: fadeIn 0.8s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 15px;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        
        .user-info h3 {
            margin: 0;
            color: #2c3e50;
            font-size: 18px;
        }
        
        .user-info p {
            margin: 5px 0 0 0;
            color: #6c757d;
            font-size: 14px;
        }
        
        .functions-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin: 20px 0;
        }
        
        .function-item {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 20px 15px;
            text-align: center;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .function-item:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }
        
        .function-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin: 0 auto 10px;
            color: white;
        }
        
        .icon-knowledge { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .icon-search { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .icon-writing { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .icon-translate { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        .icon-emotion { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
        .icon-recommend { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
        
        .function-name {
            font-size: 14px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .function-desc {
            font-size: 12px;
            color: #6c757d;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-around;
            align-items: center;
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #7A7E83;
            font-size: 12px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .nav-item.active {
            color: #2c3e50;
            transform: scale(1.1);
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 3px;
        }
        
        .chat-demo {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 25px;
            padding: 15px 30px;
            border: none;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        
        .btn-primary:hover {
            transform: scale(0.98);
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 用户信息卡片 -->
        <div class="card">
            <div class="header">
                <div class="avatar">A</div>
                <div class="user-info">
                    <h3>欢迎回来</h3>
                    <p>AI学习者</p>
                </div>
            </div>
        </div>
        
        <!-- AI对话卡片 -->
        <div class="chat-demo">
            <h3 style="color: #2c3e50; margin-bottom: 15px;">🤖 AI智能对话</h3>
            <p style="color: #6c757d; margin-bottom: 20px;">开启AI模式与AI对话，语音交互，智能回答各种问题</p>
            <button class="btn-primary">开始对话</button>
        </div>
        
        <!-- 功能网格 -->
        <div class="card">
            <h3 style="color: #2c3e50; margin-bottom: 15px;">⭐ 智能功能</h3>
            <div class="functions-grid">
                <div class="function-item">
                    <div class="function-icon icon-knowledge">📚</div>
                    <div class="function-name">知识问答</div>
                    <div class="function-desc">AI百科全书</div>
                </div>
                <div class="function-item">
                    <div class="function-icon icon-search">🔍</div>
                    <div class="function-name">信息查询</div>
                    <div class="function-desc">天气·电话·资讯</div>
                </div>
                <div class="function-item">
                    <div class="function-icon icon-writing">✍️</div>
                    <div class="function-name">文本生成</div>
                    <div class="function-desc">作文·故事·诗歌</div>
                </div>
                <div class="function-item">
                    <div class="function-icon icon-translate">🌐</div>
                    <div class="function-name">语言翻译</div>
                    <div class="function-desc">多语言互译</div>
                </div>
                <div class="function-item">
                    <div class="function-icon icon-emotion">💝</div>
                    <div class="function-name">情感陪伴</div>
                    <div class="function-desc">情感识别回应</div>
                </div>
                <div class="function-item">
                    <div class="function-icon icon-recommend">🎯</div>
                    <div class="function-name">智能推荐</div>
                    <div class="function-desc">个性化内容</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部导航栏 -->
    <div class="bottom-nav">
        <div class="nav-item active">
            <div class="nav-icon">🏠</div>
            <div>主页</div>
        </div>
        <div class="nav-item">
            <div class="nav-icon">📚</div>
            <div>知识</div>
        </div>
        <div class="nav-item">
            <div class="nav-icon">🔍</div>
            <div>查询</div>
        </div>
        <div class="nav-item">
            <div class="nav-icon">✍️</div>
            <div>写作</div>
        </div>
        <div class="nav-item">
            <div class="nav-icon">🌐</div>
            <div>翻译</div>
        </div>
    </div>
    
    <script>
        // 导航栏点击效果
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');
            });
        });
        
        // 功能卡片点击效果
        document.querySelectorAll('.function-item').forEach(item => {
            item.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html>

# 数据库脚本执行说明

## 概述

由于系统环境限制，无法直接执行MySQL命令，请按照以下步骤手动执行数据库脚本。

## 需要执行的SQL文件

1. **`ai_conversation_tables.sql`** - 主要的表结构文件
2. **`ai_conversation_sample_data.sql`** - 示例测试数据（可选）

## 执行方法

### 方法1：使用MySQL命令行客户端

```bash
# 连接到数据库
mysql -h 118.31.228.119 -P 3306 -u root -p

# 选择数据库
use tutor;

# 执行表结构脚本
source ai_conversation_tables.sql;

# 可选：插入示例数据
source ai_conversation_sample_data.sql;
```

### 方法2：使用Navicat等数据库管理工具

1. 打开Navicat或其他数据库管理工具
2. 连接到数据库：`118.31.228.119:3306`
3. 选择`tutor`数据库
4. 打开查询窗口
5. 复制`ai_conversation_tables.sql`的内容并执行
6. 可选：执行`ai_conversation_sample_data.sql`插入测试数据

### 方法3：使用phpMyAdmin

1. 访问phpMyAdmin管理界面
2. 选择`tutor`数据库
3. 点击"SQL"选项卡
4. 粘贴`ai_conversation_tables.sql`的内容
5. 点击"执行"

## 验证表创建

执行以下SQL验证表是否创建成功：

```sql
-- 查看所有AI相关表
SHOW TABLES LIKE 'ai_%';

-- 查看表结构
DESCRIBE ai_conversation;
DESCRIBE ai_message;
DESCRIBE ai_conversation_config;
DESCRIBE ai_usage_statistics;
DESCRIBE ai_feedback;

-- 查看示例数据（如果插入了）
SELECT COUNT(*) FROM ai_conversation;
SELECT COUNT(*) FROM ai_message;
```

## 预期结果

成功执行后应该看到以下5个新表：

1. `ai_conversation` - AI对话会话表
2. `ai_message` - AI消息记录表  
3. `ai_conversation_config` - AI对话配置表
4. `ai_usage_statistics` - AI使用统计表
5. `ai_feedback` - AI反馈表

## 服务启动

表创建完成后，可以启动ai-appservice服务：

```bash
# 进入服务目录
cd AiApp-service

# 启动服务
mvn spring-boot:run
```

## 测试API

服务启动后，可以通过以下方式测试：

1. **浏览器访问测试页面**：
   - `http://localhost:8082/miniprogram-ai-test.html`

2. **API健康检查**：
   - `http://localhost:8082/api/ai/test/health`

3. **小程序AI功能测试**：
   - 使用测试页面中的各个功能模块

## 常见问题

### 1. 表已存在错误
如果遇到"Table already exists"错误，说明表已经创建过了，可以：
- 跳过错误继续执行
- 或者先删除现有表再重新创建

### 2. 权限不足
如果遇到权限错误，请确保：
- 使用的数据库用户有CREATE、INSERT权限
- 数据库连接信息正确

### 3. 字符集问题
如果遇到中文乱码，请确保：
- 数据库字符集为utf8mb4
- 连接时指定正确的字符集

## 下一步

表创建完成后，可以：

1. 启动ai-appservice服务
2. 使用测试页面验证功能
3. 开发小程序前端对接这些API
4. 根据实际使用情况优化表结构和索引

## 联系支持

如果在执行过程中遇到问题，请检查：
- 网络连接是否正常
- 数据库服务是否运行
- 用户权限是否足够
- SQL语法是否正确

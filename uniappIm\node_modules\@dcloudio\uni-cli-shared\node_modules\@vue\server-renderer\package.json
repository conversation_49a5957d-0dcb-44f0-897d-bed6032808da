{"name": "@vue/server-renderer", "version": "3.2.47", "description": "@vue/server-renderer", "main": "index.js", "module": "dist/server-renderer.esm-bundler.js", "types": "dist/server-renderer.d.ts", "files": ["index.js", "dist"], "buildOptions": {"name": "VueServerR<PERSON><PERSON>", "formats": ["esm-bundler", "esm-browser", "cjs"]}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/server-renderer"}, "keywords": ["vue"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/core/issues"}, "homepage": "https://github.com/vuejs/core/tree/main/packages/server-renderer#readme", "peerDependencies": {"vue": "3.2.47"}, "dependencies": {"@vue/shared": "3.2.47", "@vue/compiler-ssr": "3.2.47"}}
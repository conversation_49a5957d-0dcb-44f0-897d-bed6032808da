"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      preferences: "",
      recommendationResult: "",
      isRecommending: false,
      userId: 123,
      selectedCategory: "books",
      categories: [
        {
          key: "books",
          name: "书籍",
          icon: "📚",
          placeholder: "请描述您喜欢的书籍类型、作者、主题等...\n例如：我喜欢科幻小说，特别是关于人工智能的",
          tags: ["科幻", "悬疑", "历史", "传记", "心理学", "编程", "文学", "哲学"]
        },
        {
          key: "movies",
          name: "电影",
          icon: "🎬",
          placeholder: "请描述您喜欢的电影类型、导演、演员等...\n例如：我喜欢悬疑片和科幻片，特别是诺兰的作品",
          tags: ["动作", "喜剧", "爱情", "科幻", "悬疑", "恐怖", "动画", "纪录片"]
        },
        {
          key: "music",
          name: "音乐",
          icon: "🎵",
          placeholder: "请描述您喜欢的音乐风格、歌手、乐器等...\n例如：我喜欢流行音乐和古典音乐，特别是钢琴曲",
          tags: ["流行", "摇滚", "古典", "爵士", "电子", "民谣", "说唱", "轻音乐"]
        },
        {
          key: "food",
          name: "美食",
          icon: "🍽️",
          placeholder: "请描述您喜欢的菜系、口味、食材等...\n例如：我喜欢川菜和日料，偏爱辣味和清淡的食物",
          tags: ["川菜", "粤菜", "日料", "韩料", "西餐", "甜品", "素食", "海鲜"]
        },
        {
          key: "travel",
          name: "旅行",
          icon: "✈️",
          placeholder: "请描述您喜欢的旅行方式、目的地类型等...\n例如：我喜欢自然风光和历史文化，偏爱安静的小城",
          tags: ["自然", "历史", "文化", "海滨", "山区", "城市", "乡村", "异国"]
        },
        {
          key: "courses",
          name: "课程",
          icon: "🎓",
          placeholder: "请描述您想学习的技能、知识领域等...\n例如：我想学习编程和设计，特别是前端开发",
          tags: ["编程", "设计", "语言", "音乐", "绘画", "摄影", "写作", "运动"]
        }
      ],
      historyRecommendations: []
    };
  },
  computed: {
    currentCategory() {
      return this.categories.find((cat) => cat.key === this.selectedCategory) || this.categories[0];
    }
  },
  onLoad() {
    this.loadUserInfo();
    this.loadHistory();
  },
  methods: {
    loadUserInfo() {
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      if (userInfo && userInfo.id) {
        this.userId = userInfo.id;
      }
    },
    loadHistory() {
      const history = common_vendor.index.getStorageSync("recommendHistory") || [];
      this.historyRecommendations = history.slice(0, 5);
    },
    saveHistory(recommendation) {
      const history = common_vendor.index.getStorageSync("recommendHistory") || [];
      const newItem = {
        categoryIcon: this.currentCategory.icon,
        categoryName: this.currentCategory.name,
        preferences: this.preferences,
        result: recommendation,
        time: this.getCurrentTime()
      };
      history.unshift(newItem);
      if (history.length > 20) {
        history.splice(20);
      }
      common_vendor.index.setStorageSync("recommendHistory", history);
      this.loadHistory();
    },
    selectCategory(categoryKey) {
      this.selectedCategory = categoryKey;
      this.preferences = "";
      this.recommendationResult = "";
    },
    addTag(tag) {
      if (this.preferences) {
        this.preferences += `，${tag}`;
      } else {
        this.preferences = tag;
      }
    },
    async getRecommendation() {
      if (!this.preferences.trim() || this.isRecommending)
        return;
      this.isRecommending = true;
      this.recommendationResult = "";
      try {
        const response = await this.callRecommendAPI(this.preferences.trim(), this.currentCategory.name);
        if (response && response.success) {
          this.recommendationResult = response.response || "抱歉，暂时无法生成推荐，请重试。";
          this.saveHistory(this.recommendationResult);
        } else {
          this.recommendationResult = "推荐服务暂时不可用，请稍后再试。";
        }
      } catch (error) {
        this.recommendationResult = "网络连接失败，请检查网络后重试。";
        common_vendor.index.__f__("error", "at pages/ai-recommend/ai-recommend.vue:250", "API调用失败:", error);
      }
      this.isRecommending = false;
    },
    async callRecommendAPI(preferences, category) {
      const apiUrl = "http://localhost:8082/api/ai/miniprogram/recommend";
      const response = await common_vendor.index.request({
        url: apiUrl,
        method: "POST",
        header: {
          "Content-Type": "application/json"
        },
        data: {
          userId: this.userId,
          preferences,
          category
        }
      });
      if (response.statusCode === 200 && response.data.code === 200) {
        return response.data.data;
      } else {
        throw new Error("API调用失败");
      }
    },
    copyResult() {
      if (!this.recommendationResult)
        return;
      common_vendor.index.setClipboardData({
        data: this.recommendationResult,
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "复制失败",
            icon: "none"
          });
        }
      });
    },
    shareResult() {
      if (!this.recommendationResult)
        return;
      common_vendor.index.showActionSheet({
        itemList: ["分享给朋友", "保存到相册"],
        success: (res) => {
          if (res.tapIndex === 0) {
            common_vendor.index.showToast({
              title: "分享功能开发中",
              icon: "none"
            });
          } else if (res.tapIndex === 1) {
            common_vendor.index.showToast({
              title: "保存功能开发中",
              icon: "none"
            });
          }
        }
      });
    },
    viewHistory(item) {
      this.preferences = item.preferences;
      this.recommendationResult = item.result;
      const category = this.categories.find((cat) => cat.name === item.categoryName);
      if (category) {
        this.selectedCategory = category.key;
      }
    },
    getCurrentTime() {
      const now = /* @__PURE__ */ new Date();
      return `${now.getMonth() + 1}/${now.getDate()} ${now.getHours().toString().padStart(2, "0")}:${now.getMinutes().toString().padStart(2, "0")}`;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.categories, (category, index, i0) => {
      return {
        a: common_vendor.t(category.icon),
        b: common_vendor.t(category.name),
        c: index,
        d: $data.selectedCategory === category.key ? 1 : "",
        e: common_vendor.o(($event) => $options.selectCategory(category.key), index)
      };
    }),
    b: $options.currentCategory.placeholder,
    c: $data.preferences,
    d: common_vendor.o(($event) => $data.preferences = $event.detail.value),
    e: common_vendor.t($data.preferences.length),
    f: common_vendor.f($options.currentCategory.tags, (tag, index, i0) => {
      return {
        a: common_vendor.t(tag),
        b: index,
        c: common_vendor.o(($event) => $options.addTag(tag), index)
      };
    }),
    g: common_vendor.t($data.isRecommending ? "⏳" : "✨"),
    h: common_vendor.t($data.isRecommending ? "推荐中..." : "获取推荐"),
    i: !$data.preferences.trim() || $data.isRecommending ? 1 : "",
    j: common_vendor.o((...args) => $options.getRecommendation && $options.getRecommendation(...args)),
    k: $data.recommendationResult || $data.isRecommending
  }, $data.recommendationResult || $data.isRecommending ? common_vendor.e({
    l: common_vendor.t($options.currentCategory.icon),
    m: $data.recommendationResult && !$data.isRecommending
  }, $data.recommendationResult && !$data.isRecommending ? {
    n: common_vendor.o((...args) => $options.getRecommendation && $options.getRecommendation(...args))
  } : {}, {
    o: $data.isRecommending
  }, $data.isRecommending ? {} : {}, {
    p: $data.recommendationResult
  }, $data.recommendationResult ? {
    q: common_vendor.t($data.recommendationResult),
    r: common_vendor.o((...args) => $options.copyResult && $options.copyResult(...args)),
    s: common_vendor.o((...args) => $options.shareResult && $options.shareResult(...args))
  } : {}) : {}, {
    t: !$data.recommendationResult && !$data.isRecommending && $data.historyRecommendations.length > 0
  }, !$data.recommendationResult && !$data.isRecommending && $data.historyRecommendations.length > 0 ? {
    v: common_vendor.f($data.historyRecommendations, (item, index, i0) => {
      return {
        a: common_vendor.t(item.categoryIcon),
        b: common_vendor.t(item.categoryName),
        c: common_vendor.t(item.time),
        d: common_vendor.t(item.preferences),
        e: index,
        f: common_vendor.o(($event) => $options.viewHistory(item), index)
      };
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/ai-recommend/ai-recommend.js.map

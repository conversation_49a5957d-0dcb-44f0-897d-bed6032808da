package cn.zhentao.service;

import cn.zhentao.util.DashScopeAiUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * AI对话服务
 * 对接小程序AI功能模块
 */
@Slf4j
public class AiConversationService {

    private DashScopeAiUtil dashScopeAiUtil;

    // 用户会话缓存
    private final Map<Long, Map<String, Object>> userSessions = new ConcurrentHashMap<>();

    /**
     * 知识问答 - AI百科全书
     */
    public DashScopeAiUtil.AiResponse knowledgeQA(Long userId, String question) {
        log.info("知识问答请求 - 用户ID: {}, 问题: {}", userId, question);
        
        String sessionId = getUserSessionId(userId, "knowledge");
        String enhancedPrompt = "作为一个知识渊博的AI助手，请详细回答以下问题：" + question;
        
        DashScopeAiUtil.AiResponse response = dashScopeAiUtil.callWithSession(enhancedPrompt, sessionId);
        updateUserSession(userId, "knowledge", response.getSessionId());
        
        return response;
    }

    /**
     * 知识问答 - 流式输出
     */
    public SseEmitter knowledgeQAStream(Long userId, String question) {
        log.info("知识问答流式请求 - 用户ID: {}, 问题: {}", userId, question);
        
        String sessionId = getUserSessionId(userId, "knowledge");
        String enhancedPrompt = "作为一个知识渊博的AI助手，请详细回答以下问题：" + question;
        
        return dashScopeAiUtil.streamCall(enhancedPrompt, sessionId);
    }

    /**
     * 信息查询 - 天气电话资讯
     */
    public DashScopeAiUtil.AiResponse informationQuery(Long userId, String query) {
        log.info("信息查询请求 - 用户ID: {}, 查询: {}", userId, query);
        
        String sessionId = getUserSessionId(userId, "info");
        String enhancedPrompt = "作为一个信息查询助手，请帮助查询以下信息（如果是天气查询，请说明需要具体城市；如果是电话查询，请提供相关建议）：" + query;
        
        DashScopeAiUtil.AiResponse response = dashScopeAiUtil.callWithSession(enhancedPrompt, sessionId);
        updateUserSession(userId, "info", response.getSessionId());
        
        return response;
    }

    /**
     * 信息查询 - 流式输出
     */
    public SseEmitter informationQueryStream(Long userId, String query) {
        log.info("信息查询流式请求 - 用户ID: {}, 查询: {}", userId, query);
        
        String sessionId = getUserSessionId(userId, "info");
        String enhancedPrompt = "作为一个信息查询助手，请帮助查询以下信息：" + query;
        
        return dashScopeAiUtil.streamCall(enhancedPrompt, sessionId);
    }

    /**
     * 文本生成 - 作文故事诗歌
     */
    public DashScopeAiUtil.AiResponse textGeneration(Long userId, String prompt, String type) {
        log.info("文本生成请求 - 用户ID: {}, 类型: {}, 提示: {}", userId, type, prompt);
        
        String sessionId = getUserSessionId(userId, "text_gen");
        String enhancedPrompt = getTextGenerationPrompt(type, prompt);
        
        DashScopeAiUtil.AiResponse response = dashScopeAiUtil.callWithSession(enhancedPrompt, sessionId);
        updateUserSession(userId, "text_gen", response.getSessionId());
        
        return response;
    }

    /**
     * 文本生成 - 流式输出
     */
    public SseEmitter textGenerationStream(Long userId, String prompt, String type) {
        log.info("文本生成流式请求 - 用户ID: {}, 类型: {}, 提示: {}", userId, type, prompt);
        
        String sessionId = getUserSessionId(userId, "text_gen");
        String enhancedPrompt = getTextGenerationPrompt(type, prompt);
        
        return dashScopeAiUtil.streamCall(enhancedPrompt, sessionId);
    }

    /**
     * 语言翻译 - 多语言互译
     */
    public DashScopeAiUtil.AiResponse languageTranslation(Long userId, String text, String fromLang, String toLang) {
        log.info("语言翻译请求 - 用户ID: {}, 从{}翻译到{}: {}", userId, fromLang, toLang, text);
        
        String sessionId = getUserSessionId(userId, "translation");
        String enhancedPrompt = String.format("请将以下%s文本翻译成%s，只返回翻译结果：%s", fromLang, toLang, text);
        
        DashScopeAiUtil.AiResponse response = dashScopeAiUtil.callWithSession(enhancedPrompt, sessionId);
        updateUserSession(userId, "translation", response.getSessionId());
        
        return response;
    }

    /**
     * 语言翻译 - 流式输出
     */
    public SseEmitter languageTranslationStream(Long userId, String text, String fromLang, String toLang) {
        log.info("语言翻译流式请求 - 用户ID: {}, 从{}翻译到{}: {}", userId, fromLang, toLang, text);
        
        String sessionId = getUserSessionId(userId, "translation");
        String enhancedPrompt = String.format("请将以下%s文本翻译成%s：%s", fromLang, toLang, text);
        
        return dashScopeAiUtil.streamCall(enhancedPrompt, sessionId);
    }

    /**
     * 情感陪伴 - 情感识别回应
     */
    public DashScopeAiUtil.AiResponse emotionalCompanion(Long userId, String message) {
        log.info("情感陪伴请求 - 用户ID: {}, 消息: {}", userId, message);
        
        String sessionId = getUserSessionId(userId, "emotion");
        String enhancedPrompt = "作为一个温暖贴心的AI伙伴，请识别用户的情感状态并给予适当的回应和安慰。用户说：" + message;
        
        DashScopeAiUtil.AiResponse response = dashScopeAiUtil.callWithSession(enhancedPrompt, sessionId);
        updateUserSession(userId, "emotion", response.getSessionId());
        
        return response;
    }

    /**
     * 情感陪伴 - 流式输出
     */
    public SseEmitter emotionalCompanionStream(Long userId, String message) {
        log.info("情感陪伴流式请求 - 用户ID: {}, 消息: {}", userId, message);
        
        String sessionId = getUserSessionId(userId, "emotion");
        String enhancedPrompt = "作为一个温暖贴心的AI伙伴，请识别用户的情感状态并给予适当的回应和安慰。用户说：" + message;
        
        return dashScopeAiUtil.streamCall(enhancedPrompt, sessionId);
    }

    /**
     * 智能推荐 - 个性化内容
     */
    public DashScopeAiUtil.AiResponse intelligentRecommendation(Long userId, String preferences, String category) {
        log.info("智能推荐请求 - 用户ID: {}, 偏好: {}, 类别: {}", userId, preferences, category);
        
        String sessionId = getUserSessionId(userId, "recommendation");
        String enhancedPrompt = String.format("根据用户偏好：%s，请为用户推荐%s相关的个性化内容，包括具体的推荐理由", preferences, category);
        
        DashScopeAiUtil.AiResponse response = dashScopeAiUtil.callWithSession(enhancedPrompt, sessionId);
        updateUserSession(userId, "recommendation", response.getSessionId());
        
        return response;
    }

    /**
     * 智能推荐 - 流式输出
     */
    public SseEmitter intelligentRecommendationStream(Long userId, String preferences, String category) {
        log.info("智能推荐流式请求 - 用户ID: {}, 偏好: {}, 类别: {}", userId, preferences, category);
        
        String sessionId = getUserSessionId(userId, "recommendation");
        String enhancedPrompt = String.format("根据用户偏好：%s，请为用户推荐%s相关的个性化内容", preferences, category);
        
        return dashScopeAiUtil.streamCall(enhancedPrompt, sessionId);
    }

    /**
     * 获取用户会话ID
     */
    private String getUserSessionId(Long userId, String type) {
        Map<String, Object> sessions = userSessions.computeIfAbsent(userId, k -> new ConcurrentHashMap<>());
        return (String) sessions.get(type);
    }

    /**
     * 更新用户会话ID
     */
    private void updateUserSession(Long userId, String type, String sessionId) {
        if (sessionId != null) {
            Map<String, Object> sessions = userSessions.computeIfAbsent(userId, k -> new ConcurrentHashMap<>());
            sessions.put(type, sessionId);
        }
    }

    /**
     * 获取文本生成提示词
     */
    private String getTextGenerationPrompt(String type, String prompt) {
        switch (type.toLowerCase()) {
            case "essay":
            case "作文":
                return "作为一个优秀的作文老师，请根据以下要求写一篇作文：" + prompt;
            case "story":
            case "故事":
                return "作为一个创意故事作家，请根据以下要求创作一个故事：" + prompt;
            case "poem":
            case "诗歌":
                return "作为一个诗人，请根据以下要求创作一首诗歌：" + prompt;
            default:
                return "请根据以下要求进行文本创作：" + prompt;
        }
    }

    /**
     * 清除用户会话
     */
    public void clearUserSessions(Long userId) {
        userSessions.remove(userId);
        log.info("已清除用户{}的所有会话", userId);
    }

    /**
     * 获取用户会话统计
     */
    public Map<String, Object> getUserSessionStats(Long userId) {
        Map<String, Object> sessions = userSessions.get(userId);
        Map<String, Object> stats = new HashMap<>();
        stats.put("userId", userId);
        stats.put("sessionCount", sessions != null ? sessions.size() : 0);
        stats.put("sessionTypes", sessions != null ? sessions.keySet() : Collections.emptySet());
        return stats;
    }
}

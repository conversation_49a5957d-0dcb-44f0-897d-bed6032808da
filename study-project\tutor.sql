/*
 Navicat Premium Dump SQL

 Source Server         : ai
 Source Server Type    : MySQL
 Source Server Version : 80100 (8.1.0)
 Source Host           : **************:3306
 Source Schema         : tutor

 Target Server Type    : MySQL
 Target Server Version : 80100 (8.1.0)
 File Encoding         : 65001

 Date: 29/07/2025 17:04:23
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for fact_info_log
-- ----------------------------
DROP TABLE IF EXISTS `fact_info_log`;
CREATE TABLE `fact_info_log`  (
  `id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `user_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '员工id',
  `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '姓名',
  `sex` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '性别',
  `age` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '年龄',
  `image_url` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '图片地址',
  `living_body` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '活体',
  `face3D` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '面部信息',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `deleted` int NULL DEFAULT 0 COMMENT '逻辑删除',
  `remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT '人脸打卡记录'
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of fact_info_log
-- ----------------------------
INSERT INTO `fact_info_log` VALUES ('1949999431743700994', NULL, '未知用户', '女', '23', 'http://**************:19000/test/aa551752-eaf0-4746-8fc9-bdc21d2b7bab.jpg', '正常', 'Face3DAngle{yaw=0.5452732, roll=-2.6407409, pitch=-8.863748, status=0}', '2025-07-29 09:04:27', 0, '人脸打卡记录');
INSERT INTO `fact_info_log` VALUES ('1950000822444986370', NULL, '未知用户', '女', '23', 'http://**************:19000/test/f8b352bc-735f-4249-98e9-8c4f12cbe9fd.jpg', '正常', 'Face3DAngle{yaw=0.5452732, roll=-2.6407409, pitch=-8.863748, status=0}', '2025-07-29 09:09:59', 0, '人脸打卡记录');
INSERT INTO `fact_info_log` VALUES ('1950001719740829697', NULL, '未知用户', '女', '23', 'http://**************:19000/test/00e0bcf9-b4e5-4c38-8a1d-a2dc84e0429a.jpg', '正常', 'Face3DAngle{yaw=0.5452732, roll=-2.6407409, pitch=-8.863748, status=0}', '2025-07-29 09:13:33', 0, '人脸打卡记录');
INSERT INTO `fact_info_log` VALUES ('1950001795754201090', NULL, '未知用户', '女', '23', 'http://**************:19000/test/e35c52bd-6d23-45e1-ac91-81194a702801.jpg', '正常', 'Face3DAngle{yaw=0.5452732, roll=-2.6407409, pitch=-8.863748, status=0}', '2025-07-29 09:13:52', 0, '人脸打卡记录');
INSERT INTO `fact_info_log` VALUES ('1950002635869097985', NULL, '未知用户', '女', '23', 'http://**************:19000/test/ec1e27d0-fd8f-4fbb-9d8a-a3ae08a1f045.jpg', '正常', 'Face3DAngle{yaw=0.5452732, roll=-2.6407409, pitch=-8.863748, status=0}', '2025-07-29 09:17:11', 0, '人脸打卡记录');
INSERT INTO `fact_info_log` VALUES ('1950004184481632257', '1950000774004969474', '孙杉杉', '女', '23', 'http://**************:19000/test/067be5d1-fa9f-43cd-b3ef-f75a8812f977.jpg', '正常', 'Face3DAngle{yaw=0.5452732, roll=-2.6407409, pitch=-8.863748, status=0}', '2025-07-29 09:23:21', 0, '人脸打卡记录');
INSERT INTO `fact_info_log` VALUES ('1950025889757290497', '1950000774004969474', '孙杉杉', '女', '23', 'http://**************:19000/test/fe66b422-f805-4b15-940f-4f1d197fd1b6.jpg', '正常', 'Face3DAngle{yaw=0.5452732, roll=-2.6407409, pitch=-8.863748, status=0}', '2025-07-29 10:49:34', 0, '人脸打卡记录');
INSERT INTO `fact_info_log` VALUES ('1950078959925174273', '1950000774004969474', '孙杉杉', '女', '23', 'http://**************:19000/test/46bdd6d8-00a9-4b36-9656-31b94094289d.jpg', '正常', 'Face3DAngle{yaw=0.5452732, roll=-2.6407409, pitch=-8.863748, status=0}', '2025-07-29 14:20:27', 0, '人脸打卡记录');
INSERT INTO `fact_info_log` VALUES ('1950102410331176961', NULL, '未知用户', '男', '22', 'http://**************:19000/test/19861a96-5cac-44e5-92a3-eee75bbb2ce5.jpg', '正常', 'Face3DAngle{yaw=0.5702109, roll=0.41834068, pitch=-7.0835853, status=0}', '2025-07-29 15:53:40', 0, '人脸打卡记录');
INSERT INTO `fact_info_log` VALUES ('1950102996069924866', '1950102349408911361', '周明震', '男', '22', 'http://**************:19000/test/40932267-bedf-48ae-9ea0-75b5e85ed957.jpg', '正常', 'Face3DAngle{yaw=0.5702109, roll=0.41834068, pitch=-7.0835853, status=0}', '2025-07-29 15:56:00', 0, '人脸打卡记录');
INSERT INTO `fact_info_log` VALUES ('1950112979331334146', '1950102349408911361', '周明震', '男', '23', 'http://**************:19000/test/4b4fd61f-25cf-46a1-b67d-a42e678732d9.jpg', '正常', 'Face3DAngle{yaw=0.020931214, roll=-0.17366101, pitch=-7.973135, status=0}', '2025-07-29 16:35:40', 0, '人脸打卡记录');
INSERT INTO `fact_info_log` VALUES ('1950117308603842562', '1950102349408911361', '周明震', '男', '23', 'http://**************:19000/test/acca998e-17ff-430d-830b-8ed18cd5f70e.jpg', '正常', 'Face3DAngle{yaw=0.020931214, roll=-0.17366101, pitch=-7.973135, status=0}', '2025-07-29 16:52:52', 0, '人脸打卡记录');

-- ----------------------------
-- Table structure for friend_request
-- ----------------------------
DROP TABLE IF EXISTS `friend_request`;
CREATE TABLE `friend_request`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_time` datetime(6) NULL DEFAULT NULL,
  `from_user_id` bigint NOT NULL,
  `message` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `status` enum('PENDING','ACCEPTED','REJECTED','EXPIRED') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `to_user_id` bigint NOT NULL,
  `update_time` datetime(6) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of friend_request
-- ----------------------------
INSERT INTO `friend_request` VALUES (1, '2025-07-29 09:40:13.919786', 3, '我是Y', 'ACCEPTED', 2, '2025-07-29 09:40:41.360499');

-- ----------------------------
-- Table structure for friendship
-- ----------------------------
DROP TABLE IF EXISTS `friendship`;
CREATE TABLE `friendship`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_time` datetime(6) NULL DEFAULT NULL,
  `friend_id` bigint NOT NULL,
  `status` enum('ACTIVE','BLOCKED','DELETED') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_time` datetime(6) NULL DEFAULT NULL,
  `user_id` bigint NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of friendship
-- ----------------------------
INSERT INTO `friendship` VALUES (1, '2025-07-29 09:40:41.201993', 2, 'ACTIVE', '2025-07-29 09:40:41.201993', 3);
INSERT INTO `friendship` VALUES (2, '2025-07-29 09:40:41.280805', 3, 'ACTIVE', '2025-07-29 09:40:41.280805', 2);

-- ----------------------------
-- Table structure for menu
-- ----------------------------
DROP TABLE IF EXISTS `menu`;
CREATE TABLE `menu`  (
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单名称',
  `parent_id` bigint NULL DEFAULT NULL COMMENT '父菜单ID',
  `order_num` int NULL DEFAULT NULL COMMENT '显示顺序',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '路由参数',
  `is_frame` int NULL DEFAULT NULL COMMENT '是否为外链（0是 1否）',
  `is_cache` int NULL DEFAULT NULL COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单图标',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜单权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of menu
-- ----------------------------

-- ----------------------------
-- Table structure for message
-- ----------------------------
DROP TABLE IF EXISTS `message`;
CREATE TABLE `message`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `from_user_id` bigint NOT NULL,
  `read_time` datetime(6) NULL DEFAULT NULL,
  `send_time` datetime(6) NULL DEFAULT NULL,
  `status` enum('SENT','DELIVERED','READ') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `to_user_id` bigint NOT NULL,
  `type` enum('TEXT','IMAGE','FILE','SYSTEM') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of message
-- ----------------------------
INSERT INTO `message` VALUES (1, '你好', 2, NULL, '2025-07-29 09:43:36.700747', 'SENT', 3, 'TEXT');
INSERT INTO `message` VALUES (2, '你好', 2, NULL, '2025-07-29 09:43:38.176471', 'SENT', 3, 'TEXT');
INSERT INTO `message` VALUES (3, '我在 您有什么需求嘛', 3, NULL, '2025-07-29 09:46:40.738900', 'SENT', 2, 'TEXT');
INSERT INTO `message` VALUES (4, '我在 您有什么需求嘛', 3, NULL, '2025-07-29 09:46:42.087291', 'SENT', 2, 'TEXT');
INSERT INTO `message` VALUES (5, '我要咨询一些问题', 3, NULL, '2025-07-29 09:59:41.499935', 'SENT', 2, 'TEXT');
INSERT INTO `message` VALUES (6, '我要咨询一些问题', 3, NULL, '2025-07-29 09:59:43.301414', 'SENT', 2, 'TEXT');
INSERT INTO `message` VALUES (7, '您好', 3, NULL, '2025-07-29 10:18:04.210884', 'SENT', 2, 'TEXT');
INSERT INTO `message` VALUES (8, '你好', 3, NULL, '2025-07-29 10:18:44.896412', 'SENT', 2, 'TEXT');
INSERT INTO `message` VALUES (9, '我在', 3, NULL, '2025-07-29 10:35:56.561270', 'SENT', 2, 'TEXT');

-- ----------------------------
-- Table structure for permission
-- ----------------------------
DROP TABLE IF EXISTS `permission`;
CREATE TABLE `permission`  (
  `permission_id` bigint NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `permission_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '权限标识（如 user:add）',
  `permission_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '权限名称（如添加用户）',
  `method` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '请求方法（GET/POST/PUT/DELETE）',
  `api_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'API路径（如 /api/users）',
  `menu_id` bigint NULL DEFAULT NULL COMMENT '关联菜单ID',
  PRIMARY KEY (`permission_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '权限定义表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of permission
-- ----------------------------

-- ----------------------------
-- Table structure for role
-- ----------------------------
DROP TABLE IF EXISTS `role`;
CREATE TABLE `role`  (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `role_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色权限字符串',
  `role_sort` int NOT NULL COMMENT '显示顺序',
  `data_scope` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `menu_check_strictly` tinyint NULL DEFAULT NULL COMMENT '菜单树选择项是否关联显示',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of role
-- ----------------------------

-- ----------------------------
-- Table structure for role_permission
-- ----------------------------
DROP TABLE IF EXISTS `role_permission`;
CREATE TABLE `role_permission`  (
  `role_id` bigint NULL DEFAULT NULL,
  `permission_id` bigint NULL DEFAULT NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of role_permission
-- ----------------------------

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `username` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户账号',
  `nickname` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户昵称',
  `user_type` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '00' COMMENT '用户类型(00系统用户)',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户邮箱',
  `phonenumber` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机号码',
  `sex` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '2' COMMENT '用户性别(0男 1女 2未知)',
  `avatar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像地址',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '密码',
  `status` int NULL DEFAULT 0 COMMENT '帐号状态(0正常 1停用)',
  `del_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志(0代表存在 2代表删除)',
  `login_date` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `user_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'OFFLINE' COMMENT '用户在线状态',
  `user` varbinary(255) NULL DEFAULT NULL COMMENT '用户信息',
  `face_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '人脸数据',
  `image_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '人脸图片路径',
  `deleted` int NULL DEFAULT NULL COMMENT '逻辑删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1950102349408911362 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user
-- ----------------------------
INSERT INTO `user` VALUES (2, 'xiaoxiao', 'YY', '00', NULL, NULL, '2', NULL, '123', 0, '0', '2025-07-29 10:36:23', NULL, '2025-07-28 21:21:55', NULL, '2025-07-29 01:23:37', NULL, 'ONLINE', NULL, NULL, NULL, 0);
INSERT INTO `user` VALUES (3, 'sunshuo', 'Y', '00', NULL, NULL, '2', NULL, '123', 0, '0', '2025-07-29 10:51:17', NULL, '2025-07-28 21:22:44', NULL, '2025-07-29 01:23:38', NULL, 'ONLINE', NULL, NULL, NULL, 0);
INSERT INTO `user` VALUES (1950000774004969474, '孙杉杉', NULL, '00', NULL, NULL, '2', NULL, NULL, 0, '0', NULL, NULL, NULL, NULL, '2025-07-29 01:22:58', 'sss1', 'OFFLINE', NULL, '{\"featureData\":[0,-128,-6,68,0,0,-96,65,118,-107,-117,60,-124,21,0,-67,14,64,-110,60,71,91,-51,60,-6,85,96,60,-114,-81,1,-66,103,-99,83,-67,-98,41,89,-69,5,117,71,-68,96,-1,-99,-67,100,-2,-100,-67,-124,37,-102,-68,54,-85,93,-67,106,127,-58,59,-49,-107,76,-67,62,36,38,-67,-116,-50,-125,-68,89,-111,-37,60,57,-118,-119,-67,-110,-48,-107,-68,118,-118,2,61,-46,-9,-32,61,-48,-109,-40,-67,122,-37,31,60,70,-12,77,61,44,127,59,60,-91,8,-77,60,-7,84,-27,-67,84,-77,66,61,-38,42,99,-68,-105,82,110,61,-12,-120,-48,61,-34,-52,47,62,-52,124,-95,-67,-64,40,12,-67,79,12,-99,-67,-44,4,108,61,-89,95,37,-69,-122,88,-120,58,-68,-99,40,60,-44,-91,-46,-69,27,86,-128,61,-85,-44,92,60,-114,-65,85,60,-126,-111,-115,58,23,-66,23,-68,94,-84,-122,61,34,77,37,59,-46,-19,-8,-67,50,-11,-75,-67,-68,54,-56,-67,-66,126,30,61,-70,-40,44,-67,105,-116,-104,-67,-2,118,108,61,30,-84,-79,-67,-59,-128,-24,-70,-60,-55,-119,-67,-12,69,-5,-69,-126,-128,-7,60,84,-101,98,61,-30,-21,60,61,63,-45,55,-67,33,-63,61,61,42,-48,35,61,-50,-85,79,-67,-76,116,-72,-68,49,-29,120,61,18,-63,-100,61,105,20,43,-67,-96,-126,-116,-67,-43,-48,-89,-68,-80,94,56,60,-39,-28,5,-67,101,-78,-125,61,119,-78,-121,60,95,95,-33,-68,-6,114,12,61,82,35,99,61,4,18,-110,-70,-16,-122,105,61,6,61,-92,-67,89,-6,5,-66,116,102,117,61,-87,-93,27,62,51,-106,-111,60,73,-90,10,62,5,90,-47,-68,29,-57,8,61,68,-72,-111,-67,-25,119,-17,60,-81,34,3,-66,-116,23,-60,60,-69,19,-122,61,7,-19,47,-67,46,98,74,-69,106,73,25,-67,15,-110,120,-67,54,76,-61,61,-109,-95,-110,-67,15,-94,-106,61,107,83,109,59,-94,-120,-37,61,85,-13,-57,61,-42,5,-55,-67,94,127,92,-67,-12,117,-75,-67,-30,97,70,61,89,-114,-98,-68,127,56,122,61,-62,55,-24,-67,-128,97,16,-66,-57,-44,-119,61,-107,-116,-53,-68,-59,125,-121,-67,64,122,-58,58,14,-87,-96,61,-98,21,-50,61,78,-56,-108,61,70,38,-62,61,101,34,36,62,-44,-90,-25,-67,-63,-49,117,-67,78,5,13,-67,-14,-78,125,-67,65,-13,-116,61,-110,0,-25,-67,-21,9,48,61,-80,96,-68,61,52,48,-106,61,-89,93,59,62,-74,103,-88,-69,117,-19,-58,61,-121,-72,53,-67,72,89,-104,61,-13,-69,-109,-67,96,-5,-64,-70,-55,-99,87,61,-39,100,124,-69,-62,92,23,-69,66,-47,-21,61,-91,63,-100,-67,11,120,-57,61,-40,-127,-116,61,-98,-11,62,-67,-100,60,-26,-68,-94,-85,-38,-68,-69,-34,8,61,-39,107,78,-67,52,-67,62,61,122,116,100,61,6,-90,-23,60,-102,-88,125,59,-41,-106,99,61,-41,-81,41,61,64,-114,31,61,-21,-98,39,-66,26,88,55,-67,-89,65,64,-67,37,-58,106,-67,-92,4,68,61,-66,-41,-106,60,-124,-96,-105,61,92,-77,-41,60,7,-57,-83,-67,44,-85,32,60,59,-53,-30,61,62,19,-114,61,-86,-19,69,61,23,42,77,-67,-101,-19,-98,-67,-58,-119,-105,61,12,-47,-37,-69,-55,81,-76,-68,-120,17,14,-67,-124,-106,-102,60,25,-124,-28,-67,-89,42,-128,61,-44,-11,14,61,-49,21,-115,61,-121,22,107,-68,-114,-112,18,60,111,-101,99,61,-98,-117,-4,-68,43,89,-97,-69,105,-114,-18,-67,28,-120,-59,61,39,83,44,60,-111,32,-124,60,85,111,106,61,-18,118,53,60,-14,-77,16,-67,-128,28,-40,60,-77,22,5,-66,-22,-113,7,61,12,-46,12,61,60,98,19,-67,-114,-66,20,61,-21,18,98,61,114,-62,-116,60,98,-91,45,-67,122,102,90,-67,-107,-42,-95,-68,119,95,2,61,-27,-96,-128,61,-35,-122,13,60,46,-100,6,-66,-76,-121,-76,61,-6,-93,-118,61,70,40,28,61,-89,29,36,-67,90,96,23,61,-71,52,-114,60,113,51,-53,60,6,-64,13,-68,16,-88,82,-68,103,2,-68,60,-2,-17,-114,-67,105,5,77,61,90,127,-52,61,85,-46,-128,-67,-39,108,42,61,-66,74,-95,-68,55,33,29,62,81,-28,50,61,-114,111,30,61,106,89,-12,60,59,21,18,-66,-106,5,84,61,77,72,-123,61,9,69,-110,-70,63,57,80,-67,105,-101,5,-67,-21,-47,31,60,-55,-43,-101,-68,-59,69,-97,59,-70,107,78,-67,-113,46,-127,-67,116,-32,-34,61,31,16,36,61,44,49,-1,60,-58,85,-116,59,112,71,1,61,-2,20,-66,61,-85,114,-100,-68,77,64,-123,-67,-7,-15,-49,-67,-110,124,-80,60,69,18,-89,-67,81,116,-37,59,-122,24,-117,-68,30,8,58,61,116,-57,35,60,22,-9,6,62,-119,89,41,60,-100,-23,1,-67]}', '4747c3ea-bc59-4b27-ba4b-3570036dd277.jpg', 0);
INSERT INTO `user` VALUES (1950102349408911361, '周明震', NULL, '00', NULL, NULL, '2', NULL, NULL, 0, '0', NULL, NULL, NULL, NULL, '2025-07-29 07:55:46', '周明震', 'OFFLINE', NULL, '{\"featureData\":[0,-128,-6,68,0,0,-96,65,-51,112,-71,60,42,119,85,-67,-49,-73,-73,59,-82,70,-49,60,-21,1,71,61,-62,-90,101,60,99,87,108,-67,-6,-59,-108,-67,-91,47,11,-67,77,23,-51,-67,-126,-58,-123,-68,83,80,-56,61,65,-33,87,61,27,-91,-10,-68,-53,-70,7,-67,-63,51,83,60,108,45,37,-67,82,-110,-28,60,51,-12,-35,61,2,33,34,60,-113,0,31,61,-55,-20,-63,-67,114,96,25,-67,-73,107,-36,-68,6,34,57,-67,-80,-78,-128,60,-115,26,-48,61,79,-112,-114,-67,-80,-118,51,61,26,-124,123,61,-71,-94,-17,61,-123,47,51,-67,67,28,-114,60,-69,-63,54,61,64,80,105,-67,-68,-37,52,-68,97,89,9,61,25,-36,61,61,95,-103,29,61,106,98,115,-68,6,67,-97,-70,51,-17,0,62,75,-36,29,60,34,84,-70,61,-31,-108,8,61,58,46,-51,-67,-64,31,-69,60,111,59,-52,61,-109,-27,-119,-67,-27,-3,-81,-68,-47,98,-25,-69,-12,-41,-56,-67,117,101,-73,-67,-46,107,118,-67,113,98,-44,60,-4,-103,0,61,-113,112,105,-67,-19,-106,-92,-68,58,-118,79,-67,39,-85,106,62,-36,-86,75,62,-19,-29,-91,60,26,78,-80,-67,-30,-3,-100,-72,3,-127,-51,61,47,-8,98,61,-121,41,52,-66,-92,-86,-29,-68,59,-35,-112,-67,-100,118,93,60,123,43,-56,60,85,53,16,-68,87,-26,25,61,-109,-28,-74,-67,-4,-73,36,61,95,102,-89,-69,-128,-105,-101,-68,-84,36,-61,61,-17,54,84,-68,-81,-50,21,-66,2,-87,99,61,96,45,85,-67,71,-53,34,-67,-53,-70,40,61,-58,-67,17,-67,52,-15,-70,60,-118,-81,94,-68,-2,-63,-74,61,-99,-99,-46,-67,48,-4,26,-67,95,-16,-95,-68,-64,27,-28,60,80,96,118,61,105,4,-111,61,-109,-102,6,61,93,92,100,-67,29,67,-93,-68,-18,-45,-51,61,-49,-113,-18,-67,-33,95,-42,61,-18,108,95,60,76,102,-24,-68,-99,19,-53,-69,-111,124,-87,-67,-47,-63,100,-67,43,50,-118,-67,-117,-24,-14,61,-114,-49,-81,61,-10,-68,-128,-67,30,-34,59,61,-57,69,-81,-67,100,-46,-80,61,-7,114,-98,61,-58,-121,-73,-68,105,98,86,61,-54,-126,-31,61,-101,-69,-68,-68,67,22,-111,-67,-95,36,9,-67,115,64,-31,61,-27,-83,-36,-68,-16,-109,-21,61,5,83,13,-67,-91,-51,-118,61,-128,70,-77,-69,54,-24,-119,-69,-115,-4,-89,-67,-117,0,39,59,-108,-91,98,60,-23,59,-83,61,126,117,97,61,-11,-109,80,-67,14,-21,-96,61,10,19,-7,60,-34,-123,26,61,-68,-76,-92,-67,-59,-61,-104,-67,113,39,-72,59,-49,-123,47,61,12,19,60,-67,-51,98,49,-67,-41,85,-126,-67,-44,114,13,-67,-102,-86,70,60,-82,18,-103,59,22,10,-75,-67,51,53,-108,-68,-20,66,110,-67,57,-98,-124,61,-16,17,7,60,88,-110,0,61,-121,6,-22,61,-1,37,23,60,26,-34,-95,60,20,-48,20,-66,126,69,-46,61,66,-7,-106,-67,82,17,109,-67,86,-39,16,-66,84,-107,-99,-69,-27,12,14,-67,-115,105,-34,60,-102,78,29,-68,-59,-9,-89,59,27,66,-59,-67,63,97,-126,-68,-50,-45,-90,-68,-113,42,-97,60,-126,28,-53,59,-39,-50,89,-67,12,49,81,-67,-20,-71,67,61,16,60,29,-67,-91,15,65,-67,118,-82,-103,-68,-13,42,-69,-68,124,-61,10,62,60,-108,-81,-67,-28,46,-53,-68,54,-58,-115,-68,-121,126,22,61,50,-69,-110,-69,127,-71,120,-67,94,18,80,61,-75,-38,-94,-67,17,-97,-1,-67,-95,-25,16,-67,83,-112,-121,61,51,33,-50,60,-125,-102,-109,61,-78,82,-64,-67,13,70,124,61,-4,-41,28,-66,-78,116,116,-68,-100,-13,-81,-69,-6,-85,-74,-68,96,84,37,58,-115,-10,13,-66,25,48,103,-67,97,48,54,61,54,-42,-113,-67,50,-11,-81,60,-64,-101,-34,-69,-71,66,-109,61,-29,-86,-49,60,25,-6,-91,-68,-60,16,0,-66,-46,-7,-60,-69,-69,54,101,61,-96,54,-81,-68,68,-4,92,61,-91,-7,-110,61,-83,-104,112,-67,-25,-98,77,61,45,-117,41,61,73,43,2,-66,-45,-125,-89,59,-28,-44,-52,59,110,110,49,-67,-21,31,-79,61,-79,18,96,-67,81,40,90,60,14,48,-36,-68,19,-17,-114,60,-122,-41,-46,59,-26,9,94,-68,-108,57,1,-67,-84,-71,-70,-67,-122,42,85,61,-69,-33,-118,-67,124,121,41,-69,19,122,-96,61,-98,-119,-126,61,-4,-40,-90,56,2,42,-71,61,-110,37,-115,60,-35,87,-32,60,49,2,-9,-67,30,-30,-37,61,-17,-99,-35,-68,-128,116,41,61,117,-3,-72,-67,125,7,-33,60,55,52,-99,-67,-121,56,-108,-68,62,-101,6,61,-85,-128,-56,61,64,50,-86,61,-80,125,113,-67,21,69,-93,61,17,13,69,61,101,-108,98,60,-94,89,-54,61,63,84,-93,60,3,-45,74,61,-66,55,9,61]}', '8bb6dc55-647e-455f-a475-7c1530d169af.jpg', 0);

-- ----------------------------
-- Table structure for user_role
-- ----------------------------
DROP TABLE IF EXISTS `user_role`;
CREATE TABLE `user_role`  (
  `id` int NULL DEFAULT NULL,
  `role_id` int NULL DEFAULT NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_role
-- ----------------------------

-- ----------------------------
-- Table structure for ai_conversation
-- ----------------------------
DROP TABLE IF EXISTS `ai_conversation`;
CREATE TABLE `ai_conversation`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '对话ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `conversation_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '对话标题',
  `session_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'AI服务会话ID',
  `conversation_type` enum('GENERAL','TUTORING','VOICE','IMAGE') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'GENERAL' COMMENT '对话类型：通用对话、辅导对话、语音对话、图像对话',
  `status` enum('ACTIVE','ARCHIVED','DELETED') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'ACTIVE' COMMENT '对话状态：活跃、已归档、已删除',
  `last_message_time` datetime NULL DEFAULT NULL COMMENT '最后消息时间',
  `message_count` int NULL DEFAULT 0 COMMENT '消息总数',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_session_id`(`session_id` ASC) USING BTREE,
  INDEX `idx_status_update_time`(`status` ASC, `update_time` DESC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'AI对话会话表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ai_message
-- ----------------------------
DROP TABLE IF EXISTS `ai_message`;
CREATE TABLE `ai_message`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `conversation_id` bigint NOT NULL COMMENT '对话ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `message_type` enum('USER','AI','SYSTEM') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '消息类型：用户消息、AI回复、系统消息',
  `content_type` enum('TEXT','IMAGE','FILE','VOICE','SYSTEM_NOTICE') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'TEXT' COMMENT '内容类型：文本、图片、文件、语音、系统通知',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '消息内容',
  `content_length` int NULL DEFAULT 0 COMMENT '内容长度',
  `ai_model` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'AI模型名称',
  `prompt_tokens` int NULL DEFAULT 0 COMMENT '输入token数',
  `completion_tokens` int NULL DEFAULT 0 COMMENT '输出token数',
  `total_tokens` int NULL DEFAULT 0 COMMENT '总token数',
  `response_time` int NULL DEFAULT 0 COMMENT '响应时间(毫秒)',
  `is_streaming` tinyint NULL DEFAULT 0 COMMENT '是否流式输出：0-否，1-是',
  `stream_status` enum('PENDING','STREAMING','COMPLETED','FAILED') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'COMPLETED' COMMENT '流式状态：待处理、流式中、已完成、失败',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '错误信息',
  `metadata` json NULL COMMENT '扩展元数据(JSON格式)',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_conversation_id`(`conversation_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_message_type`(`message_type` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` DESC) USING BTREE,
  INDEX `idx_stream_status`(`stream_status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'AI消息记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ai_conversation_config
-- ----------------------------
DROP TABLE IF EXISTS `ai_conversation_config`;
CREATE TABLE `ai_conversation_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `conversation_id` bigint NOT NULL COMMENT '对话ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `ai_model` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'qwen-turbo' COMMENT 'AI模型',
  `temperature` decimal(3,2) NULL DEFAULT 0.70 COMMENT '温度参数(0.0-1.0)',
  `max_tokens` int NULL DEFAULT 2000 COMMENT '最大token数',
  `system_prompt` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '系统提示词',
  `enable_streaming` tinyint NULL DEFAULT 1 COMMENT '是否启用流式输出：0-否，1-是',
  `enable_context` tinyint NULL DEFAULT 1 COMMENT '是否启用上下文：0-否，1-是',
  `context_length` int NULL DEFAULT 10 COMMENT '上下文长度(消息条数)',
  `auto_title` tinyint NULL DEFAULT 1 COMMENT '是否自动生成标题：0-否，1-是',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_conversation_id`(`conversation_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'AI对话配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ai_usage_statistics
-- ----------------------------
DROP TABLE IF EXISTS `ai_usage_statistics`;
CREATE TABLE `ai_usage_statistics`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `date` date NOT NULL COMMENT '统计日期',
  `conversation_count` int NULL DEFAULT 0 COMMENT '对话数量',
  `message_count` int NULL DEFAULT 0 COMMENT '消息数量',
  `user_message_count` int NULL DEFAULT 0 COMMENT '用户消息数量',
  `ai_message_count` int NULL DEFAULT 0 COMMENT 'AI回复数量',
  `total_tokens` bigint NULL DEFAULT 0 COMMENT '总token消耗',
  `prompt_tokens` bigint NULL DEFAULT 0 COMMENT '输入token消耗',
  `completion_tokens` bigint NULL DEFAULT 0 COMMENT '输出token消耗',
  `total_response_time` bigint NULL DEFAULT 0 COMMENT '总响应时间(毫秒)',
  `avg_response_time` int NULL DEFAULT 0 COMMENT '平均响应时间(毫秒)',
  `streaming_count` int NULL DEFAULT 0 COMMENT '流式输出次数',
  `error_count` int NULL DEFAULT 0 COMMENT '错误次数',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_date`(`user_id` ASC, `date` ASC) USING BTREE,
  INDEX `idx_date`(`date` DESC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'AI使用统计表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ai_feedback
-- ----------------------------
DROP TABLE IF EXISTS `ai_feedback`;
CREATE TABLE `ai_feedback`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '反馈ID',
  `message_id` bigint NOT NULL COMMENT '消息ID',
  `conversation_id` bigint NOT NULL COMMENT '对话ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `feedback_type` enum('LIKE','DISLIKE','REPORT') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '反馈类型：喜欢、不喜欢、举报',
  `feedback_reason` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '反馈原因',
  `feedback_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '反馈内容',
  `rating` tinyint NULL DEFAULT NULL COMMENT '评分(1-5)',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_message_user`(`message_id` ASC, `user_id` ASC) USING BTREE,
  INDEX `idx_conversation_id`(`conversation_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_feedback_type`(`feedback_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'AI反馈表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

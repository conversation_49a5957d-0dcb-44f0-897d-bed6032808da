{"name": "babel-plugin-polyfill-regenerator", "version": "0.6.5", "description": "A Babel plugin to inject imports to regenerator-runtime", "repository": {"type": "git", "url": "https://github.com/babel/babel-polyfills.git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-define-polyfill-provider": "^0.6.5"}, "devDependencies": {"@babel/core": "^7.27.7", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/plugin-transform-regenerator": "~7.14.5", "regenerator-runtime": "^0.14.1"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "gitHead": "fddd6fc6e7c3c41b1234d82e53faf5de832bbf2b"}
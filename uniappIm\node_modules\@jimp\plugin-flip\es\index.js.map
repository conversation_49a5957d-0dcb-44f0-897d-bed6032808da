{"version": 3, "sources": ["../src/index.js"], "names": ["flipFn", "horizontal", "vertical", "cb", "throwError", "call", "bitmap", "<PERSON><PERSON><PERSON>", "alloc", "data", "length", "scanQuiet", "width", "height", "x", "y", "idx", "_x", "_y", "_idx", "readUInt32BE", "writeUInt32BE", "from", "flip", "mirror"], "mappings": ";;;;;;;AAAA;;AAEA;;;;;;;AAOA,SAASA,MAAT,CAAgBC,UAAhB,EAA4BC,QAA5B,EAAsCC,EAAtC,EAA0C;AACxC,MAAI,OAAOF,UAAP,KAAsB,SAAtB,IAAmC,OAAOC,QAAP,KAAoB,SAA3D,EACE,OAAOE,kBAAWC,IAAX,CACL,IADK,EAEL,0CAFK,EAGLF,EAHK,CAAP;AAMF,MAAMG,MAAM,GAAGC,MAAM,CAACC,KAAP,CAAa,KAAKF,MAAL,CAAYG,IAAZ,CAAiBC,MAA9B,CAAf;AACA,OAAKC,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,KAAKL,MAAL,CAAYM,KAAjC,EAAwC,KAAKN,MAAL,CAAYO,MAApD,EAA4D,UAC1DC,CAD0D,EAE1DC,CAF0D,EAG1DC,GAH0D,EAI1D;AACA,QAAMC,EAAE,GAAGhB,UAAU,GAAG,KAAKK,MAAL,CAAYM,KAAZ,GAAoB,CAApB,GAAwBE,CAA3B,GAA+BA,CAApD;;AACA,QAAMI,EAAE,GAAGhB,QAAQ,GAAG,KAAKI,MAAL,CAAYO,MAAZ,GAAqB,CAArB,GAAyBE,CAA5B,GAAgCA,CAAnD;;AACA,QAAMI,IAAI,GAAI,KAAKb,MAAL,CAAYM,KAAZ,GAAoBM,EAApB,GAAyBD,EAA1B,IAAiC,CAA9C;;AACA,QAAMR,IAAI,GAAG,KAAKH,MAAL,CAAYG,IAAZ,CAAiBW,YAAjB,CAA8BJ,GAA9B,CAAb;AAEAV,IAAAA,MAAM,CAACe,aAAP,CAAqBZ,IAArB,EAA2BU,IAA3B;AACD,GAXD;AAaA,OAAKb,MAAL,CAAYG,IAAZ,GAAmBF,MAAM,CAACe,IAAP,CAAYhB,MAAZ,CAAnB;;AAEA,MAAI,0BAAcH,EAAd,CAAJ,EAAuB;AACrBA,IAAAA,EAAE,CAACE,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,SAAO,IAAP;AACD;;eAEc;AAAA,SAAO;AACpBkB,IAAAA,IAAI,EAAEvB,MADc;AAEpBwB,IAAAA,MAAM,EAAExB;AAFY,GAAP;AAAA,C", "sourcesContent": ["import { isNodePattern, throwError } from '@jimp/utils';\n\n/**\n * Flip the image horizontally\n * @param {boolean} horizontal a Boolean, if true the image will be flipped horizontally\n * @param {boolean} vertical a Boolean, if true the image will be flipped vertically\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\nfunction flipFn(horizontal, vertical, cb) {\n  if (typeof horizontal !== 'boolean' || typeof vertical !== 'boolean')\n    return throwError.call(\n      this,\n      'horizontal and vertical must be Booleans',\n      cb\n    );\n\n  const bitmap = Buffer.alloc(this.bitmap.data.length);\n  this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function(\n    x,\n    y,\n    idx\n  ) {\n    const _x = horizontal ? this.bitmap.width - 1 - x : x;\n    const _y = vertical ? this.bitmap.height - 1 - y : y;\n    const _idx = (this.bitmap.width * _y + _x) << 2;\n    const data = this.bitmap.data.readUInt32BE(idx);\n\n    bitmap.writeUInt32BE(data, _idx);\n  });\n\n  this.bitmap.data = Buffer.from(bitmap);\n\n  if (isNodePattern(cb)) {\n    cb.call(this, null, this);\n  }\n\n  return this;\n}\n\nexport default () => ({\n  flip: flipFn,\n  mirror: flipFn\n});\n"], "file": "index.js"}
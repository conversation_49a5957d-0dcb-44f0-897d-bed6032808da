{"version": 3, "sources": ["../src/index.js"], "names": ["shadow", "options", "cb", "opacity", "size", "x", "y", "blur", "orig", "clone", "scan", "bitmap", "width", "height", "idx", "data", "constructor", "limit255", "resize", "composite", "call"], "mappings": ";;;;;;;AAAA;;AAEA;;;;;;;;;;;eAWe;AAAA,SAAO;AACpBA,IAAAA,MADoB,oBACK;AAAA;;AAAA,UAAlBC,OAAkB,uEAAR,EAAQ;AAAA,UAAJC,EAAI;;AACvB,UAAI,OAAOD,OAAP,KAAmB,UAAvB,EAAmC;AACjCC,QAAAA,EAAE,GAAGD,OAAL;AACAA,QAAAA,OAAO,GAAG,EAAV;AACD;;AAJsB,qBAM0CA,OAN1C;AAAA,sCAMfE,OANe;AAAA,UAMfA,OANe,iCAML,GANK;AAAA,mCAMAC,IANA;AAAA,UAMAA,IANA,8BAMO,GANP;AAAA,gCAMYC,CANZ;AAAA,UAMYA,CANZ,2BAMgB,CAAC,EANjB;AAAA,gCAMqBC,CANrB;AAAA,UAMqBA,CANrB,2BAMyB,EANzB;AAAA,mCAM6BC,IAN7B;AAAA,UAM6BA,IAN7B,8BAMoC,CANpC,kBAQvB;;AACA,UAAMC,IAAI,GAAG,KAAKC,KAAL,EAAb;AACA,UAAMT,MAAM,GAAG,KAAKS,KAAL,EAAf,CAVuB,CAYvB;;AACAT,MAAAA,MAAM,CAACU,IAAP,CACE,CADF,EAEE,CAFF,EAGEV,MAAM,CAACW,MAAP,CAAcC,KAHhB,EAIEZ,MAAM,CAACW,MAAP,CAAcE,MAJhB,EAKE,UAACR,CAAD,EAAIC,CAAJ,EAAOQ,GAAP,EAAe;AACbd,QAAAA,MAAM,CAACW,MAAP,CAAcI,IAAd,CAAmBD,GAAnB,IAA0B,IAA1B;AACAd,QAAAA,MAAM,CAACW,MAAP,CAAcI,IAAd,CAAmBD,GAAG,GAAG,CAAzB,IAA8B,IAA9B;AACAd,QAAAA,MAAM,CAACW,MAAP,CAAcI,IAAd,CAAmBD,GAAG,GAAG,CAAzB,IAA8B,IAA9B,CAHa,CAIb;;AACAd,QAAAA,MAAM,CAACW,MAAP,CAAcI,IAAd,CAAmBD,GAAG,GAAG,CAAzB,IAA8Bd,MAAM,CAACgB,WAAP,CAAmBC,QAAnB,CAC5BjB,MAAM,CAACW,MAAP,CAAcI,IAAd,CAAmBD,GAAG,GAAG,CAAzB,IAA8BX,OADF,CAA9B;AAIA,QAAA,KAAI,CAACQ,MAAL,CAAYI,IAAZ,CAAiBD,GAAjB,IAAwB,IAAxB;AACA,QAAA,KAAI,CAACH,MAAL,CAAYI,IAAZ,CAAiBD,GAAG,GAAG,CAAvB,IAA4B,IAA5B;AACA,QAAA,KAAI,CAACH,MAAL,CAAYI,IAAZ,CAAiBD,GAAG,GAAG,CAAvB,IAA4B,IAA5B;AACA,QAAA,KAAI,CAACH,MAAL,CAAYI,IAAZ,CAAiBD,GAAG,GAAG,CAAvB,IAA4B,IAA5B;AACD,OAlBH,EAbuB,CAkCvB;;AACAd,MAAAA,MAAM,CACHkB,MADH,CACUlB,MAAM,CAACW,MAAP,CAAcC,KAAd,GAAsBR,IADhC,EACsCJ,MAAM,CAACW,MAAP,CAAcE,MAAd,GAAuBT,IAD7D,EAEGG,IAFH,CAEQA,IAFR,EAnCuB,CAuCvB;;AACA,WAAKY,SAAL,CAAenB,MAAf,EAAuBK,CAAvB,EAA0BC,CAA1B;AACA,WAAKa,SAAL,CAAeX,IAAf,EAAqB,CAArB,EAAwB,CAAxB;;AAEA,UAAI,0BAAcN,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACkB,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD;AAjDmB,GAAP;AAAA,C", "sourcesContent": ["import { isNodePattern } from '@jimp/utils';\n\n/**\n * Creates a circle out of an image.\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} options (optional)\n * opacity - opacity of the shadow between 0 and 1\n * size,- of the shadow\n * blur - how blurry the shadow is\n * x- x position of shadow\n * y - y position of shadow\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\nexport default () => ({\n  shadow(options = {}, cb) {\n    if (typeof options === 'function') {\n      cb = options;\n      options = {};\n    }\n\n    const { opacity = 0.7, size = 1.1, x = -25, y = 25, blur = 5 } = options;\n\n    // clone the image\n    const orig = this.clone();\n    const shadow = this.clone();\n\n    // turn all it's pixels black\n    shadow.scan(\n      0,\n      0,\n      shadow.bitmap.width,\n      shadow.bitmap.height,\n      (x, y, idx) => {\n        shadow.bitmap.data[idx] = 0x00;\n        shadow.bitmap.data[idx + 1] = 0x00;\n        shadow.bitmap.data[idx + 2] = 0x00;\n        // up the opacity a little,\n        shadow.bitmap.data[idx + 3] = shadow.constructor.limit255(\n          shadow.bitmap.data[idx + 3] * opacity\n        );\n\n        this.bitmap.data[idx] = 0x00;\n        this.bitmap.data[idx + 1] = 0x00;\n        this.bitmap.data[idx + 2] = 0x00;\n        this.bitmap.data[idx + 3] = 0x00;\n      }\n    );\n\n    // enlarge it. This creates a \"shadow\".\n    shadow\n      .resize(shadow.bitmap.width * size, shadow.bitmap.height * size)\n      .blur(blur);\n\n    // Then blit the \"shadow\" onto the background and the image on top of that.\n    this.composite(shadow, x, y);\n    this.composite(orig, 0, 0);\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  }\n});\n"], "file": "index.js"}
@echo off
echo ========================================
echo AI智能家教小程序 - 项目检查
echo ========================================
echo.

echo 正在检查项目完整性...
echo.

set error_count=0

REM 检查核心配置文件
if exist "pages.json" (
    echo ✓ pages.json 配置文件存在
) else (
    echo ✗ pages.json 配置文件缺失
    set /a error_count+=1
)

if exist "manifest.json" (
    echo ✓ manifest.json 应用配置存在
) else (
    echo ✗ manifest.json 应用配置缺失
    set /a error_count+=1
)

if exist "App.vue" (
    echo ✓ App.vue 应用入口存在
) else (
    echo ✗ App.vue 应用入口缺失
    set /a error_count+=1
)

echo.
echo 检查页面文件...

REM 检查页面文件
if exist "pages\index\index.vue" (
    echo ✓ 首页文件存在
) else (
    echo ✗ 首页文件缺失
    set /a error_count+=1
)

if exist "pages\login\login.vue" (
    echo ✓ 登录页面存在
) else (
    echo ✗ 登录页面缺失
    set /a error_count+=1
)

if exist "pages\register\register.vue" (
    echo ✓ 注册页面存在
) else (
    echo ✗ 注册页面缺失
    set /a error_count+=1
)

if exist "pages\ai-home\ai-home.vue" (
    echo ✓ AI家教首页存在
) else (
    echo ✗ AI家教首页缺失
    set /a error_count+=1
)

echo.
echo 检查静态资源...

if exist "static" (
    echo ✓ 静态资源目录存在
) else (
    echo ✗ 静态资源目录缺失
    set /a error_count+=1
)

echo.
echo ========================================
if %error_count%==0 (
    echo ✅ 项目检查完成，所有文件都存在！
    echo.
    echo 🚀 可以在微信开发者工具中打开项目了
    echo.
    echo 使用步骤:
    echo 1. 打开微信开发者工具
    echo 2. 选择 "小程序" 项目类型
    echo 3. 项目目录选择: %cd%
    echo 4. AppID 可以使用测试号
    echo 5. 点击 "确定" 创建项目
    echo 6. 编译运行即可预览
) else (
    echo ❌ 发现 %error_count% 个问题，请检查缺失的文件
)

echo ========================================
echo.

pause

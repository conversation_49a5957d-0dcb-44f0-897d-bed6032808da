import{_ as e,o as a,c as s,w as t,i as l,a as n,b as c,d as r,h as i,r as o,F as d,t as u,e as h,f,I as m,S as _,j as k,g as w,n as p}from"./index-0fa1fc91.js";import{g as y,e as g,f as v,h as C,i as b,j as R,c as F,d as T}from"./api.44778c12.js";const I=e({data:()=>({currentTab:0,tabs:[{name:"好友列表",badge:0,showBadge:!1},{name:"收到申请",badge:0,showBadge:!0},{name:"发出申请",badge:0,showBadge:!1}],friendList:[],receivedRequests:[],sentRequests:[],showSearchModalFlag:!1,searchKeyword:"",searchResults:[],showRemarkModalFlag:!1,selectedFriend:{},remarkInput:"",saving:!1,friendSearchKeyword:""}),onLoad(){this.loadAllData()},computed:{filteredFriendList(){if(!this.friendSearchKeyword.trim())return this.friendList;const e=this.friendSearchKeyword.toLowerCase();return this.friendList.filter(a=>{const s=(a.remark||"").toLowerCase(),t=(a.nickname||"").toLowerCase(),l=(a.username||"").toLowerCase();return s.includes(e)||t.includes(e)||l.includes(e)})}},onShow(){this.loadAllData()},methods:{switchTab(e){this.currentTab=e},async loadAllData(){await Promise.all([this.loadFriendList(),this.loadReceivedRequests(),this.loadSentRequests()])},async loadFriendList(){try{const e=await y();console.log("好友列表API响应:",e),200===e.code&&(this.friendList=e.data||[],console.log("好友列表数据:",this.friendList))}catch(e){console.error("加载好友列表失败:",e)}},async loadReceivedRequests(){try{const e=await g();200===e.code&&(this.receivedRequests=e.data||[],this.tabs[1].badge=this.receivedRequests.filter(e=>"PENDING"===e.status).length)}catch(e){console.error("加载收到的申请失败:",e)}},async loadSentRequests(){try{const e=await v();200===e.code&&(this.sentRequests=e.data||[],this.tabs[2].badge=this.sentRequests.length)}catch(e){console.error("加载发出的申请失败:",e)}},async handleRequest(e,a){try{const s=await C(e,a);200===s.code?(uni.showToast({title:"accept"===a?"已同意":"已拒绝",icon:"success"}),await this.loadAllData(),0===this.receivedRequests.length&&(this.tabs[1].badge=0)):uni.showToast({title:s.message,icon:"none"})}catch(s){uni.showToast({title:"操作失败",icon:"none"})}},deleteFriendConfirm(e){uni.showModal({title:"确认删除",content:`确定要删除好友 ${e.nickname||e.username} 吗？`,success:a=>{a.confirm&&this.deleteFriendAction(e.userId||e.id)}})},async deleteFriendAction(e){try{const a=await b(e);200===a.code?(uni.showToast({title:"删除成功",icon:"success"}),this.loadFriendList()):uni.showToast({title:a.message,icon:"none"})}catch(a){uni.showToast({title:"删除失败",icon:"none"})}},openChat(e){const a=e.userId||e.id,s=this.getDisplayName(e);uni.navigateTo({url:`/pages/chatDetail/chatDetail?userId=${a}&nickname=${encodeURIComponent(s)}`})},getDisplayName:e=>e.remark&&e.remark.trim()?e.remark:e.nickname||e.username||"未知用户",showRemarkModal(e){console.log("显示备注模态框，好友数据:",e),this.selectedFriend=e,this.remarkInput=e.remark||"",this.showRemarkModalFlag=!0},hideRemarkModal(){this.showRemarkModalFlag=!1,this.selectedFriend={},this.remarkInput="",this.saving=!1},onRemarkInput(e){this.remarkInput=e.detail.value},onInputFocus(){console.log("输入框获得焦点")},onInputBlur(){console.log("输入框失去焦点")},async saveRemark(){if(!this.saving){this.saving=!0;try{const e=await R(this.selectedFriend.userId,this.remarkInput.trim());200===e.code?(uni.showToast({title:"备注设置成功",icon:"success"}),this.hideRemarkModal(),this.loadFriendList()):uni.showToast({title:e.message||"设置失败",icon:"none"})}catch(e){console.error("设置备注失败:",e),uni.showToast({title:"网络错误，请重试",icon:"none"})}finally{this.saving=!1}}},onFriendSearch(){},clearFriendSearch(){this.friendSearchKeyword=""},showSearchModal(){this.showSearchModalFlag=!0},hideSearchModal(){this.showSearchModalFlag=!1,this.searchKeyword="",this.searchResults=[]},onSearchInput(){this.searchKeyword.trim()||(this.searchResults=[])},async searchUsers(){if(this.searchKeyword.trim())try{const e=await F(this.searchKeyword);200===e.code?this.searchResults=e.data||[]:uni.showToast({title:e.message,icon:"none"})}catch(e){uni.showToast({title:"搜索失败",icon:"none"})}else uni.showToast({title:"请输入搜索关键词",icon:"none"})},async sendFriendRequestToUser(e){try{const a=await T({toUserId:e.userId||e.id,message:"请求添加您为好友"});200===a.code?(uni.showToast({title:"申请已发送",icon:"success"}),this.hideSearchModal()):uni.showToast({title:a.message,icon:"none"})}catch(a){uni.showToast({title:"发送失败",icon:"none"})}},formatTime(e){const a=new Date(e),s=new Date-a;return s<6e4?"刚刚":s<36e5?Math.floor(s/6e4)+"分钟前":s<864e5?Math.floor(s/36e5)+"小时前":Math.floor(s/864e5)+"天前"},getStatusText:e=>({PENDING:"待处理",ACCEPTED:"已同意",REJECTED:"已拒绝"}[e]||e),getStatusClass:e=>({"status-pending":"PENDING"===e,"status-accepted":"ACCEPTED"===e,"status-rejected":"REJECTED"===e})}},[["render",function(e,y,g,v,C,b){const R=f,F=l,T=m,I=_,q=k,S=w;return a(),s(F,{class:"friend-container"},{default:t(()=>[n(" 顶部导航栏 "),c(F,{class:"header"},{default:t(()=>[c(F,{class:"header-content"},{default:t(()=>[c(R,{class:"title"},{default:t(()=>[r("好友管理")]),_:1})]),_:1})]),_:1}),n(" 标签页 "),c(F,{class:"tabs"},{default:t(()=>[(a(!0),i(d,null,o(C.tabs,(e,l)=>(a(),s(F,{key:l,class:p(["tab-item",{active:C.currentTab===l}]),onClick:e=>b.switchTab(l)},{default:t(()=>[c(R,{class:"tab-text"},{default:t(()=>[r(u(e.name),1)]),_:2},1024),e.badge>0&&e.showBadge?(a(),s(R,{key:0,class:"tab-badge"},{default:t(()=>[r(u(e.badge),1)]),_:2},1024)):n("v-if",!0)]),_:2},1032,["class","onClick"]))),128))]),_:1}),n(" 内容区域 "),c(I,{class:"content","scroll-y":"true"},{default:t(()=>[n(" 好友列表 "),0===C.currentTab?(a(),s(F,{key:0,class:"friend-list"},{default:t(()=>[n(" 搜索框 "),c(F,{class:"search-section"},{default:t(()=>[c(F,{class:"search-box"},{default:t(()=>[c(F,{class:"search-icon"},{default:t(()=>[r("🔍")]),_:1}),c(T,{modelValue:C.friendSearchKeyword,"onUpdate:modelValue":y[0]||(y[0]=e=>C.friendSearchKeyword=e),class:"search-input",placeholder:"搜索好友（昵称/备注/用户名）",onInput:b.onFriendSearch},null,8,["modelValue","onInput"]),C.friendSearchKeyword?(a(),s(F,{key:0,class:"clear-icon",onClick:b.clearFriendSearch},{default:t(()=>[c(R,null,{default:t(()=>[r("×")]),_:1})]),_:1},8,["onClick"])):n("v-if",!0)]),_:1})]),_:1}),(a(!0),i(d,null,o(b.filteredFriendList,e=>(a(),s(F,{key:e.userId,class:"friend-item",onClick:a=>b.openChat(e)},{default:t(()=>[c(F,{class:"avatar"},{default:t(()=>[c(R,{class:"avatar-text"},{default:t(()=>[r(u(b.getDisplayName(e).charAt(0)),1)]),_:2},1024)]),_:2},1024),c(F,{class:"friend-info"},{default:t(()=>[c(R,{class:"friend-name"},{default:t(()=>[r(u(b.getDisplayName(e)),1)]),_:2},1024),c(R,{class:"friend-username"},{default:t(()=>[r("@"+u(e.username),1)]),_:2},1024),e.remark?(a(),s(R,{key:0,class:"friend-remark"},{default:t(()=>[r("备注: "+u(e.remark),1)]),_:2},1024)):n("v-if",!0)]),_:2},1024),c(F,{class:"friend-actions"},{default:t(()=>[c(F,{class:"action-btn remark-btn",onClick:h(a=>b.showRemarkModal(e),["stop"])},{default:t(()=>[c(F,{class:"btn-icon"},{default:t(()=>[c(R,{class:"icon-text"},{default:t(()=>[r("备注")]),_:1})]),_:1})]),_:2},1032,["onClick"]),c(F,{class:"action-btn delete-btn",onClick:h(a=>b.deleteFriendConfirm(e),["stop"])},{default:t(()=>[c(F,{class:"btn-icon"},{default:t(()=>[c(R,{class:"icon-text"},{default:t(()=>[r("删除")]),_:1})]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1032,["onClick"]))),128)),0===b.filteredFriendList.length?(a(),s(F,{key:0,class:"empty-state"},{default:t(()=>[c(R,{class:"empty-text"},{default:t(()=>[r(u(C.friendSearchKeyword?"未找到相关好友":"暂无好友"),1)]),_:1}),c(R,{class:"empty-hint"},{default:t(()=>[r(u(C.friendSearchKeyword?"尝试其他关键词":"点击右上角搜索添加好友"),1)]),_:1})]),_:1})):n("v-if",!0)]),_:1})):n("v-if",!0),n(" 收到的申请 "),1===C.currentTab?(a(),s(F,{key:1,class:"request-list"},{default:t(()=>[(a(!0),i(d,null,o(C.receivedRequests,e=>(a(),s(F,{key:e.id,class:"request-item"},{default:t(()=>[c(F,{class:"avatar"},{default:t(()=>[c(R,{class:"avatar-text"},{default:t(()=>[r(u((e.fromUser.nickname||e.fromUser.username||"?").charAt(0)),1)]),_:2},1024)]),_:2},1024),c(F,{class:"request-info"},{default:t(()=>[c(R,{class:"request-name"},{default:t(()=>[r(u(e.fromUser.nickname||e.fromUser.username),1)]),_:2},1024),c(R,{class:"request-message"},{default:t(()=>[r(u(e.message||"请求添加您为好友"),1)]),_:2},1024),c(R,{class:"request-time"},{default:t(()=>[r(u(b.formatTime(e.createTime)),1)]),_:2},1024)]),_:2},1024),c(F,{class:"request-actions"},{default:t(()=>[c(F,{class:"accept-btn",onClick:a=>b.handleRequest(e.id,"accept")},{default:t(()=>[c(R,null,{default:t(()=>[r("同意")]),_:1})]),_:2},1032,["onClick"]),c(F,{class:"reject-btn",onClick:a=>b.handleRequest(e.id,"reject")},{default:t(()=>[c(R,null,{default:t(()=>[r("拒绝")]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024))),128)),0===C.receivedRequests.length?(a(),s(F,{key:0,class:"empty-state"},{default:t(()=>[c(R,{class:"empty-text"},{default:t(()=>[r("暂无好友申请")]),_:1})]),_:1})):n("v-if",!0)]),_:1})):n("v-if",!0),n(" 发出的申请 "),2===C.currentTab?(a(),s(F,{key:2,class:"request-list"},{default:t(()=>[(a(!0),i(d,null,o(C.sentRequests,e=>(a(),s(F,{key:e.id,class:"request-item"},{default:t(()=>[c(F,{class:"avatar"},{default:t(()=>[c(R,{class:"avatar-text"},{default:t(()=>[r(u((e.toUser.nickname||e.toUser.username||"?").charAt(0)),1)]),_:2},1024)]),_:2},1024),c(F,{class:"request-info"},{default:t(()=>[c(R,{class:"request-name"},{default:t(()=>[r(u(e.toUser.nickname||e.toUser.username),1)]),_:2},1024),c(R,{class:"request-message"},{default:t(()=>[r(u(e.message||"请求添加为好友"),1)]),_:2},1024),c(R,{class:"request-time"},{default:t(()=>[r(u(b.formatTime(e.createTime)),1)]),_:2},1024)]),_:2},1024),c(F,{class:"request-status"},{default:t(()=>[c(R,{class:p(["status-text",b.getStatusClass(e.status)])},{default:t(()=>[r(u(b.getStatusText(e.status)),1)]),_:2},1032,["class"])]),_:2},1024)]),_:2},1024))),128)),0===C.sentRequests.length?(a(),s(F,{key:0,class:"empty-state"},{default:t(()=>[c(R,{class:"empty-text"},{default:t(()=>[r("暂无发出的申请")]),_:1})]),_:1})):n("v-if",!0)]),_:1})):n("v-if",!0)]),_:1}),n(" 搜索弹窗 "),C.showSearchModalFlag?(a(),s(F,{key:0,class:"search-modal",onClick:b.hideSearchModal},{default:t(()=>[c(F,{class:"search-content",onClick:y[2]||(y[2]=h(()=>{},["stop"]))},{default:t(()=>[c(F,{class:"search-header"},{default:t(()=>[c(R,{class:"search-title"},{default:t(()=>[r("搜索用户")]),_:1}),c(F,{class:"close-btn",onClick:b.hideSearchModal},{default:t(()=>[c(R,null,{default:t(()=>[r("✕")]),_:1})]),_:1},8,["onClick"])]),_:1}),c(F,{class:"search-input-group"},{default:t(()=>[c(T,{modelValue:C.searchKeyword,"onUpdate:modelValue":y[1]||(y[1]=e=>C.searchKeyword=e),placeholder:"输入用户名或昵称",class:"search-input",onInput:b.onSearchInput},null,8,["modelValue","onInput"]),c(F,{class:"search-btn",onClick:b.searchUsers},{default:t(()=>[c(R,null,{default:t(()=>[r("搜索")]),_:1})]),_:1},8,["onClick"])]),_:1}),c(I,{class:"search-results","scroll-y":"true"},{default:t(()=>[(a(!0),i(d,null,o(C.searchResults,e=>(a(),s(F,{key:e.id,class:"search-item"},{default:t(()=>[c(F,{class:"avatar"},{default:t(()=>[c(R,{class:"avatar-text"},{default:t(()=>[r(u((e.nickname||e.username||"?").charAt(0)),1)]),_:2},1024)]),_:2},1024),c(F,{class:"user-info"},{default:t(()=>[c(R,{class:"user-name"},{default:t(()=>[r(u(e.nickname||e.username),1)]),_:2},1024),c(R,{class:"user-username"},{default:t(()=>[r("@"+u(e.username),1)]),_:2},1024)]),_:2},1024),c(F,{class:"add-btn",onClick:a=>b.sendFriendRequestToUser(e)},{default:t(()=>[c(R,null,{default:t(()=>[r("添加")]),_:1})]),_:2},1032,["onClick"])]),_:2},1024))),128)),0===C.searchResults.length&&C.searchKeyword?(a(),s(F,{key:0,class:"empty-state"},{default:t(()=>[c(R,{class:"empty-text"},{default:t(()=>[r("未找到相关用户")]),_:1})]),_:1})):n("v-if",!0)]),_:1})]),_:1})]),_:1},8,["onClick"])):n("v-if",!0),n(" 备注设置模态框 "),C.showRemarkModalFlag?(a(),s(F,{key:1,class:"modal-overlay",onClick:b.hideRemarkModal},{default:t(()=>[c(F,{class:"remark-modal",onClick:y[4]||(y[4]=h(()=>{},["stop"]))},{default:t(()=>[c(F,{class:"modal-header"},{default:t(()=>[c(R,{class:"modal-title"},{default:t(()=>[r("设置备注")]),_:1}),c(F,{class:"close-btn",onClick:b.hideRemarkModal},{default:t(()=>[c(R,{class:"close-icon"},{default:t(()=>[r("×")]),_:1})]),_:1},8,["onClick"])]),_:1}),c(F,{class:"modal-content"},{default:t(()=>[c(F,{class:"friend-preview"},{default:t(()=>[c(F,{class:"preview-avatar"},{default:t(()=>[c(R,{class:"preview-avatar-text"},{default:t(()=>[r(u(b.getDisplayName(C.selectedFriend).charAt(0)),1)]),_:1})]),_:1}),c(F,{class:"preview-info"},{default:t(()=>[c(R,{class:"preview-name"},{default:t(()=>[r(u(b.getDisplayName(C.selectedFriend)),1)]),_:1}),c(R,{class:"preview-username"},{default:t(()=>[r("@"+u(C.selectedFriend.username),1)]),_:1})]),_:1})]),_:1}),c(F,{class:"input-section"},{default:t(()=>[c(R,{class:"input-label"},{default:t(()=>[r("备注名称")]),_:1}),c(q,{modelValue:C.remarkInput,"onUpdate:modelValue":y[3]||(y[3]=e=>C.remarkInput=e),class:"remark-input",placeholder:"请输入备注名称",maxlength:"20","auto-height":"","show-confirm-bar":!1,onInput:b.onRemarkInput,onFocus:b.onInputFocus,onBlur:b.onInputBlur},null,8,["modelValue","onInput","onFocus","onBlur"])]),_:1})]),_:1}),c(F,{class:"modal-footer"},{default:t(()=>[c(S,{class:"cancel-btn",onClick:b.hideRemarkModal},{default:t(()=>[r("取消")]),_:1},8,["onClick"]),c(S,{class:"confirm-btn",onClick:b.saveRemark,disabled:C.saving},{default:t(()=>[r(u(C.saving?"保存中...":"保存"),1)]),_:1},8,["onClick","disabled"])]),_:1})]),_:1})]),_:1},8,["onClick"])):n("v-if",!0)]),_:1})}],["__scopeId","data-v-56202780"]]);export{I as default};

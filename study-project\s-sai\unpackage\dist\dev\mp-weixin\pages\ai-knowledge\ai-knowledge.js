"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      messages: [],
      inputText: "",
      isLoading: false,
      isAiTyping: false,
      scrollTop: 0,
      userId: 123,
      // 从用户信息获取
      quickQuestions: [
        "地球的直径是多少？",
        "中国古代四大发明是什么？",
        "光的传播速度是多少？",
        "什么是人工智能？",
        "太阳系有几颗行星？"
      ]
    };
  },
  onLoad() {
    this.loadUserInfo();
    this.addWelcomeMessage();
  },
  methods: {
    loadUserInfo() {
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      if (userInfo && userInfo.id) {
        this.userId = userInfo.id;
      }
    },
    addWelcomeMessage() {
      this.messages.push({
        type: "ai",
        content: "你好！我是AI知识助手，可以回答各种问题。请随时向我提问！",
        time: this.getCurrentTime()
      });
      this.scrollToBottom();
    },
    askQuestion(question) {
      this.inputText = question;
      this.sendMessage();
    },
    async sendMessage() {
      if (!this.inputText.trim() || this.isLoading)
        return;
      const userMessage = {
        type: "user",
        content: this.inputText.trim(),
        time: this.getCurrentTime()
      };
      this.messages.push(userMessage);
      const question = this.inputText.trim();
      this.inputText = "";
      this.isLoading = true;
      this.isAiTyping = true;
      this.scrollToBottom();
      try {
        const response = await this.callKnowledgeAPI(question);
        this.isAiTyping = false;
        if (response && response.success) {
          this.messages.push({
            type: "ai",
            content: response.response || "抱歉，我暂时无法回答这个问题。",
            time: this.getCurrentTime()
          });
        } else {
          this.messages.push({
            type: "ai",
            content: "抱歉，服务暂时不可用，请稍后再试。",
            time: this.getCurrentTime()
          });
        }
      } catch (error) {
        this.isAiTyping = false;
        this.messages.push({
          type: "ai",
          content: "网络连接失败，请检查网络后重试。",
          time: this.getCurrentTime()
        });
        common_vendor.index.__f__("error", "at pages/ai-knowledge/ai-knowledge.vue:169", "API调用失败:", error);
      }
      this.isLoading = false;
      this.scrollToBottom();
    },
    async callKnowledgeAPI(question) {
      const apiUrl = "http://localhost:8082/api/ai/miniprogram/knowledge/qa";
      const response = await common_vendor.index.request({
        url: apiUrl,
        method: "POST",
        header: {
          "Content-Type": "application/json"
        },
        data: {
          userId: this.userId,
          question
        }
      });
      if (response.statusCode === 200 && response.data.code === 200) {
        return response.data.data;
      } else {
        throw new Error("API调用失败");
      }
    },
    getCurrentTime() {
      const now = /* @__PURE__ */ new Date();
      return `${now.getHours().toString().padStart(2, "0")}:${now.getMinutes().toString().padStart(2, "0")}`;
    },
    scrollToBottom() {
      this.$nextTick(() => {
        this.scrollTop = 999999;
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.messages, (message, index, i0) => {
      return {
        a: common_vendor.t(message.type === "user" ? "我" : "AI"),
        b: common_vendor.t(message.content),
        c: common_vendor.t(message.time),
        d: index,
        e: common_vendor.n(message.type)
      };
    }),
    b: $data.isAiTyping
  }, $data.isAiTyping ? {} : {}, {
    c: $data.scrollTop,
    d: $data.isLoading,
    e: common_vendor.o((...args) => $options.sendMessage && $options.sendMessage(...args)),
    f: $data.inputText,
    g: common_vendor.o(($event) => $data.inputText = $event.detail.value),
    h: common_vendor.t($data.isLoading ? "发送中" : "发送"),
    i: !$data.inputText.trim() || $data.isLoading ? 1 : "",
    j: common_vendor.o((...args) => $options.sendMessage && $options.sendMessage(...args)),
    k: $data.messages.length === 0
  }, $data.messages.length === 0 ? {
    l: common_vendor.f($data.quickQuestions, (question, index, i0) => {
      return {
        a: common_vendor.t(question),
        b: index,
        c: common_vendor.o(($event) => $options.askQuestion(question), index)
      };
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/ai-knowledge/ai-knowledge.js.map

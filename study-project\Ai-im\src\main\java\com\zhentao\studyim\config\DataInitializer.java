package com.zhentao.studyim.config;

import com.zhentao.studyim.entity.Friendship;
import com.zhentao.studyim.entity.User;
import com.zhentao.studyim.repository.FriendshipRepository;
import com.zhentao.studyim.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 数据初始化器
 * 在应用启动时创建一些测试数据
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DataInitializer implements CommandLineRunner {

    private final UserRepository userRepository;
    private final FriendshipRepository friendshipRepository;

    @Override
    public void run(String... args) throws Exception {
        try {
            initializeTestData();
        } catch (Exception e) {
            log.error("初始化测试数据失败", e);
        }
    }

    private void initializeTestData() {
        log.info("开始初始化测试数据...");

        // 检查是否已有数据
        if (userRepository.count() > 0) {
            log.info("数据库中已有用户数据，跳过初始化");
            return;
        }

        // 创建测试用户
        User user1 = createUser("test", "test123", "测试用户1");
        User user2 = createUser("zhang", "123456", "张三");
        User user3 = createUser("li", "123456", "李四");
        User user4 = createUser("wang", "123456", "王五");
        User user5 = createUser("zhao", "123456", "赵六");

        // 保存用户
        List<User> users = List.of(user1, user2, user3, user4, user5);
        userRepository.saveAll(users);
        log.info("创建了 {} 个测试用户", users.size());

        // 创建好友关系
        createFriendship(user1, user2);  // test 和 张三 是好友
        createFriendship(user1, user3);  // test 和 李四 是好友
        createFriendship(user2, user3);  // 张三 和 李四 是好友
        createFriendship(user2, user4);  // 张三 和 王五 是好友

        log.info("测试数据初始化完成");
    }

    private User createUser(String username, String password, String nickname) {
        User user = new User();
        user.setUsername(username);
        user.setPassword(password); // 简化处理，直接存储明文密码用于测试
        user.setNickname(nickname);
        user.setCreateTime(LocalDateTime.now());
        return user;
    }

    private void createFriendship(User user1, User user2) {
        // 创建双向好友关系
        Friendship friendship1 = new Friendship();
        friendship1.setUserId(user1.getUserId());
        friendship1.setFriendId(user2.getUserId());
        friendship1.setCreateTime(LocalDateTime.now());

        Friendship friendship2 = new Friendship();
        friendship2.setUserId(user2.getUserId());
        friendship2.setFriendId(user1.getUserId());
        friendship2.setCreateTime(LocalDateTime.now());

        friendshipRepository.saveAll(List.of(friendship1, friendship2));
        log.info("创建好友关系: {} <-> {}", user1.getNickname(), user2.getNickname());
    }
}

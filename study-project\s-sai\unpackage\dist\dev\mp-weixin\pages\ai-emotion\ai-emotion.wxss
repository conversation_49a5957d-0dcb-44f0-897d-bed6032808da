
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #ffeef8 0%, #f0f8ff 100%);
}
.header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  border-bottom: 1rpx solid rgba(233, 236, 239, 0.5);
}
.header-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}
.header-info {
  flex: 1;
}
.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  display: block;
}
.header-desc {
  font-size: 24rpx;
  color: #6c757d;
  margin-top: 8rpx;
  display: block;
}
.emotion-selector {
  background: rgba(255, 255, 255, 0.9);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  padding: 30rpx;
  border-bottom: 1rpx solid rgba(233, 236, 239, 0.5);
}
.selector-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20rpx;
  display: block;
}
.emotion-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}
.emotion-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 10rpx;
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 0.7);
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}
.emotion-item.active {
  background: rgba(0, 123, 255, 0.1);
  border-color: #007bff;
  transform: scale(1.05);
}
.emotion-item:active {
  transform: scale(0.95);
}
.emotion-icon {
  font-size: 36rpx;
  margin-bottom: 8rpx;
}
.emotion-name {
  font-size: 22rpx;
  color: #495057;
  font-weight: 500;
}
.emotion-item.active .emotion-name {
  color: #007bff;
  font-weight: bold;
}
.chat-area {
  flex: 1;
  padding: 20rpx;
}
.message-item {
  display: flex;
  margin-bottom: 30rpx;
  animation: fadeInUp 0.3s ease;
}
.message-item.user {
  flex-direction: row-reverse;
}
.message-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6b9d, #c44569);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 157, 0.3);
}
.message-item.user .message-avatar {
  background: linear-gradient(135deg, #007bff, #0056b3);
  box-shadow: 0 4rpx 12rpx rgba(0, 123, 255, 0.3);
}
.avatar-text {
  font-size: 24rpx;
  color: #fff;
  font-weight: bold;
}
.message-content {
  flex: 1;
  max-width: 70%;
}
.message-bubble {
  background: rgba(255, 255, 255, 0.9);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  padding: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  position: relative;
}
.message-item.user .message-bubble {
  background: linear-gradient(135deg, #007bff, #0056b3);
}
.message-text {
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
  display: block;
  white-space: pre-wrap;
}
.message-item.user .message-text {
  color: #fff;
}
.message-time {
  font-size: 20rpx;
  color: #999;
  margin-top: 10rpx;
  display: block;
}
.message-item.user .message-time {
  color: rgba(255, 255, 255, 0.8);
}
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #ff6b9d;
  animation: typing 1.4s infinite;
}
.dot:nth-child(2) { animation-delay: 0.2s;
}
.dot:nth-child(3) { animation-delay: 0.4s;
}
@keyframes typing {
0%, 60%, 100% { opacity: 0.3;
}
30% { opacity: 1;
}
}
@keyframes fadeInUp {
from {
    opacity: 0;
    transform: translateY(20rpx);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
.input-area {
  background: rgba(255, 255, 255, 0.9);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  padding: 20rpx;
  border-top: 1rpx solid rgba(233, 236, 239, 0.5);
}
.input-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.input-field {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  border: 1rpx solid rgba(233, 236, 239, 0.5);
  border-radius: 40rpx;
  font-size: 28rpx;
  background: rgba(248, 249, 250, 0.8);
}
.send-btn {
  width: 120rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #ff6b9d, #c44569);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 157, 0.3);
}
.send-btn.disabled {
  background: #ccc;
  color: #999;
  box-shadow: none;
}
.emotion-suggestions {
  position: absolute;
  bottom: 140rpx;
  left: 20rpx;
  right: 20rpx;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}
.suggestions-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20rpx;
  display: block;
}
.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.suggestion-item {
  padding: 20rpx;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 12rpx;
  border: 1rpx solid rgba(233, 236, 239, 0.5);
  transition: all 0.3s ease;
}
.suggestion-item:active {
  background: rgba(233, 236, 239, 0.8);
  transform: scale(0.98);
}
.suggestion-text {
  font-size: 26rpx;
  color: #495057;
}

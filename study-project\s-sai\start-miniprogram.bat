@echo off
echo ========================================
echo AI智能家教小程序 - 启动脚本
echo ========================================
echo.

echo 检查项目结构...
if exist "pages\ai-home\ai-home.vue" (
    echo ✓ AI家教首页已创建
) else (
    echo ✗ AI家教首页缺失
)

if exist "pages\login\login.vue" (
    echo ✓ 登录页面存在
) else (
    echo ✗ 登录页面缺失
)

if exist "pages\register\register.vue" (
    echo ✓ 注册页面存在
) else (
    echo ✗ 注册页面缺失
)

if exist "pages\index\index.vue" (
    echo ✓ 首页存在
) else (
    echo ✗ 首页缺失
)

if exist "pages.json" (
    echo ✓ 页面配置文件存在
) else (
    echo ✗ 页面配置文件缺失
)

if exist "manifest.json" (
    echo ✓ 应用配置文件存在
) else (
    echo ✗ 应用配置文件缺失
)

echo.
echo 项目功能说明:
echo ========================================
echo 📱 人脸登录 → AI家教首页
echo 🤖 AI语音对话与智能功能
echo 👁️ 实时学习监控
echo 📷 拍照搜题辅导
echo 📹 视频通话功能
echo 📝 模拟考试系统
echo 📚 课本同步学习
echo ⚙️ 个性化设置
echo 👨‍👩‍👧‍👦 家长中心
echo 💎 会员权益系统
echo ========================================
echo.

echo 使用说明:
echo 1. 在微信开发者工具中打开此项目
echo 2. 选择小程序项目类型
echo 3. 项目目录选择: %cd%
echo 4. AppID可以使用测试号
echo 5. 点击编译即可预览
echo.

echo 功能特色:
echo • 人脸识别登录后自动跳转AI家教首页
echo • 9大智能功能模块，支持长按查看详情
echo • 实时学习监控，专注度分析
echo • 4大快捷功能，一键直达
echo • 个性化设置，满足不同需求
echo • 简约大气的现代化界面设计
echo.
echo 🎨 全新简约配色:
echo • 浅灰白色背景，温和护眼
echo • 深蓝灰色主色调，专业稳重
echo • 扁平化图标设计，简洁明了
echo • 去除花哨渐变，突出内容本身
echo.

echo 💡 提示: 长按功能图标可查看详细说明和使用示例
echo.

pause

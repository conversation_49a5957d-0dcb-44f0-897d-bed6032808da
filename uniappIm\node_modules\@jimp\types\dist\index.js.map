{"version": 3, "sources": ["../src/index.js"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAEA;;AACA;;AACA;;AACA;;AACA;;eAEe;AAAA,SAAM,qBAAU,uBAAV,EAAkB,sBAAlB,EAAyB,sBAAzB,EAAgC,uBAAhC,EAAwC,sBAAxC,CAAN;AAAA,C", "sourcesContent": ["import { mergeDeep } from 'timm';\n\nimport jpeg from '@jimp/jpeg';\nimport png from '@jimp/png';\nimport bmp from '@jimp/bmp';\nimport tiff from '@jimp/tiff';\nimport gif from '@jimp/gif';\n\nexport default () => mergeDeep(jpeg(), png(), bmp(), tiff(), gif());\n"], "file": "index.js"}
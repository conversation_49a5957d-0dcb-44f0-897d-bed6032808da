{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_helperReplaceSupers", "_core", "replacePropertySuper", "path", "getObjectRef", "file", "replaceSupers", "ReplaceSupers", "methodPath", "replace", "_default", "exports", "default", "declare", "api", "assertVersion", "newLets", "Set", "name", "visitor", "Loop", "exit", "for<PERSON>ach", "v", "scopePath", "scope", "push", "id", "kind", "crawl", "requeue", "delete", "ObjectExpression", "state", "objectRef", "generateUidIdentifier", "get", "prop<PERSON>ath", "isMethod", "findParent", "p", "isFunction", "isProgram", "isLoop", "useLet", "add", "t", "cloneNode", "replaceWith", "assignmentExpression", "node"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport ReplaceSupers from \"@babel/helper-replace-supers\";\nimport { types as t } from \"@babel/core\";\nimport type { File, NodePath } from \"@babel/core\";\n\nfunction replacePropertySuper(\n  path: NodePath<t.ObjectMethod>,\n  getObjectRef: () => t.Identifier,\n  file: File,\n) {\n  // @ts-expect-error todo(flow->ts):\n  const replaceSupers = new ReplaceSupers({\n    getObjectRef: getObjectRef,\n    methodPath: path,\n    file: file,\n  });\n\n  replaceSupers.replace();\n}\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n  const newLets = new Set<{\n    scopePath: NodePath;\n    id: t.Identifier;\n  }>();\n\n  return {\n    name: \"transform-object-super\",\n\n    visitor: {\n      Loop: {\n        exit(path) {\n          newLets.forEach(v => {\n            if (v.scopePath === path) {\n              path.scope.push({\n                id: v.id,\n                kind: \"let\",\n              });\n              path.scope.crawl();\n              path.requeue();\n              newLets.delete(v);\n            }\n          });\n        },\n      },\n      ObjectExpression(path, state) {\n        let objectRef: t.Identifier;\n        const getObjectRef = () =>\n          (objectRef = objectRef || path.scope.generateUidIdentifier(\"obj\"));\n\n        path.get(\"properties\").forEach(propPath => {\n          if (!propPath.isMethod()) return;\n\n          replacePropertySuper(propPath, getObjectRef, state.file);\n        });\n\n        if (objectRef) {\n          const scopePath = path.findParent(\n            p => p.isFunction() || p.isProgram() || p.isLoop(),\n          );\n          const useLet = scopePath.isLoop();\n          // For transform-block-scoping\n          if (useLet) {\n            newLets.add({ scopePath, id: t.cloneNode(objectRef) });\n          } else {\n            path.scope.push({\n              id: t.cloneNode(objectRef),\n              kind: \"var\",\n            });\n          }\n\n          path.replaceWith(\n            t.assignmentExpression(\"=\", t.cloneNode(objectRef), path.node),\n          );\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,oBAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAGA,SAASG,oBAAoBA,CAC3BC,IAA8B,EAC9BC,YAAgC,EAChCC,IAAU,EACV;EAEA,MAAMC,aAAa,GAAG,IAAIC,4BAAa,CAAC;IACtCH,YAAY,EAAEA,YAAY;IAC1BI,UAAU,EAAEL,IAAI;IAChBE,IAAI,EAAEA;EACR,CAAC,CAAC;EAEFC,aAAa,CAACG,OAAO,CAAC,CAAC;AACzB;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEc,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAkB,CAAE,CAAC;EACtC,MAAMC,OAAO,GAAG,IAAIC,GAAG,CAGpB,CAAC;EAEJ,OAAO;IACLC,IAAI,EAAE,wBAAwB;IAE9BC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAIA,CAAClB,IAAI,EAAE;UACTa,OAAO,CAACM,OAAO,CAACC,CAAC,IAAI;YACnB,IAAIA,CAAC,CAACC,SAAS,KAAKrB,IAAI,EAAE;cACxBA,IAAI,CAACsB,KAAK,CAACC,IAAI,CAAC;gBACdC,EAAE,EAAEJ,CAAC,CAACI,EAAE;gBACRC,IAAI,EAAE;cACR,CAAC,CAAC;cACFzB,IAAI,CAACsB,KAAK,CAACI,KAAK,CAAC,CAAC;cAClB1B,IAAI,CAAC2B,OAAO,CAAC,CAAC;cACdd,OAAO,CAACe,MAAM,CAACR,CAAC,CAAC;YACnB;UACF,CAAC,CAAC;QACJ;MACF,CAAC;MACDS,gBAAgBA,CAAC7B,IAAI,EAAE8B,KAAK,EAAE;QAC5B,IAAIC,SAAuB;QAC3B,MAAM9B,YAAY,GAAGA,CAAA,KAClB8B,SAAS,GAAGA,SAAS,IAAI/B,IAAI,CAACsB,KAAK,CAACU,qBAAqB,CAAC,KAAK,CAAE;QAEpEhC,IAAI,CAACiC,GAAG,CAAC,YAAY,CAAC,CAACd,OAAO,CAACe,QAAQ,IAAI;UACzC,IAAI,CAACA,QAAQ,CAACC,QAAQ,CAAC,CAAC,EAAE;UAE1BpC,oBAAoB,CAACmC,QAAQ,EAAEjC,YAAY,EAAE6B,KAAK,CAAC5B,IAAI,CAAC;QAC1D,CAAC,CAAC;QAEF,IAAI6B,SAAS,EAAE;UACb,MAAMV,SAAS,GAAGrB,IAAI,CAACoC,UAAU,CAC/BC,CAAC,IAAIA,CAAC,CAACC,UAAU,CAAC,CAAC,IAAID,CAAC,CAACE,SAAS,CAAC,CAAC,IAAIF,CAAC,CAACG,MAAM,CAAC,CACnD,CAAC;UACD,MAAMC,MAAM,GAAGpB,SAAS,CAACmB,MAAM,CAAC,CAAC;UAEjC,IAAIC,MAAM,EAAE;YACV5B,OAAO,CAAC6B,GAAG,CAAC;cAAErB,SAAS;cAAEG,EAAE,EAAEmB,WAAC,CAACC,SAAS,CAACb,SAAS;YAAE,CAAC,CAAC;UACxD,CAAC,MAAM;YACL/B,IAAI,CAACsB,KAAK,CAACC,IAAI,CAAC;cACdC,EAAE,EAAEmB,WAAC,CAACC,SAAS,CAACb,SAAS,CAAC;cAC1BN,IAAI,EAAE;YACR,CAAC,CAAC;UACJ;UAEAzB,IAAI,CAAC6C,WAAW,CACdF,WAAC,CAACG,oBAAoB,CAAC,GAAG,EAAEH,WAAC,CAACC,SAAS,CAACb,SAAS,CAAC,EAAE/B,IAAI,CAAC+C,IAAI,CAC/D,CAAC;QACH;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}
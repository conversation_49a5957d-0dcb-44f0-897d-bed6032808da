# AI对话功能数据库设计说明

## 概述

本设计为AiTutor项目新增AI对话功能的数据库表结构，支持单个用户多个对话、流式输出、消息管理等功能。

## 设计原则

1. **不修改现有表结构** - 只新增表，保持现有系统稳定性
2. **支持多对话管理** - 每个用户可以创建多个独立的对话会话
3. **流式输出支持** - 完整的流式输出状态管理
4. **扩展性设计** - 支持多种消息类型和AI模型
5. **统计与反馈** - 完整的使用统计和用户反馈机制

## 表结构设计

### 1. ai_conversation (AI对话会话表)

**功能**: 管理用户的多个对话会话

**主要字段**:
- `id`: 对话唯一标识
- `user_id`: 关联现有用户表
- `conversation_title`: 对话标题（可自动生成或用户自定义）
- `session_id`: AI服务的会话ID（对接ai-appservice）
- `conversation_type`: 对话类型（通用、辅导、语音、图像）
- `status`: 对话状态（活跃、已归档、已删除）
- `last_message_time`: 最后消息时间
- `message_count`: 消息总数

**索引设计**:
- 用户ID索引：快速查询用户的所有对话
- 会话ID索引：通过AI服务会话ID快速定位
- 状态+更新时间复合索引：支持按状态和时间排序

### 2. ai_message (AI消息记录表)

**功能**: 存储所有对话消息，支持流式输出状态管理

**主要字段**:
- `id`: 消息唯一标识
- `conversation_id`: 关联对话会话
- `user_id`: 消息所属用户
- `message_type`: 消息类型（用户消息、AI回复、系统消息）
- `content_type`: 内容类型（文本、图片、文件、语音、系统通知）
- `content`: 消息内容（支持长文本）
- `ai_model`: AI模型名称
- `prompt_tokens/completion_tokens/total_tokens`: Token使用统计
- `response_time`: 响应时间统计
- `is_streaming`: 是否流式输出
- `stream_status`: 流式状态（待处理、流式中、已完成、失败）
- `error_message`: 错误信息
- `metadata`: 扩展元数据（JSON格式）

**流式输出支持**:
- `is_streaming`: 标识是否为流式输出消息
- `stream_status`: 跟踪流式输出状态
- 支持实时更新消息内容和状态

### 3. ai_conversation_config (AI对话配置表)

**功能**: 存储每个对话的个性化配置

**主要字段**:
- `conversation_id`: 关联对话（一对一关系）
- `ai_model`: AI模型选择
- `temperature`: 温度参数
- `max_tokens`: 最大token数
- `system_prompt`: 系统提示词
- `enable_streaming`: 是否启用流式输出
- `enable_context`: 是否启用上下文
- `context_length`: 上下文长度
- `auto_title`: 是否自动生成标题

### 4. ai_usage_statistics (AI使用统计表)

**功能**: 记录用户AI使用情况，支持数据分析

**主要字段**:
- `user_id`: 用户ID
- `date`: 统计日期
- `conversation_count`: 对话数量
- `message_count`: 消息数量
- `user_message_count/ai_message_count`: 分类消息统计
- `total_tokens/prompt_tokens/completion_tokens`: Token使用统计
- `total_response_time/avg_response_time`: 响应时间统计
- `streaming_count`: 流式输出次数
- `error_count`: 错误次数

### 5. ai_feedback (AI反馈表)

**功能**: 收集用户对AI回复的反馈

**主要字段**:
- `message_id`: 关联具体消息
- `feedback_type`: 反馈类型（喜欢、不喜欢、举报）
- `feedback_reason`: 反馈原因
- `feedback_content`: 详细反馈内容
- `rating`: 评分（1-5分）

## 与现有系统的集成

### 用户关联
- 所有新表都通过`user_id`字段关联现有的`user`表
- 保持用户数据的一致性

### AI服务对接
- `session_id`字段对接ai-appservice中的会话管理
- 支持DashScopeAiUtil的流式输出功能

### 消息系统扩展
- 可与现有的`message`表并存
- AI消息与普通用户消息分离管理

## 流式输出实现方案

### 数据库层面
1. 创建消息记录时设置`is_streaming=1`，`stream_status='PENDING'`
2. 开始流式输出时更新`stream_status='STREAMING'`
3. 实时更新`content`字段内容
4. 完成时设置`stream_status='COMPLETED'`

### 应用层面
1. 利用现有的SseEmitter实现
2. 结合数据库状态管理
3. 支持断线重连和状态恢复

## 性能优化

### 索引策略
- 主要查询路径都有对应索引
- 复合索引支持常见查询场景

### 数据分区
- 可按时间对大表进行分区
- 历史数据可定期归档

### 缓存策略
- 活跃对话可缓存到Redis
- 配置信息适合缓存

## 扩展性考虑

### 多模态支持
- `content_type`支持图片、语音等类型
- `metadata`字段支持扩展信息

### 多AI模型支持
- `ai_model`字段支持不同AI服务
- 配置表支持模型特定参数

### 国际化支持
- 使用utf8mb4字符集
- 支持多语言内容

## 安全性考虑

### 数据隔离
- 严格的用户ID隔离
- 防止跨用户数据访问

### 敏感信息保护
- 可对敏感内容进行加密存储
- 审计日志记录

## 部署说明

1. 执行`ai_conversation_tables.sql`创建表结构
2. 确认索引创建成功
3. 配置应用程序连接
4. 进行功能测试

## 后续优化建议

1. 根据实际使用情况调整索引
2. 监控表大小，考虑分区策略
3. 收集用户反馈，优化表结构
4. 定期分析统计数据，优化AI服务

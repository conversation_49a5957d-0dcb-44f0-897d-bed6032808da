<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="PhpCodeSniffer">
    <phpcs_settings>
      <PhpCSConfiguration max_messages_per_file="100" standards="Drupal;DrupalPractice;ItgalaxyCodingStandards;MySource;PEAR;PHPCS;PSR1;PSR2;Squiz;WordPress;WordPress-Core;WordPress-Docs;WordPress-Extra;WordPress-VIP;Zend" tool_path="$PROJECT_DIR$/../insteria/wp-content/themes/insteria/vendor/bin/phpcs" timeout="30000" />
    </phpcs_settings>
  </component>
  <component name="PhpProjectSharedConfiguration" php_language_level="7.1" />
</project>
{"version": 3, "sources": ["../src/index.js"], "names": ["MIME_TYPE", "mime", "constants", "MIME_GIF", "decoders", "data", "gifObj", "GIF", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gifData", "<PERSON><PERSON><PERSON>", "alloc", "width", "height", "decodeAndBlitFrameRGBA"], "mappings": ";;;;;;;;;;;AAAA;;AAEA,IAAMA,SAAS,GAAG,WAAlB;;eAEe;AAAA,SAAO;AACpBC,IAAAA,IAAI,uCAAKD,SAAL,EAAiB,CAAC,KAAD,CAAjB,CADgB;AAGpBE,IAAAA,SAAS,EAAE;AACTC,MAAAA,QAAQ,EAAEH;AADD,KAHS;AAOpBI,IAAAA,QAAQ,uCACLJ,SADK,EACO,UAAAK,IAAI,EAAI;AACnB,UAAMC,MAAM,GAAG,IAAIC,mBAAIC,SAAR,CAAkBH,IAAlB,CAAf;AACA,UAAMI,OAAO,GAAGC,MAAM,CAACC,KAAP,CAAaL,MAAM,CAACM,KAAP,GAAeN,MAAM,CAACO,MAAtB,GAA+B,CAA5C,CAAhB;AAEAP,MAAAA,MAAM,CAACQ,sBAAP,CAA8B,CAA9B,EAAiCL,OAAjC;AAEA,aAAO;AACLJ,QAAAA,IAAI,EAAEI,OADD;AAELG,QAAAA,KAAK,EAAEN,MAAM,CAACM,KAFT;AAGLC,QAAAA,MAAM,EAAEP,MAAM,CAACO;AAHV,OAAP;AAKD,KAZK;AAPY,GAAP;AAAA,C", "sourcesContent": ["import GIF from 'omggif';\n\nconst MIME_TYPE = 'image/gif';\n\nexport default () => ({\n  mime: { [MIME_TYPE]: ['gif'] },\n\n  constants: {\n    MIME_GIF: MIME_TYPE\n  },\n\n  decoders: {\n    [MIME_TYPE]: data => {\n      const gifObj = new GIF.GifReader(data);\n      const gifData = Buffer.alloc(gifObj.width * gifObj.height * 4);\n\n      gifObj.decodeAndBlitFrameRGBA(0, gifData);\n\n      return {\n        data: gifData,\n        width: gifObj.width,\n        height: gifObj.height\n      };\n    }\n  }\n});\n"], "file": "index.js"}
var has = require('./has');
var isArr = require('./isArr');
exports = function(str, obj) {
    if (isArr(str)) return str;
    if (obj && has(obj, str)) return [str];
    var ret = [];
    str.replace(regPropName, function(match, number, quote, str) {
        ret.push(quote ? str.replace(regEscapeChar, '$1') : number || match);
    });
    return ret;
};

var regPropName = /[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g;
var regEscapeChar = /\\(\\)?/g;

module.exports = exports;

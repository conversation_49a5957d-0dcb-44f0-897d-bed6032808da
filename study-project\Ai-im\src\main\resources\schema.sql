-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS tutor CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE tutor;

-- 删除现有表（如果存在）
DROP TABLE IF EXISTS friend_request;
DROP TABLE IF EXISTS friendship;
DROP TABLE IF EXISTS message;
DROP TABLE IF EXISTS user;

-- 创建用户表
CREATE TABLE user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(30) NOT NULL UNIQUE COMMENT '用户账号',
    nickname VARCHAR(30) NOT NULL COMMENT '用户昵称',
    user_type VARCHAR(2) DEFAULT '00' COMMENT '用户类型(00系统用户)',
    email VARCHAR(50) COMMENT '用户邮箱',
    phonenumber VARCHAR(11) COMMENT '手机号码',
    sex VARCHAR(1) DEFAULT '2' COMMENT '用户性别(0男 1女 2未知)',
    avatar VARCHAR(100) COMMENT '头像地址',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    status INT DEFAULT 0 COMMENT '帐号状态(0正常 1停用)',
    del_flag VARCHAR(1) DEFAULT '0' COMMENT '删除标志(0代表存在 2代表删除)',
    login_date DATETIME COMMENT '最后登录时间',
    create_by VARCHAR(64) COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) COMMENT '备注',
    userStatus VARCHAR(20) DEFAULT 'OFFLINE' COMMENT '用户在线状态',
    user VARBINARY(255) COMMENT '用户信息'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户信息表';

-- 创建好友关系表
CREATE TABLE friendship (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '好友关系ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    friend_id BIGINT NOT NULL COMMENT '好友ID',
    remark VARCHAR(100) COMMENT '好友备注名称',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '关系状态(ACTIVE-正常, BLOCKED-已屏蔽, DELETED-已删除)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id),
    INDEX idx_friend_id (friend_id),
    INDEX idx_status (status),
    UNIQUE KEY uk_user_friend (user_id, friend_id),
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
    FOREIGN KEY (friend_id) REFERENCES user(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='好友关系表';

-- 创建好友申请表
CREATE TABLE friend_request (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '申请ID',
    from_user_id BIGINT NOT NULL COMMENT '申请人用户ID',
    to_user_id BIGINT NOT NULL COMMENT '被申请人用户ID',
    message VARCHAR(200) COMMENT '申请消息',
    status VARCHAR(20) DEFAULT 'PENDING' COMMENT '申请状态(PENDING-待处理, ACCEPTED-已同意, REJECTED-已拒绝, EXPIRED-已过期)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_from_user (from_user_id),
    INDEX idx_to_user (to_user_id),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time),
    UNIQUE KEY uk_from_to_user (from_user_id, to_user_id),
    FOREIGN KEY (from_user_id) REFERENCES user(id) ON DELETE CASCADE,
    FOREIGN KEY (to_user_id) REFERENCES user(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='好友申请表';

-- 创建消息表
CREATE TABLE message (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '消息ID',
    from_user_id BIGINT NOT NULL COMMENT '发送者用户ID',
    to_user_id BIGINT NOT NULL COMMENT '接收者用户ID',
    content TEXT NOT NULL COMMENT '消息内容',
    type VARCHAR(20) DEFAULT 'TEXT' COMMENT '消息类型',
    status VARCHAR(20) DEFAULT 'SENT' COMMENT '消息状态',
    send_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '发送时间',
    read_status TINYINT DEFAULT 0 COMMENT '已读状态(0未读 1已读)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_from_user (from_user_id),
    INDEX idx_to_user (to_user_id),
    INDEX idx_send_time (send_time),
    FOREIGN KEY (from_user_id) REFERENCES user(id) ON DELETE CASCADE,
    FOREIGN KEY (to_user_id) REFERENCES user(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息表';

-- 插入测试数据
INSERT INTO user (username, nickname, password, email, create_time) VALUES
('admin', '管理员', 'admin123', '<EMAIL>', NOW()),
('test', '测试用户', 'test123', '<EMAIL>', NOW()),
('user1', '用户1', 'password123', '<EMAIL>', NOW()),
('user2', '用户2', 'password123', '<EMAIL>', NOW());

-- 插入测试消息
INSERT INTO message (from_user_id, to_user_id, content, send_time) VALUES
(1, 2, '你好！欢迎使用IM系统', NOW()),
(2, 1, '谢谢！系统很不错', NOW()),
(1, 3, '大家好', NOW()),
(3, 1, '管理员好', NOW());
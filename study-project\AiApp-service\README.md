# AI App Service

## 概述

AI App Service 是 AiTutor 项目的AI应用服务模块，集成了阿里云百炼AI服务，提供智能对话功能。

## 功能特性

- 🤖 **阿里云百炼AI集成** - 支持单次对话和多轮会话
- 🔧 **工具类封装** - 提供易用的AI调用工具类
- 🌐 **RESTful API** - 完整的HTTP接口支持
- 📝 **会话管理** - 支持会话ID的传递和管理
- 🔍 **健康检查** - 服务状态监控和AI服务可用性检查

## API 接口

### 基础接口

#### 1. 健康检查
```
GET /api/ai/test/health
```

#### 2. AI服务可用性检查
```
GET /api/ai/test/check
```

### AI对话接口

#### 3. 简单对话（GET方式）
```
GET /api/ai/test/simple?prompt=你好
```

#### 4. 单次对话（POST方式）
```
POST /api/ai/test/single
Content-Type: application/json

{
    "prompt": "你是谁？"
}
```

#### 5. 会话对话
```
POST /api/ai/test/session
Content-Type: application/json

{
    "prompt": "你有什么技能？",
    "sessionId": "session-id-from-previous-call"
}
```

#### 6. 多轮对话测试
```
POST /api/ai/test/multi-turn
```

### 流式输出接口

#### 7. 流式对话（GET方式）
```
GET /api/ai/test/stream?prompt=你好
Accept: text/event-stream
```

#### 8. 流式对话（POST方式）
```
POST /api/ai/test/stream
Content-Type: application/json
Accept: text/event-stream

{
    "prompt": "请介绍一下人工智能",
    "sessionId": "session-id-from-previous-call"
}
```

#### 9. 快速流式对话（按句子分割）
```
GET /api/ai/test/fast-stream?prompt=请介绍人工智能的发展历史
Accept: text/event-stream
```

## 配置说明

### application.yml
```yaml
server:
  port: 8082

dashscope:
  api-key: sk-0e7519f33026415d9b58ff4679ead7c4
  app-id: b8e521e1b0ee4a9cad5b00495a701f16
```

### 环境变量支持
```bash
export DASHSCOPE_API_KEY=your-api-key
export DASHSCOPE_APP_ID=your-app-id
```

## 使用示例

### 1. 启动服务
```bash
mvn spring-boot:run -pl AiApp-service
```

### 2. 测试AI服务
```bash
# 健康检查
curl http://localhost:8082/api/ai/test/health

# 检查AI服务
curl http://localhost:8082/api/ai/test/check

# 简单对话
curl "http://localhost:8082/api/ai/test/simple?prompt=你好"

# 单次对话
curl -X POST http://localhost:8082/api/ai/test/single \
  -H "Content-Type: application/json" \
  -d '{"prompt": "你是谁？"}'

# 会话对话
curl -X POST http://localhost:8082/api/ai/test/session \
  -H "Content-Type: application/json" \
  -d '{"prompt": "你有什么技能？", "sessionId": "your-session-id"}'

# 多轮对话测试
curl -X POST http://localhost:8082/api/ai/test/multi-turn

# 流式对话测试
curl -N -H "Accept: text/event-stream" \
  "http://localhost:8082/api/ai/test/stream?prompt=请介绍一下你自己"

# 快速流式对话测试
curl -N -H "Accept: text/event-stream" \
  "http://localhost:8082/api/ai/test/fast-stream?prompt=请介绍人工智能"
```

## 工具类使用

### DashScopeAiUtil

```java
@Autowired
private DashScopeAiUtil dashScopeAiUtil;

// 单次调用
AiResponse response = dashScopeAiUtil.singleCall("你好");

// 会话调用
AiResponse response = dashScopeAiUtil.callWithSession("你好", sessionId);

// 多轮对话
AiResponse response = dashScopeAiUtil.multiTurnConversation();

// 检查服务可用性
boolean available = dashScopeAiUtil.isServiceAvailable();
```

## 响应格式

所有API接口都返回统一的响应格式：

```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "prompt": "你好",
        "response": "你好！我是通义千问，一个由阿里云开发的AI助手...",
        "sessionId": "session-12345",
        "success": true,
        "timestamp": "2024-07-29T07:50:00"
    },
    "timestamp": 1690603800000
}
```

## 注意事项

1. **API Key安全**: 生产环境中请使用环境变量配置API Key
2. **会话管理**: 会话ID用于维持多轮对话的上下文
3. **错误处理**: 所有异常都会被捕获并返回友好的错误信息
4. **日志记录**: 所有AI调用都会记录详细的日志信息

## 依赖版本

- Spring Boot: 3.1.5
- DashScope SDK: 2.21.0
- Java: 17+

{"name": "file-type", "version": "9.0.0", "description": "Detect the file type of a Buffer/Uint8Array", "license": "MIT", "repository": "sindresorhus/file-type", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["mime", "file", "type", "archive", "image", "img", "pic", "picture", "flash", "photo", "video", "detect", "check", "is", "exif", "exe", "binary", "buffer", "uint8array", "jpg", "png", "gif", "webp", "flif", "cr2", "tif", "bmp", "jxr", "psd", "zip", "tar", "rar", "gz", "bz2", "7z", "dmg", "mp4", "m4v", "mid", "mkv", "webm", "mov", "avi", "mpg", "mp2", "mp3", "m4a", "ogg", "opus", "flac", "wav", "amr", "pdf", "epub", "mobi", "swf", "rtf", "woff", "woff2", "eot", "ttf", "otf", "ico", "flv", "ps", "xz", "sqlite", "xpi", "cab", "deb", "ar", "rpm", "Z", "lz", "msi", "mxf", "mts", "wasm", "webassembly", "blend", "bpg", "docx", "pptx", "xlsx", "3gp", "jp2", "jpm", "jpx", "mj2", "aif", "odt", "ods", "odp", "xml", "heic"], "devDependencies": {"ava": "*", "read-chunk": "^2.0.0", "xo": "*"}}
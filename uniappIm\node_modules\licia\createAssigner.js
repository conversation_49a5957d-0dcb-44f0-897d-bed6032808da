var isUndef = require('./isUndef');
var each = require('./each');
exports = function(keysFn, defaults) {
    return function(obj) {
        each(arguments, function(src, idx) {
            if (idx === 0) return;
            var keys = keysFn(src);
            each(keys, function(key) {
                if (!defaults || isUndef(obj[key])) obj[key] = src[key];
            });
        });
        return obj;
    };
};

module.exports = exports;

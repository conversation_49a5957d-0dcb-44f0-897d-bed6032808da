const set = require('regenerate')(0x21, 0x2E, 0x3F, 0x589, 0x6D4, 0x7F9, 0x837, 0x839, 0x1362, 0x166E, 0x1803, 0x1809, 0x2024, 0x2E2E, 0x2E3C, 0x3002, 0xA4FF, 0xA6F3, 0xA6F7, 0xA92F, 0xABEB, 0xFE12, 0xFE52, 0xFF01, 0xFF0E, 0xFF1F, 0xFF61, 0x111CD, 0x112A9, 0x11944, 0x11946, 0x16AF5, 0x16B44, 0x16E98, 0x1BC9F, 0x1DA88);
set.addRange(0x61D, 0x61F).addRange(0x700, 0x702).addRange(0x83D, 0x83E).addRange(0x964, 0x965).addRange(0x104A, 0x104B).addRange(0x1367, 0x1368).addRange(0x1735, 0x1736).addRange(0x17D4, 0x17D5).addRange(0x1944, 0x1945).addRange(0x1AA8, 0x1AAB).addRange(0x1B4E, 0x1B4F).addRange(0x1B5A, 0x1B5B).addRange(0x1B5E, 0x1B5F).addRange(0x1B7D, 0x1B7F).addRange(0x1C3B, 0x1C3C).addRange(0x1C7E, 0x1C7F).addRange(0x203C, 0x203D).addRange(0x2047, 0x2049).addRange(0x2CF9, 0x2CFB).addRange(0x2E53, 0x2E54).addRange(0xA60E, 0xA60F).addRange(0xA876, 0xA877).addRange(0xA8CE, 0xA8CF).addRange(0xA9C8, 0xA9C9).addRange(0xAA5D, 0xAA5F).addRange(0xAAF0, 0xAAF1).addRange(0xFE15, 0xFE16).addRange(0xFE56, 0xFE57).addRange(0x10A56, 0x10A57).addRange(0x10F55, 0x10F59).addRange(0x10F86, 0x10F89).addRange(0x11047, 0x11048).addRange(0x110BE, 0x110C1).addRange(0x11141, 0x11143).addRange(0x111C5, 0x111C6).addRange(0x111DE, 0x111DF).addRange(0x11238, 0x11239).addRange(0x1123B, 0x1123C).addRange(0x113D4, 0x113D5).addRange(0x1144B, 0x1144C).addRange(0x115C2, 0x115C3).addRange(0x115C9, 0x115D7).addRange(0x11641, 0x11642).addRange(0x1173C, 0x1173E).addRange(0x11A42, 0x11A43).addRange(0x11A9B, 0x11A9C).addRange(0x11C41, 0x11C42).addRange(0x11EF7, 0x11EF8).addRange(0x11F43, 0x11F44).addRange(0x16A6E, 0x16A6F).addRange(0x16B37, 0x16B38);
set.addRange(0x16D6E, 0x16D6F);
exports.characters = set;

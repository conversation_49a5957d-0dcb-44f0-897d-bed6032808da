<template>
  <view class="container">
    <!-- 顶部标题 -->
    <view class="header">
      <text class="page-title">💎 精选单词</text>
      <text class="page-subtitle">每日单词学习</text>
    </view>
    
    <!-- 学习进度 -->
    <view class="progress-section">
      <text class="progress-title">今日学习进度</text>
      <view class="progress-bar">
        <view class="progress-fill" style="width: 60%"></view>
      </view>
      <text class="progress-text">12/20 个单词</text>
    </view>
    
    <!-- 今日单词 -->
    <view class="word-section">
      <text class="section-title">📖 今日单词</text>
      <view class="word-card">
        <text class="word-english">Beautiful</text>
        <text class="word-phonetic">/ˈbjuːtɪfl/</text>
        <text class="word-chinese">美丽的，漂亮的</text>
        <view class="word-example">
          <text class="example-label">例句：</text>
          <text class="example-text">She has a beautiful smile.</text>
        </view>
      </view>
    </view>
    
    <!-- 单词分类 -->
    <view class="category-section">
      <text class="section-title">📚 单词分类</text>
      <view class="category-list">
        <view class="category-item">
          <view class="category-icon">🎓</view>
          <text class="category-name">四级词汇</text>
          <text class="category-count">1200词</text>
        </view>
        <view class="category-item">
          <view class="category-icon">🏆</view>
          <text class="category-name">六级词汇</text>
          <text class="category-count">800词</text>
        </view>
        <view class="category-item">
          <view class="category-icon">🌟</view>
          <text class="category-name">托福词汇</text>
          <text class="category-count">2000词</text>
        </view>
      </view>
    </view>
    
    <!-- 底部导航栏 -->
    <view class="bottom-navigation">
      <view class="nav-item" @tap="switchTab('home')">
        <text class="nav-icon">🏠</text>
        <text class="nav-text">首页</text>
      </view>
      <view class="nav-item" @tap="switchTab('study')">
        <text class="nav-icon">📚</text>
        <text class="nav-text">学习路线</text>
      </view>

      <!-- 中间的拍照搜题按钮 -->
      <view class="camera-btn" @tap="takePhoto">
        <view class="camera-icon">📷</view>
      </view>

      <view class="nav-item active" @tap="switchTab('tools')">
        <text class="nav-icon">💎</text>
        <text class="nav-text">精选单词</text>
      </view>
      <view class="nav-item" @tap="switchTab('profile')">
        <text class="nav-icon">👤</text>
        <text class="nav-text">主页</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      
    }
  },
  methods: {
    switchTab(tab) {
      switch(tab) {
        case 'home':
          uni.reLaunch({
            url: '/pages/index/index'
          });
          break;
        case 'study':
          uni.reLaunch({
            url: '/pages/study-route/study-route'
          });
          break;
        case 'tools':
          // 当前页面，不需要跳转
          break;
        case 'profile':
          uni.reLaunch({
            url: '/pages/profile/profile'
          });
          break;
      }
    },

    // 拍照搜题功能
    takePhoto() {
      uni.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'],
        sourceType: ['camera'],
        success: (res) => {
          uni.showToast({
            title: '拍照成功！正在识别题目...',
            icon: 'success',
            duration: 2000
          });
        },
        fail: (err) => {
          uni.showToast({
            title: '拍照失败，请重试',
            icon: 'none'
          });
        }
      });
    }
  }
}
</script>

<style>
.container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 20rpx;
  padding-bottom: 140rpx;
}

.header {
  text-align: center;
  padding: 40rpx 0;
  background: white;
  border-radius: 15rpx;
  margin-bottom: 30rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  display: block;
  margin-bottom: 10rpx;
}

.page-subtitle {
  font-size: 24rpx;
  color: #6c757d;
}

.progress-section {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx 20rpx;
  margin-bottom: 30rpx;
  text-align: center;
}

.progress-title {
  font-size: 26rpx;
  color: #2c3e50;
  display: block;
  margin-bottom: 20rpx;
}

.progress-bar {
  width: 100%;
  height: 10rpx;
  background: #e9ecef;
  border-radius: 5rpx;
  margin-bottom: 15rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007AFF, #5856D6);
  border-radius: 5rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #6c757d;
}

.word-section {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx 20rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  display: block;
  margin-bottom: 20rpx;
}

.word-card {
  text-align: center;
  padding: 30rpx 20rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
}

.word-english {
  font-size: 40rpx;
  font-weight: bold;
  color: #007AFF;
  display: block;
  margin-bottom: 10rpx;
}

.word-phonetic {
  font-size: 24rpx;
  color: #6c757d;
  display: block;
  margin-bottom: 15rpx;
}

.word-chinese {
  font-size: 28rpx;
  color: #2c3e50;
  display: block;
  margin-bottom: 20rpx;
}

.word-example {
  text-align: left;
  padding: 20rpx;
  background: white;
  border-radius: 8rpx;
}

.example-label {
  font-size: 22rpx;
  color: #6c757d;
  font-weight: bold;
}

.example-text {
  font-size: 24rpx;
  color: #2c3e50;
  font-style: italic;
}

.category-section {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx 20rpx;
}

.category-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 25rpx 20rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
}

.category-icon {
  font-size: 35rpx;
  margin-right: 20rpx;
}

.category-name {
  flex: 1;
  font-size: 26rpx;
  font-weight: bold;
  color: #2c3e50;
}

.category-count {
  font-size: 22rpx;
  color: #6c757d;
}

/* 底部导航栏样式 */
.bottom-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: white;
  display: flex;
  align-items: center;
  justify-content: space-around;
  border-top: 1rpx solid #e5e5e5;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 10rpx 0;
  transition: all 0.3s ease;
}

.nav-item.active .nav-icon {
  color: #007AFF;
  transform: scale(1.1);
}

.nav-item.active .nav-text {
  color: #007AFF;
  font-weight: 600;
}

.nav-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
  color: #666;
  transition: all 0.3s ease;
}

.nav-text {
  font-size: 20rpx;
  color: #666;
  transition: all 0.3s ease;
}

/* 拍照搜题按钮 */
.camera-btn {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 15rpx rgba(255, 107, 107, 0.4);
  transform: translateY(-10rpx);
  transition: all 0.3s ease;
}

.camera-btn:active {
  transform: translateY(-8rpx) scale(0.95);
  box-shadow: 0 2rpx 10rpx rgba(255, 107, 107, 0.6);
}

.camera-icon {
  font-size: 45rpx;
  color: white;
}
</style>

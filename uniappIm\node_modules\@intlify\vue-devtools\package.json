{"name": "@intlify/vue-devtools", "version": "9.1.9", "description": "@intlify/vue-devtools", "keywords": ["i18n", "internationalization", "intlify", "vue-devtools"], "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/intlify/vue-i18n-next/tree/master/packages/vue-devtools#readme", "repository": {"type": "git", "url": "git+https://github.com/intlify/vue-i18n-next.git", "directory": "packages/vue-devtools"}, "bugs": {"url": "https://github.com/intlify/vue-i18n-next/issues"}, "files": ["index.js", "dist"], "main": "index.js", "module": "dist/vue-devtools.esm-bundler.js", "types": "dist/vue-devtools.d.ts", "dependencies": {"@intlify/message-resolver": "9.1.9", "@intlify/runtime": "9.1.9", "@intlify/shared": "9.1.9"}, "engines": {"node": ">= 10"}, "buildOptions": {"name": "IntlifyVueDevtools", "formats": ["esm-bundler", "cjs"]}, "publishConfig": {"access": "public"}, "sideEffects": false}
{"name": "@vue/reactivity-transform", "version": "3.2.47", "description": "@vue/reactivity-transform", "main": "dist/reactivity-transform.cjs.js", "files": ["dist"], "buildOptions": {"formats": ["cjs"], "prod": false}, "types": "dist/reactivity-transform.d.ts", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/reactivity-transform"}, "keywords": ["vue"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/core/issues"}, "homepage": "https://github.com/vuejs/core/tree/dev/packages/reactivity-transform#readme", "dependencies": {"@babel/parser": "^7.16.4", "@vue/compiler-core": "3.2.47", "@vue/shared": "3.2.47", "estree-walker": "^2.0.2", "magic-string": "^0.25.7"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/types": "^7.16.0"}}
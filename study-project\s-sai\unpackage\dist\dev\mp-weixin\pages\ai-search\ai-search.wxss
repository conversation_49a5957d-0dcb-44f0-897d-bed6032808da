
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}
.header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: #fff;
  border-bottom: 1rpx solid #e9ecef;
}
.header-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}
.header-info {
  flex: 1;
}
.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  display: block;
}
.header-desc {
  font-size: 24rpx;
  color: #6c757d;
  margin-top: 8rpx;
  display: block;
}
.category-tabs {
  display: flex;
  background: #fff;
  border-bottom: 1rpx solid #e9ecef;
  padding: 0 20rpx;
}
.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 10rpx;
  transition: all 0.3s ease;
  border-bottom: 4rpx solid transparent;
}
.tab-item.active {
  border-bottom-color: #007bff;
}
.tab-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}
.tab-text {
  font-size: 24rpx;
  color: #6c757d;
}
.tab-item.active .tab-text {
  color: #007bff;
  font-weight: bold;
}
.chat-area {
  flex: 1;
  padding: 20rpx;
}
.message-item {
  display: flex;
  margin-bottom: 30rpx;
  animation: fadeInUp 0.3s ease;
}
.message-item.user {
  flex-direction: row-reverse;
}
.message-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #2c3e50;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 20rpx;
}
.message-item.user .message-avatar {
  background: #007bff;
}
.avatar-text {
  font-size: 24rpx;
  color: #fff;
  font-weight: bold;
}
.message-content {
  flex: 1;
  max-width: 70%;
}
.message-bubble {
  background: #fff;
  padding: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  position: relative;
}
.message-item.user .message-bubble {
  background: #007bff;
}
.message-text {
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
  display: block;
}
.message-item.user .message-text {
  color: #fff;
}
.message-time {
  font-size: 20rpx;
  color: #999;
  margin-top: 10rpx;
  display: block;
}
.message-item.user .message-time {
  color: rgba(255,255,255,0.8);
}
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #999;
  animation: typing 1.4s infinite;
}
.dot:nth-child(2) { animation-delay: 0.2s;
}
.dot:nth-child(3) { animation-delay: 0.4s;
}
@keyframes typing {
0%, 60%, 100% { opacity: 0.3;
}
30% { opacity: 1;
}
}
@keyframes fadeInUp {
from {
    opacity: 0;
    transform: translateY(20rpx);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
.input-area {
  background: #fff;
  padding: 20rpx;
  border-top: 1rpx solid #e9ecef;
}
.input-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.input-field {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  border: 1rpx solid #e9ecef;
  border-radius: 40rpx;
  font-size: 28rpx;
  background: #f8f9fa;
}
.send-btn {
  width: 120rpx;
  height: 80rpx;
  background: #007bff;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.send-btn.disabled {
  background: #ccc;
  color: #999;
}
.quick-queries {
  position: absolute;
  bottom: 140rpx;
  left: 20rpx;
  right: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}
.quick-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20rpx;
  display: block;
}
.query-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.query-item {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s ease;
}
.query-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}
.query-text {
  font-size: 26rpx;
  color: #495057;
}

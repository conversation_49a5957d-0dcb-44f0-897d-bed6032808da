var VueReactivity=function(t){"use strict";function e(t,e){const n=Object.create(null),s=t.split(",");for(let i=0;i<s.length;i++)n[s[i]]=!0;return e?t=>!!n[t.toLowerCase()]:t=>!!n[t]}const n=()=>{},s=Object.assign,i=Object.prototype.hasOwnProperty,r=(t,e)=>i.call(t,e),c=Array.isArray,o=t=>"[object Map]"===l(t),u=t=>"symbol"==typeof t,a=t=>null!==t&&"object"==typeof t,h=Object.prototype.toString,l=t=>h.call(t),f=t=>"string"==typeof t&&"NaN"!==t&&"-"!==t[0]&&""+parseInt(t,10)===t,_=(t,e)=>!Object.is(t,e);let d;class p{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this.parent=d,!t&&d&&(this.index=(d.scopes||(d.scopes=[])).push(this)-1)}get active(){return this._active}run(t){if(this._active){const e=d;try{return d=this,t()}finally{d=e}}}on(){d=this}off(){d=this.parent}stop(t){if(this._active){let e,n;for(e=0,n=this.effects.length;e<n;e++)this.effects[e].stop();for(e=0,n=this.cleanups.length;e<n;e++)this.cleanups[e]();if(this.scopes)for(e=0,n=this.scopes.length;e<n;e++)this.scopes[e].stop(!0);if(!this.detached&&this.parent&&!t){const t=this.parent.scopes.pop();t&&t!==this&&(this.parent.scopes[this.index]=t,t.index=this.index)}this.parent=void 0,this._active=!1}}}function v(t,e=d){e&&e.active&&e.effects.push(t)}const g=t=>{const e=new Set(t);return e.w=0,e.n=0,e},y=t=>(t.w&m)>0,w=t=>(t.n&m)>0,R=new WeakMap;let b=0,m=1;let S;const k=Symbol(""),O=Symbol("");class j{constructor(t,e=null,n){this.fn=t,this.scheduler=e,this.active=!0,this.deps=[],this.parent=void 0,v(this,n)}run(){if(!this.active)return this.fn();let t=S,e=P;for(;t;){if(t===this)return;t=t.parent}try{return this.parent=S,S=this,P=!0,m=1<<++b,b<=30?(({deps:t})=>{if(t.length)for(let e=0;e<t.length;e++)t[e].w|=m})(this):E(this),this.fn()}finally{b<=30&&(t=>{const{deps:e}=t;if(e.length){let n=0;for(let s=0;s<e.length;s++){const i=e[s];y(i)&&!w(i)?i.delete(t):e[n++]=i,i.w&=~m,i.n&=~m}e.length=n}})(this),m=1<<--b,S=this.parent,P=e,this.parent=void 0,this.deferStop&&this.stop()}}stop(){S===this?this.deferStop=!0:this.active&&(E(this),this.onStop&&this.onStop(),this.active=!1)}}function E(t){const{deps:e}=t;if(e.length){for(let n=0;n<e.length;n++)e[n].delete(t);e.length=0}}let P=!0;const x=[];function M(){x.push(P),P=!1}function z(){const t=x.pop();P=void 0===t||t}function V(t,e,n){if(P&&S){let e=R.get(t);e||R.set(t,e=new Map);let s=e.get(n);s||e.set(n,s=g()),W(s)}}function W(t,e){let n=!1;b<=30?w(t)||(t.n|=m,n=!y(t)):n=!t.has(S),n&&(t.add(S),S.deps.push(t))}function A(t,e,n,s,i,r){const u=R.get(t);if(!u)return;let a=[];if("clear"===e)a=[...u.values()];else if("length"===n&&c(t)){const t=Number(s);u.forEach(((e,n)=>{("length"===n||n>=t)&&a.push(e)}))}else switch(void 0!==n&&a.push(u.get(n)),e){case"add":c(t)?f(n)&&a.push(u.get("length")):(a.push(u.get(k)),o(t)&&a.push(u.get(O)));break;case"delete":c(t)||(a.push(u.get(k)),o(t)&&a.push(u.get(O)));break;case"set":o(t)&&a.push(u.get(k))}if(1===a.length)a[0]&&N(a[0]);else{const t=[];for(const e of a)e&&t.push(...e);N(g(t))}}function N(t,e){const n=c(t)?t:[...t];for(const s of n)s.computed&&T(s);for(const s of n)s.computed||T(s)}function T(t,e){(t!==S||t.allowRecurse)&&(t.scheduler?t.scheduler():t.run())}const C=e("__proto__,__v_isRef,__isVue"),I=new Set(Object.getOwnPropertyNames(Symbol).filter((t=>"arguments"!==t&&"caller"!==t)).map((t=>Symbol[t])).filter(u)),K=G(),D=G(!1,!0),L=G(!0),Y=G(!0,!0),q=B();function B(){const t={};return["includes","indexOf","lastIndexOf"].forEach((e=>{t[e]=function(...t){const n=zt(this);for(let e=0,i=this.length;e<i;e++)V(n,0,e+"");const s=n[e](...t);return-1===s||!1===s?n[e](...t.map(zt)):s}})),["push","pop","shift","unshift","splice"].forEach((e=>{t[e]=function(...t){M();const n=zt(this)[e].apply(this,t);return z(),n}})),t}function F(t){const e=zt(this);return V(e,0,t),e.hasOwnProperty(t)}function G(t=!1,e=!1){return function(n,s,i){if("__v_isReactive"===s)return!t;if("__v_isReadonly"===s)return t;if("__v_isShallow"===s)return e;if("__v_raw"===s&&i===(t?e?St:mt:e?bt:Rt).get(n))return n;const o=c(n);if(!t){if(o&&r(q,s))return Reflect.get(q,s,i);if("hasOwnProperty"===s)return F}const h=Reflect.get(n,s,i);return(u(s)?I.has(s):C(s))?h:(t||V(n,0,s),e?h:Tt(h)?o&&f(s)?h:h.value:a(h)?t?jt(h):Ot(h):h)}}function H(t=!1){return function(e,n,s,i){let o=e[n];if(xt(o)&&Tt(o)&&!Tt(s))return!1;if(!t&&(Mt(s)||xt(s)||(o=zt(o),s=zt(s)),!c(e)&&Tt(o)&&!Tt(s)))return o.value=s,!0;const u=c(e)&&f(n)?Number(n)<e.length:r(e,n),a=Reflect.set(e,n,s,i);return e===zt(i)&&(u?_(s,o)&&A(e,"set",n,s):A(e,"add",n,s)),a}}const J={get:K,set:H(),deleteProperty:function(t,e){const n=r(t,e),s=Reflect.deleteProperty(t,e);return s&&n&&A(t,"delete",e,void 0),s},has:function(t,e){const n=Reflect.has(t,e);return u(e)&&I.has(e)||V(t,0,e),n},ownKeys:function(t){return V(t,0,c(t)?"length":k),Reflect.ownKeys(t)}},Q={get:L,set:(t,e)=>!0,deleteProperty:(t,e)=>!0},U=s({},J,{get:D,set:H(!0)}),X=s({},Q,{get:Y}),Z=t=>t,$=t=>Reflect.getPrototypeOf(t);function tt(t,e,n=!1,s=!1){const i=zt(t=t.__v_raw),r=zt(e);n||(e!==r&&V(i,0,e),V(i,0,r));const{has:c}=$(i),o=s?Z:n?Wt:Vt;return c.call(i,e)?o(t.get(e)):c.call(i,r)?o(t.get(r)):void(t!==i&&t.get(e))}function et(t,e=!1){const n=this.__v_raw,s=zt(n),i=zt(t);return e||(t!==i&&V(s,0,t),V(s,0,i)),t===i?n.has(t):n.has(t)||n.has(i)}function nt(t,e=!1){return t=t.__v_raw,!e&&V(zt(t),0,k),Reflect.get(t,"size",t)}function st(t){t=zt(t);const e=zt(this);return $(e).has.call(e,t)||(e.add(t),A(e,"add",t,t)),this}function it(t,e){e=zt(e);const n=zt(this),{has:s,get:i}=$(n);let r=s.call(n,t);r||(t=zt(t),r=s.call(n,t));const c=i.call(n,t);return n.set(t,e),r?_(e,c)&&A(n,"set",t,e):A(n,"add",t,e),this}function rt(t){const e=zt(this),{has:n,get:s}=$(e);let i=n.call(e,t);i||(t=zt(t),i=n.call(e,t)),s&&s.call(e,t);const r=e.delete(t);return i&&A(e,"delete",t,void 0),r}function ct(){const t=zt(this),e=0!==t.size,n=t.clear();return e&&A(t,"clear",void 0,void 0),n}function ot(t,e){return function(n,s){const i=this,r=i.__v_raw,c=zt(r),o=e?Z:t?Wt:Vt;return!t&&V(c,0,k),r.forEach(((t,e)=>n.call(s,o(t),o(e),i)))}}function ut(t,e,n){return function(...s){const i=this.__v_raw,r=zt(i),c=o(r),u="entries"===t||t===Symbol.iterator&&c,a="keys"===t&&c,h=i[t](...s),l=n?Z:e?Wt:Vt;return!e&&V(r,0,a?O:k),{next(){const{value:t,done:e}=h.next();return e?{value:t,done:e}:{value:u?[l(t[0]),l(t[1])]:l(t),done:e}},[Symbol.iterator](){return this}}}}function at(t){return function(...e){return"delete"!==t&&this}}function ht(){const t={get(t){return tt(this,t)},get size(){return nt(this)},has:et,add:st,set:it,delete:rt,clear:ct,forEach:ot(!1,!1)},e={get(t){return tt(this,t,!1,!0)},get size(){return nt(this)},has:et,add:st,set:it,delete:rt,clear:ct,forEach:ot(!1,!0)},n={get(t){return tt(this,t,!0)},get size(){return nt(this,!0)},has(t){return et.call(this,t,!0)},add:at("add"),set:at("set"),delete:at("delete"),clear:at("clear"),forEach:ot(!0,!1)},s={get(t){return tt(this,t,!0,!0)},get size(){return nt(this,!0)},has(t){return et.call(this,t,!0)},add:at("add"),set:at("set"),delete:at("delete"),clear:at("clear"),forEach:ot(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((i=>{t[i]=ut(i,!1,!1),n[i]=ut(i,!0,!1),e[i]=ut(i,!1,!0),s[i]=ut(i,!0,!0)})),[t,n,e,s]}const[lt,ft,_t,dt]=ht();function pt(t,e){const n=e?t?dt:_t:t?ft:lt;return(e,s,i)=>"__v_isReactive"===s?!t:"__v_isReadonly"===s?t:"__v_raw"===s?e:Reflect.get(r(n,s)&&s in e?n:e,s,i)}const vt={get:pt(!1,!1)},gt={get:pt(!1,!0)},yt={get:pt(!0,!1)},wt={get:pt(!0,!0)},Rt=new WeakMap,bt=new WeakMap,mt=new WeakMap,St=new WeakMap;function kt(t){return t.__v_skip||!Object.isExtensible(t)?0:function(t){switch(t){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((t=>l(t).slice(8,-1))(t))}function Ot(t){return xt(t)?t:Et(t,!1,J,vt,Rt)}function jt(t){return Et(t,!0,Q,yt,mt)}function Et(t,e,n,s,i){if(!a(t))return t;if(t.__v_raw&&(!e||!t.__v_isReactive))return t;const r=i.get(t);if(r)return r;const c=kt(t);if(0===c)return t;const o=new Proxy(t,2===c?s:n);return i.set(t,o),o}function Pt(t){return xt(t)?Pt(t.__v_raw):!(!t||!t.__v_isReactive)}function xt(t){return!(!t||!t.__v_isReadonly)}function Mt(t){return!(!t||!t.__v_isShallow)}function zt(t){const e=t&&t.__v_raw;return e?zt(e):t}const Vt=t=>a(t)?Ot(t):t,Wt=t=>a(t)?jt(t):t;function At(t){P&&S&&W((t=zt(t)).dep||(t.dep=g()))}function Nt(t,e){const n=(t=zt(t)).dep;n&&N(n)}function Tt(t){return!(!t||!0!==t.__v_isRef)}function Ct(t,e){return Tt(t)?t:new It(t,e)}class It{constructor(t,e){this.__v_isShallow=e,this.dep=void 0,this.__v_isRef=!0,this._rawValue=e?t:zt(t),this._value=e?t:Vt(t)}get value(){return At(this),this._value}set value(t){const e=this.__v_isShallow||Mt(t)||xt(t);t=e?t:zt(t),_(t,this._rawValue)&&(this._rawValue=t,this._value=e?t:Vt(t),Nt(this))}}function Kt(t){return Tt(t)?t.value:t}const Dt={get:(t,e,n)=>Kt(Reflect.get(t,e,n)),set:(t,e,n,s)=>{const i=t[e];return Tt(i)&&!Tt(n)?(i.value=n,!0):Reflect.set(t,e,n,s)}};class Lt{constructor(t){this.dep=void 0,this.__v_isRef=!0;const{get:e,set:n}=t((()=>At(this)),(()=>Nt(this)));this._get=e,this._set=n}get value(){return this._get()}set value(t){this._set(t)}}class Yt{constructor(t,e,n){this._object=t,this._key=e,this._defaultValue=n,this.__v_isRef=!0}get value(){const t=this._object[this._key];return void 0===t?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return function(t,e){var n;return null===(n=R.get(t))||void 0===n?void 0:n.get(e)}(zt(this._object),this._key)}}function qt(t,e,n){const s=t[e];return Tt(s)?s:new Yt(t,e,n)}var Bt,Ft;class Gt{constructor(t,e,n,s){this._setter=e,this.dep=void 0,this.__v_isRef=!0,this[Bt]=!1,this._dirty=!0,this.effect=new j(t,(()=>{this._dirty||(this._dirty=!0,Nt(this))})),this.effect.computed=this,this.effect.active=this._cacheable=!s,this.__v_isReadonly=n}get value(){const t=zt(this);return At(t),!t._dirty&&t._cacheable||(t._dirty=!1,t._value=t.effect.run()),t._value}set value(t){this._setter(t)}}Bt="__v_isReadonly";const Ht=Promise.resolve(),Jt=[];let Qt=!1;const Ut=()=>{for(let t=0;t<Jt.length;t++)Jt[t]();Jt.length=0,Qt=!1};class Xt{constructor(t){let e;this.dep=void 0,this._dirty=!0,this.__v_isRef=!0,this[Ft]=!0;let n=!1,s=!1;this.effect=new j(t,(t=>{if(this.dep){if(t)e=this._value,n=!0;else if(!s){const t=n?e:this._value;s=!0,n=!1,Jt.push((()=>{this.effect.active&&this._get()!==t&&Nt(this),s=!1})),Qt||(Qt=!0,Ht.then(Ut))}for(const t of this.dep)t.computed instanceof Xt&&t.scheduler(!0)}this._dirty=!0})),this.effect.computed=this}_get(){return this._dirty?(this._dirty=!1,this._value=this.effect.run()):this._value}get value(){return At(this),zt(this)._get()}}return Ft="__v_isReadonly",t.EffectScope=p,t.ITERATE_KEY=k,t.ReactiveEffect=j,t.computed=function(t,e,s=!1){let i,r;const c="function"==typeof t;return c?(i=t,r=n):(i=t.get,r=t.set),new Gt(i,r,c||!r,s)},t.customRef=function(t){return new Lt(t)},t.deferredComputed=function(t){return new Xt(t)},t.effect=function(t,e){t.effect&&(t=t.effect.fn);const n=new j(t);e&&(s(n,e),e.scope&&v(n,e.scope)),e&&e.lazy||n.run();const i=n.run.bind(n);return i.effect=n,i},t.effectScope=function(t){return new p(t)},t.enableTracking=function(){x.push(P),P=!0},t.getCurrentScope=function(){return d},t.isProxy=function(t){return Pt(t)||xt(t)},t.isReactive=Pt,t.isReadonly=xt,t.isRef=Tt,t.isShallow=Mt,t.markRaw=function(t){return((t,e,n)=>{Object.defineProperty(t,e,{configurable:!0,enumerable:!1,value:n})})(t,"__v_skip",!0),t},t.onScopeDispose=function(t){d&&d.cleanups.push(t)},t.pauseTracking=M,t.proxyRefs=function(t){return Pt(t)?t:new Proxy(t,Dt)},t.reactive=Ot,t.readonly=jt,t.ref=function(t){return Ct(t,!1)},t.resetTracking=z,t.shallowReactive=function(t){return Et(t,!1,U,gt,bt)},t.shallowReadonly=function(t){return Et(t,!0,X,wt,St)},t.shallowRef=function(t){return Ct(t,!0)},t.stop=function(t){t.effect.stop()},t.toRaw=zt,t.toRef=qt,t.toRefs=function(t){const e=c(t)?new Array(t.length):{};for(const n in t)e[n]=qt(t,n);return e},t.track=V,t.trigger=A,t.triggerRef=function(t){Nt(t)},t.unref=Kt,Object.defineProperty(t,"__esModule",{value:!0}),t}({});

<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 
          http://maven.apache.org/xsd/settings-1.0.0.xsd">

    <!-- 本地仓库路径 -->
    <localRepository>C:\Users\<USER>\.m2\repository</localRepository>

    <!-- 镜像配置 - 为了支持JavaCV，不使用全局镜像 -->
    <mirrors>
        <!-- 阿里云镜像仅用于常见依赖 -->
        <mirror>
            <id>nexus-aliyun</id>
            <mirrorOf>central,!sonatype-snapshots,!bytedeco,!maven-central-direct</mirrorOf>
            <name>Nexus aliyun</name>
            <url>http://maven.aliyun.com/nexus/content/groups/public</url>
        </mirror>
    </mirrors>

    <!-- 配置文件 -->
    <profiles>
        <profile>
            <id>default</id>
            <repositories>
                <!-- 中央仓库 -->
                <repository>
                    <id>central</id>
                    <name>Central Repository</name>
                    <url>https://repo1.maven.org/maven2</url>
                    <layout>default</layout>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
                
                <!-- 阿里云仓库 -->
                <repository>
                    <id>aliyun</id>
                    <name>Aliyun Repository</name>
                    <url>http://maven.aliyun.com/nexus/content/groups/public</url>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
                
                <!-- Spring仓库 -->
                <repository>
                    <id>spring-releases</id>
                    <name>Spring Releases</name>
                    <url>https://repo.spring.io/release</url>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
                
                <!-- Spring里程碑版本 -->
                <repository>
                    <id>spring-milestones</id>
                    <name>Spring Milestones</name>
                    <url>https://repo.spring.io/milestone</url>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
                
                <!-- JCenter仓库 -->
                <repository>
                    <id>jcenter</id>
                    <name>JCenter</name>
                    <url>https://jcenter.bintray.com</url>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
                
                <!-- Maven Central Snapshots -->
                <repository>
                    <id>sonatype-snapshots</id>
                    <name>Sonatype Snapshots</name>
                    <url>https://oss.sonatype.org/content/repositories/snapshots</url>
                    <releases>
                        <enabled>false</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
                
                <!-- Bytedeco仓库 (用于JavaCV/OpenCV) -->
                <repository>
                    <id>bytedeco</id>
                    <name>Bytedeco Repository</name>
                    <url>https://oss.sonatype.org/content/repositories/releases</url>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>

                <!-- Maven Central直接访问 -->
                <repository>
                    <id>maven-central-direct</id>
                    <name>Maven Central Direct</name>
                    <url>https://repo1.maven.org/maven2</url>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
            </repositories>
            
            <pluginRepositories>
                <pluginRepository>
                    <id>central</id>
                    <name>Central Repository</name>
                    <url>https://repo1.maven.org/maven2</url>
                    <layout>default</layout>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                    <releases>
                        <updatePolicy>never</updatePolicy>
                    </releases>
                </pluginRepository>
                
                <pluginRepository>
                    <id>aliyun-plugin</id>
                    <name>Aliyun Plugin Repository</name>
                    <url>http://maven.aliyun.com/nexus/content/groups/public</url>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>

    <!-- 激活配置文件 -->
    <activeProfiles>
        <activeProfile>default</activeProfile>
    </activeProfiles>

</settings>

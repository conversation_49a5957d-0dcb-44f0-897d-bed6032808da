<template>
  <view class="container">
    <!-- 顶部标题 -->
    <view class="header">
      <view class="header-icon">🌐</view>
      <view class="header-info">
        <text class="header-title">语言翻译</text>
        <text class="header-desc">多语言互译，支持主流语言</text>
      </view>
    </view>

    <!-- 语言选择区域 -->
    <view class="language-selector">
      <view class="language-item">
        <text class="language-label">源语言</text>
        <picker @change="onFromLangChange" :value="fromLangIndex" :range="languages" range-key="name">
          <view class="language-picker">
            <text class="language-text">{{ languages[fromLangIndex].name }}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>
      
      <view class="swap-btn" @tap="swapLanguages">
        <text class="swap-icon">⇄</text>
      </view>
      
      <view class="language-item">
        <text class="language-label">目标语言</text>
        <picker @change="onToLangChange" :value="toLangIndex" :range="languages" range-key="name">
          <view class="language-picker">
            <text class="language-text">{{ languages[toLangIndex].name }}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 翻译区域 -->
    <view class="translate-area">
      <!-- 输入区域 -->
      <view class="input-section">
        <view class="section-header">
          <text class="section-title">{{ languages[fromLangIndex].flag }} 输入文本</text>
          <text class="char-count">{{ inputText.length }}/1000</text>
        </view>
        <textarea 
          class="input-textarea" 
          v-model="inputText" 
          :placeholder="`请输入要翻译的${languages[fromLangIndex].name}文本...`"
          :maxlength="1000"
          @input="onInputChange"
        />
        <view class="input-actions">
          <button class="action-btn clear" @tap="clearInput" v-if="inputText">
            <text class="btn-icon">🗑️</text>
            <text class="btn-text">清空</text>
          </button>
          <button class="action-btn paste" @tap="pasteText">
            <text class="btn-icon">📋</text>
            <text class="btn-text">粘贴</text>
          </button>
        </view>
      </view>

      <!-- 翻译按钮 -->
      <view class="translate-btn-section">
        <button 
          class="translate-btn" 
          :class="{ disabled: !inputText.trim() || isTranslating }" 
          @tap="translateText"
        >
          <text class="btn-icon">{{ isTranslating ? '⏳' : '🔄' }}</text>
          <text class="btn-text">{{ isTranslating ? '翻译中...' : '开始翻译' }}</text>
        </button>
      </view>

      <!-- 输出区域 -->
      <view class="output-section">
        <view class="section-header">
          <text class="section-title">{{ languages[toLangIndex].flag }} 翻译结果</text>
          <view v-if="translatedText && !isTranslating" class="output-actions">
            <button class="action-btn copy" @tap="copyResult">
              <text class="btn-icon">📋</text>
              <text class="btn-text">复制</text>
            </button>
          </view>
        </view>
        
        <!-- 翻译中的动画 -->
        <view v-if="isTranslating" class="translating-animation">
          <view class="typing-indicator">
            <view class="dot"></view>
            <view class="dot"></view>
            <view class="dot"></view>
          </view>
          <text class="translating-text">AI正在翻译中...</text>
        </view>
        
        <!-- 翻译结果 -->
        <view v-else-if="translatedText" class="output-content">
          <text class="output-text">{{ translatedText }}</text>
        </view>
        
        <!-- 空状态 -->
        <view v-else class="empty-state">
          <text class="empty-icon">🌐</text>
          <text class="empty-text">翻译结果将在这里显示</text>
        </view>
      </view>
    </view>

    <!-- 快捷翻译 -->
    <view v-if="!inputText && !translatedText" class="quick-translate">
      <text class="quick-title">💡 试试这些翻译</text>
      <view class="quick-list">
        <view 
          v-for="(item, index) in quickTranslations" 
          :key="index" 
          class="quick-item" 
          @tap="useQuickTranslation(item)"
        >
          <text class="quick-text">{{ item }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      inputText: '',
      translatedText: '',
      isTranslating: false,
      userId: 123,
      fromLangIndex: 1, // 默认中文
      toLangIndex: 0,   // 默认英文
      languages: [
        { name: '英文', code: 'en', flag: '🇺🇸' },
        { name: '中文', code: 'zh', flag: '🇨🇳' },
        { name: '日文', code: 'ja', flag: '🇯🇵' },
        { name: '韩文', code: 'ko', flag: '🇰🇷' },
        { name: '法文', code: 'fr', flag: '🇫🇷' },
        { name: '德文', code: 'de', flag: '🇩🇪' },
        { name: '西班牙文', code: 'es', flag: '🇪🇸' },
        { name: '俄文', code: 'ru', flag: '🇷🇺' }
      ],
      quickTranslations: [
        'Hello, how are you?',
        '我喜欢学习编程',
        'Thank you very much',
        '今天天气很好',
        'Good morning',
        '很高兴认识你'
      ]
    }
  },
  onLoad() {
    this.loadUserInfo();
  },
  methods: {
    loadUserInfo() {
      const userInfo = uni.getStorageSync('userInfo');
      if (userInfo && userInfo.id) {
        this.userId = userInfo.id;
      }
    },

    onFromLangChange(e) {
      this.fromLangIndex = e.detail.value;
      this.clearResults();
    },

    onToLangChange(e) {
      this.toLangIndex = e.detail.value;
      this.clearResults();
    },

    swapLanguages() {
      const temp = this.fromLangIndex;
      this.fromLangIndex = this.toLangIndex;
      this.toLangIndex = temp;
      
      // 交换输入和输出文本
      const tempText = this.inputText;
      this.inputText = this.translatedText;
      this.translatedText = tempText;
    },

    onInputChange() {
      this.translatedText = '';
    },

    clearInput() {
      this.inputText = '';
      this.translatedText = '';
    },

    clearResults() {
      this.translatedText = '';
    },

    async pasteText() {
      try {
        const clipboardData = await uni.getClipboardData();
        if (clipboardData.data) {
          this.inputText = clipboardData.data;
          uni.showToast({
            title: '粘贴成功',
            icon: 'success'
          });
        }
      } catch (error) {
        uni.showToast({
          title: '粘贴失败',
          icon: 'none'
        });
      }
    },

    useQuickTranslation(text) {
      this.inputText = text;
      this.translateText();
    },

    async translateText() {
      if (!this.inputText.trim() || this.isTranslating) return;

      this.isTranslating = true;
      this.translatedText = '';

      try {
        const fromLang = this.languages[this.fromLangIndex].name;
        const toLang = this.languages[this.toLangIndex].name;
        
        const response = await this.callTranslateAPI(this.inputText.trim(), fromLang, toLang);
        
        if (response && response.success) {
          this.translatedText = response.response || '翻译失败，请重试。';
        } else {
          this.translatedText = '翻译服务暂时不可用，请稍后再试。';
        }
      } catch (error) {
        this.translatedText = '网络连接失败，请检查网络后重试。';
        console.error('API调用失败:', error);
      }

      this.isTranslating = false;
    },

    async callTranslateAPI(text, fromLang, toLang) {
      const apiUrl = 'http://localhost:8082/api/ai/miniprogram/translate';
      
      const response = await uni.request({
        url: apiUrl,
        method: 'POST',
        header: {
          'Content-Type': 'application/json'
        },
        data: {
          userId: this.userId,
          text: text,
          fromLang: fromLang,
          toLang: toLang
        }
      });

      if (response.statusCode === 200 && response.data.code === 200) {
        return response.data.data;
      } else {
        throw new Error('API调用失败');
      }
    },

    copyResult() {
      if (!this.translatedText) return;
      
      uni.setClipboardData({
        data: this.translatedText,
        success: () => {
          uni.showToast({
            title: '复制成功',
            icon: 'success'
          });
        },
        fail: () => {
          uni.showToast({
            title: '复制失败',
            icon: 'none'
          });
        }
      });
    }
  }
}
</script>

<style>
.container {
  min-height: 100vh;
  background: #f8f9fa;
}

.header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: #fff;
  border-bottom: 1rpx solid #e9ecef;
}

.header-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.header-info {
  flex: 1;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  display: block;
}

.header-desc {
  font-size: 24rpx;
  color: #6c757d;
  margin-top: 8rpx;
  display: block;
}

.language-selector {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: #fff;
  border-bottom: 1rpx solid #e9ecef;
}

.language-item {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.language-label {
  font-size: 24rpx;
  color: #6c757d;
  margin-bottom: 12rpx;
}

.language-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
}

.language-text {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 500;
}

.picker-arrow {
  font-size: 20rpx;
  color: #6c757d;
}

.swap-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: #007bff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 30rpx;
  transition: all 0.3s ease;
}

.swap-btn:active {
  transform: scale(0.9);
  background: #0056b3;
}

.swap-icon {
  font-size: 32rpx;
  color: #fff;
  font-weight: bold;
}

.translate-area {
  padding: 30rpx;
}

.input-section, .output-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
}

.char-count {
  font-size: 24rpx;
  color: #6c757d;
}

.input-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.5;
  background: #f8f9fa;
  box-sizing: border-box;
}

.input-actions, .output-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 20rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.clear {
  background: #dc3545;
  color: #fff;
}

.action-btn.paste {
  background: #6c757d;
  color: #fff;
}

.action-btn.copy {
  background: #28a745;
  color: #fff;
}

.action-btn:active {
  transform: scale(0.95);
}

.translate-btn-section {
  margin-bottom: 30rpx;
}

.translate-btn {
  width: 100%;
  height: 100rpx;
  background: #007bff;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  transition: all 0.3s ease;
}

.translate-btn.disabled {
  background: #ccc;
  color: #999;
}

.translate-btn:not(.disabled):active {
  transform: scale(0.98);
  background: #0056b3;
}

.translating-animation {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #007bff;
  animation: typing 1.4s infinite;
}

.dot:nth-child(2) { animation-delay: 0.2s; }
.dot:nth-child(3) { animation-delay: 0.4s; }

@keyframes typing {
  0%, 60%, 100% { opacity: 0.3; transform: scale(1); }
  30% { opacity: 1; transform: scale(1.2); }
}

.translating-text {
  font-size: 28rpx;
  color: #6c757d;
}

.output-content {
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 30rpx;
  background: #f8f9fa;
}

.output-text {
  font-size: 28rpx;
  line-height: 1.8;
  color: #333;
  white-space: pre-wrap;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}

.empty-icon {
  font-size: 64rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #6c757d;
}

.quick-translate {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
}

.quick-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20rpx;
  display: block;
}

.quick-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.quick-item {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.quick-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.quick-text {
  font-size: 26rpx;
  color: #495057;
}
</style>

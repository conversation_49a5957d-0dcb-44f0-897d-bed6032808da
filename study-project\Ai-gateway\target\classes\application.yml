server:
  port: 7080

spring:
  application:
    name: gateway
  cloud:
    nacos:
      discovery:
        server-addr: 118.31.228.119:8848
        enabled: true
        username: nacos
        password: nacos
        fail-fast: false
        # 强制使用HTTP协议
        client-config:
          connection-timeout: 10000
          socket-timeout: 10000
        # 禁用gRPC
        config:
          remote-first: false
    gateway:
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true

# 系统属性配置
nacos:
  remote:
    client:
      grpc:
        enable: false

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,gateway
  endpoint:
    health:
      show-details: always
    gateway:
      enabled: true

# 日志配置
logging:
  level:
    org.springframework.cloud.gateway: INFO
    reactor.netty: WARN

{"version": 3, "file": "main.js", "sources": ["pages/main/main.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbWFpbi9tYWluLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"main-container\">\n    <!-- 首页 - 显示占位内容 -->\n    <view v-if=\"currentTab === 'home'\" class=\"page-content\">\n      <view class=\"placeholder-page\">\n        <view class=\"placeholder-icon\">🏠</view>\n        <text class=\"placeholder-title\">首页</text>\n        <text class=\"placeholder-desc\">欢迎来到慕课网前端学习平台</text>\n      </view>\n    </view>\n\n    <!-- 学习路线页面 -->\n    <view v-else-if=\"currentTab === 'learn'\" class=\"page-content\">\n      <view class=\"placeholder-page\">\n        <view class=\"placeholder-icon\">📚</view>\n        <text class=\"placeholder-title\">学习路线</text>\n        <text class=\"placeholder-desc\">个性化学习路径规划</text>\n      </view>\n    </view>\n    \n    <!-- 免费教程页面 -->\n    <view v-else-if=\"currentTab === 'course'\" class=\"page-content\">\n      <view class=\"placeholder-page\">\n        <view class=\"placeholder-icon\">📺</view>\n        <text class=\"placeholder-title\">免费教程</text>\n        <text class=\"placeholder-desc\">丰富的免费学习资源</text>\n      </view>\n    </view>\n    \n    <!-- 面试刷题页面 -->\n    <view v-else-if=\"currentTab === 'question'\" class=\"page-content\">\n      <view class=\"placeholder-page\">\n        <view class=\"placeholder-icon\">📝</view>\n        <text class=\"placeholder-title\">面试刷题</text>\n        <text class=\"placeholder-desc\">海量题库助力求职</text>\n      </view>\n    </view>\n    \n    <!-- 精品课程页面 -->\n    <view v-else-if=\"currentTab === 'premium'\" class=\"page-content\">\n      <view class=\"placeholder-page\">\n        <view class=\"placeholder-icon\">💎</view>\n        <text class=\"placeholder-title\">精品课程</text>\n        <text class=\"placeholder-desc\">高质量付费课程</text>\n      </view>\n    </view>\n\n    <!-- AI智能家教页面 - 点击主页按钮时显示 -->\n    <view v-else-if=\"currentTab === 'ai-home'\" class=\"page-content\">\n      <ai-home></ai-home>\n    </view>\n\n    <!-- 底部导航栏 -->\n    <bottom-nav \n      :activeTab=\"currentTab\" \n      @tab-change=\"handleTabChange\"\n    ></bottom-nav>\n  </view>\n</template>\n\n<script>\nimport AiHome from '../ai-home/ai-home.vue'\nimport BottomNav from '../../components/bottom-nav/bottom-nav.vue'\n\nexport default {\n  name: 'Main',\n  components: {\n    AiHome,\n    BottomNav\n  },\n  data() {\n    return {\n      currentTab: 'home' // 默认显示主页（AI智能家教）\n    }\n  },\n  methods: {\n    handleTabChange(tab) {\n      this.currentTab = tab;\n    }\n  }\n}\n</script>\n\n<style scoped>\n.main-container {\n  min-height: 100vh;\n  background: #f8f9fa;\n}\n\n.page-content {\n  min-height: 100vh;\n  padding-bottom: 120rpx; /* 为底部导航栏留出空间 */\n}\n\n/* 占位页面样式 */\n.placeholder-page {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-height: 80vh;\n  padding: 40rpx;\n}\n\n.placeholder-icon {\n  font-size: 120rpx;\n  margin-bottom: 30rpx;\n}\n\n.placeholder-title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 20rpx;\n}\n\n.placeholder-desc {\n  font-size: 28rpx;\n  color: #666;\n  text-align: center;\n}\n</style>\n", "import MiniProgramPage from 'D:/study/study-project/s-sai/pages/main/main.vue'\nwx.createPage(MiniProgramPage)"], "names": [], "mappings": ";;AA6DA,MAAK,SAAU,MAAW;AAC1B,MAAO,YAAW,MAAW;AAE7B,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,YAAY;AAAA,IACV;AAAA,IACA;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,YAAY;AAAA;AAAA,IACd;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,gBAAgB,KAAK;AACnB,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;AC/EA,GAAG,WAAW,eAAe;"}
{"version": 3, "sources": ["../src/measure-text.js"], "names": ["measureText", "font", "text", "x", "i", "length", "chars", "kerning", "kernings", "xadvance", "measureTextHeight", "max<PERSON><PERSON><PERSON>", "words", "split", "line", "textTotalHeight", "common", "lineHeight", "n", "testLine", "testWidth"], "mappings": ";;;;;;;;AAAO,SAASA,WAAT,CAAqBC,IAArB,EAA2BC,IAA3B,EAAiC;AACtC,MAAIC,CAAC,GAAG,CAAR;;AAEA,OAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,IAAI,CAACG,MAAzB,EAAiCD,CAAC,EAAlC,EAAsC;AACpC,QAAIH,IAAI,CAACK,KAAL,CAAWJ,IAAI,CAACE,CAAD,CAAf,CAAJ,EAAyB;AACvB,UAAMG,OAAO,GACXN,IAAI,CAACO,QAAL,CAAcN,IAAI,CAACE,CAAD,CAAlB,KAA0BH,IAAI,CAACO,QAAL,CAAcN,IAAI,CAACE,CAAD,CAAlB,EAAuBF,IAAI,CAACE,CAAC,GAAG,CAAL,CAA3B,CAA1B,GACIH,IAAI,CAACO,QAAL,CAAcN,IAAI,CAACE,CAAD,CAAlB,EAAuBF,IAAI,CAACE,CAAC,GAAG,CAAL,CAA3B,CADJ,GAEI,CAHN;AAKAD,MAAAA,CAAC,IAAI,CAACF,IAAI,CAACK,KAAL,CAAWJ,IAAI,CAACE,CAAD,CAAf,EAAoBK,QAApB,IAAgC,CAAjC,IAAsCF,OAA3C;AACD;AACF;;AAED,SAAOJ,CAAP;AACD;;AAEM,SAASO,iBAAT,CAA2BT,IAA3B,EAAiCC,IAAjC,EAAuCS,QAAvC,EAAiD;AACtD,MAAMC,KAAK,GAAGV,IAAI,CAACW,KAAL,CAAW,GAAX,CAAd;AACA,MAAIC,IAAI,GAAG,EAAX;AACA,MAAIC,eAAe,GAAGd,IAAI,CAACe,MAAL,CAAYC,UAAlC;;AAEA,OAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGN,KAAK,CAACP,MAA1B,EAAkCa,CAAC,EAAnC,EAAuC;AACrC,QAAMC,QAAQ,GAAGL,IAAI,GAAGF,KAAK,CAACM,CAAD,CAAZ,GAAkB,GAAnC;AACA,QAAME,SAAS,GAAGpB,WAAW,CAACC,IAAD,EAAOkB,QAAP,CAA7B;;AAEA,QAAIC,SAAS,GAAGT,QAAZ,IAAwBO,CAAC,GAAG,CAAhC,EAAmC;AACjCH,MAAAA,eAAe,IAAId,IAAI,CAACe,MAAL,CAAYC,UAA/B;AACAH,MAAAA,IAAI,GAAGF,KAAK,CAACM,CAAD,CAAL,GAAW,GAAlB;AACD,KAHD,MAGO;AACLJ,MAAAA,IAAI,GAAGK,QAAP;AACD;AACF;;AAED,SAAOJ,eAAP;AACD", "sourcesContent": ["export function measureText(font, text) {\n  let x = 0;\n\n  for (let i = 0; i < text.length; i++) {\n    if (font.chars[text[i]]) {\n      const kerning =\n        font.kernings[text[i]] && font.kernings[text[i]][text[i + 1]]\n          ? font.kernings[text[i]][text[i + 1]]\n          : 0;\n\n      x += (font.chars[text[i]].xadvance || 0) + kerning;\n    }\n  }\n\n  return x;\n}\n\nexport function measureTextHeight(font, text, maxWidth) {\n  const words = text.split(' ');\n  let line = '';\n  let textTotalHeight = font.common.lineHeight;\n\n  for (let n = 0; n < words.length; n++) {\n    const testLine = line + words[n] + ' ';\n    const testWidth = measureText(font, testLine);\n\n    if (testWidth > maxWidth && n > 0) {\n      textTotalHeight += font.common.lineHeight;\n      line = words[n] + ' ';\n    } else {\n      line = testLine;\n    }\n  }\n\n  return textTotalHeight;\n}\n"], "file": "measure-text.js"}
<template>
  <view class="container">
    <!-- 顶部标题 -->
    <view class="header">
      <view class="header-icon">📚</view>
      <view class="header-info">
        <text class="header-title">知识问答</text>
        <text class="header-desc">AI百科全书，解答各种问题</text>
      </view>
    </view>

    <!-- 聊天区域 -->
    <scroll-view class="chat-area" scroll-y="true" :scroll-top="scrollTop" scroll-with-animation="true">
      <view class="message-list">
        <view v-for="(message, index) in messages" :key="index" class="message-item" :class="message.type">
          <view class="message-avatar">
            <text class="avatar-text">{{ message.type === 'user' ? '我' : 'AI' }}</text>
          </view>
          <view class="message-content">
            <view class="message-bubble">
              <text class="message-text">{{ message.content }}</text>
              <text class="message-time">{{ message.time }}</text>
            </view>
          </view>
        </view>
        
        <!-- AI正在输入提示 -->
        <view v-if="isAiTyping" class="message-item ai typing">
          <view class="message-avatar">
            <text class="avatar-text">AI</text>
          </view>
          <view class="message-content">
            <view class="message-bubble">
              <view class="typing-indicator">
                <view class="dot"></view>
                <view class="dot"></view>
                <view class="dot"></view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 输入区域 -->
    <view class="input-area">
      <view class="input-container">
        <input 
          class="input-field" 
          v-model="inputText" 
          placeholder="请输入您的问题..." 
          :disabled="isLoading"
          @confirm="sendMessage"
        />
        <button 
          class="send-btn" 
          :class="{ disabled: !inputText.trim() || isLoading }" 
          @tap="sendMessage"
        >
          {{ isLoading ? '发送中' : '发送' }}
        </button>
      </view>
    </view>

    <!-- 快捷问题 -->
    <view v-if="messages.length === 0" class="quick-questions">
      <text class="quick-title">💡 试试这些问题</text>
      <view class="question-list">
        <view 
          v-for="(question, index) in quickQuestions" 
          :key="index" 
          class="question-item" 
          @tap="askQuestion(question)"
        >
          <text class="question-text">{{ question }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      messages: [],
      inputText: '',
      isLoading: false,
      isAiTyping: false,
      scrollTop: 0,
      userId: 123, // 从用户信息获取
      quickQuestions: [
        '地球的直径是多少？',
        '中国古代四大发明是什么？',
        '光的传播速度是多少？',
        '什么是人工智能？',
        '太阳系有几颗行星？'
      ]
    }
  },
  onLoad() {
    this.loadUserInfo();
    this.addWelcomeMessage();
  },
  methods: {
    loadUserInfo() {
      const userInfo = uni.getStorageSync('userInfo');
      if (userInfo && userInfo.id) {
        this.userId = userInfo.id;
      }
    },

    addWelcomeMessage() {
      this.messages.push({
        type: 'ai',
        content: '你好！我是AI知识助手，可以回答各种问题。请随时向我提问！',
        time: this.getCurrentTime()
      });
      this.scrollToBottom();
    },

    askQuestion(question) {
      this.inputText = question;
      this.sendMessage();
    },

    async sendMessage() {
      if (!this.inputText.trim() || this.isLoading) return;

      const userMessage = {
        type: 'user',
        content: this.inputText.trim(),
        time: this.getCurrentTime()
      };

      this.messages.push(userMessage);
      const question = this.inputText.trim();
      this.inputText = '';
      this.isLoading = true;
      this.isAiTyping = true;
      this.scrollToBottom();

      try {
        // 调用AI知识问答API
        const response = await this.callKnowledgeAPI(question);
        
        this.isAiTyping = false;
        
        if (response && response.success) {
          this.messages.push({
            type: 'ai',
            content: response.response || '抱歉，我暂时无法回答这个问题。',
            time: this.getCurrentTime()
          });
        } else {
          this.messages.push({
            type: 'ai',
            content: '抱歉，服务暂时不可用，请稍后再试。',
            time: this.getCurrentTime()
          });
        }
      } catch (error) {
        this.isAiTyping = false;
        this.messages.push({
          type: 'ai',
          content: '网络连接失败，请检查网络后重试。',
          time: this.getCurrentTime()
        });
        console.error('API调用失败:', error);
      }

      this.isLoading = false;
      this.scrollToBottom();
    },

    async callKnowledgeAPI(question) {
      const apiUrl = 'http://localhost:8082/api/ai/miniprogram/knowledge/qa';
      
      const response = await uni.request({
        url: apiUrl,
        method: 'POST',
        header: {
          'Content-Type': 'application/json'
        },
        data: {
          userId: this.userId,
          question: question
        }
      });

      if (response.statusCode === 200 && response.data.code === 200) {
        return response.data.data;
      } else {
        throw new Error('API调用失败');
      }
    },

    getCurrentTime() {
      const now = new Date();
      return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    },

    scrollToBottom() {
      this.$nextTick(() => {
        this.scrollTop = 999999;
      });
    }
  }
}
</script>

<style>
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: 120rpx; /* 为底部导航栏留出空间 */
}

.header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: #fff;
  border-bottom: 1rpx solid #e9ecef;
}

.header-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.header-info {
  flex: 1;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  display: block;
}

.header-desc {
  font-size: 24rpx;
  color: #6c757d;
  margin-top: 8rpx;
  display: block;
}

.chat-area {
  flex: 1;
  padding: 20rpx;
}

.message-item {
  display: flex;
  margin-bottom: 30rpx;
  animation: fadeInUp 0.3s ease;
}

.message-item.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #2c3e50;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 20rpx;
}

.message-item.user .message-avatar {
  background: #007bff;
}

.avatar-text {
  font-size: 24rpx;
  color: #fff;
  font-weight: bold;
}

.message-content {
  flex: 1;
  max-width: 70%;
}

.message-bubble {
  background: #fff;
  padding: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  position: relative;
}

.message-item.user .message-bubble {
  background: #007bff;
}

.message-text {
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
  display: block;
}

.message-item.user .message-text {
  color: #fff;
}

.message-time {
  font-size: 20rpx;
  color: #999;
  margin-top: 10rpx;
  display: block;
}

.message-item.user .message-time {
  color: rgba(255,255,255,0.8);
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #999;
  animation: typing 1.4s infinite;
}

.dot:nth-child(2) { animation-delay: 0.2s; }
.dot:nth-child(3) { animation-delay: 0.4s; }

@keyframes typing {
  0%, 60%, 100% { opacity: 0.3; }
  30% { opacity: 1; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.input-area {
  background: #fff;
  padding: 20rpx;
  border-top: 1rpx solid #e9ecef;
}

.input-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.input-field {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  border: 1rpx solid #e9ecef;
  border-radius: 40rpx;
  font-size: 28rpx;
  background: #f8f9fa;
}

.send-btn {
  width: 120rpx;
  height: 80rpx;
  background: #007bff;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-btn.disabled {
  background: #ccc;
  color: #999;
}

.quick-questions {
  position: absolute;
  bottom: 140rpx;
  left: 20rpx;
  right: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.quick-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20rpx;
  display: block;
}

.question-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.question-item {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.question-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.question-text {
  font-size: 26rpx;
  color: #495057;
}
</style>

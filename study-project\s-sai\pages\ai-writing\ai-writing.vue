<template>
  <view class="container">
    <!-- 顶部标题 -->
    <view class="header">
      <view class="header-icon">✍️</view>
      <view class="header-info">
        <text class="header-title">文本生成</text>
        <text class="header-desc">作文·故事·诗歌创作</text>
      </view>
    </view>

    <!-- 创作类型选择 -->
    <view class="type-selector">
      <view 
        v-for="(type, index) in writingTypes" 
        :key="index"
        class="type-item"
        :class="{ active: activeType === type.key }"
        @tap="switchType(type.key)"
      >
        <text class="type-icon">{{ type.icon }}</text>
        <text class="type-text">{{ type.name }}</text>
      </view>
    </view>

    <!-- 创作区域 -->
    <view class="writing-area">
      <!-- 输入提示 -->
      <view class="prompt-section">
        <text class="prompt-title">{{ currentType.icon }} {{ currentType.name }}创作</text>
        <textarea 
          class="prompt-input" 
          v-model="promptText" 
          :placeholder="currentType.placeholder"
          :maxlength="500"
          show-confirm-bar="false"
        />
        <view class="char-count">{{ promptText.length }}/500</view>
      </view>

      <!-- 生成按钮 -->
      <view class="generate-section">
        <button 
          class="generate-btn" 
          :class="{ disabled: !promptText.trim() || isGenerating }" 
          @tap="generateText"
        >
          <text class="btn-icon">{{ isGenerating ? '⏳' : '✨' }}</text>
          <text class="btn-text">{{ isGenerating ? '创作中...' : '开始创作' }}</text>
        </button>
      </view>

      <!-- 生成结果 -->
      <view v-if="generatedText || isGenerating" class="result-section">
        <view class="result-header">
          <text class="result-title">{{ currentType.icon }} 创作结果</text>
          <view v-if="generatedText && !isGenerating" class="result-actions">
            <button class="action-btn copy" @tap="copyText">📋 复制</button>
            <button class="action-btn regenerate" @tap="regenerateText">🔄 重新生成</button>
          </view>
        </view>
        
        <!-- 生成中的动画 -->
        <view v-if="isGenerating" class="generating-animation">
          <view class="typing-indicator">
            <view class="dot"></view>
            <view class="dot"></view>
            <view class="dot"></view>
          </view>
          <text class="generating-text">AI正在为您创作...</text>
        </view>
        
        <!-- 生成的文本 -->
        <view v-if="generatedText" class="result-content">
          <text class="result-text">{{ generatedText }}</text>
        </view>
      </view>

      <!-- 创作示例 -->
      <view v-if="!generatedText && !isGenerating" class="examples-section">
        <text class="examples-title">💡 {{ currentType.name }}示例</text>
        <view class="examples-list">
          <view 
            v-for="(example, index) in currentType.examples" 
            :key="index" 
            class="example-item" 
            @tap="useExample(example)"
          >
            <text class="example-text">{{ example }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      promptText: '',
      generatedText: '',
      isGenerating: false,
      userId: 123,
      activeType: 'essay',
      writingTypes: [
        {
          key: 'essay',
          name: '作文',
          icon: '📝',
          placeholder: '请输入作文题目或要求，例如：写一篇关于春天的作文...',
          examples: [
            '写一篇关于春天的作文',
            '我的理想职业',
            '难忘的一件事',
            '保护环境的重要性'
          ]
        },
        {
          key: 'story',
          name: '故事',
          icon: '📚',
          placeholder: '请输入故事主题或情节，例如：写一个关于友谊的故事...',
          examples: [
            '写一个关于友谊的故事',
            '小动物的冒险故事',
            '未来世界的科幻故事',
            '童话故事：勇敢的小公主'
          ]
        },
        {
          key: 'poem',
          name: '诗歌',
          icon: '🎭',
          placeholder: '请输入诗歌主题或风格，例如：写一首关于月亮的诗...',
          examples: [
            '写一首关于月亮的诗',
            '赞美老师的诗歌',
            '描写秋天的现代诗',
            '关于母爱的诗歌'
          ]
        },
        {
          key: 'general',
          name: '其他',
          icon: '📄',
          placeholder: '请输入您想要创作的文本类型和要求...',
          examples: [
            '写一段产品介绍',
            '编写一份活动策划',
            '写一封感谢信',
            '创作一段演讲稿'
          ]
        }
      ]
    }
  },
  computed: {
    currentType() {
      return this.writingTypes.find(type => type.key === this.activeType) || this.writingTypes[0];
    }
  },
  onLoad() {
    this.loadUserInfo();
  },
  methods: {
    loadUserInfo() {
      const userInfo = uni.getStorageSync('userInfo');
      if (userInfo && userInfo.id) {
        this.userId = userInfo.id;
      }
    },

    switchType(typeKey) {
      this.activeType = typeKey;
      this.promptText = '';
      this.generatedText = '';
    },

    useExample(example) {
      this.promptText = example;
    },

    async generateText() {
      if (!this.promptText.trim() || this.isGenerating) return;

      this.isGenerating = true;
      this.generatedText = '';

      try {
        // 调用AI文本生成API
        const response = await this.callWritingAPI(this.promptText.trim(), this.activeType);
        
        if (response && response.success) {
          this.generatedText = response.response || '抱歉，生成失败，请重试。';
        } else {
          this.generatedText = '抱歉，创作服务暂时不可用，请稍后再试。';
        }
      } catch (error) {
        this.generatedText = '网络连接失败，请检查网络后重试。';
        console.error('API调用失败:', error);
      }

      this.isGenerating = false;
    },

    async regenerateText() {
      if (!this.promptText.trim()) return;
      this.generateText();
    },

    async callWritingAPI(prompt, type) {
      const apiUrl = 'http://localhost:8082/api/ai/miniprogram/text/generate';
      
      const response = await uni.request({
        url: apiUrl,
        method: 'POST',
        header: {
          'Content-Type': 'application/json'
        },
        data: {
          userId: this.userId,
          prompt: prompt,
          type: type
        }
      });

      if (response.statusCode === 200 && response.data.code === 200) {
        return response.data.data;
      } else {
        throw new Error('API调用失败');
      }
    },

    copyText() {
      if (!this.generatedText) return;
      
      uni.setClipboardData({
        data: this.generatedText,
        success: () => {
          uni.showToast({
            title: '复制成功',
            icon: 'success'
          });
        },
        fail: () => {
          uni.showToast({
            title: '复制失败',
            icon: 'none'
          });
        }
      });
    }
  }
}
</script>

<style>
.container {
  min-height: 100vh;
  background: #f8f9fa;
}

.header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: #fff;
  border-bottom: 1rpx solid #e9ecef;
}

.header-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.header-info {
  flex: 1;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  display: block;
}

.header-desc {
  font-size: 24rpx;
  color: #6c757d;
  margin-top: 8rpx;
  display: block;
}

.type-selector {
  display: flex;
  background: #fff;
  border-bottom: 1rpx solid #e9ecef;
  padding: 0 20rpx;
}

.type-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 10rpx;
  transition: all 0.3s ease;
  border-bottom: 4rpx solid transparent;
}

.type-item.active {
  border-bottom-color: #007bff;
}

.type-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.type-text {
  font-size: 24rpx;
  color: #6c757d;
}

.type-item.active .type-text {
  color: #007bff;
  font-weight: bold;
}

.writing-area {
  padding: 30rpx;
}

.prompt-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
}

.prompt-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20rpx;
  display: block;
}

.prompt-input {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.5;
  background: #f8f9fa;
  box-sizing: border-box;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #6c757d;
  margin-top: 10rpx;
}

.generate-section {
  margin-bottom: 30rpx;
}

.generate-btn {
  width: 100%;
  height: 100rpx;
  background: #007bff;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  transition: all 0.3s ease;
}

.generate-btn.disabled {
  background: #ccc;
  color: #999;
}

.generate-btn:not(.disabled):active {
  transform: scale(0.98);
  background: #0056b3;
}

.btn-icon {
  font-size: 36rpx;
}

.btn-text {
  font-weight: bold;
}

.result-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.result-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}

.result-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.copy {
  background: #28a745;
  color: #fff;
}

.action-btn.regenerate {
  background: #ffc107;
  color: #333;
}

.action-btn:active {
  transform: scale(0.95);
}

.generating-animation {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #007bff;
  animation: typing 1.4s infinite;
}

.dot:nth-child(2) { animation-delay: 0.2s; }
.dot:nth-child(3) { animation-delay: 0.4s; }

@keyframes typing {
  0%, 60%, 100% { opacity: 0.3; transform: scale(1); }
  30% { opacity: 1; transform: scale(1.2); }
}

.generating-text {
  font-size: 28rpx;
  color: #6c757d;
}

.result-content {
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 30rpx;
  background: #f8f9fa;
}

.result-text {
  font-size: 28rpx;
  line-height: 1.8;
  color: #333;
  white-space: pre-wrap;
}

.examples-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
}

.examples-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20rpx;
  display: block;
}

.examples-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.example-item {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.example-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.example-text {
  font-size: 26rpx;
  color: #495057;
}
</style>

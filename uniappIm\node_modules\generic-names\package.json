{"name": "generic-names", "version": "4.0.0", "description": "Helper for building generic names, similar to webpack", "main": "index.js", "types": "index.d.ts", "scripts": {"test": "tape test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/css-modules/generic-names.git"}, "keywords": ["css-modules", "postcss-modules-scope", "webpack"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/css-modules/generic-names/issues"}, "homepage": "https://github.com/css-modules/generic-names#readme", "devDependencies": {"tape": "^4.6.2"}, "dependencies": {"loader-utils": "^3.2.0"}}
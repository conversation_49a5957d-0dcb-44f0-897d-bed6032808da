import{_ as e,o as s,c as l,w as a,i as o,a as t,b as i,d as n,t as r,e as u,f as c,I as d,g as p}from"./index-0fa1fc91.js";import{l as g,r as m}from"./api.44778c12.js";const f=e({data:()=>({loading:!1,showRegisterModal:!1,loginForm:{username:"",password:""},registerForm:{username:"",password:"",nickname:""}}),methods:{async handleLogin(){if(this.loginForm.username&&this.loginForm.password){this.loading=!0;try{console.log("=== 开始登录 ==="),console.log("登录表单数据:",this.loginForm),console.log("API地址:","http://localhost:8080/api/auth/login");const e=await g(this.loginForm);console.log("登录API响应:",e),200===e.code?(uni.setStorageSync("token",e.data.token),uni.setStorageSync("userInfo",e.data.user),console.log("登录成功，保存的用户信息:",e.data.user),uni.showToast({title:"登录成功",icon:"success"}),uni.reLaunch({url:"/pages/chat/chat"})):(console.log("登录失败:",e.message),uni.showToast({title:e.message||"登录失败",icon:"none"}))}catch(e){console.error("登录异常:",e),uni.showToast({title:"网络错误，请检查后端服务: "+e.message,icon:"none"})}finally{this.loading=!1}}else uni.showToast({title:"请填写完整信息",icon:"none"})},handleRegister(){this.showRegisterModal=!0},closeRegisterPopup(){this.showRegisterModal=!1,this.registerForm={username:"",password:"",nickname:""}},async submitRegister(){if(this.registerForm.username&&this.registerForm.password)try{const e=await m(this.registerForm);200===e.code?(uni.showToast({title:"注册成功",icon:"success"}),this.closeRegisterPopup(),this.loginForm.username=this.registerForm.username,this.loginForm.password=this.registerForm.password):uni.showToast({title:e.message,icon:"none"})}catch(e){uni.showToast({title:"注册失败",icon:"none"})}else uni.showToast({title:"请填写完整信息",icon:"none"})}}},[["render",function(e,g,m,f,h,_){const w=o,F=c,k=d,b=p;return s(),l(w,{class:"login-container"},{default:a(()=>[t(" 登录头部 "),i(w,{class:"login-header"},{default:a(()=>[i(w,{class:"logo-section"},{default:a(()=>[i(w,{class:"logo-container"},{default:a(()=>[i(w,{class:"logo-icon"},{default:a(()=>[n("🤖")]),_:1}),i(w,{class:"logo-glow"})]),_:1}),i(F,{class:"brand-name"},{default:a(()=>[n("伴伴AI")]),_:1}),i(F,{class:"brand-slogan"},{default:a(()=>[n("智能陪伴，温暖相随")]),_:1})]),_:1}),i(w,{class:"floating-elements"},{default:a(()=>[i(w,{class:"float-item float-1"},{default:a(()=>[n("✨")]),_:1}),i(w,{class:"float-item float-2"},{default:a(()=>[n("💫")]),_:1}),i(w,{class:"float-item float-3"},{default:a(()=>[n("🌟")]),_:1})]),_:1})]),_:1}),t(" 登录表单 "),i(w,{class:"login-form"},{default:a(()=>[i(w,{class:"form-title"},{default:a(()=>[i(F,{class:"welcome-text"},{default:a(()=>[n("欢迎回来")]),_:1}),i(F,{class:"welcome-subtitle"},{default:a(()=>[n("登录您的伴伴AI账户")]),_:1})]),_:1}),i(w,{class:"input-group"},{default:a(()=>[i(w,{class:"input-icon"},{default:a(()=>[n("👤")]),_:1}),i(k,{modelValue:h.loginForm.username,"onUpdate:modelValue":g[0]||(g[0]=e=>h.loginForm.username=e),placeholder:"请输入用户名",class:"input-field"},null,8,["modelValue"])]),_:1}),i(w,{class:"input-group"},{default:a(()=>[i(w,{class:"input-icon"},{default:a(()=>[n("🔒")]),_:1}),i(k,{modelValue:h.loginForm.password,"onUpdate:modelValue":g[1]||(g[1]=e=>h.loginForm.password=e),placeholder:"请输入密码",type:"password",class:"input-field"},null,8,["modelValue"])]),_:1}),i(b,{onClick:_.handleLogin,class:"login-btn",disabled:h.loading},{default:a(()=>[i(w,{class:"btn-content"},{default:a(()=>[h.loading?(s(),l(F,{key:0,class:"loading-icon"},{default:a(()=>[n("⏳")]),_:1})):t("v-if",!0),i(F,{class:"btn-text"},{default:a(()=>[n(r(h.loading?"登录中...":"开始AI之旅"),1)]),_:1})]),_:1})]),_:1},8,["onClick","disabled"]),i(w,{class:"divider"},{default:a(()=>[i(w,{class:"divider-line"}),i(F,{class:"divider-text"},{default:a(()=>[n("或")]),_:1}),i(w,{class:"divider-line"})]),_:1}),i(w,{class:"register-link"},{default:a(()=>[i(F,{onClick:_.handleRegister},{default:a(()=>[n("还没有账号？立即注册")]),_:1},8,["onClick"])]),_:1})]),_:1}),t(" 注册弹窗 "),h.showRegisterModal?(s(),l(w,{key:0,class:"modal-overlay",onClick:_.closeRegisterPopup},{default:a(()=>[i(w,{class:"register-popup",onClick:g[5]||(g[5]=u(()=>{},["stop"]))},{default:a(()=>[i(w,{class:"popup-header"},{default:a(()=>[i(w,{class:"popup-icon"},{default:a(()=>[n("🎉")]),_:1}),i(w,{class:"popup-title"},{default:a(()=>[n("加入伴伴AI")]),_:1}),i(w,{class:"popup-subtitle"},{default:a(()=>[n("开启您的AI陪伴之旅")]),_:1})]),_:1}),i(w,{class:"input-group"},{default:a(()=>[i(w,{class:"input-icon"},{default:a(()=>[n("👤")]),_:1}),i(k,{modelValue:h.registerForm.username,"onUpdate:modelValue":g[2]||(g[2]=e=>h.registerForm.username=e),placeholder:"请输入用户名",class:"input-field"},null,8,["modelValue"])]),_:1}),i(w,{class:"input-group"},{default:a(()=>[i(w,{class:"input-icon"},{default:a(()=>[n("🔒")]),_:1}),i(k,{modelValue:h.registerForm.password,"onUpdate:modelValue":g[3]||(g[3]=e=>h.registerForm.password=e),placeholder:"请输入密码",type:"password",class:"input-field"},null,8,["modelValue"])]),_:1}),i(w,{class:"input-group"},{default:a(()=>[i(w,{class:"input-icon"},{default:a(()=>[n("✨")]),_:1}),i(k,{modelValue:h.registerForm.nickname,"onUpdate:modelValue":g[4]||(g[4]=e=>h.registerForm.nickname=e),placeholder:"请输入昵称（可选）",class:"input-field"},null,8,["modelValue"])]),_:1}),i(w,{class:"popup-buttons"},{default:a(()=>[i(b,{onClick:_.closeRegisterPopup,class:"cancel-btn"},{default:a(()=>[i(F,null,{default:a(()=>[n("取消")]),_:1})]),_:1},8,["onClick"]),i(b,{onClick:_.submitRegister,class:"confirm-btn"},{default:a(()=>[i(F,{class:"confirm-icon"},{default:a(()=>[n("🚀")]),_:1}),i(F,null,{default:a(()=>[n("立即注册")]),_:1})]),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1},8,["onClick"])):t("v-if",!0)]),_:1})}],["__scopeId","data-v-039cbbe4"]]);export{f as default};

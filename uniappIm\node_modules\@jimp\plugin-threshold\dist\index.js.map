{"version": 3, "sources": ["../src/index.js"], "names": ["threshold", "cb", "max", "replace", "autoGreyscale", "throwError", "call", "constructor", "limit255", "greyscale", "scanQuiet", "bitmap", "width", "height", "x", "y", "idx", "grey", "data"], "mappings": ";;;;;;;;;;;AAAA;;AAEA;;;;;;;;;eASe;AAAA,SAAO;AACpBA,IAAAA,SADoB,2BACoCC,EADpC,EACwC;AAAA;;AAAA,UAAhDC,GAAgD,QAAhDA,GAAgD;AAAA,8BAA3CC,OAA2C;AAAA,UAA3CA,OAA2C,6BAAjC,GAAiC;AAAA,oCAA5BC,aAA4B;AAAA,UAA5BA,aAA4B,mCAAZ,IAAY;;AAC1D,UAAI,OAAOF,GAAP,KAAe,QAAnB,EAA6B;AAC3B,eAAOG,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,sBAAtB,EAA8CL,EAA9C,CAAP;AACD;;AAED,UAAI,OAAOE,OAAP,KAAmB,QAAvB,EAAiC;AAC/B,eAAOE,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,0BAAtB,EAAkDL,EAAlD,CAAP;AACD;;AAED,UAAI,OAAOG,aAAP,KAAyB,SAA7B,EAAwC;AACtC,eAAOC,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,iCAAtB,EAAyDL,EAAzD,CAAP;AACD;;AAEDC,MAAAA,GAAG,GAAG,KAAKK,WAAL,CAAiBC,QAAjB,CAA0BN,GAA1B,CAAN;AACAC,MAAAA,OAAO,GAAG,KAAKI,WAAL,CAAiBC,QAAjB,CAA0BL,OAA1B,CAAV;;AAEA,UAAIC,aAAJ,EAAmB;AACjB,aAAKK,SAAL;AACD;;AAED,WAAKC,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,KAAKC,MAAL,CAAYC,KAAjC,EAAwC,KAAKD,MAAL,CAAYE,MAApD,EAA4D,UAACC,CAAD,EAAIC,CAAJ,EAAOC,GAAP,EAAe;AACzE,YAAMC,IAAI,GACR,KAAI,CAACN,MAAL,CAAYO,IAAZ,CAAiBF,GAAjB,IAAwBd,GAAxB,GAA8B,KAAI,CAACS,MAAL,CAAYO,IAAZ,CAAiBF,GAAjB,CAA9B,GAAsDb,OADxD;AAGA,QAAA,KAAI,CAACQ,MAAL,CAAYO,IAAZ,CAAiBF,GAAjB,IAAwBC,IAAxB;AACA,QAAA,KAAI,CAACN,MAAL,CAAYO,IAAZ,CAAiBF,GAAG,GAAG,CAAvB,IAA4BC,IAA5B;AACA,QAAA,KAAI,CAACN,MAAL,CAAYO,IAAZ,CAAiBF,GAAG,GAAG,CAAvB,IAA4BC,IAA5B;AACD,OAPD;;AASA,UAAI,0BAAchB,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACK,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD;AAnCmB,GAAP;AAAA,C", "sourcesContent": ["import { isNodePattern, throwError } from '@jimp/utils';\n\n/**\n * Applies a minimum color threshold to a greyscale image.  Converts image to greyscale by default\n * @param {number} options object\n *  max: A number auto limited between 0 - 255\n *  replace: (optional) A number auto limited between 0 - 255 (default 255)\n *  autoGreyscale: (optional) A boolean whether to apply greyscale beforehand (default true)\n * @param {number} cb (optional) a callback for when complete\n * @return {this} this for chaining of methods\n */\nexport default () => ({\n  threshold({ max, replace = 255, autoGreyscale = true }, cb) {\n    if (typeof max !== 'number') {\n      return throwError.call(this, 'max must be a number', cb);\n    }\n\n    if (typeof replace !== 'number') {\n      return throwError.call(this, 'replace must be a number', cb);\n    }\n\n    if (typeof autoGreyscale !== 'boolean') {\n      return throwError.call(this, 'autoGreyscale must be a boolean', cb);\n    }\n\n    max = this.constructor.limit255(max);\n    replace = this.constructor.limit255(replace);\n\n    if (autoGreyscale) {\n      this.greyscale();\n    }\n\n    this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, (x, y, idx) => {\n      const grey =\n        this.bitmap.data[idx] < max ? this.bitmap.data[idx] : replace;\n\n      this.bitmap.data[idx] = grey;\n      this.bitmap.data[idx + 1] = grey;\n      this.bitmap.data[idx + 2] = grey;\n    });\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  }\n});\n"], "file": "index.js"}
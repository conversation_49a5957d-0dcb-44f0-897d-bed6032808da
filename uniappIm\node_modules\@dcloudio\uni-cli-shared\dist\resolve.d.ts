export declare function requireResolve(filename: string, basedir: string): string;
export declare function relativeFile(from: string, to: string): string;
export declare const resolveMainPathOnce: (inputDir: string) => string;
export declare function getBuiltInPaths(): string[];
export declare function resolveBuiltIn(path: string): string;
export declare function resolveVueI18nRuntime(): string;
export declare function resolveComponentsLibPath(): string;

uni-actionsheet {
  display: block;
  box-sizing: border-box;
}

.uni-actionsheet {
  position: fixed;
  left: 6px;
  right: 6px;
  bottom: 6px;
  transform: translate(0, 100%);
  backface-visibility: hidden;
  z-index: 999;
  visibility: hidden;
  transition: transform 0.3s, visibility 0.3s;
}

.uni-actionsheet.uni-actionsheet_toggle {
  visibility: visible;
  transform: translate(0, 0);
}

.uni-actionsheet * {
  box-sizing: border-box;
}

.uni-actionsheet__menu,
.uni-actionsheet__action {
  border-radius: 5px;
  background-color: #fcfcfd;
}

.uni-actionsheet__action {
  margin-top: 6px;
}

.uni-actionsheet__cell,
.uni-actionsheet__title {
  position: relative;
  padding: 10px 6px;
  text-align: center;
  font-size: 18px;
  text-overflow: ellipsis;
  overflow: hidden;
  cursor: pointer;
}

.uni-actionsheet__title {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1;
  color: var(--UI-FG);
  background-color: #fff;
  border-radius: 5px 5px 0 0;
  border-bottom: 1px solid #e5e5e5;
}

.uni-actionsheet__cell:before {
  content: ' ';
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid #e5e5e5;
  color: #e5e5e5;
  transform-origin: 0 0;
  transform: scaleY(0.5);
}

.uni-actionsheet__cell:active {
  background-color: #ececec;
}

.uni-actionsheet__cell:first-child:before {
  display: none;
}

@media screen and (min-width: 500px) and (min-height: 500px) {
  .uni-mask.uni-actionsheet__mask {
    background: none;
  }

  .uni-actionsheet {
    width: 300px;
    left: 50%;
    right: auto;
    top: 50%;
    bottom: auto;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: opacity 0.3s, visibility 0.3s;
  }

  .uni-actionsheet.uni-actionsheet_toggle {
    opacity: 1;
    transform: translate(-50%, -50%);
  }

  .uni-actionsheet__menu {
    box-shadow: 0px 0 20px 5px rgba(0, 0, 0, 0.3);
  }

  .uni-actionsheet__action {
    display: none;
  }
}

@media (prefers-color-scheme: dark) {
  .uni-actionsheet__title {
    background-color: var(--UI-BG-1);
    border-bottom-color: var(--UI-BORDER-CLOLOR-1);
  }

  .uni-actionsheet__action,
  .uni-actionsheet__menu {
    background-color: var(--UI-BG-2);
  }

  .uni-actionsheet__cell:active {
    background-color: var(--UI-BG-CLOLOR-ACTIVE);
  }

  .uni-actionsheet__cell:before {
    border-top-color: var(--UI-BORDER-CLOLOR-1);
    color: var(--UI-BORDER-CLOLOR-1);
  }
}

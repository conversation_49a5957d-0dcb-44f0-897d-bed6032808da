<template>
  <view class="container">
    <!-- 顶部标题 -->
    <view class="header">
      <view class="header-icon">🎯</view>
      <view class="header-info">
        <text class="header-title">智能推荐</text>
        <text class="header-desc">个性化内容推荐</text>
      </view>
    </view>

    <!-- 推荐类别选择 -->
    <view class="category-selector">
      <text class="selector-title">🏷️ 推荐类别</text>
      <scroll-view class="category-scroll" scroll-x="true" show-scrollbar="false">
        <view class="category-list">
          <view 
            v-for="(category, index) in categories" 
            :key="index"
            class="category-item"
            :class="{ active: selectedCategory === category.key }"
            @tap="selectCategory(category.key)"
          >
            <text class="category-icon">{{ category.icon }}</text>
            <text class="category-name">{{ category.name }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 偏好输入区域 -->
    <view class="preference-section">
      <text class="section-title">💭 告诉我你的偏好</text>
      <textarea 
        class="preference-input" 
        v-model="preferences" 
        :placeholder="currentCategory.placeholder"
        :maxlength="300"
        show-confirm-bar="false"
      />
      <view class="char-count">{{ preferences.length }}/300</view>
      
      <!-- 快捷偏好标签 -->
      <view class="quick-tags">
        <text class="tags-title">🏷️ 快捷标签</text>
        <view class="tags-list">
          <view 
            v-for="(tag, index) in currentCategory.tags" 
            :key="index" 
            class="tag-item"
            @tap="addTag(tag)"
          >
            <text class="tag-text">{{ tag }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 推荐按钮 -->
    <view class="recommend-section">
      <button 
        class="recommend-btn" 
        :class="{ disabled: !preferences.trim() || isRecommending }" 
        @tap="getRecommendation"
      >
        <text class="btn-icon">{{ isRecommending ? '⏳' : '✨' }}</text>
        <text class="btn-text">{{ isRecommending ? '推荐中...' : '获取推荐' }}</text>
      </button>
    </view>

    <!-- 推荐结果 -->
    <view v-if="recommendationResult || isRecommending" class="result-section">
      <view class="result-header">
        <text class="result-title">{{ currentCategory.icon }} 为您推荐</text>
        <view v-if="recommendationResult && !isRecommending" class="result-actions">
          <button class="action-btn refresh" @tap="getRecommendation">🔄 重新推荐</button>
        </view>
      </view>
      
      <!-- 推荐中的动画 -->
      <view v-if="isRecommending" class="recommending-animation">
        <view class="loading-circle">
          <view class="circle-dot"></view>
          <view class="circle-dot"></view>
          <view class="circle-dot"></view>
          <view class="circle-dot"></view>
        </view>
        <text class="recommending-text">AI正在为您精心挑选...</text>
      </view>
      
      <!-- 推荐结果内容 -->
      <view v-if="recommendationResult" class="result-content">
        <text class="result-text">{{ recommendationResult }}</text>
        <view class="result-footer">
          <button class="footer-btn copy" @tap="copyResult">📋 复制推荐</button>
          <button class="footer-btn share" @tap="shareResult">📤 分享</button>
        </view>
      </view>
    </view>

    <!-- 历史推荐 -->
    <view v-if="!recommendationResult && !isRecommending && historyRecommendations.length > 0" class="history-section">
      <text class="history-title">📚 历史推荐</text>
      <view class="history-list">
        <view 
          v-for="(item, index) in historyRecommendations" 
          :key="index" 
          class="history-item"
          @tap="viewHistory(item)"
        >
          <view class="history-header">
            <text class="history-category">{{ item.categoryIcon }} {{ item.categoryName }}</text>
            <text class="history-time">{{ item.time }}</text>
          </view>
          <text class="history-preference">{{ item.preferences }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      preferences: '',
      recommendationResult: '',
      isRecommending: false,
      userId: 123,
      selectedCategory: 'books',
      categories: [
        {
          key: 'books',
          name: '书籍',
          icon: '📚',
          placeholder: '请描述您喜欢的书籍类型、作者、主题等...\n例如：我喜欢科幻小说，特别是关于人工智能的',
          tags: ['科幻', '悬疑', '历史', '传记', '心理学', '编程', '文学', '哲学']
        },
        {
          key: 'movies',
          name: '电影',
          icon: '🎬',
          placeholder: '请描述您喜欢的电影类型、导演、演员等...\n例如：我喜欢悬疑片和科幻片，特别是诺兰的作品',
          tags: ['动作', '喜剧', '爱情', '科幻', '悬疑', '恐怖', '动画', '纪录片']
        },
        {
          key: 'music',
          name: '音乐',
          icon: '🎵',
          placeholder: '请描述您喜欢的音乐风格、歌手、乐器等...\n例如：我喜欢流行音乐和古典音乐，特别是钢琴曲',
          tags: ['流行', '摇滚', '古典', '爵士', '电子', '民谣', '说唱', '轻音乐']
        },
        {
          key: 'food',
          name: '美食',
          icon: '🍽️',
          placeholder: '请描述您喜欢的菜系、口味、食材等...\n例如：我喜欢川菜和日料，偏爱辣味和清淡的食物',
          tags: ['川菜', '粤菜', '日料', '韩料', '西餐', '甜品', '素食', '海鲜']
        },
        {
          key: 'travel',
          name: '旅行',
          icon: '✈️',
          placeholder: '请描述您喜欢的旅行方式、目的地类型等...\n例如：我喜欢自然风光和历史文化，偏爱安静的小城',
          tags: ['自然', '历史', '文化', '海滨', '山区', '城市', '乡村', '异国']
        },
        {
          key: 'courses',
          name: '课程',
          icon: '🎓',
          placeholder: '请描述您想学习的技能、知识领域等...\n例如：我想学习编程和设计，特别是前端开发',
          tags: ['编程', '设计', '语言', '音乐', '绘画', '摄影', '写作', '运动']
        }
      ],
      historyRecommendations: []
    }
  },
  computed: {
    currentCategory() {
      return this.categories.find(cat => cat.key === this.selectedCategory) || this.categories[0];
    }
  },
  onLoad() {
    this.loadUserInfo();
    this.loadHistory();
  },
  methods: {
    loadUserInfo() {
      const userInfo = uni.getStorageSync('userInfo');
      if (userInfo && userInfo.id) {
        this.userId = userInfo.id;
      }
    },

    loadHistory() {
      const history = uni.getStorageSync('recommendHistory') || [];
      this.historyRecommendations = history.slice(0, 5); // 只显示最近5条
    },

    saveHistory(recommendation) {
      const history = uni.getStorageSync('recommendHistory') || [];
      const newItem = {
        categoryIcon: this.currentCategory.icon,
        categoryName: this.currentCategory.name,
        preferences: this.preferences,
        result: recommendation,
        time: this.getCurrentTime()
      };
      
      history.unshift(newItem);
      if (history.length > 20) {
        history.splice(20); // 只保留最近20条
      }
      
      uni.setStorageSync('recommendHistory', history);
      this.loadHistory();
    },

    selectCategory(categoryKey) {
      this.selectedCategory = categoryKey;
      this.preferences = '';
      this.recommendationResult = '';
    },

    addTag(tag) {
      if (this.preferences) {
        this.preferences += `，${tag}`;
      } else {
        this.preferences = tag;
      }
    },

    async getRecommendation() {
      if (!this.preferences.trim() || this.isRecommending) return;

      this.isRecommending = true;
      this.recommendationResult = '';

      try {
        const response = await this.callRecommendAPI(this.preferences.trim(), this.currentCategory.name);
        
        if (response && response.success) {
          this.recommendationResult = response.response || '抱歉，暂时无法生成推荐，请重试。';
          this.saveHistory(this.recommendationResult);
        } else {
          this.recommendationResult = '推荐服务暂时不可用，请稍后再试。';
        }
      } catch (error) {
        this.recommendationResult = '网络连接失败，请检查网络后重试。';
        console.error('API调用失败:', error);
      }

      this.isRecommending = false;
    },

    async callRecommendAPI(preferences, category) {
      const apiUrl = 'http://localhost:8082/api/ai/miniprogram/recommend';
      
      const response = await uni.request({
        url: apiUrl,
        method: 'POST',
        header: {
          'Content-Type': 'application/json'
        },
        data: {
          userId: this.userId,
          preferences: preferences,
          category: category
        }
      });

      if (response.statusCode === 200 && response.data.code === 200) {
        return response.data.data;
      } else {
        throw new Error('API调用失败');
      }
    },

    copyResult() {
      if (!this.recommendationResult) return;
      
      uni.setClipboardData({
        data: this.recommendationResult,
        success: () => {
          uni.showToast({
            title: '复制成功',
            icon: 'success'
          });
        },
        fail: () => {
          uni.showToast({
            title: '复制失败',
            icon: 'none'
          });
        }
      });
    },

    shareResult() {
      if (!this.recommendationResult) return;
      
      uni.showActionSheet({
        itemList: ['分享给朋友', '保存到相册'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 分享功能
            uni.showToast({
              title: '分享功能开发中',
              icon: 'none'
            });
          } else if (res.tapIndex === 1) {
            // 保存功能
            uni.showToast({
              title: '保存功能开发中',
              icon: 'none'
            });
          }
        }
      });
    },

    viewHistory(item) {
      this.preferences = item.preferences;
      this.recommendationResult = item.result;
      
      // 切换到对应的类别
      const category = this.categories.find(cat => cat.name === item.categoryName);
      if (category) {
        this.selectedCategory = category.key;
      }
    },

    getCurrentTime() {
      const now = new Date();
      return `${now.getMonth() + 1}/${now.getDate()} ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    }
  }
}
</script>

<style>
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
}

.header-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.header-info {
  flex: 1;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  display: block;
}

.header-desc {
  font-size: 24rpx;
  color: #6c757d;
  margin-top: 8rpx;
  display: block;
}

.category-selector {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  padding: 30rpx 0;
}

.selector-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin: 0 30rpx 20rpx;
  display: block;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: inline-flex;
  padding: 0 30rpx;
  gap: 20rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  min-width: 120rpx;
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 0.8);
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.category-item.active {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
  transform: scale(1.05);
}

.category-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.category-name {
  font-size: 24rpx;
  color: #495057;
  font-weight: 500;
  white-space: nowrap;
}

.category-item.active .category-name {
  color: #667eea;
  font-weight: bold;
}

.preference-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20rpx;
  display: block;
}

.preference-input {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.5;
  background: #f8f9fa;
  box-sizing: border-box;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #6c757d;
  margin-top: 10rpx;
}

.quick-tags {
  margin-top: 30rpx;
}

.tags-title {
  font-size: 24rpx;
  color: #6c757d;
  margin-bottom: 16rpx;
  display: block;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.tag-item {
  padding: 12rpx 20rpx;
  background: #e9ecef;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.tag-item:active {
  background: #667eea;
  transform: scale(0.95);
}

.tag-item:active .tag-text {
  color: #fff;
}

.tag-text {
  font-size: 24rpx;
  color: #495057;
}

.recommend-section {
  padding: 30rpx;
}

.recommend-btn {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
  border: none;
  border-radius: 50rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  transition: all 0.3s ease;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.recommend-btn.disabled {
  background: #ccc;
  color: #999;
  box-shadow: none;
}

.recommend-btn:not(.disabled):active {
  transform: scale(0.98);
}

.result-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  margin: 0 20rpx 30rpx;
  border-radius: 16rpx;
  padding: 30rpx;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.result-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
}

.result-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.refresh {
  background: #ffc107;
  color: #333;
}

.recommending-animation {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}

.loading-circle {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 30rpx;
}

.circle-dot {
  position: absolute;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #667eea;
  animation: circleRotate 1.2s linear infinite;
}

.circle-dot:nth-child(1) { top: 0; left: 50%; margin-left: -8rpx; animation-delay: 0s; }
.circle-dot:nth-child(2) { top: 50%; right: 0; margin-top: -8rpx; animation-delay: 0.3s; }
.circle-dot:nth-child(3) { bottom: 0; left: 50%; margin-left: -8rpx; animation-delay: 0.6s; }
.circle-dot:nth-child(4) { top: 50%; left: 0; margin-top: -8rpx; animation-delay: 0.9s; }

@keyframes circleRotate {
  0% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.3; transform: scale(0.5); }
  100% { opacity: 1; transform: scale(1); }
}

.recommending-text {
  font-size: 28rpx;
  color: #6c757d;
}

.result-content {
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 30rpx;
  background: #f8f9fa;
}

.result-text {
  font-size: 28rpx;
  line-height: 1.8;
  color: #333;
  white-space: pre-wrap;
  display: block;
  margin-bottom: 30rpx;
}

.result-footer {
  display: flex;
  gap: 16rpx;
}

.footer-btn {
  flex: 1;
  height: 70rpx;
  border: none;
  border-radius: 35rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.footer-btn.copy {
  background: #28a745;
  color: #fff;
}

.footer-btn.share {
  background: #17a2b8;
  color: #fff;
}

.footer-btn:active {
  transform: scale(0.95);
}

.history-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  margin: 0 20rpx 30rpx;
  border-radius: 16rpx;
  padding: 30rpx;
}

.history-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20rpx;
  display: block;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.history-item {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.history-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.history-category {
  font-size: 24rpx;
  color: #667eea;
  font-weight: bold;
}

.history-time {
  font-size: 20rpx;
  color: #6c757d;
}

.history-preference {
  font-size: 26rpx;
  color: #495057;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>

package cn.zhentao.controller;

import cn.zhentao.service.MinioService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Map;

/**
 * 文件管理控制器
 *
 * 提供文件相关的REST API接口，主要功能包括：
 * 1. 图片文件下载 - 从MinIO获取并返回图片文件
 * 2. 文件上传 - 处理文件上传请求（预留接口）
 *
 * 与MinIO对象存储服务集成，提供高效的文件存储和访问服务
 *
 * <AUTHOR>
 * @since 2023-04-16
 */
@Slf4j
@RestController
@RequestMapping("/file-info")
public class FilesController {

    /**
     * MinIO文件存储服务，用于文件的上传、下载和管理
     */
    @Resource
    MinioService minioService;

    /**
     * 文件上传接口（预留）
     *
     * 提供通用的文件上传功能，可根据业务需求进行扩展
     *
     * @param resout 请求体，包含文件上传相关参数
     * @return 上传结果
     */
    @PutMapping("/put")
    Object putFile(@RequestBody Map<String, Object> resout) {
        // TODO: 实现文件上传逻辑
        log.info("收到文件上传请求，参数: {}", resout);
        return Map.of("message", "文件上传接口待实现");
    }

    /**
     * 图片文件下载接口
     *
     * 根据文件ID从MinIO对象存储中获取图片文件并返回给客户端
     * 支持直接在浏览器中显示图片，也支持下载
     *
     * @param fileId 图片文件的唯一标识符（MinIO中的文件名）
     * @param res HTTP响应对象，用于返回文件流
     * @throws IOException 当文件读取或网络传输发生异常时抛出
     */
    @GetMapping("/image/{fileId}")
    public void getFile(@PathVariable String fileId, HttpServletResponse res) throws IOException {
        InputStream inputStream = null;
        OutputStream outputStream = null;

        try {
            log.info("请求下载图片文件: {}", fileId);

            // 参数验证
            if (fileId == null || fileId.trim().isEmpty()) {
                log.warn("文件ID为空");
                res.sendError(HttpServletResponse.SC_BAD_REQUEST, "文件ID不能为空");
                return;
            }

            // 检查文件是否存在
            if (!minioService.fileExists(fileId)) {
                log.warn("请求的文件不存在: {}", fileId);
                res.sendError(HttpServletResponse.SC_NOT_FOUND, "文件不存在");
                return;
            }

            // 从MinIO获取文件流
            inputStream = minioService.downloadFile(fileId);

            // 设置响应头
            res.setContentType("image/jpeg");
            res.setHeader("Cache-Control", "max-age=3600"); // 缓存1小时

            // 设置文件名，支持中文文件名
            String encodedFileName = java.net.URLEncoder.encode(fileId, "UTF-8");
            res.setHeader("Content-Disposition", "inline; filename=\"" + encodedFileName + "\"");

            // 将文件流写入响应
            outputStream = res.getOutputStream();
            byte[] buffer = new byte[8192]; // 8KB缓冲区
            int bytesRead;

            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            outputStream.flush();
            log.info("图片文件下载完成: {}", fileId);

        } catch (Exception e) {
            log.error("下载图片文件时发生异常: {}", fileId, e);

            // 如果响应还没有提交，发送错误状态
            if (!res.isCommitted()) {
                res.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "文件下载失败");
            }
        } finally {
            // 关闭流资源
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.warn("关闭输入流时发生异常", e);
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.warn("关闭输出流时发生异常", e);
                }
            }
        }
    }
}
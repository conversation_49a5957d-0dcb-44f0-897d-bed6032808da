<view class="container"><view class="header"><view class="header-icon">💝</view><view class="header-info"><text class="header-title">情感陪伴</text><text class="header-desc">情感识别回应，温暖陪伴</text></view></view><view class="emotion-selector"><text class="selector-title">💭 当前心情</text><view class="emotion-grid"><view wx:for="{{a}}" wx:for-item="emotion" wx:key="c" class="{{['emotion-item', emotion.d && 'active']}}" bindtap="{{emotion.e}}"><text class="emotion-icon">{{emotion.a}}</text><text class="emotion-name">{{emotion.b}}</text></view></view></view><scroll-view class="chat-area" scroll-y="true" scroll-top="{{d}}" scroll-with-animation="true"><view class="message-list"><view wx:for="{{b}}" wx:for-item="message" wx:key="d" class="{{['message-item', message.e]}}"><view class="message-avatar"><text class="avatar-text">{{message.a}}</text></view><view class="message-content"><view class="message-bubble"><text class="message-text">{{message.b}}</text><text class="message-time">{{message.c}}</text></view></view></view><view wx:if="{{c}}" class="message-item ai typing"><view class="message-avatar"><text class="avatar-text">💝</text></view><view class="message-content"><view class="message-bubble"><view class="typing-indicator"><view class="dot"></view><view class="dot"></view><view class="dot"></view></view></view></view></view></view></scroll-view><view class="input-area"><view class="input-container"><input class="input-field" placeholder="分享您的感受，我会用心倾听..." disabled="{{e}}" bindconfirm="{{f}}" value="{{g}}" bindinput="{{h}}"/><button class="{{['send-btn', j && 'disabled']}}" bindtap="{{k}}">{{i}}</button></view></view><view wx:if="{{l}}" class="emotion-suggestions"><text class="suggestions-title">💡 可以这样表达</text><view class="suggestions-list"><view wx:for="{{m}}" wx:for-item="suggestion" wx:key="b" class="suggestion-item" bindtap="{{suggestion.c}}"><text class="suggestion-text">{{suggestion.a}}</text></view></view></view></view>
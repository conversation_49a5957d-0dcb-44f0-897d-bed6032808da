package com.zhentao.studyim.dto;

import com.zhentao.studyim.entity.User;
import lombok.Data;

/**
 * 好友信息DTO
 * 包含用户基本信息和备注
 */
@Data
public class FriendInfo {
    
    private Long userId;           // 用户ID
    private String username;       // 用户名
    private String nickname;       // 昵称
    private String remark;         // 备注名称
    private String email;          // 邮箱
    private String phonenumber;    // 手机号
    private String avatar;         // 头像
    private String sex;            // 性别
    private User.UserStatus userStatus; // 在线状态
    
    /**
     * 从User实体和备注创建FriendInfo
     */
    public static FriendInfo fromUser(User user, String remark) {
        FriendInfo friendInfo = new FriendInfo();
        friendInfo.setUserId(user.getUserId());
        friendInfo.setUsername(user.getUsername());
        friendInfo.setNickname(user.getNickname());
        friendInfo.setRemark(remark);
        friendInfo.setEmail(user.getEmail());
        friendInfo.setPhonenumber(user.getPhonenumber());
        friendInfo.setAvatar(user.getAvatar());
        friendInfo.setSex(user.getSex());
        friendInfo.setUserStatus(user.getUserStatus());
        return friendInfo;
    }
    
    /**
     * 获取显示名称（优先显示备注，其次昵称，最后用户名）
     */
    public String getDisplayName() {
        if (remark != null && !remark.trim().isEmpty()) {
            return remark;
        }
        if (nickname != null && !nickname.trim().isEmpty()) {
            return nickname;
        }
        return username;
    }
}

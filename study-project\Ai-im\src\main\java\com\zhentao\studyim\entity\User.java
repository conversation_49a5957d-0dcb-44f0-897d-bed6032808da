package com.zhentao.studyim.entity;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 用户实体类
 * 
 * @Data 注解自动生成getter/setter方法
 * @Entity 标记这是一个JPA实体
 * @Table 指定数据库表名
 */
@Data
@Entity
@Table(name = "user")
public class User {

    @Id // 主键
    @GeneratedValue(strategy = GenerationType.IDENTITY) // 自增主键
    @Column(name = "id") // 数据库中的字段名是id
    private Long userId; // 用户ID，但映射到数据库的id字段

    @Column(name = "username", length = 30, nullable = false, unique = true) // 用户账号
    private String username;

    @Column(name = "nickname", length = 30, nullable = false) // 用户昵称
    private String nickname;

    @Column(name = "user_type", length = 2, columnDefinition = "VARCHAR(2) DEFAULT '00'") // 用户类型(00系统用户)
    private String userType = "00";

    @Column(name = "email", length = 50) // 用户邮箱
    private String email;

    @Column(name = "phonenumber", length = 11) // 手机号码
    private String phonenumber;

    @Column(name = "sex", length = 1, columnDefinition = "VARCHAR(1) DEFAULT '2'") // 用户性别(0男 1女 2未知)
    private String sex = "2";

    @Column(name = "avatar", length = 100) // 头像地址
    private String avatar;

    @Column(name = "password", length = 100, nullable = false) // 密码
    private String password;

    @Column(name = "status", columnDefinition = "INT DEFAULT 0") // 帐号状态(0正常 1停用)
    private Integer status = 0;

    @Column(name = "del_flag", length = 1, columnDefinition = "VARCHAR(1) DEFAULT '0'") // 删除标志(0代表存在 2代表删除)
    private String delFlag = "0";

    @Column(name = "login_date") // 最后登录时间
    private LocalDateTime loginDate;

    @Column(name = "create_by", length = 64) // 创建者
    private String createBy;

    @Column(name = "create_time", columnDefinition = "DATETIME DEFAULT CURRENT_TIMESTAMP") // 创建时间
    private LocalDateTime createTime;

    @Column(name = "update_by", length = 64) // 更新者
    private String updateBy;

    @Column(name = "update_time", columnDefinition = "DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP") // 更新时间
    private LocalDateTime updateTime;

    @Column(name = "remark", length = 500) // 备注
    private String remark;

    /**
     * 用户状态枚举
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "userStatus", length = 20, columnDefinition = "VARCHAR(20) DEFAULT 'OFFLINE'")
    private UserStatus userStatus = UserStatus.OFFLINE; // 用户在线状态

    /**
     * 用户在线状态枚举
     */
    public enum UserStatus {
        ONLINE, // 在线
        OFFLINE, // 离线
        BUSY // 忙碌
    }

    /**
     * 在保存前设置默认值
     */
    @PrePersist
    public void prePersist() {
        if (this.userType == null) {
            this.userType = "00";
        }
        if (this.sex == null) {
            this.sex = "2";
        }
        if (this.status == null) {
            this.status = 0;
        }
        if (this.delFlag == null) {
            this.delFlag = "0";
        }
        if (this.userStatus == null) {
            this.userStatus = UserStatus.OFFLINE;
        }
        if (this.createTime == null) {
            this.createTime = LocalDateTime.now();
        }
        if (this.updateTime == null) {
            this.updateTime = LocalDateTime.now();
        }
    }
}
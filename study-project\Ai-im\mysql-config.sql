-- 查看当前MySQL的最大连接数配置
SHOW VARIABLES LIKE 'max_connections';

-- 查看当前活跃连接数
SHOW STATUS LIKE 'Threads_connected';

-- 查看历史最大连接数
SHOW STATUS LIKE 'Max_used_connections';

-- 设置MySQL最大连接数为500（临时设置，重启后失效）
SET GLOBAL max_connections = 500;

-- 再次查看设置后的最大连接数
SHOW VARIABLES LIKE 'max_connections';

-- 查看所有当前连接
SHOW PROCESSLIST;

-- 如果需要杀死某些长时间运行的连接，可以使用：
-- KILL CONNECTION_ID;

-- 注意：要永久设置最大连接数，需要在MySQL配置文件（my.cnf或my.ini）中添加：
-- [mysqld]
-- max_connections = 500
-- 然后重启MySQL服务

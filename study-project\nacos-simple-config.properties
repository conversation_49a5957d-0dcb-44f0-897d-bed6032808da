# Spring相关配置
server.servlet.contextPath=/nacos
server.port=8848
server.address=**************

# 数据源配置
spring.datasource.platform=embedded

# 单机模式
nacos.standalone=true

# 暂时禁用认证进行测试
nacos.core.auth.enabled=false

# 管理端点
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always

# 日志配置
logging.level.root=INFO
logging.level.com.alibaba.nacos=INFO

# 其他配置
nacos.naming.distro.taskDispatchThreadCount=1
nacos.naming.distro.taskDispatchPeriod=200
nacos.naming.distro.batchSyncKeyCount=1000

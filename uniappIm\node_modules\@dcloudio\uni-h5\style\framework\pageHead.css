uni-page-head {
  display: block;
  box-sizing: border-box;
}

.uni-page-head {
  position: fixed;
  left: var(--window-left);
  right: var(--window-right);
  height: 44px;
  height: calc(44px + constant(safe-area-inset-top));
  height: calc(44px + env(safe-area-inset-top));
  padding: 7px 3px;
  padding-top: calc(7px + constant(safe-area-inset-top));
  padding-top: calc(7px + env(safe-area-inset-top));
  display: flex;
  overflow: hidden;
  justify-content: space-between;
  box-sizing: border-box;
  z-index: 998;
  color: #fff;
  background-color: #000;
  transition-property: all;
}

.uni-page-head * {
  box-sizing: border-box;
}

.uni-page-head .uni-btn-icon {
  overflow: hidden;
  min-width: 1em;
  font-style: normal;
}

.uni-page-head-titlePenetrate,
.uni-page-head-titlePenetrate .uni-page-head-bd,
.uni-page-head-titlePenetrate .uni-page-head-bd * {
  pointer-events: none;
}

.uni-page-head-titlePenetrate * {
  pointer-events: auto;
}

.uni-page-head.uni-page-head-transparent .uni-page-head-ft > div {
  justify-content: center;
}

.uni-page-head ~ .uni-placeholder {
  width: 100%;
  height: 44px;
  height: calc(44px + constant(safe-area-inset-top));
  height: calc(44px + env(safe-area-inset-top));
}

.uni-placeholder-titlePenetrate {
  pointer-events: none;
}

.uni-page-head-hd {
  display: flex;
  align-items: center;
  font-size: 16px;
}

.uni-page-head-bd {
  position: absolute;
  left: 70px;
  right: 70px;
  min-width: 0;
  user-select: auto;
}

.uni-page-head-btn {
  position: relative;
  width: auto;
  margin: 0 2px;
  word-break: keep-all;
  white-space: pre;
  cursor: pointer;
}

.uni-page-head-btn svg {
  margin-top: -2px;
}

.uni-page-head-transparent .uni-page-head-btn {
  display: flex;
  align-items: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
}

.uni-page-head-btn-red-dot::after {
  content: attr(badge-text);
  position: absolute;
  right: 0;
  top: 0;
  background-color: red;
  color: white;
  width: 18px;
  height: 18px;
  line-height: 18px;
  border-radius: 18px;
  overflow: hidden;
  transform: scale(0.5) translate(40%, -40%);
  transform-origin: 100% 0;
}

.uni-page-head-btn-red-dot[badge-text]::after {
  font-size: 12px;
  width: auto;
  min-width: 18px;
  max-width: 42px;
  text-align: center;
  padding: 0 3px;
  transform: scale(0.7) translate(40%, -40%);
}

.uni-page-head-btn-select svg {
  vertical-align: middle;
  margin-left: 2px;
  transform: rotate(270deg) scale(0.8);
}

.uni-page-head-search {
  position: relative;
  display: flex;
  flex: 1;
  margin: 0 2px;
  line-height: 30px;
  font-size: 15px;
}

.uni-page-head-search-input {
  width: 100%;
  height: 100%;
  padding-left: 34px;
  text-align: left;
}

.uni-page-head-search-placeholder {
  position: absolute;
  max-width: 100%;
  height: 100%;
  padding-left: 34px;
  overflow: hidden;
  word-break: keep-all;
  white-space: pre;
}

.uni-page-head-search-placeholder-right {
  right: 0;
}

.uni-page-head-search-placeholder-center {
  left: 50%;
  transform: translateX(-50%);
}

.uni-page-head-search-icon {
  position: absolute;
  top: 0;
  left: 2px;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.uni-page-head-ft {
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
  font-size: 13px;
}

.uni-page-head__title {
  font-weight: bold;
  font-size: 16px;
  line-height: 30px;
  text-align: center;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.uni-page-head__title .uni-loading {
  width: 16px;
  height: 16px;
  margin-top: -3px;
}

.uni-page-head__title .uni-page-head__title_image {
  width: auto;
  height: 26px;
  vertical-align: middle;
}

.uni-page-head-shadow {
  overflow: visible;
}

.uni-page-head-shadow::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: 100%;
  height: 5px;
  background-size: 100% 100%;
}

.uni-page-head-shadow-grey::after {
  background-image: url('https://cdn.dcloud.net.cn/img/shadow-grey.png');
}

.uni-page-head-shadow-blue::after {
  background-image: url('https://cdn.dcloud.net.cn/img/shadow-blue.png');
}

.uni-page-head-shadow-green::after {
  background-image: url('https://cdn.dcloud.net.cn/img/shadow-green.png');
}

.uni-page-head-shadow-orange::after {
  background-image: url('https://cdn.dcloud.net.cn/img/shadow-orange.png');
}

.uni-page-head-shadow-red::after {
  background-image: url('https://cdn.dcloud.net.cn/img/shadow-red.png');
}

.uni-page-head-shadow-yellow::after {
  background-image: url('https://cdn.dcloud.net.cn/img/shadow-yellow.png');
}

uni-page-head[uni-page-head-type='default'] ~ uni-page-wrapper {
  height: calc(100% - 44px);
  height: calc(100% - 44px - constant(safe-area-inset-top));
  height: calc(100% - 44px - env(safe-area-inset-top));
}

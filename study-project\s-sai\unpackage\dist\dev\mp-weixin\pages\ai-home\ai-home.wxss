
/* 自定义状态栏 */
.custom-status-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 88rpx; /* 状态栏高度 */
  background: #2c3e50;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.status-content {
  display: flex;
  align-items: center;
  justify-content: center;
}
.app-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
}
.container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 20rpx;
  padding-top: 108rpx; /* 为自定义状态栏留出空间 */
  padding-bottom: 0; /* 移除底部padding，因为现在由main页面管理 */
  animation: fadeIn 0.8s ease-out;
}
@keyframes fadeIn {
from {
    opacity: 0;
    transform: translateY(30rpx);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}

/* 顶部用户信息 */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 20rpx;
  background: white;
  border-radius: 15rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.user-info {
  display: flex;
  align-items: center;
}
.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: #007AFF;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}
.avatar-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
}
.user-details {
  display: flex;
  flex-direction: column;
}
.welcome-text {
  font-size: 24rpx;
  color: #6c757d;
  margin-bottom: 8rpx;
}
.user-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}
.header-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.ai-status {
  display: flex;
  align-items: center;
}
.settings-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: 1rpx solid #e9ecef;
}
.settings-btn:active {
  transform: scale(0.9);
  background: #e9ecef;
}
.settings-icon {
  font-size: 28rpx;
}
.status-indicator {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #ff4757;
  margin-right: 12rpx;
  animation: pulse 2s infinite;
}
.status-indicator.active {
  background: #2ed573;
}
.status-text {
  font-size: 24rpx;
  color: #6c757d;
}
@keyframes pulse {
0%, 100% { opacity: 1;
}
50% { opacity: 0.5;
}
}

/* AI对话区域 */
.ai-chat-section {
  margin-bottom: 30rpx;
}
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}
.title-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}
.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}
.chat-card {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.chat-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}
.voice-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
  animation: bounce 2s infinite;
}
@keyframes bounce {
0%, 20%, 50%, 80%, 100% { transform: translateY(0);
}
40% { transform: translateY(-10rpx);
}
60% { transform: translateY(-5rpx);
}
}
.chat-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.chat-desc {
  font-size: 24rpx;
  color: #666;
}
.chat-arrow {
  font-size: 32rpx;
  color: #2c3e50;
  font-weight: bold;
}

/* 功能网格 */
.functions-section {
  margin-bottom: 30rpx;
}
.functions-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}
.function-item {
  background: white;
  border-radius: 12rpx;
  padding: 24rpx 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  animation: slideInUp 0.6s ease-out;
  animation-fill-mode: both;
}
.function-item:nth-child(1) { animation-delay: 0.1s;
}
.function-item:nth-child(2) { animation-delay: 0.2s;
}
.function-item:nth-child(3) { animation-delay: 0.3s;
}
.function-item:nth-child(4) { animation-delay: 0.4s;
}
.function-item:nth-child(5) { animation-delay: 0.5s;
}
.function-item:nth-child(6) { animation-delay: 0.6s;
}
.function-item:nth-child(7) { animation-delay: 0.7s;
}
.function-item:nth-child(8) { animation-delay: 0.8s;
}
.function-item:nth-child(9) { animation-delay: 0.9s;
}
@keyframes slideInUp {
from {
    opacity: 0;
    transform: translateY(30rpx);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
.function-item:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  border-color: rgba(255, 255, 255, 0.5);
}
.function-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  margin-bottom: 12rpx;
}
.function-icon.knowledge { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.function-icon.search { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}
.function-icon.writing { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}
.function-icon.translate { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}
.function-icon.emotion { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}
.function-icon.recommend { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}
.function-icon.reminder { background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
}
.function-icon.game { background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%);
}
.function-icon.health { background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);
}
.function-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 6rpx;
}
.function-desc {
  font-size: 20rpx;
  color: #6c757d;
  line-height: 1.3;
}

/* 监控区域 */
.monitor-section {
  margin-bottom: 30rpx;
}
.monitor-card {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.monitor-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}
.status-item {
  display: flex;
  flex-direction: column;
}
.status-label {
  font-size: 24rpx;
  color: #6c757d;
  margin-bottom: 8rpx;
}
.status-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #ff4757;
}
.status-value.active {
  color: #2ed573;
}
.monitor-toggle {
  display: flex;
  align-items: center;
}
.toggle-btn {
  width: 80rpx;
  height: 40rpx;
  border-radius: 20rpx;
  background: #dee2e6;
  position: relative;
  transition: all 0.3s ease;
}
.toggle-btn.active {
  background: #27ae60;
}
.toggle-slider {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: #fff;
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
.toggle-btn.active .toggle-slider {
  left: 44rpx;
}
.monitor-features {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}
.feature-tag {
  background: #2c3e50;
  color: #fff;
  font-size: 20rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

/* 快捷功能 */
.quick-actions {
  display: flex;
  justify-content: space-around;
  background: white;
  border-radius: 15rpx;
  padding: 30rpx 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
}
.action-icon {
  font-size: 40rpx;
  margin-bottom: 12rpx;
  padding: 16rpx;
  border-radius: 50%;
  background: #007AFF;
}
.action-text {
  font-size: 24rpx;
  color: #2c3e50;
  font-weight: 500;
}

/* 退出按钮 */
.logout-section {
  padding: 30rpx 20rpx;
  margin-bottom: 20rpx;
}
.logout-btn {
  width: 100%;
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 15rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 10rpx rgba(255, 71, 87, 0.3);
  transition: all 0.3s ease;
}
.logout-btn:active {
  transform: scale(0.98);
  background: #ff3742;
}
.logout-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}
.logout-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 底部提示 */
.bottom-tip {
  text-align: center;
  padding: 20rpx;
}
.tip-text {
  font-size: 24rpx;
  color: #6c757d;
}

/* 底部导航栏 */
.bottom-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: white;
  display: flex;
  align-items: center;
  justify-content: space-around;
  border-top: 1rpx solid #e5e5e5;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 拍照搜题按钮区域 */
.camera-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.camera-btn {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 15rpx rgba(255, 107, 107, 0.4);
  transform: translateY(-10rpx); /* 稍微向上突出 */
  transition: all 0.3s ease;
  margin-bottom: 8rpx;
}
.camera-btn:active {
  transform: translateY(-8rpx) scale(0.95);
  box-shadow: 0 2rpx 10rpx rgba(255, 107, 107, 0.6);
}
.camera-icon {
  font-size: 45rpx;
  color: white;
}
.camera-text {
  font-size: 20rpx;
  color: #666;
  font-weight: 500;
  text-align: center;
  transform: translateY(-10rpx); /* 与按钮保持一致的位置 */
}
.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 10rpx 0;
  transition: all 0.3s ease;
}
.nav-item.active .nav-icon {
  color: #007AFF;
  transform: scale(1.1);
}
.nav-item.active .nav-text {
  color: #007AFF;
  font-weight: 600;
}
.nav-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
  color: #666;
  transition: all 0.3s ease;
}
.nav-text {
  font-size: 20rpx;
  color: #666;
  transition: all 0.3s ease;
}

/* 为了给底部导航栏留出空间，调整容器底部padding */
.container {
  padding-bottom: 140rpx; /* 给底部导航栏留出空间 */
}

package cn.zhentao.controller;

import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONUtil;

import cn.zhentao.pojo.FactInfo;
import cn.zhentao.pojo.FactInfoLog;
import cn.zhentao.service.FactInfoLogService;
import cn.zhentao.service.MinioService;
import cn.zhentao.service.impl.FactInfoServiceImpl;
import cn.zhentao.utils.ArcFaceEngineUtil;
import cn.zhentao.utils.ImageQualityChecker;
import com.arcsoft.face.FaceFeature;
import com.arcsoft.face.FaceSimilar;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 人脸信息管理控制器
 *
 * 提供人脸识别相关的REST API接口，包括：
 * 1. 人脸注册 - 将用户人脸信息存储到数据库和MinIO
 * 2. 人脸比对 - 比较两张人脸图片的相似度
 * 3. 人脸识别登录 - 通过人脸识别进行用户身份验证
 * 4. 人脸信息管理 - 查询、删除人脸信息
 *
 * 使用虹软人脸识别SDK进行人脸特征提取和比对
 * 使用MinIO进行图片文件存储
 *
 * <AUTHOR>
 * @since 2023-04-16
 */
@Slf4j
@CrossOrigin(origins = "*")
@RestController
@RequestMapping("/fact-info")
public class FactInfoController {

    /**
     * 文件访问基础URL，从配置文件中读取
     */
    @Value("${file.url}")
    public String url;

    /**
     * 人脸信息服务实现类，用于数据库操作
     */
    @Resource
    FactInfoServiceImpl factInfoService;

    /**
     * 虹软人脸识别引擎工具类，用于人脸特征提取和比对
     */
    @Resource
    ArcFaceEngineUtil arcFaceEngineUtil;

    /**
     * 人脸信息日志服务，用于记录人脸识别操作日志
     */
    @Resource
    FactInfoLogService factInfoLogService;

    /**
     * MinIO文件存储服务，用于图片文件的上传和管理
     */
    @Resource
    MinioService minioService;

    /**
     * 图片质量检查工具，用于验证图片是否适合人脸识别
     */
    @Resource
    ImageQualityChecker imageQualityChecker;

    /**
     * 人脸比对接口
     *
     * 接收两张人脸图片，提取人脸特征并计算相似度
     * 主要用于验证两张照片是否为同一人
     *
     * @param face1 第一张人脸图片文件
     * @param face2 第二张人脸图片文件
     * @return ResponseEntity 包含比对结果的响应实体
     *         - faceInfo1: 第一张图片的人脸特征JSON字符串
     *         - faceInfo2: 第二张图片的人脸特征JSON字符串
     *         - faceSimilar: 人脸相似度对象，包含相似度分数
     */
    @PostMapping("/compareFaceInfo")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> compareFaceInfo(@RequestParam MultipartFile face1, @RequestParam MultipartFile face2) {
        HashMap<String, Object> map = new HashMap<>();
        File tempFile1 = null;
        File tempFile2 = null;

        try {
            log.info("开始人脸比对，文件1大小: {}KB, 文件2大小: {}KB",
                    face1.getSize() / 1024, face2.getSize() / 1024);

            // 创建临时文件保存第一张图片
            tempFile1 = File.createTempFile("face1_", ".jpg");
            try (FileOutputStream fileOutputStream1 = new FileOutputStream(tempFile1)) {
                fileOutputStream1.write(face1.getBytes());
            }

            // 提取第一张图片的人脸特征
            FaceFeature faceFeature1 = arcFaceEngineUtil.getFaceFeature(tempFile1);
            if (faceFeature1 == null) {
                map.put("code", 501);
                map.put("message", "第一张图片未检测到人脸信息");
                return new ResponseEntity<>(map, HttpStatus.OK);
            }
            String jsonStr1 = JSONUtil.toJsonStr(faceFeature1);

            // 创建临时文件保存第二张图片
            tempFile2 = File.createTempFile("face2_", ".jpg");
            try (FileOutputStream fileOutputStream2 = new FileOutputStream(tempFile2)) {
                fileOutputStream2.write(face2.getBytes());
            }

            // 提取第二张图片的人脸特征
            FaceFeature faceFeature2 = arcFaceEngineUtil.getFaceFeature(tempFile2);
            if (faceFeature2 == null) {
                map.put("code", 501);
                map.put("message", "第二张图片未检测到人脸信息");
                return new ResponseEntity<>(map, HttpStatus.OK);
            }
            String jsonStr2 = JSONUtil.toJsonStr(faceFeature2);

            // 比较两张人脸的相似度
            FaceSimilar faceSimilar = arcFaceEngineUtil.compareFaceFeature(faceFeature1, faceFeature2);

            // 构建返回结果
            map.put("code", 200);
            map.put("message", "人脸比对完成");
            map.put("faceInfo1", jsonStr1);
            map.put("faceInfo2", jsonStr2);
            map.put("faceSimilar", faceSimilar);
            map.put("similarity", faceSimilar.getScore());
            map.put("isSamePerson", faceSimilar.getScore() > 0.78 ? "是" : "否");

            log.info("人脸比对完成，相似度: {}", faceSimilar.getScore());
            return new ResponseEntity<>(map, HttpStatus.OK);

        } catch (IOException e) {
            log.error("人脸比对过程中发生IO异常", e);
            map.put("code", 500);
            map.put("message", "文件处理失败：" + e.getMessage());
            return new ResponseEntity<>(map, HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (Exception e) {
            log.error("人脸比对过程中发生未知异常", e);
            map.put("code", 500);
            map.put("message", "人脸比对失败：" + e.getMessage());
            return new ResponseEntity<>(map, HttpStatus.INTERNAL_SERVER_ERROR);
        } finally {
            // 清理临时文件
            if (tempFile1 != null && tempFile1.exists()) {
                tempFile1.delete();
            }
            if (tempFile2 != null && tempFile2.exists()) {
                tempFile2.delete();
            }
        }
    }

    /**
     * 人脸注册接口
     *
     * 接收用户上传的人脸图片，提取人脸特征并存储到数据库
     * 同时将图片文件上传到MinIO对象存储服务器
     *
     * @param files 用户上传的人脸图片文件数组（支持多张图片，但只处理第一张）
     * @param name 用户姓名
     * @param remark 备注信息
     * @return ResponseEntity 包含注册结果的响应实体
     *         - code: 状态码（200成功，500失败）
     *         - message: 操作结果消息
     *         - data: 注册成功的用户信息
     * @throws IOException 文件操作异常
     */
    @PostMapping("/save")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> saveFaceInfo(@RequestParam MultipartFile[] files, String name, String remark) throws IOException {
        HashMap<String, Object> map = new HashMap<>();
        File tempFile = null;

        try {
            // 参数验证
            if (files == null || files.length == 0) {
                map.put("code", 400);
                map.put("message", "请上传人脸图片");
                return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
            }

            if (name == null || name.trim().isEmpty()) {
                map.put("code", 400);
                map.put("message", "请输入用户姓名");
                return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
            }

            log.info("开始注册用户人脸信息，用户名: {}, 图片数量: {}", name, files.length);

            // 只处理第一张图片
            MultipartFile faceFile = files[0];

            // 创建临时文件用于人脸特征提取
            tempFile = File.createTempFile("register_face_", ".jpg");
            try (FileOutputStream fileOutputStream = new FileOutputStream(tempFile)) {
                fileOutputStream.write(faceFile.getBytes());
            }

            // 提取人脸特征
            FaceFeature faceFeature = arcFaceEngineUtil.getFaceFeature(tempFile);
            if (faceFeature == null) {
                map.put("code", 501);
                map.put("message", "未检测到人脸信息，请上传清晰的人脸照片");
                return new ResponseEntity<>(map, HttpStatus.OK);
            }

            // 将图片上传到MinIO
            String fileName = null;
            try {
                fileName = minioService.uploadFile(faceFile, null);
                log.info("图片上传到MinIO成功，文件名: {}", fileName);
            } catch (Exception e) {
                log.error("图片上传到MinIO失败", e);
                map.put("code", 500);
                map.put("message", "图片上传失败：" + e.getMessage());
                return new ResponseEntity<>(map, HttpStatus.INTERNAL_SERVER_ERROR);
            }

            // 构建用户人脸信息对象
            FactInfo factInfo = new FactInfo();
            factInfo.setName(name.trim());
            factInfo.setRemark(remark != null ? remark.trim() : "");

            // 将人脸特征转换为JSON字符串存储
            String jsonStr = JSONUtil.toJsonStr(faceFeature);
            factInfo.setFaceData(jsonStr);

            // 设置图片文件名（用于后续访问）
            factInfo.setImageUrl(fileName);

            // 保存到数据库
            HashMap<String, Object> saveResult = factInfoService.saveFaceInfo(factInfo);
            saveResult.put("data", factInfo);
            saveResult.put("imageUrl", url + fileName);

            log.info("用户人脸信息注册成功，用户名: {}, ID: {}", name, factInfo.getId());
            return new ResponseEntity<>(saveResult, HttpStatus.OK);

        } catch (Exception e) {
            log.error("人脸注册过程中发生异常，用户名: {}", name, e);
            map.put("code", 500);
            map.put("message", "注册失败：" + e.getMessage());
            return new ResponseEntity<>(map, HttpStatus.INTERNAL_SERVER_ERROR);
        } finally {
            // 清理临时文件
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    /**
     * 获取所有人脸信息接口
     *
     * 查询数据库中所有已注册的人脸信息
     * 返回用户列表，包含姓名、备注等基本信息
     *
     * @return ResponseEntity 包含所有人脸信息的响应实体
     *         - code: 状态码
     *         - message: 操作结果消息
     *         - data: 人脸信息列表
     */
    @PostMapping("/get")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getAllFaceInfo() {
        try {
            log.info("查询所有人脸信息");
            HashMap<String, Object> allFaceInfo = factInfoService.getAllFaceInfo();
            log.info("查询完成，共找到 {} 条记录",
                    allFaceInfo.get("data") != null ? ((List<?>) allFaceInfo.get("data")).size() : 0);
            return new ResponseEntity<>(allFaceInfo, HttpStatus.OK);
        } catch (Exception e) {
            log.error("查询人脸信息时发生异常", e);
            HashMap<String, Object> errorMap = new HashMap<>();
            errorMap.put("code", 500);
            errorMap.put("message", "查询失败：" + e.getMessage());
            return new ResponseEntity<>(errorMap, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 删除人脸信息接口
     *
     * 根据用户ID删除数据库中的人脸信息记录
     * 同时删除MinIO中对应的图片文件
     *
     * @param resout 请求体，包含要删除的用户ID
     * @return ResponseEntity 包含删除结果的响应实体
     *         - code: 状态码（200成功，500失败）
     *         - message: 操作结果消息
     */
    @PostMapping("/delete")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> deleteFaceInfo(@RequestBody Map<String, Object> resout) {
        HashMap<String, Object> map = new HashMap<>();

        try {
            // 参数验证
            if (resout.get("id") == null) {
                map.put("code", 400);
                map.put("message", "请提供要删除的用户ID");
                return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
            }

            String userId = resout.get("id").toString();
            log.info("开始删除人脸信息，用户ID: {}", userId);

            // 先查询用户信息，获取图片文件名
            FactInfo factInfo = factInfoService.getById(userId);
            if (factInfo != null && factInfo.getImageUrl() != null) {
                try {
                    // 删除MinIO中的图片文件
                    minioService.deleteFile(factInfo.getImageUrl());
                    log.info("MinIO中的图片文件删除成功: {}", factInfo.getImageUrl());
                } catch (Exception e) {
                    log.warn("删除MinIO中的图片文件失败: {}", factInfo.getImageUrl(), e);
                    // 继续执行数据库删除操作，不因为文件删除失败而中断
                }
            }

            // 删除数据库记录
            Map<String, Object> deleteResult = factInfoService.deleteFaceInfo(userId);
            log.info("用户人脸信息删除完成，用户ID: {}", userId);

            return new ResponseEntity<>(deleteResult, HttpStatus.OK);

        } catch (Exception e) {
            log.error("删除人脸信息时发生异常", e);
            map.put("code", 500);
            map.put("message", "删除失败：" + e.getMessage());
            return new ResponseEntity<>(map, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    /**
     * 人脸识别登录接口
     *
     * 接收前端传来的Base64编码的人脸图片，进行人脸识别验证
     * 与数据库中已注册的人脸信息进行比对，判断是否为合法用户
     * 同时记录登录日志和人脸分析信息（年龄、性别、活体检测等）
     *
     * @param resout 请求体，包含Base64编码的图片数据
     * @return ResponseEntity 包含识别结果的响应实体
     *         - code: 状态码（200登录成功，500非法用户，501无人脸信息）
     *         - message: 操作结果消息
     *         - data: 人脸分析信息（年龄、性别等）
     *         - userInfo: 匹配成功的用户信息
     * @throws IOException 文件操作异常
     */
    @PostMapping("/check")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> checkFaceInfo(@RequestBody Map<String, Object> resout) throws IOException {
        HashMap<String, Object> map = new HashMap<>();
        File tempFile = null;
        String uploadedFileName = null;

        try {
            // 参数验证
            if (resout.get("imageData") == null) {
                map.put("code", 400);
                map.put("message", "请提供图片数据");
                return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
            }

            log.info("开始人脸识别登录验证");

            // 解析Base64图片数据
            String imageDataStr = resout.get("imageData").toString();
            log.info("接收到的图片数据长度: {}", imageDataStr.length());

            String[] imageData = imageDataStr.split(",");
            if (imageData.length < 2) {
                map.put("code", 400);
                map.put("message", "图片数据格式错误，请确保是完整的Base64格式");
                return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
            }

            // 检查Base64头部信息
            String header = imageData[0];
            log.info("图片格式头部: {}", header);

            // Base64解码
            byte[] result;
            try {
                result = Base64.decode(imageData[1]);
                log.info("Base64解码成功，图片大小: {}KB", result.length / 1024);
            } catch (Exception e) {
                log.error("Base64解码失败", e);
                map.put("code", 400);
                map.put("message", "Base64解码失败，请检查图片数据格式");
                return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
            }

            // 调整字节数据
            for (int i = 0; i < result.length; ++i) {
                if (result[i] < 0) {
                    // 调整异常数据
                    result[i] += 256;
                }
            }

            // 检查图片质量
            ImageQualityChecker.ImageQualityResult qualityResult = imageQualityChecker.checkImageQuality(result);
            log.info("图片质量检查结果: {}", qualityResult);

            if (!qualityResult.isValid()) {
                map.put("code", 400);
                map.put("message", "图片质量不符合要求: " + String.join(", ", qualityResult.getErrors()));
                map.put("qualityCheck", qualityResult);
                return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
            }

            // 如果有警告，记录但不阻止处理
            if (!qualityResult.getWarnings().isEmpty()) {
                log.warn("图片质量警告: {}", qualityResult.getWarnings());
            }

            // 生成唯一的图片ID
            String imageId = UUID.randomUUID().toString();

            // 将图片上传到MinIO
            try {
                uploadedFileName = minioService.uploadFile(result, imageId + ".jpg", "image/jpeg");
                log.info("登录图片上传到MinIO成功，文件名: {}", uploadedFileName);
            } catch (Exception e) {
                log.error("登录图片上传到MinIO失败", e);
                map.put("code", 500);
                map.put("message", "图片处理失败");
                return new ResponseEntity<>(map, HttpStatus.INTERNAL_SERVER_ERROR);
            }

            // 创建临时文件用于人脸识别
            tempFile = File.createTempFile("login_face_", ".jpg");
            try (FileOutputStream fileOutputStream = new FileOutputStream(tempFile)) {
                fileOutputStream.write(result);
            }

            // 提取人脸特征
            FaceFeature faceFeature = arcFaceEngineUtil.getFaceFeature(tempFile);

            // 创建登录日志记录
            FactInfoLog factInfoLog = new FactInfoLog();
            factInfoLog.setImageUrl(url + uploadedFileName);
            factInfoLog.setCreateTime(LocalDateTime.now());

            // 检查是否检测到人脸
            if (faceFeature == null) {
                map.put("code", 501);
                map.put("message", "未检测到人脸信息，请确保人脸清晰可见");
                map.put("data", null);

                // 记录失败日志
                factInfoLog.setName("未知用户");
                factInfoLog.setLivingBody("无人脸");
                factInfoLogService.save(factInfoLog);

                return new ResponseEntity<>(map, HttpStatus.OK);
            }

            // 获取人脸详细信息（年龄、性别、活体检测等）
            HashMap<String, Object> userInfo = arcFaceEngineUtil.getUserInfo(tempFile);
            factInfoLog.setFace3D(userInfo.get("3DAngle").toString());
            factInfoLog.setAge(userInfo.get("age").toString());
            factInfoLog.setSex("0".equals(userInfo.get("gender").toString()) ? "男" : "女");
            factInfoLog.setLivingBody("1".equals(userInfo.get("liveness").toString()) ? "正常" : "图片打卡嫌疑");

            // 与数据库中所有注册用户进行人脸比对
            List<FactInfo> registeredUsers = factInfoService.list();
            System.out.println(registeredUsers);
            log.info("开始与 {} 个注册用户进行人脸比对", registeredUsers.size());

            for (FactInfo registeredUser : registeredUsers) {
                // 将存储的人脸特征JSON转换为FaceFeature对象
                FaceFeature registeredFaceFeature = JSONUtil.toBean(registeredUser.getFaceData(), FaceFeature.class);

                // 计算人脸相似度
                FaceSimilar faceSimilar = arcFaceEngineUtil.compareFaceFeature(faceFeature, registeredFaceFeature);
                double similarity = faceSimilar.getScore();

                log.debug("与用户 {} 的相似度: {}", registeredUser.getName(), similarity);

                // 相似度阈值判断（0.78为推荐阈值）
                if (similarity > 0.78) {
                    // 登录成功
                    map.put("code", 200);
                    map.put("data", userInfo);
                    map.put("message", "恭喜 " + registeredUser.getName() + " 登录成功！");
                    map.put("userInfo", registeredUser);
                    map.put("similarity", similarity);

                    // 记录成功登录日志
                    factInfoLog.setName(registeredUser.getName());
                    factInfoLog.setUserId(String.valueOf(registeredUser.getId()));
                    factInfoLogService.save(factInfoLog);

                    log.info("用户 {} 人脸识别登录成功，相似度: {}", registeredUser.getName(), similarity);
                    return new ResponseEntity<>(map, HttpStatus.OK);
                }
            }

            // 未找到匹配的用户
            map.put("code", 500);
            map.put("message", "人脸识别失败，未找到匹配的用户信息");
            map.put("data", userInfo);

            // 记录失败登录日志
            factInfoLog.setName("未知用户");
            factInfoLogService.save(factInfoLog);

            log.info("人脸识别登录失败，未找到匹配用户");
            return new ResponseEntity<>(map, HttpStatus.OK);

        } catch (Exception e) {
            log.error("人脸识别登录过程中发生异常", e);
            map.put("code", 500);
            map.put("message", "登录验证失败：" + e.getMessage());
            return new ResponseEntity<>(map, HttpStatus.INTERNAL_SERVER_ERROR);
        } finally {
            // 清理临时文件
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }
}

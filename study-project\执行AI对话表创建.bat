@echo off
echo ====================================
echo AI对话功能数据库表创建脚本
echo ====================================
echo.

echo 正在连接数据库并执行SQL脚本...
echo 数据库地址: 118.31.228.119:3306
echo 数据库名称: tutor
echo.

REM 检查是否存在MySQL客户端
where mysql >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到MySQL客户端工具
    echo 请确保MySQL客户端已安装并添加到系统PATH中
    echo.
    echo 手动执行方法:
    echo 1. 打开MySQL客户端或数据库管理工具
    echo 2. 连接到数据库: 118.31.228.119:3306/tutor
    echo 3. 执行文件: ai_conversation_tables.sql
    echo.
    pause
    exit /b 1
)

REM 执行SQL脚本
mysql -h 118.31.228.119 -P 3306 -u root -p tutor < ai_conversation_tables.sql

if %errorlevel% equ 0 (
    echo.
    echo ====================================
    echo 表创建成功！
    echo ====================================
    echo.
    echo 已创建以下表:
    echo - ai_conversation        (AI对话会话表)
    echo - ai_message            (AI消息记录表)
    echo - ai_conversation_config (AI对话配置表)
    echo - ai_usage_statistics   (AI使用统计表)
    echo - ai_feedback           (AI反馈表)
    echo.
    echo 请查看 "AI对话功能数据库设计说明.md" 了解详细设计
) else (
    echo.
    echo ====================================
    echo 执行失败！
    echo ====================================
    echo.
    echo 可能的原因:
    echo 1. 数据库连接失败
    echo 2. 权限不足
    echo 3. SQL语法错误
    echo.
    echo 请检查网络连接和数据库权限
)

echo.
pause

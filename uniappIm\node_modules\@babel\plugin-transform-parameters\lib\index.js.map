{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_params", "_rest", "_default", "exports", "default", "declare", "api", "options", "_api$assumption", "_api$assumption2", "assertVersion", "ignoreFunctionLength", "assumption", "loose", "noNewArrows", "name", "visitor", "Function", "path", "isArrowFunctionExpression", "get", "some", "param", "isRestElement", "isAssignmentPattern", "arrowFunctionToExpression", "allowInsertArrowWithRest", "isFunctionExpression", "convertedRest", "convertFunctionRest", "convertedParams", "convertFunctionParams", "scope", "crawl"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport convertFunctionParams from \"./params.ts\";\nimport convertFunctionRest from \"./rest.ts\";\nexport { convertFunctionParams };\n\nexport interface Options {\n  loose?: boolean;\n}\n\nexport default declare((api, options: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const ignoreFunctionLength =\n    api.assumption(\"ignoreFunctionLength\") ?? options.loose;\n  // Todo(BABEL 8): Consider default it to false\n  const noNewArrows = api.assumption(\"noNewArrows\") ?? true;\n\n  return {\n    name: \"transform-parameters\",\n\n    visitor: {\n      Function(path) {\n        if (\n          path.isArrowFunctionExpression() &&\n          path\n            .get(\"params\")\n            .some(param => param.isRestElement() || param.isAssignmentPattern())\n        ) {\n          // default/rest visitors require access to `arguments`, so it cannot be an arrow\n          path.arrowFunctionToExpression({\n            allowInsertArrowWithRest: false,\n            noNewArrows,\n          });\n\n          // In some cases arrowFunctionToExpression replaces the function with a wrapper.\n          // Return early; the wrapped function will be visited later in the AST traversal.\n          if (!path.isFunctionExpression()) return;\n        }\n\n        const convertedRest = convertFunctionRest(path);\n        const convertedParams = convertFunctionParams(\n          path,\n          ignoreFunctionLength,\n        );\n\n        if (convertedRest || convertedParams) {\n          // Manually reprocess this scope to ensure that the moved params are updated.\n          path.scope.crawl();\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAA4C,IAAAG,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAO7B,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAEC,OAAgB,KAAK;EAAA,IAAAC,eAAA,EAAAC,gBAAA;EAChDH,GAAG,CAACI,aAAa,CAAkB,CAAE,CAAC;EAEtC,MAAMC,oBAAoB,IAAAH,eAAA,GACxBF,GAAG,CAACM,UAAU,CAAC,sBAAsB,CAAC,YAAAJ,eAAA,GAAID,OAAO,CAACM,KAAK;EAEzD,MAAMC,WAAW,IAAAL,gBAAA,GAAGH,GAAG,CAACM,UAAU,CAAC,aAAa,CAAC,YAAAH,gBAAA,GAAI,IAAI;EAEzD,OAAO;IACLM,IAAI,EAAE,sBAAsB;IAE5BC,OAAO,EAAE;MACPC,QAAQA,CAACC,IAAI,EAAE;QACb,IACEA,IAAI,CAACC,yBAAyB,CAAC,CAAC,IAChCD,IAAI,CACDE,GAAG,CAAC,QAAQ,CAAC,CACbC,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,aAAa,CAAC,CAAC,IAAID,KAAK,CAACE,mBAAmB,CAAC,CAAC,CAAC,EACtE;UAEAN,IAAI,CAACO,yBAAyB,CAAC;YAC7BC,wBAAwB,EAAE,KAAK;YAC/BZ;UACF,CAAC,CAAC;UAIF,IAAI,CAACI,IAAI,CAACS,oBAAoB,CAAC,CAAC,EAAE;QACpC;QAEA,MAAMC,aAAa,GAAG,IAAAC,aAAmB,EAACX,IAAI,CAAC;QAC/C,MAAMY,eAAe,GAAG,IAAAC,eAAqB,EAC3Cb,IAAI,EACJP,oBACF,CAAC;QAED,IAAIiB,aAAa,IAAIE,eAAe,EAAE;UAEpCZ,IAAI,CAACc,KAAK,CAACC,KAAK,CAAC,CAAC;QACpB;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}
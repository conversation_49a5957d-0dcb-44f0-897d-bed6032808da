
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  position: relative;
}
.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}
.header {
  text-align: center;
  margin-bottom: 60rpx;
  padding-top: 60rpx;
  position: relative;
  z-index: 1;
}
.logo {
  font-size: 100rpx;
  margin-bottom: 30rpx;
  animation: float 3s ease-in-out infinite;
}
@keyframes float {
0%, 100% { transform: translateY(0px);
}
50% { transform: translateY(-10px);
}
}
.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20rpx;
  letter-spacing: 2rpx;
}
.subtitle {
  font-size: 28rpx;
  color: #6c757d;
  line-height: 1.5;
}
.card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #e9ecef;
  margin-bottom: 40rpx;
  position: relative;
  z-index: 1;
}
.camera-section {
  margin-bottom: 40rpx;
}
.camera-area {
  border: 2rpx dashed #dee2e6;
  border-radius: 12rpx;
  padding: 60rpx 20rpx;
  text-align: center;
  background: #f8f9fa;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
.camera-area::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(118, 75, 162, 0.1), transparent);
  transition: left 0.5s ease;
}
.camera-area:active::before {
  left: 100%;
}
.camera-area:active {
  border-color: #2c3e50;
  background: #e9ecef;
  transform: scale(0.98);
}
.camera-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
}
.camera-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  animation: pulse 2s ease-in-out infinite;
}
@keyframes pulse {
0%, 100% { transform: scale(1);
}
50% { transform: scale(1.05);
}
}
.camera-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 12rpx;
}
.camera-hint {
  font-size: 24rpx;
  color: #999;
}
.captured-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 20rpx;
  border: 4rpx solid #e1e5e9;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  animation: fadeIn 0.5s ease;
}
@keyframes fadeIn {
from { opacity: 0; transform: scale(0.8);
}
to { opacity: 1; transform: scale(1);
}
}
.loading-section {
  margin-bottom: 40rpx;
}
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}
.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #e9ecef;
  border-top: 6rpx solid #2c3e50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 20rpx;
}
@keyframes spin {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.loading-text {
  font-size: 32rpx;
  color: #2c3e50;
  font-weight: 500;
}
.result-section {
  margin-bottom: 20rpx;
}
.result-message {
  padding: 24rpx 32rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  background: #2c3e50;
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(44, 62, 80, 0.2);
  line-height: 1.5;
  animation: slideIn 0.5s ease;
}
@keyframes slideIn {
from { opacity: 0; transform: translateY(20rpx);
}
to { opacity: 1; transform: translateY(0);
}
}
.footer {
  text-align: center;
  margin-top: auto;
  padding-bottom: 40rpx;
  position: relative;
  z-index: 1;
}
.footer-text {
  font-size: 28rpx;
  color: #6c757d;
}
.register-link {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 600;
  margin-left: 12rpx;
  text-decoration: underline;
  transition: all 0.3s ease;
}
.register-link:active {
  transform: scale(0.95);
}

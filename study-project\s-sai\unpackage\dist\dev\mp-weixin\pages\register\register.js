"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      registerImage: "",
      registerName: "",
      registerRemark: "",
      registerLoading: false,
      registerResult: ""
    };
  },
  computed: {
    canSubmit() {
      return this.registerImage && this.registerName.trim();
    }
  },
  methods: {
    chooseImage() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          this.registerImage = res.tempFilePaths[0];
          this.registerResult = "";
        }
      });
    },
    submitRegister() {
      if (!this.canSubmit) {
        this.registerResult = "请上传人脸图片并填写姓名";
        return;
      }
      this.registerLoading = true;
      this.registerResult = "";
      common_vendor.index.uploadFile({
        url: "http://localhost:8081/fact-info/save",
        filePath: this.registerImage,
        name: "files",
        formData: {
          name: this.registerName.trim(),
          remark: this.registerRemark.trim()
        },
        success: (res) => {
          let data = {};
          try {
            data = JSON.parse(res.data);
          } catch (e) {
            this.registerResult = "注册失败，返回数据异常";
            return;
          }
          if (data.code === 200) {
            this.registerResult = "✅ 注册成功！欢迎 " + data.data.name;
            setTimeout(() => {
              this.registerImage = "";
              this.registerName = "";
              this.registerRemark = "";
            }, 2e3);
          } else {
            this.registerResult = "❌ 注册失败：" + data.message;
          }
        },
        fail: (err) => {
          this.registerResult = "❌ 注册失败：" + err.errMsg;
        },
        complete: () => {
          this.registerLoading = false;
        }
      });
    },
    goToLogin() {
      common_vendor.index.navigateTo({
        url: "/pages/login/login"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.registerImage
  }, !$data.registerImage ? {} : {
    b: $data.registerImage
  }, {
    c: common_vendor.o((...args) => $options.chooseImage && $options.chooseImage(...args)),
    d: $data.registerName,
    e: common_vendor.o(($event) => $data.registerName = $event.detail.value),
    f: $data.registerRemark,
    g: common_vendor.o(($event) => $data.registerRemark = $event.detail.value),
    h: !$data.registerLoading
  }, !$data.registerLoading ? {
    i: common_vendor.t($data.registerLoading ? "注册中..." : "立即注册")
  } : {}, {
    j: common_vendor.o((...args) => $options.submitRegister && $options.submitRegister(...args)),
    k: $data.registerLoading,
    l: !$options.canSubmit,
    m: $data.registerResult
  }, $data.registerResult ? {
    n: common_vendor.t($data.registerResult)
  } : {}, {
    o: common_vendor.o((...args) => $options.goToLogin && $options.goToLogin(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/register/register.js.map

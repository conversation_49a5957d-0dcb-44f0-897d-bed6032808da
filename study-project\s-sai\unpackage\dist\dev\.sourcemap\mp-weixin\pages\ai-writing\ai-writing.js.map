{"version": 3, "file": "ai-writing.js", "sources": ["pages/ai-writing/ai-writing.vue", "../../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYWktd3JpdGluZy9haS13cml0aW5nLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <!-- 顶部标题 -->\r\n    <view class=\"header\">\r\n      <view class=\"header-icon\">✍️</view>\r\n      <view class=\"header-info\">\r\n        <text class=\"header-title\">文本生成</text>\r\n        <text class=\"header-desc\">作文·故事·诗歌创作</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 创作类型选择 -->\r\n    <view class=\"type-selector\">\r\n      <view \r\n        v-for=\"(type, index) in writingTypes\" \r\n        :key=\"index\"\r\n        class=\"type-item\"\r\n        :class=\"{ active: activeType === type.key }\"\r\n        @tap=\"switchType(type.key)\"\r\n      >\r\n        <text class=\"type-icon\">{{ type.icon }}</text>\r\n        <text class=\"type-text\">{{ type.name }}</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 创作区域 -->\r\n    <view class=\"writing-area\">\r\n      <!-- 输入提示 -->\r\n      <view class=\"prompt-section\">\r\n        <text class=\"prompt-title\">{{ currentType.icon }} {{ currentType.name }}创作</text>\r\n        <textarea \r\n          class=\"prompt-input\" \r\n          v-model=\"promptText\" \r\n          :placeholder=\"currentType.placeholder\"\r\n          :maxlength=\"500\"\r\n          show-confirm-bar=\"false\"\r\n        />\r\n        <view class=\"char-count\">{{ promptText.length }}/500</view>\r\n      </view>\r\n\r\n      <!-- 生成按钮 -->\r\n      <view class=\"generate-section\">\r\n        <button \r\n          class=\"generate-btn\" \r\n          :class=\"{ disabled: !promptText.trim() || isGenerating }\" \r\n          @tap=\"generateText\"\r\n        >\r\n          <text class=\"btn-icon\">{{ isGenerating ? '⏳' : '✨' }}</text>\r\n          <text class=\"btn-text\">{{ isGenerating ? '创作中...' : '开始创作' }}</text>\r\n        </button>\r\n      </view>\r\n\r\n      <!-- 生成结果 -->\r\n      <view v-if=\"generatedText || isGenerating\" class=\"result-section\">\r\n        <view class=\"result-header\">\r\n          <text class=\"result-title\">{{ currentType.icon }} 创作结果</text>\r\n          <view v-if=\"generatedText && !isGenerating\" class=\"result-actions\">\r\n            <button class=\"action-btn copy\" @tap=\"copyText\">📋 复制</button>\r\n            <button class=\"action-btn regenerate\" @tap=\"regenerateText\">🔄 重新生成</button>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 生成中的动画 -->\r\n        <view v-if=\"isGenerating\" class=\"generating-animation\">\r\n          <view class=\"typing-indicator\">\r\n            <view class=\"dot\"></view>\r\n            <view class=\"dot\"></view>\r\n            <view class=\"dot\"></view>\r\n          </view>\r\n          <text class=\"generating-text\">AI正在为您创作...</text>\r\n        </view>\r\n        \r\n        <!-- 生成的文本 -->\r\n        <view v-if=\"generatedText\" class=\"result-content\">\r\n          <text class=\"result-text\">{{ generatedText }}</text>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 创作示例 -->\r\n      <view v-if=\"!generatedText && !isGenerating\" class=\"examples-section\">\r\n        <text class=\"examples-title\">💡 {{ currentType.name }}示例</text>\r\n        <view class=\"examples-list\">\r\n          <view \r\n            v-for=\"(example, index) in currentType.examples\" \r\n            :key=\"index\" \r\n            class=\"example-item\" \r\n            @tap=\"useExample(example)\"\r\n          >\r\n            <text class=\"example-text\">{{ example }}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      promptText: '',\r\n      generatedText: '',\r\n      isGenerating: false,\r\n      userId: 123,\r\n      activeType: 'essay',\r\n      writingTypes: [\r\n        {\r\n          key: 'essay',\r\n          name: '作文',\r\n          icon: '📝',\r\n          placeholder: '请输入作文题目或要求，例如：写一篇关于春天的作文...',\r\n          examples: [\r\n            '写一篇关于春天的作文',\r\n            '我的理想职业',\r\n            '难忘的一件事',\r\n            '保护环境的重要性'\r\n          ]\r\n        },\r\n        {\r\n          key: 'story',\r\n          name: '故事',\r\n          icon: '📚',\r\n          placeholder: '请输入故事主题或情节，例如：写一个关于友谊的故事...',\r\n          examples: [\r\n            '写一个关于友谊的故事',\r\n            '小动物的冒险故事',\r\n            '未来世界的科幻故事',\r\n            '童话故事：勇敢的小公主'\r\n          ]\r\n        },\r\n        {\r\n          key: 'poem',\r\n          name: '诗歌',\r\n          icon: '🎭',\r\n          placeholder: '请输入诗歌主题或风格，例如：写一首关于月亮的诗...',\r\n          examples: [\r\n            '写一首关于月亮的诗',\r\n            '赞美老师的诗歌',\r\n            '描写秋天的现代诗',\r\n            '关于母爱的诗歌'\r\n          ]\r\n        },\r\n        {\r\n          key: 'general',\r\n          name: '其他',\r\n          icon: '📄',\r\n          placeholder: '请输入您想要创作的文本类型和要求...',\r\n          examples: [\r\n            '写一段产品介绍',\r\n            '编写一份活动策划',\r\n            '写一封感谢信',\r\n            '创作一段演讲稿'\r\n          ]\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    currentType() {\r\n      return this.writingTypes.find(type => type.key === this.activeType) || this.writingTypes[0];\r\n    }\r\n  },\r\n  onLoad() {\r\n    this.loadUserInfo();\r\n  },\r\n  methods: {\r\n    loadUserInfo() {\r\n      const userInfo = uni.getStorageSync('userInfo');\r\n      if (userInfo && userInfo.id) {\r\n        this.userId = userInfo.id;\r\n      }\r\n    },\r\n\r\n    switchType(typeKey) {\r\n      this.activeType = typeKey;\r\n      this.promptText = '';\r\n      this.generatedText = '';\r\n    },\r\n\r\n    useExample(example) {\r\n      this.promptText = example;\r\n    },\r\n\r\n    async generateText() {\r\n      if (!this.promptText.trim() || this.isGenerating) return;\r\n\r\n      this.isGenerating = true;\r\n      this.generatedText = '';\r\n\r\n      try {\r\n        // 调用AI文本生成API\r\n        const response = await this.callWritingAPI(this.promptText.trim(), this.activeType);\r\n        \r\n        if (response && response.success) {\r\n          this.generatedText = response.response || '抱歉，生成失败，请重试。';\r\n        } else {\r\n          this.generatedText = '抱歉，创作服务暂时不可用，请稍后再试。';\r\n        }\r\n      } catch (error) {\r\n        this.generatedText = '网络连接失败，请检查网络后重试。';\r\n        console.error('API调用失败:', error);\r\n      }\r\n\r\n      this.isGenerating = false;\r\n    },\r\n\r\n    async regenerateText() {\r\n      if (!this.promptText.trim()) return;\r\n      this.generateText();\r\n    },\r\n\r\n    async callWritingAPI(prompt, type) {\r\n      const apiUrl = 'http://localhost:8082/api/ai/miniprogram/text/generate';\r\n      \r\n      const response = await uni.request({\r\n        url: apiUrl,\r\n        method: 'POST',\r\n        header: {\r\n          'Content-Type': 'application/json'\r\n        },\r\n        data: {\r\n          userId: this.userId,\r\n          prompt: prompt,\r\n          type: type\r\n        }\r\n      });\r\n\r\n      if (response.statusCode === 200 && response.data.code === 200) {\r\n        return response.data.data;\r\n      } else {\r\n        throw new Error('API调用失败');\r\n      }\r\n    },\r\n\r\n    copyText() {\r\n      if (!this.generatedText) return;\r\n      \r\n      uni.setClipboardData({\r\n        data: this.generatedText,\r\n        success: () => {\r\n          uni.showToast({\r\n            title: '复制成功',\r\n            icon: 'success'\r\n          });\r\n        },\r\n        fail: () => {\r\n          uni.showToast({\r\n            title: '复制失败',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.container {\r\n  min-height: 100vh;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.header {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 30rpx;\r\n  background: #fff;\r\n  border-bottom: 1rpx solid #e9ecef;\r\n}\r\n\r\n.header-icon {\r\n  font-size: 48rpx;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.header-info {\r\n  flex: 1;\r\n}\r\n\r\n.header-title {\r\n  font-size: 36rpx;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  display: block;\r\n}\r\n\r\n.header-desc {\r\n  font-size: 24rpx;\r\n  color: #6c757d;\r\n  margin-top: 8rpx;\r\n  display: block;\r\n}\r\n\r\n.type-selector {\r\n  display: flex;\r\n  background: #fff;\r\n  border-bottom: 1rpx solid #e9ecef;\r\n  padding: 0 20rpx;\r\n}\r\n\r\n.type-item {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 20rpx 10rpx;\r\n  transition: all 0.3s ease;\r\n  border-bottom: 4rpx solid transparent;\r\n}\r\n\r\n.type-item.active {\r\n  border-bottom-color: #007bff;\r\n}\r\n\r\n.type-icon {\r\n  font-size: 32rpx;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.type-text {\r\n  font-size: 24rpx;\r\n  color: #6c757d;\r\n}\r\n\r\n.type-item.active .type-text {\r\n  color: #007bff;\r\n  font-weight: bold;\r\n}\r\n\r\n.writing-area {\r\n  padding: 30rpx;\r\n}\r\n\r\n.prompt-section {\r\n  background: #fff;\r\n  border-radius: 16rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 30rpx;\r\n  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);\r\n}\r\n\r\n.prompt-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  margin-bottom: 20rpx;\r\n  display: block;\r\n}\r\n\r\n.prompt-input {\r\n  width: 100%;\r\n  min-height: 200rpx;\r\n  padding: 20rpx;\r\n  border: 1rpx solid #e9ecef;\r\n  border-radius: 12rpx;\r\n  font-size: 28rpx;\r\n  line-height: 1.5;\r\n  background: #f8f9fa;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.char-count {\r\n  text-align: right;\r\n  font-size: 24rpx;\r\n  color: #6c757d;\r\n  margin-top: 10rpx;\r\n}\r\n\r\n.generate-section {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.generate-btn {\r\n  width: 100%;\r\n  height: 100rpx;\r\n  background: #007bff;\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 50rpx;\r\n  font-size: 32rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 16rpx;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.generate-btn.disabled {\r\n  background: #ccc;\r\n  color: #999;\r\n}\r\n\r\n.generate-btn:not(.disabled):active {\r\n  transform: scale(0.98);\r\n  background: #0056b3;\r\n}\r\n\r\n.btn-icon {\r\n  font-size: 36rpx;\r\n}\r\n\r\n.btn-text {\r\n  font-weight: bold;\r\n}\r\n\r\n.result-section {\r\n  background: #fff;\r\n  border-radius: 16rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 30rpx;\r\n  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.result-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n}\r\n\r\n.result-actions {\r\n  display: flex;\r\n  gap: 16rpx;\r\n}\r\n\r\n.action-btn {\r\n  padding: 12rpx 24rpx;\r\n  border-radius: 20rpx;\r\n  font-size: 24rpx;\r\n  border: none;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-btn.copy {\r\n  background: #28a745;\r\n  color: #fff;\r\n}\r\n\r\n.action-btn.regenerate {\r\n  background: #ffc107;\r\n  color: #333;\r\n}\r\n\r\n.action-btn:active {\r\n  transform: scale(0.95);\r\n}\r\n\r\n.generating-animation {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 60rpx 0;\r\n}\r\n\r\n.typing-indicator {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.dot {\r\n  width: 16rpx;\r\n  height: 16rpx;\r\n  border-radius: 50%;\r\n  background: #007bff;\r\n  animation: typing 1.4s infinite;\r\n}\r\n\r\n.dot:nth-child(2) { animation-delay: 0.2s; }\r\n.dot:nth-child(3) { animation-delay: 0.4s; }\r\n\r\n@keyframes typing {\r\n  0%, 60%, 100% { opacity: 0.3; transform: scale(1); }\r\n  30% { opacity: 1; transform: scale(1.2); }\r\n}\r\n\r\n.generating-text {\r\n  font-size: 28rpx;\r\n  color: #6c757d;\r\n}\r\n\r\n.result-content {\r\n  border: 1rpx solid #e9ecef;\r\n  border-radius: 12rpx;\r\n  padding: 30rpx;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.result-text {\r\n  font-size: 28rpx;\r\n  line-height: 1.8;\r\n  color: #333;\r\n  white-space: pre-wrap;\r\n}\r\n\r\n.examples-section {\r\n  background: #fff;\r\n  border-radius: 16rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);\r\n}\r\n\r\n.examples-title {\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  margin-bottom: 20rpx;\r\n  display: block;\r\n}\r\n\r\n.examples-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16rpx;\r\n}\r\n\r\n.example-item {\r\n  padding: 20rpx;\r\n  background: #f8f9fa;\r\n  border-radius: 12rpx;\r\n  border: 1rpx solid #e9ecef;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.example-item:active {\r\n  background: #e9ecef;\r\n  transform: scale(0.98);\r\n}\r\n\r\n.example-text {\r\n  font-size: 26rpx;\r\n  color: #495057;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/study/studyuProject/study-project/s-sai/pages/ai-writing/ai-writing.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAiGA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,cAAc;AAAA,QACZ;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACD;AAAA,QACD;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACD;AAAA,QACD;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACD;AAAA,QACD;AAAA,UACE,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,cAAc;AACZ,aAAO,KAAK,aAAa,KAAK,UAAQ,KAAK,QAAQ,KAAK,UAAU,KAAK,KAAK,aAAa,CAAC;AAAA,IAC5F;AAAA,EACD;AAAA,EACD,SAAS;AACP,SAAK,aAAY;AAAA,EAClB;AAAA,EACD,SAAS;AAAA,IACP,eAAe;AACb,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9C,UAAI,YAAY,SAAS,IAAI;AAC3B,aAAK,SAAS,SAAS;AAAA,MACzB;AAAA,IACD;AAAA,IAED,WAAW,SAAS;AAClB,WAAK,aAAa;AAClB,WAAK,aAAa;AAClB,WAAK,gBAAgB;AAAA,IACtB;AAAA,IAED,WAAW,SAAS;AAClB,WAAK,aAAa;AAAA,IACnB;AAAA,IAED,MAAM,eAAe;AACnB,UAAI,CAAC,KAAK,WAAW,KAAI,KAAM,KAAK;AAAc;AAElD,WAAK,eAAe;AACpB,WAAK,gBAAgB;AAErB,UAAI;AAEF,cAAM,WAAW,MAAM,KAAK,eAAe,KAAK,WAAW,KAAM,GAAE,KAAK,UAAU;AAElF,YAAI,YAAY,SAAS,SAAS;AAChC,eAAK,gBAAgB,SAAS,YAAY;AAAA,eACrC;AACL,eAAK,gBAAgB;AAAA,QACvB;AAAA,MACA,SAAO,OAAO;AACd,aAAK,gBAAgB;AACrBA,sBAAc,MAAA,MAAA,SAAA,0CAAA,YAAY,KAAK;AAAA,MACjC;AAEA,WAAK,eAAe;AAAA,IACrB;AAAA,IAED,MAAM,iBAAiB;AACrB,UAAI,CAAC,KAAK,WAAW,KAAM;AAAE;AAC7B,WAAK,aAAY;AAAA,IAClB;AAAA,IAED,MAAM,eAAe,QAAQ,MAAM;AACjC,YAAM,SAAS;AAEf,YAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,QACjC,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,gBAAgB;AAAA,QACjB;AAAA,QACD,MAAM;AAAA,UACJ,QAAQ,KAAK;AAAA,UACb;AAAA,UACA;AAAA,QACF;AAAA,MACF,CAAC;AAED,UAAI,SAAS,eAAe,OAAO,SAAS,KAAK,SAAS,KAAK;AAC7D,eAAO,SAAS,KAAK;AAAA,aAChB;AACL,cAAM,IAAI,MAAM,SAAS;AAAA,MAC3B;AAAA,IACD;AAAA,IAED,WAAW;AACT,UAAI,CAAC,KAAK;AAAe;AAEzBA,oBAAAA,MAAI,iBAAiB;AAAA,QACnB,MAAM,KAAK;AAAA,QACX,SAAS,MAAM;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACF;AAAA,QACD,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7PA,GAAG,WAAW,eAAe;"}
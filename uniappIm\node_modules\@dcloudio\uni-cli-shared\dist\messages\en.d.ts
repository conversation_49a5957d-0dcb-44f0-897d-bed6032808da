declare const _default: {
    readonly 'app.compiler.version': "Compiler version: {version}";
    readonly compiling: "Compiling...";
    readonly 'dev.performance': "Please note that in running mode, due to log output, sourcemap, and uncompressed source code, the performance and package size are not as good as release mode.";
    readonly 'dev.performance.nvue': "Especially the sourcemap of app-nvue has a greater impact";
    readonly 'dev.performance.mp': "To officially release, please click the release menu or use the cli release command to release";
    readonly 'build.done': "DONE  Build complete.";
    readonly 'dev.watching.start': "Compiling...";
    readonly 'dev.watching.end': "DONE  Build complete. Watching for changes...";
    readonly 'dev.watching.end.pages': "DONE  Build complete. PAGES:{pages}";
    readonly 'dev.watching.end.files': "DONE  Build complete. FILES:{files}";
    readonly 'compiler.build.failed': "Build failed with errors.";
    readonly 'stat.warn.appid': "The current application is not configured with Appid, and uni statistics cannot be used. For details, see https://ask.dcloud.net.cn/article/36303";
    readonly 'stat.warn.version': "The uni statistics version is not configured. The default version is 1.0.uni statistics version 2.0 is recommended, private deployment data is more secure and code is open source and customizable. details: https://uniapp.dcloud.io/uni-stat";
    readonly 'stat.warn.tip': "uni statistics version: {version}";
    readonly 'i18n.fallbackLocale.default': "fallbackLocale is missing in manifest.json, use: {locale}";
    readonly 'i18n.fallbackLocale.missing': "./local/{locale}.json is missing";
    readonly 'easycom.conflict': "easycom component conflict: ";
    readonly 'mp.component.args[0]': "The first parameter of {0} must be a static string";
    readonly 'mp.component.args[1]': "{0} requires two parameters";
    readonly 'mp.360.unsupported': "360 is unsupported";
    readonly 'file.notfound': "{file} is not found";
    readonly 'uts.ios.tips': "The project uses the uts plugin. After the uts plug-in code is modified, the [Custom playground native runner](https://uniapp.dcloud.net.cn/tutorial/run/run-app.html#customplayground) needs to be regenerated to take effect";
    readonly 'uts.android.compiler.server': "The project uses the uts plugin, installing the uts Android runtime extension...";
    readonly 'uts.ios.windows.tips': "When running on Windows to iOS mobile phone, the modification of the uts plugin code needs to be submitted to the cloud to package the custom playground to take effect.";
    readonly 'uts.ios.standard.tips': "When the standard playground runs to an IOS phone, the uts plugin is temporarily not supported. If you need to call the uts plugin, please use a custom playground";
    readonly 'prompt.run.message': "Run method: open {devtools}, import {outputDir} run.";
    readonly 'prompt.run.devtools.app': "HBuilderX";
    readonly 'prompt.run.devtools.mp-alipay': "Alipay Mini Program Devtools";
    readonly 'prompt.run.devtools.mp-baidu': "Baidu Mini Program Devtools";
    readonly 'prompt.run.devtools.mp--kuaishou': "Kuaishou Mini Program Devtools";
    readonly 'prompt.run.devtools.mp-lark': "Lark Mini Program Devtools";
    readonly 'prompt.run.devtools.mp-qq': "QQ Mini Program Devtools";
    readonly 'prompt.run.devtools.mp-toutiao': "Douyin Mini Program Devtools";
    readonly 'prompt.run.devtools.mp-weixin': "Weixin Mini Program Devtools";
    readonly 'prompt.run.devtools.mp-jd': "Jingdong Mini Program Devtools";
    readonly 'prompt.run.devtools.mp-xhs': "Xiaohongshu Mini Program Devtools";
    readonly 'prompt.run.devtools.quickapp-webview': "Quick App Alliance Devtools | Huawei Quick App Devtools";
    readonly 'prompt.run.devtools.quickapp-webview-huawei': "Huawei Quick App Devtools";
    readonly 'prompt.run.devtools.quickapp-webview-union': "Quick App Alliance Devtools";
    readonly 'uvue.unsupported': "uvue does not support {platform} platform";
};
export default _default;

export type ModuleName = string;

export type Target =
  | 'android'
  | 'bun'
  | 'chrome'
  | 'chrome-android'
  | 'deno'
  | 'edge'
  | 'electron'
  | 'firefox'
  | 'firefox-android'
  | 'hermes'
  | 'ie'
  | 'ios'
  | 'node'
  | 'opera'
  | 'opera-android'
  | 'phantom'
  | 'quest'
  | 'react-native'
  | 'rhino'
  | 'safari'
  | 'samsung'
  /** `quest` alias */
  | 'oculus'
  /** `react-native` alias */
  | 'react'
  /** @deprecated use `opera-android` instead */
  | 'opera_mobile';

export type TargetVersion = string;

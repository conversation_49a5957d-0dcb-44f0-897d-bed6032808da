package cn.zhentao.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 权限定义表
 * @TableName permission
 */
@TableName(value ="permission")
@Data
public class Permission implements Serializable {
    /**
     * 权限ID
     */
    @TableId(type = IdType.AUTO)
    private Long permissionId;

    /**
     * 权限标识（如 user:add）
     */
    private String permissionCode;

    /**
     * 权限名称（如添加用户）
     */
    private String permissionName;

    /**
     * 请求方法（GET/POST/PUT/DELETE）
     */
    private String method;

    /**
     * API路径（如 /api/users）
     */
    private String apiPath;

    /**
     * 关联菜单ID
     */
    private Long menuId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
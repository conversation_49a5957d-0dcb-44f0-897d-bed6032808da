{"version": 3, "sources": ["../src/index.js"], "names": ["advancedRotate", "deg", "mode", "rad", "Math", "PI", "cosine", "cos", "sine", "sin", "w", "bitmap", "width", "h", "height", "ceil", "abs", "c", "clone<PERSON>uiet", "scanQuiet", "x", "y", "idx", "data", "writeUInt32BE", "_background", "max", "resize", "blit", "bW", "bH", "dst<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "alloc", "length", "createTranslationFunction", "deltaX", "deltaY", "translate2Cartesian", "translate2Screen", "cartesian", "source", "dstIdx", "srcIdx", "pixelRGBA", "readUInt32BE", "crop", "rotate", "cb", "throwError", "call"], "mappings": ";;;;;;;AAAA;;AAEA;;;;;AAKA,SAASA,cAAT,CAAwBC,GAAxB,EAA6BC,IAA7B,EAAmC;AACjCD,EAAAA,GAAG,IAAI,GAAP;AACA,MAAME,GAAG,GAAIF,GAAG,GAAGG,IAAI,CAACC,EAAZ,GAAkB,GAA9B;AACA,MAAMC,MAAM,GAAGF,IAAI,CAACG,GAAL,CAASJ,GAAT,CAAf;AACA,MAAMK,IAAI,GAAGJ,IAAI,CAACK,GAAL,CAASN,GAAT,CAAb,CAJiC,CAMjC;;AACA,MAAIO,CAAC,GAAG,KAAKC,MAAL,CAAYC,KAApB;AACA,MAAIC,CAAC,GAAG,KAAKF,MAAL,CAAYG,MAApB;;AAEA,MAAIZ,IAAI,KAAK,IAAT,IAAiB,OAAOA,IAAP,KAAgB,QAArC,EAA+C;AAC7C;AACA;AAEA;AACA;AACAQ,IAAAA,CAAC,GACCN,IAAI,CAACW,IAAL,CACEX,IAAI,CAACY,GAAL,CAAS,KAAKL,MAAL,CAAYC,KAAZ,GAAoBN,MAA7B,IACEF,IAAI,CAACY,GAAL,CAAS,KAAKL,MAAL,CAAYG,MAAZ,GAAqBN,IAA9B,CAFJ,IAGI,CAJN;AAKAK,IAAAA,CAAC,GACCT,IAAI,CAACW,IAAL,CACEX,IAAI,CAACY,GAAL,CAAS,KAAKL,MAAL,CAAYC,KAAZ,GAAoBJ,IAA7B,IACEJ,IAAI,CAACY,GAAL,CAAS,KAAKL,MAAL,CAAYG,MAAZ,GAAqBR,MAA9B,CAFJ,IAGI,CAJN,CAX6C,CAgB7C;;AACA,QAAII,CAAC,GAAG,CAAJ,KAAU,CAAd,EAAiB;AACfA,MAAAA,CAAC;AACF;;AAED,QAAIG,CAAC,GAAG,CAAJ,KAAU,CAAd,EAAiB;AACfA,MAAAA,CAAC;AACF;;AAED,QAAMI,CAAC,GAAG,KAAKC,UAAL,EAAV;AACA,SAAKC,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,KAAKR,MAAL,CAAYC,KAAjC,EAAwC,KAAKD,MAAL,CAAYG,MAApD,EAA4D,UAC1DM,CAD0D,EAE1DC,CAF0D,EAG1DC,GAH0D,EAI1D;AACA,WAAKX,MAAL,CAAYY,IAAZ,CAAiBC,aAAjB,CAA+B,KAAKC,WAApC,EAAiDH,GAAjD;AACD,KAND;AAQA,QAAMI,GAAG,GAAGtB,IAAI,CAACsB,GAAL,CAAShB,CAAT,EAAYG,CAAZ,EAAe,KAAKF,MAAL,CAAYC,KAA3B,EAAkC,KAAKD,MAAL,CAAYG,MAA9C,CAAZ;AACA,SAAKa,MAAL,CAAYD,GAAZ,EAAiBA,GAAjB,EAAsBxB,IAAtB;AAEA,SAAK0B,IAAL,CACEX,CADF,EAEE,KAAKN,MAAL,CAAYC,KAAZ,GAAoB,CAApB,GAAwBK,CAAC,CAACN,MAAF,CAASC,KAAT,GAAiB,CAF3C,EAGE,KAAKD,MAAL,CAAYG,MAAZ,GAAqB,CAArB,GAAyBG,CAAC,CAACN,MAAF,CAASG,MAAT,GAAkB,CAH7C;AAKD;;AAED,MAAMe,EAAE,GAAG,KAAKlB,MAAL,CAAYC,KAAvB;AACA,MAAMkB,EAAE,GAAG,KAAKnB,MAAL,CAAYG,MAAvB;AACA,MAAMiB,SAAS,GAAGC,MAAM,CAACC,KAAP,CAAa,KAAKtB,MAAL,CAAYY,IAAZ,CAAiBW,MAA9B,CAAlB;;AAEA,WAASC,yBAAT,CAAmCC,MAAnC,EAA2CC,MAA3C,EAAmD;AACjD,WAAO,UAASjB,CAAT,EAAYC,CAAZ,EAAe;AACpB,aAAO;AACLD,QAAAA,CAAC,EAAEA,CAAC,GAAGgB,MADF;AAELf,QAAAA,CAAC,EAAEA,CAAC,GAAGgB;AAFF,OAAP;AAID,KALD;AAMD;;AAED,MAAMC,mBAAmB,GAAGH,yBAAyB,CAAC,EAAEN,EAAE,GAAG,CAAP,CAAD,EAAY,EAAEC,EAAE,GAAG,CAAP,CAAZ,CAArD;AACA,MAAMS,gBAAgB,GAAGJ,yBAAyB,CAChDN,EAAE,GAAG,CAAL,GAAS,GADuC,EAEhDC,EAAE,GAAG,CAAL,GAAS,GAFuC,CAAlD;;AAKA,OAAK,IAAIT,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAIS,EAArB,EAAyBT,CAAC,EAA1B,EAA8B;AAC5B,SAAK,IAAID,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAIS,EAArB,EAAyBT,CAAC,EAA1B,EAA8B;AAC5B,UAAMoB,SAAS,GAAGF,mBAAmB,CAAClB,CAAD,EAAIC,CAAJ,CAArC;AACA,UAAMoB,MAAM,GAAGF,gBAAgB,CAC7BjC,MAAM,GAAGkC,SAAS,CAACpB,CAAnB,GAAuBZ,IAAI,GAAGgC,SAAS,CAACnB,CADX,EAE7Bf,MAAM,GAAGkC,SAAS,CAACnB,CAAnB,GAAuBb,IAAI,GAAGgC,SAAS,CAACpB,CAFX,CAA/B;AAIA,UAAMsB,MAAM,GAAIb,EAAE,IAAIR,CAAC,GAAG,CAAR,CAAF,GAAeD,CAAf,GAAmB,CAApB,IAA0B,CAAzC;;AAEA,UAAIqB,MAAM,CAACrB,CAAP,IAAY,CAAZ,IAAiBqB,MAAM,CAACrB,CAAP,GAAWS,EAA5B,IAAkCY,MAAM,CAACpB,CAAP,IAAY,CAA9C,IAAmDoB,MAAM,CAACpB,CAAP,GAAWS,EAAlE,EAAsE;AACpE,YAAMa,MAAM,GAAG,CAAEd,EAAE,IAAIY,MAAM,CAACpB,CAAP,GAAW,CAAf,CAAF,GAAsBoB,MAAM,CAACrB,CAA9B,GAAmC,CAApC,KAA0C,CAAzD;AACA,YAAMwB,SAAS,GAAG,KAAKjC,MAAL,CAAYY,IAAZ,CAAiBsB,YAAjB,CAA8BF,MAA9B,CAAlB;AACAZ,QAAAA,SAAS,CAACP,aAAV,CAAwBoB,SAAxB,EAAmCF,MAAnC;AACD,OAJD,MAIO;AACL;AACAX,QAAAA,SAAS,CAACP,aAAV,CAAwB,KAAKC,WAA7B,EAA0CiB,MAA1C;AACD;AACF;AACF;;AAED,OAAK/B,MAAL,CAAYY,IAAZ,GAAmBQ,SAAnB;;AAEA,MAAI7B,IAAI,KAAK,IAAT,IAAiB,OAAOA,IAAP,KAAgB,QAArC,EAA+C;AAC7C;AACA,QAAMkB,EAAC,GAAGS,EAAE,GAAG,CAAL,GAASnB,CAAC,GAAG,CAAvB;;AACA,QAAMW,EAAC,GAAGS,EAAE,GAAG,CAAL,GAASjB,CAAC,GAAG,CAAvB;;AACA,SAAKiC,IAAL,CAAU1B,EAAV,EAAaC,EAAb,EAAgBX,CAAhB,EAAmBG,CAAnB;AACD;AACF;;eAEc;AAAA,SAAO;AACpB;;;;;;;AAOAkC,IAAAA,MARoB,kBAQb9C,GARa,EAQRC,IARQ,EAQF8C,EARE,EAQE;AACpB;AACA,UAAI,OAAO9C,IAAP,KAAgB,WAAhB,IAA+BA,IAAI,KAAK,IAA5C,EAAkD;AAChD;AACA;AACA;AACAA,QAAAA,IAAI,GAAG,IAAP;AACD;;AAED,UAAI,OAAOA,IAAP,KAAgB,UAAhB,IAA8B,OAAO8C,EAAP,KAAc,WAAhD,EAA6D;AAC3D;AACAA,QAAAA,EAAE,GAAG9C,IAAL;AACAA,QAAAA,IAAI,GAAG,IAAP;AACD;;AAED,UAAI,OAAOD,GAAP,KAAe,QAAnB,EAA6B;AAC3B,eAAOgD,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,sBAAtB,EAA8CF,EAA9C,CAAP;AACD;;AAED,UAAI,OAAO9C,IAAP,KAAgB,SAAhB,IAA6B,OAAOA,IAAP,KAAgB,QAAjD,EAA2D;AACzD,eAAO+C,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,oCAAtB,EAA4DF,EAA5D,CAAP;AACD;;AAEDhD,MAAAA,cAAc,CAACkD,IAAf,CAAoB,IAApB,EAA0BjD,GAA1B,EAA+BC,IAA/B,EAAqC8C,EAArC;;AAEA,UAAI,0BAAcA,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACE,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD;AAtCmB,GAAP;AAAA,C", "sourcesContent": ["import { throwError, isNodePattern } from '@jimp/utils';\n\n/**\n * Rotates an image clockwise by an arbitrary number of degrees. NB: 'this' must be a Jimp object.\n * @param {number} deg the number of degrees to rotate the image by\n * @param {string|boolean} mode (optional) resize mode or a boolean, if false then the width and height of the image will not be changed\n */\nfunction advancedRotate(deg, mode) {\n  deg %= 360;\n  const rad = (deg * Math.PI) / 180;\n  const cosine = Math.cos(rad);\n  const sine = Math.sin(rad);\n\n  // the final width and height will change if resize == true\n  let w = this.bitmap.width;\n  let h = this.bitmap.height;\n\n  if (mode === true || typeof mode === 'string') {\n    // resize the image to it maximum dimension and blit the existing image\n    // onto the center so that when it is rotated the image is kept in bounds\n\n    // http://stackoverflow.com/questions/3231176/how-to-get-size-of-a-rotated-rectangle\n    // Plus 1 border pixel to ensure to show all rotated result for some cases.\n    w =\n      Math.ceil(\n        Math.abs(this.bitmap.width * cosine) +\n          Math.abs(this.bitmap.height * sine)\n      ) + 1;\n    h =\n      Math.ceil(\n        Math.abs(this.bitmap.width * sine) +\n          Math.abs(this.bitmap.height * cosine)\n      ) + 1;\n    // Ensure destination to have even size to a better result.\n    if (w % 2 !== 0) {\n      w++;\n    }\n\n    if (h % 2 !== 0) {\n      h++;\n    }\n\n    const c = this.cloneQuiet();\n    this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function(\n      x,\n      y,\n      idx\n    ) {\n      this.bitmap.data.writeUInt32BE(this._background, idx);\n    });\n\n    const max = Math.max(w, h, this.bitmap.width, this.bitmap.height);\n    this.resize(max, max, mode);\n\n    this.blit(\n      c,\n      this.bitmap.width / 2 - c.bitmap.width / 2,\n      this.bitmap.height / 2 - c.bitmap.height / 2\n    );\n  }\n\n  const bW = this.bitmap.width;\n  const bH = this.bitmap.height;\n  const dstBuffer = Buffer.alloc(this.bitmap.data.length);\n\n  function createTranslationFunction(deltaX, deltaY) {\n    return function(x, y) {\n      return {\n        x: x + deltaX,\n        y: y + deltaY\n      };\n    };\n  }\n\n  const translate2Cartesian = createTranslationFunction(-(bW / 2), -(bH / 2));\n  const translate2Screen = createTranslationFunction(\n    bW / 2 + 0.5,\n    bH / 2 + 0.5\n  );\n\n  for (let y = 1; y <= bH; y++) {\n    for (let x = 1; x <= bW; x++) {\n      const cartesian = translate2Cartesian(x, y);\n      const source = translate2Screen(\n        cosine * cartesian.x - sine * cartesian.y,\n        cosine * cartesian.y + sine * cartesian.x\n      );\n      const dstIdx = (bW * (y - 1) + x - 1) << 2;\n\n      if (source.x >= 0 && source.x < bW && source.y >= 0 && source.y < bH) {\n        const srcIdx = ((bW * (source.y | 0) + source.x) | 0) << 2;\n        const pixelRGBA = this.bitmap.data.readUInt32BE(srcIdx);\n        dstBuffer.writeUInt32BE(pixelRGBA, dstIdx);\n      } else {\n        // reset off-image pixels\n        dstBuffer.writeUInt32BE(this._background, dstIdx);\n      }\n    }\n  }\n\n  this.bitmap.data = dstBuffer;\n\n  if (mode === true || typeof mode === 'string') {\n    // now crop the image to the final size\n    const x = bW / 2 - w / 2;\n    const y = bH / 2 - h / 2;\n    this.crop(x, y, w, h);\n  }\n}\n\nexport default () => ({\n  /**\n   * Rotates the image clockwise by a number of degrees. By default the width and height of the image will be resized appropriately.\n   * @param {number} deg the number of degrees to rotate the image by\n   * @param {string|boolean} mode (optional) resize mode or a boolean, if false then the width and height of the image will not be changed\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp} this for chaining of methods\n   */\n  rotate(deg, mode, cb) {\n    // enable overloading\n    if (typeof mode === 'undefined' || mode === null) {\n      // e.g. image.resize(120);\n      // e.g. image.resize(120, null, cb);\n      // e.g. image.resize(120, undefined, cb);\n      mode = true;\n    }\n\n    if (typeof mode === 'function' && typeof cb === 'undefined') {\n      // e.g. image.resize(120, cb);\n      cb = mode;\n      mode = true;\n    }\n\n    if (typeof deg !== 'number') {\n      return throwError.call(this, 'deg must be a number', cb);\n    }\n\n    if (typeof mode !== 'boolean' && typeof mode !== 'string') {\n      return throwError.call(this, 'mode must be a boolean or a string', cb);\n    }\n\n    advancedRotate.call(this, deg, mode, cb);\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  }\n});\n"], "file": "index.js"}
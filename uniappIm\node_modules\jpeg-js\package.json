{"name": "jpeg-js", "version": "0.3.7", "description": "A pure javascript JPEG encoder and decoder", "main": "index.js", "scripts": {"test": "node_modules/.bin/tape test/*.js"}, "repository": {"type": "git", "url": "https://github.com/eugeneware/jpeg-js"}, "keywords": ["jpeg", "jpg", "encoder", "decoder", "codec", "image", "javascript", "js"], "author": "<PERSON> <<EMAIL>>", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/eugeneware/jpeg-js/issues"}, "dependencies": {}, "devDependencies": {"redtape": "~0.1.0", "tape": "~2.3.2"}}
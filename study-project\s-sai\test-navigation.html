<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能家教 - 导航测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            height: 100vh;
            overflow: hidden;
        }
        
        /* 自定义状态栏 */
        .custom-status-bar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 44px;
            background: #2c3e50;
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .app-title {
            font-size: 16px;
            font-weight: bold;
            color: #fff;
        }
        
        .container {
            min-height: 100vh;
            background: #f8f9fa;
            padding: 10px;
            padding-top: 54px;
            padding-bottom: 70px;
        }
        
        .header-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
        }
        
        .user-info {
            display: flex;
            align-items: center;
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #007AFF;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            color: white;
            font-weight: bold;
        }
        
        .user-details {
            display: flex;
            flex-direction: column;
        }
        
        .welcome-text {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 4px;
        }
        
        .user-name {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .header-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .ai-status {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
        }
        
        .status-text {
            font-size: 12px;
            color: #28a745;
        }
        
        .settings-btn {
            font-size: 18px;
            cursor: pointer;
        }
        
        /* 底部导航栏 */
        .bottom-navigation {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: white;
            display: flex;
            align-items: center;
            justify-content: space-around;
            box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            flex: 1;
            padding: 5px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .nav-item.active .nav-icon {
            color: #007AFF;
            transform: scale(1.1);
        }
        
        .nav-item.active .nav-text {
            color: #007AFF;
            font-weight: 600;
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
            color: #666;
            transition: all 0.3s ease;
        }
        
        .nav-text {
            font-size: 10px;
            color: #666;
            transition: all 0.3s ease;
        }
        
        .camera-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .camera-btn {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #FF6B6B, #FF8E53);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);
            transform: translateY(-5px);
            margin-bottom: 4px;
        }

        .camera-btn:hover {
            transform: translateY(-5px) scale(1.05);
        }

        .camera-icon {
            font-size: 22px;
            color: white;
        }

        .camera-text {
            font-size: 10px;
            color: #666;
            font-weight: 500;
            text-align: center;
            transform: translateY(-5px);
        }
        
        .content {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .demo-text {
            color: #2c3e50;
            font-size: 16px;
            margin-bottom: 10px;
        }
        
        .demo-desc {
            color: #6c757d;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <!-- 自定义状态栏 -->
    <div class="custom-status-bar">
        <div class="app-title">AI智能家教</div>
    </div>
    
    <div class="container">
        <!-- 顶部用户信息区域 -->
        <div class="header-section">
            <div class="user-info">
                <div class="avatar">A</div>
                <div class="user-details">
                    <div class="welcome-text">欢迎回来</div>
                    <div class="user-name">AI学习者</div>
                </div>
            </div>
            <div class="header-actions">
                <div class="ai-status">
                    <div class="status-indicator"></div>
                    <div class="status-text">AI在线</div>
                </div>
                <div class="settings-btn">⚙️</div>
            </div>
        </div>
        
        <!-- 修改成功提示 -->
        <div class="success-message">
            <strong>✅ 导航栏优化完成！</strong><br>
            • 修正了按钮状态：现在"我的"按钮为高亮状态<br>
            • 美化了拍照搜题按钮：添加了渐变效果和文字标签<br>
            • 首页按钮显示"首页功能开发中"提示<br>
            • 拍照按钮下方显示"拍照搜题"文字说明
        </div>
        
        <div class="content">
            <div class="demo-text">🎉 导航栏美化完成</div>
            <div class="demo-desc">
                按钮状态和样式已优化：<br><br>
                <strong>首页：</strong>普通状态，点击显示开发中提示<br>
                <strong>拍照搜题：</strong>美化渐变按钮，带文字标签<br>
                <strong>我的：</strong>高亮状态（当前页面）<br><br>
                <strong>视觉效果：</strong>拍照按钮向上突出，渐变色彩更美观
            </div>
        </div>
    </div>
    
    <!-- 底部导航栏 -->
    <div class="bottom-navigation">
        <div class="nav-item" onclick="showHomeInfo()">
            <div class="nav-icon">🏠</div>
            <div class="nav-text">首页</div>
        </div>

        <!-- 美化的拍照搜题按钮 -->
        <div class="camera-section">
            <div class="camera-btn" onclick="showCameraInfo()">
                <div class="camera-icon">📷</div>
            </div>
            <div class="camera-text">拍照搜题</div>
        </div>

        <div class="nav-item active" onclick="showProfileInfo()">
            <div class="nav-icon">👤</div>
            <div class="nav-text">我的</div>
        </div>
    </div>
    
    <script>
        function showHomeInfo() {
            alert('首页功能开发中');
        }

        function showCameraInfo() {
            alert('拍照搜题功能开发中');
        }

        function showProfileInfo() {
            alert('个人中心功能开发中');
        }
    </script>
</body>
</html>

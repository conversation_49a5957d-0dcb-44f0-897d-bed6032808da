
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.header-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}
.header-info {
  flex: 1;
}
.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  display: block;
}
.header-desc {
  font-size: 24rpx;
  color: #6c757d;
  margin-top: 8rpx;
  display: block;
}
.category-selector {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  padding: 30rpx 0;
}
.selector-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin: 0 30rpx 20rpx;
  display: block;
}
.category-scroll {
  white-space: nowrap;
}
.category-list {
  display: inline-flex;
  padding: 0 30rpx;
  gap: 20rpx;
}
.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  min-width: 120rpx;
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 0.8);
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}
.category-item.active {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
  transform: scale(1.05);
}
.category-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}
.category-name {
  font-size: 24rpx;
  color: #495057;
  font-weight: 500;
  white-space: nowrap;
}
.category-item.active .category-name {
  color: #667eea;
  font-weight: bold;
}
.preference-section {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
}
.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20rpx;
  display: block;
}
.preference-input {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.5;
  background: #f8f9fa;
  box-sizing: border-box;
}
.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #6c757d;
  margin-top: 10rpx;
}
.quick-tags {
  margin-top: 30rpx;
}
.tags-title {
  font-size: 24rpx;
  color: #6c757d;
  margin-bottom: 16rpx;
  display: block;
}
.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}
.tag-item {
  padding: 12rpx 20rpx;
  background: #e9ecef;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}
.tag-item:active {
  background: #667eea;
  transform: scale(0.95);
}
.tag-item:active .tag-text {
  color: #fff;
}
.tag-text {
  font-size: 24rpx;
  color: #495057;
}
.recommend-section {
  padding: 30rpx;
}
.recommend-btn {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
  border: none;
  border-radius: 50rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  transition: all 0.3s ease;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}
.recommend-btn.disabled {
  background: #ccc;
  color: #999;
  box-shadow: none;
}
.recommend-btn:not(.disabled):active {
  transform: scale(0.98);
}
.result-section {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  margin: 0 20rpx 30rpx;
  border-radius: 16rpx;
  padding: 30rpx;
}
.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.result-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
}
.result-actions {
  display: flex;
  gap: 16rpx;
}
.action-btn {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
  transition: all 0.3s ease;
}
.action-btn.refresh {
  background: #ffc107;
  color: #333;
}
.recommending-animation {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}
.loading-circle {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 30rpx;
}
.circle-dot {
  position: absolute;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #667eea;
  animation: circleRotate 1.2s linear infinite;
}
.circle-dot:nth-child(1) { top: 0; left: 50%; margin-left: -8rpx; animation-delay: 0s;
}
.circle-dot:nth-child(2) { top: 50%; right: 0; margin-top: -8rpx; animation-delay: 0.3s;
}
.circle-dot:nth-child(3) { bottom: 0; left: 50%; margin-left: -8rpx; animation-delay: 0.6s;
}
.circle-dot:nth-child(4) { top: 50%; left: 0; margin-top: -8rpx; animation-delay: 0.9s;
}
@keyframes circleRotate {
0% { opacity: 1; transform: scale(1);
}
50% { opacity: 0.3; transform: scale(0.5);
}
100% { opacity: 1; transform: scale(1);
}
}
.recommending-text {
  font-size: 28rpx;
  color: #6c757d;
}
.result-content {
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 30rpx;
  background: #f8f9fa;
}
.result-text {
  font-size: 28rpx;
  line-height: 1.8;
  color: #333;
  white-space: pre-wrap;
  display: block;
  margin-bottom: 30rpx;
}
.result-footer {
  display: flex;
  gap: 16rpx;
}
.footer-btn {
  flex: 1;
  height: 70rpx;
  border: none;
  border-radius: 35rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.footer-btn.copy {
  background: #28a745;
  color: #fff;
}
.footer-btn.share {
  background: #17a2b8;
  color: #fff;
}
.footer-btn:active {
  transform: scale(0.95);
}
.history-section {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  margin: 0 20rpx 30rpx;
  border-radius: 16rpx;
  padding: 30rpx;
}
.history-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20rpx;
  display: block;
}
.history-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.history-item {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s ease;
}
.history-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}
.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}
.history-category {
  font-size: 24rpx;
  color: #667eea;
  font-weight: bold;
}
.history-time {
  font-size: 20rpx;
  color: #6c757d;
}
.history-preference {
  font-size: 26rpx;
  color: #495057;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

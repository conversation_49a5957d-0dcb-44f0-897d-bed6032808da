<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI流式输出测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .input-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 80px;
            resize: vertical;
        }
        .button-group {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
            transition: background-color 0.3s;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .response-area {
            margin-top: 30px;
        }
        .response-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 20px;
            min-height: 200px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            line-height: 1.6;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.connecting {
            background: #fff3cd;
            color: #856404;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .status.completed {
            background: #d1ecf1;
            color: #0c5460;
        }
        .clear-btn {
            background: #6c757d;
            float: right;
            margin-bottom: 10px;
        }
        .clear-btn:hover {
            background: #545b62;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI流式输出测试</h1>
        
        <div class="input-group">
            <label for="prompt">输入问题：</label>
            <textarea id="prompt" placeholder="请输入您的问题...">请详细介绍一下人工智能的发展历史和未来趋势</textarea>
        </div>
        
        <div class="input-group">
            <label for="sessionId">会话ID（可选）：</label>
            <input type="text" id="sessionId" placeholder="留空将创建新会话">
        </div>
        
        <div class="button-group">
            <button onclick="startStream()">开始流式对话</button>
            <button onclick="startFastStream()">快速流式对话</button>
            <button onclick="stopStream()" id="stopBtn" disabled>停止</button>
        </div>
        
        <div class="response-area">
            <button class="clear-btn" onclick="clearResponse()">清空</button>
            <div id="response" class="response-box">等待AI响应...</div>
            <div id="status" class="status">准备就绪</div>
        </div>
    </div>

    <script>
        let eventSource = null;
        let currentSessionId = null;

        function startStream() {
            const prompt = document.getElementById('prompt').value.trim();
            const sessionId = document.getElementById('sessionId').value.trim();
            
            if (!prompt) {
                alert('请输入问题');
                return;
            }
            
            stopStream(); // 停止之前的连接
            clearResponse();
            
            const url = sessionId ? 
                `/api/ai/test/stream` : 
                `/api/ai/test/stream?prompt=${encodeURIComponent(prompt)}`;
            
            if (sessionId) {
                // POST请求
                fetch('/api/ai/test/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        sessionId: sessionId
                    })
                }).then(response => {
                    if (response.ok) {
                        handleStreamResponse(response);
                    } else {
                        throw new Error('请求失败');
                    }
                }).catch(error => {
                    updateStatus('error', '连接失败: ' + error.message);
                });
            } else {
                // GET请求
                eventSource = new EventSource(url);
                setupEventSource();
            }
        }

        function startFastStream() {
            const prompt = document.getElementById('prompt').value.trim();
            const sessionId = document.getElementById('sessionId').value.trim();
            
            if (!prompt) {
                alert('请输入问题');
                return;
            }
            
            stopStream();
            clearResponse();
            
            const url = `/api/ai/test/fast-stream?prompt=${encodeURIComponent(prompt)}` + 
                       (sessionId ? `&sessionId=${encodeURIComponent(sessionId)}` : '');
            
            eventSource = new EventSource(url);
            setupEventSource();
        }

        function setupEventSource() {
            updateStatus('connecting', '正在连接AI服务...');
            document.getElementById('stopBtn').disabled = false;
            
            eventSource.onopen = function(event) {
                updateStatus('connected', '已连接，等待AI响应...');
            };
            
            eventSource.addEventListener('start', function(event) {
                const data = JSON.parse(event.data);
                currentSessionId = data.sessionId;
                document.getElementById('sessionId').value = currentSessionId;
                updateStatus('connected', '开始接收响应...');
            });
            
            eventSource.addEventListener('data', function(event) {
                const data = JSON.parse(event.data);
                appendToResponse(data.content);
            });
            
            eventSource.addEventListener('end', function(event) {
                const data = JSON.parse(event.data);
                updateStatus('completed', '响应完成！会话ID: ' + data.sessionId);
                stopStream();
            });
            
            eventSource.addEventListener('error', function(event) {
                const data = JSON.parse(event.data);
                updateStatus('error', '错误: ' + data.message);
                stopStream();
            });
            
            eventSource.onerror = function(event) {
                updateStatus('error', '连接错误，请检查服务是否正常运行');
                stopStream();
            };
        }

        function stopStream() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }
            document.getElementById('stopBtn').disabled = true;
        }

        function clearResponse() {
            document.getElementById('response').textContent = '';
        }

        function appendToResponse(content) {
            const responseDiv = document.getElementById('response');
            responseDiv.textContent += content;
            responseDiv.scrollTop = responseDiv.scrollHeight;
        }

        function updateStatus(type, message) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = 'status ' + type;
            statusDiv.textContent = message;
        }

        // 页面卸载时关闭连接
        window.addEventListener('beforeunload', function() {
            stopStream();
        });
    </script>
</body>
</html>

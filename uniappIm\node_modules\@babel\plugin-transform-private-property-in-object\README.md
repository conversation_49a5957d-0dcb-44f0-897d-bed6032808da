# @babel/plugin-transform-private-property-in-object

> This plugin transforms checks for a private property in an object

See our website [@babel/plugin-transform-private-property-in-object](https://babeljs.io/docs/babel-plugin-transform-private-property-in-object) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-transform-private-property-in-object
```

or using yarn:

```sh
yarn add @babel/plugin-transform-private-property-in-object --dev
```

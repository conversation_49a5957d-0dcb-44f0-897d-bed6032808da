import{_ as e,o as s,c as t,w as o,i as a,a as i,b as n,d as l,t as c,h as r,r as d,F as u,n as h,e as m,f,S as g,j as w}from"./index-0fa1fc91.js";import{b as _,m as k,s as p}from"./api.44778c12.js";import{a as M,c as I,s as T}from"./websocket.6c3599e6.js";const C=e({data:()=>({userId:null,nickname:"",userInfo:null,messages:[],inputMessage:"",scrollTop:0,sending:!1,showEmojiPanel:!1,showMorePanel:!1,showChatMenuModal:!1,hasMoreMessages:!1,loadingMore:!1,currentPage:1,pageSize:20,lastMessageTime:null,isConnected:!1,messageListHeight:600,isRecordingVoice:!1,scrollTimer:null,emojiList:["😀","😃","😄","😁","😆","😅","😂","🤣","😊","😇","🙂","🙃","😉","😌","😍","🥰","😘","😗","😙","😚","😋","😛","😝","😜","🤪","🤨","🧐","🤓","😎","🤩","🥳","😏","😒","😞","😔","😟","😕","🙁","☹️","😣","😖","😫","😩","🥺","😢","😭","😤","😠","😡","🤬","🤯","😳","🥵","🥶","😱","😨","😰","😥","😓","🤗","🤔","🤭","🤫","🤥","😶","😐","😑","😬","🙄","😯","😦","😧","😮","😲","🥱","😴","🤤","😪","😵","🤐","🥴","🤢","🤮","🤧","😷","🤒","🤕","🤑","🤠","😈","👿","👹","👺","🤡","💩","👻","💀","☠️","👽","👾","🤖","🎃","😺","😸","😹","😻","😼","😽","🙀","😿","😾","👋","🤚","🖐️","✋","🖖","👌","🤏","✌️","🤞","🤟","🤘","🤙","👈","👉","👆","🖕","👇","☝️","👍","👎","👊","✊","🤛","🤜","👏","🙌","👐","🤲","🤝","🙏","✍️","💅","🤳","❤️","🧡","💛","💚","💙","💜","🖤","🤍","🤎","💔","❣️","💕","💞","💓","💗","💖","💘","💝","💟","☮️","✝️","☪️","🕉️","☸️","✡️","🔯","🕎","☯️","☦️","🛐","⛎","♈","♉","♊","♋","♌","♍","♎","♏","♐","♑","♒","♓","🆔","⚛️","🉑","☢️","☣️"]}),computed:{computedMessageListHeight(){try{const e=uni.getSystemInfoSync(),s=88,t=this.showEmojiPanel||this.showMorePanel?400:100,o=e.statusBarHeight||0;return e.windowHeight-s-t-o}catch(e){return console.error("计算高度失败:",e),600}}},onLoad(e){this.userId=parseInt(e.userId),this.nickname=decodeURIComponent(e.nickname||"未知用户"),this.userInfo=uni.getStorageSync("userInfo"),console.log("聊天详情页面参数:",{userId:this.userId,nickname:this.nickname,userInfo:this.userInfo}),uni.setNavigationBarTitle({title:this.nickname}),this.calculateMessageListHeight(),this.loadChatHistory(),this.initWebSocket()},onUnload(){M(),this.scrollTimer&&clearTimeout(this.scrollTimer)},onReady(){this.calculateMessageListHeight()},methods:{async loadChatHistory(){try{const e=await _(this.userId);if(200===e.code){const s=e.data||[];this.messages=s.sort((e,s)=>new Date(e.sendTime)-new Date(s.sendTime)),this.scrollToBottom(),this.markAsRead()}else console.error("加载聊天记录失败:",e.message),uni.showToast({title:e.message||"加载失败",icon:"none"})}catch(e){console.error("加载聊天记录失败:",e),uni.showToast({title:"网络错误",icon:"none"})}},async loadMoreMessages(){},initWebSocket(){const e=uni.getStorageSync("token");if(!e)return console.error("未找到token，无法连接WebSocket"),void(this.isConnected=!0);try{I(e,e=>{if("chat"===e.type){if(e.fromUserId!==this.userInfo.userId){this.messages.find(s=>s.id===e.messageId||s.content===e.content&&s.fromUserId===e.fromUserId&&Math.abs(new Date(s.sendTime)-new Date(e.sendTime))<1e3)||(this.messages.push({id:e.messageId||Date.now(),fromUserId:e.fromUserId,toUserId:e.toUserId,content:e.content,sendTime:e.sendTime||(new Date).toISOString()}),this.scrollToBottom(),this.markAsRead())}}else"auth_success"===e.type&&(this.isConnected=!0)}),this.isConnected=!0}catch(s){console.error("WebSocket连接失败:",s),this.isConnected=!0}},reconnectWebSocket(){this.isConnected=!1,uni.showLoading({title:"连接中..."}),setTimeout(()=>{this.initWebSocket(),uni.hideLoading()},1e3)},async sendMessage(){if(!this.inputMessage.trim()||this.sending)return;const e=this.inputMessage.trim(),s=Date.now(),t={id:s,fromUserId:this.userInfo.userId,toUserId:this.userId,content:e,sendTime:(new Date).toISOString(),status:"sending"};this.messages.push(t),this.inputMessage="",this.sending=!0,this.hideEmojiPanel(),this.hideMorePanel(),this.scrollToBottom();try{const t=await this.sendMessageAPI({toUserId:this.userId,content:e});if(200!==t.code)throw new Error(t.message||"发送失败");{const e=this.messages.findIndex(e=>e.id===s);e>-1&&(this.messages[e].id=t.data.id,this.messages[e].status="sent",this.messages[e].sendTime=t.data.sendTime)}}catch(o){console.error("发送消息失败:",o);const e=this.messages.findIndex(e=>e.id===s);e>-1&&(this.messages[e].status="failed"),uni.showToast({title:o.message||"发送失败",icon:"none"})}finally{this.sending=!1}},formatTime(e){if(!e)return"";return new Date(e).toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",hour12:!1})},formatDetailTime(e){if(!e)return"";const s=new Date(e),t=new Date,o=new Date(t.getFullYear(),t.getMonth(),t.getDate()),a=new Date(o.getTime()-864e5);return s>=o?"今天 "+s.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",hour12:!1}):s>=a?"昨天 "+s.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",hour12:!1}):s.toLocaleDateString("zh-CN")+" "+s.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",hour12:!1})},shouldShowTime(e){const s=this.messages.findIndex(s=>s.id===e.id);if(0===s)return!0;const t=this.messages[s-1];if(!t)return!0;return new Date(e.sendTime)-new Date(t.sendTime)>6e5},scrollToBottom(){this.scrollTimer&&clearTimeout(this.scrollTimer),this.scrollTimer=setTimeout(()=>{this.$nextTick(()=>{this.scrollTop=999999})},30)},async markAsRead(){try{await k(this.userId),uni.$emit("clearUnreadCount",{userId:this.userId})}catch(e){console.error("标记会话已读失败:",e)}},toggleEmojiPanel(){this.showEmojiPanel=!this.showEmojiPanel},hideEmojiPanel(){this.showEmojiPanel=!1},hideMorePanel(){this.showMorePanel=!1},sendMessageAPI:async e=>await p(e),insertEmoji(e){this.inputMessage+=e},goBack(){uni.navigateBack()},showChatMenu(){this.showChatMenuModal=!0},hideChatMenu(){this.showChatMenuModal=!1},calculateMessageListHeight(){try{const e=uni.getSystemInfoSync(),s=88,t=100,o=e.statusBarHeight||0;this.messageListHeight=e.windowHeight-s-t-o}catch(e){console.error("计算消息列表高度失败:",e),this.messageListHeight=600}},onInputFocus(){this.showEmojiPanel=!1,this.showMorePanel=!1,this.calculateMessageListHeight(),this.sendTypingStatus(!0)},onInputBlur(){this.calculateMessageListHeight(),this.sendTypingStatus(!1)},sendTypingStatus(e){try{const s={type:"typing",toUserId:this.userId,fromUserId:this.userInfo.userId,isTyping:e};T(s)}catch(s){console.log("发送输入状态失败:",s)}},toggleMorePanel(){this.showMorePanel=!this.showMorePanel,this.showEmojiPanel=!1,this.calculateMessageListHeight()},toggleEmojiPanel(){this.showEmojiPanel=!this.showEmojiPanel,this.showMorePanel=!1,this.calculateMessageListHeight()},startVoiceRecord(){this.isRecordingVoice=!0,uni.showToast({title:"开始录音",icon:"none"})},endVoiceRecord(){this.isRecordingVoice=!1,uni.showToast({title:"录音结束",icon:"none"})},chooseImage(){this.showMorePanel=!1,uni.chooseImage({count:1,success:e=>{uni.showToast({title:"图片功能开发中",icon:"none"})}})},takePhoto(){this.showMorePanel=!1,uni.showToast({title:"拍照功能开发中",icon:"none"})},chooseVideo(){this.showMorePanel=!1,uni.showToast({title:"视频功能开发中",icon:"none"})},chooseFile(){this.showMorePanel=!1,uni.showToast({title:"文件功能开发中",icon:"none"})},shareLocation(){this.showMorePanel=!1,uni.showToast({title:"位置功能开发中",icon:"none"})},sendVoice(){this.showMorePanel=!1,uni.showToast({title:"语音功能开发中",icon:"none"})},sendRedPacket(){this.showMorePanel=!1,uni.showToast({title:"红包功能开发中",icon:"none"})},transfer(){this.showMorePanel=!1,uni.showToast({title:"转账功能开发中",icon:"none"})},viewProfile(){this.hideChatMenu(),uni.showToast({title:"查看资料功能开发中",icon:"none"})},clearHistory(){this.hideChatMenu(),uni.showModal({title:"确认清空",content:"确定要清空所有聊天记录吗？",success:e=>{e.confirm&&(this.messages=[],uni.showToast({title:"已清空聊天记录",icon:"success"}))}})},setBackground(){this.hideChatMenu(),uni.showToast({title:"设置背景功能开发中",icon:"none"})}}},[["render",function(e,_,k,p,M,I){const T=f,C=a,y=g,P=w;return s(),t(C,{class:"chat-detail"},{default:o(()=>[i(" 顶部导航栏 "),n(C,{class:"chat-header"},{default:o(()=>[n(C,{class:"header-content"},{default:o(()=>[n(C,{class:"header-left",onClick:I.goBack},{default:o(()=>[n(T,{class:"back-icon"},{default:o(()=>[l("‹")]),_:1})]),_:1},8,["onClick"]),n(C,{class:"header-center"},{default:o(()=>[n(T,{class:"chat-title"},{default:o(()=>[l(c(M.nickname),1)]),_:1}),n(T,{class:"online-status"},{default:o(()=>[l(c(M.isConnected?"在线":"离线"),1)]),_:1})]),_:1}),n(C,{class:"header-right"},{default:o(()=>[n(C,{class:"more-icon",onClick:I.showChatMenu},{default:o(()=>[n(T,null,{default:o(()=>[l("⋯")]),_:1})]),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1}),i(" 连接状态提示 - 已隐藏，改为静默重连 "),i(" 消息列表 "),n(y,{class:"message-list","scroll-y":"","scroll-top":M.scrollTop,"scroll-with-animation":!0,onScrolltoupper:I.loadMoreMessages,"enable-back-to-top":!1,enhanced:!0,bounces:!1,"show-scrollbar":!1},{default:o(()=>[i(" 背景图片 "),n(C,{class:"chat-background"}),i(" 加载更多提示 "),M.hasMoreMessages?(s(),t(C,{key:0,class:"load-more"},{default:o(()=>[n(C,{class:"loading-indicator"},{default:o(()=>[n(T,{class:"loading-text"},{default:o(()=>[l(c(M.loadingMore?"加载中...":"下拉加载更多"),1)]),_:1})]),_:1})]),_:1})):i("v-if",!0),i(" 消息列表 "),(s(!0),r(u,null,d(M.messages,e=>(s(),t(C,{key:e.id,class:h(["message-item",{"own-message":e.fromUserId===M.userInfo.userId}])},{default:o(()=>[i(" 时间分割线 "),I.shouldShowTime(e)?(s(),t(C,{key:0,class:"time-divider"},{default:o(()=>[n(C,{class:"time-label"},{default:o(()=>[n(T,{class:"time-text"},{default:o(()=>[l(c(I.formatDetailTime(e.sendTime)),1)]),_:2},1024)]),_:2},1024)]),_:2},1024)):i("v-if",!0),i(" 消息内容 "),n(C,{class:h(["message-wrapper",{"own-message-wrapper":e.fromUserId===M.userInfo.userId}])},{default:o(()=>[i(" 消息气泡 "),n(C,{class:h(["message-bubble",{"own-bubble":e.fromUserId===M.userInfo.userId}])},{default:o(()=>[i(" 消息内容 "),n(C,{class:"message-content"},{default:o(()=>[n(T,{class:"message-text"},{default:o(()=>[l(c(e.content),1)]),_:2},1024)]),_:2},1024)]),_:2},1032,["class"])]),_:2},1032,["class"]),i(" 消息状态和时间 "),e.fromUserId===M.userInfo.userId?(s(),t(C,{key:1,class:"message-status own-status"},{default:o(()=>[n(T,{class:"message-time"},{default:o(()=>[l(c(I.formatTime(e.sendTime)),1)]),_:2},1024),n(C,{class:"send-status"},{default:o(()=>["sending"===e.status?(s(),t(T,{key:0,class:"status-sending"},{default:o(()=>[l("发送中")]),_:1})):"failed"===e.status?(s(),t(T,{key:1,class:"status-failed"},{default:o(()=>[l("发送失败")]),_:1})):(s(),t(T,{key:2,class:"status-sent"},{default:o(()=>[l("✓")]),_:1}))]),_:2},1024)]),_:2},1024)):i("v-if",!0)]),_:2},1032,["class"]))),128)),i(" 空状态 "),0===M.messages.length?(s(),t(C,{key:1,class:"empty-messages"},{default:o(()=>[n(C,{class:"empty-icon"},{default:o(()=>[l("💬")]),_:1}),n(T,{class:"empty-text"},{default:o(()=>[l("暂无聊天记录")]),_:1}),n(T,{class:"empty-tip"},{default:o(()=>[l("开始你们的第一次对话吧")]),_:1})]),_:1})):i("v-if",!0)]),_:1},8,["scroll-top","onScrolltoupper"]),i(" 输入区域 "),n(C,{class:h(["input-area",{"panel-open":M.showEmojiPanel||M.showMorePanel}])},{default:o(()=>[n(C,{class:"input-wrapper"},{default:o(()=>[i(" 语音/更多功能按钮 "),n(C,{class:h(["function-btn",{active:M.showMorePanel}]),onClick:I.toggleMorePanel},{default:o(()=>[n(T,{class:"function-icon"},{default:o(()=>[l(c(M.showMorePanel?"⌨️":"+"),1)]),_:1})]),_:1},8,["onClick","class"]),i(" 输入框容器 "),n(C,{class:"input-container"},{default:o(()=>[i(" 输入框 "),n(P,{modelValue:M.inputMessage,"onUpdate:modelValue":_[0]||(_[0]=e=>M.inputMessage=e),placeholder:"输入消息...",class:"message-input","auto-height":!0,maxlength:500,onConfirm:I.sendMessage,onFocus:I.onInputFocus,onBlur:I.onInputBlur,"adjust-position":!1,"show-confirm-bar":!1},null,8,["modelValue","onConfirm","onFocus","onBlur"])]),_:1}),i(" 表情按钮 "),n(C,{class:h(["function-btn",{active:M.showEmojiPanel}]),onClick:I.toggleEmojiPanel},{default:o(()=>[n(T,{class:"function-icon"},{default:o(()=>[l("😊")]),_:1})]),_:1},8,["onClick","class"]),i(" 发送按钮 "),M.inputMessage.trim()?(s(),t(C,{key:0,class:h(["send-btn",{sending:M.sending}]),onClick:I.sendMessage},{default:o(()=>[n(T,{class:"send-text"},{default:o(()=>[l(c(M.sending?"...":"发送"),1)]),_:1})]),_:1},8,["onClick","class"])):(s(),r(u,{key:1},[i(" 语音按钮 "),n(C,{class:"voice-btn",onTouchstart:I.startVoiceRecord,onTouchend:I.endVoiceRecord},{default:o(()=>[n(T,{class:"voice-icon"},{default:o(()=>[l("🎤")]),_:1})]),_:1},8,["onTouchstart","onTouchend"])],2112))]),_:1}),i(" 表情面板 "),M.showEmojiPanel?(s(),t(C,{key:0,class:"emoji-panel"},{default:o(()=>[n(C,{class:"emoji-header"},{default:o(()=>[n(T,{class:"emoji-title"},{default:o(()=>[l("表情")]),_:1})]),_:1}),n(y,{class:"emoji-scroll","scroll-y":""},{default:o(()=>[n(C,{class:"emoji-grid"},{default:o(()=>[(s(!0),r(u,null,d(M.emojiList,e=>(s(),t(C,{key:e,class:"emoji-item",onClick:s=>I.insertEmoji(e)},{default:o(()=>[n(T,{class:"emoji-text"},{default:o(()=>[l(c(e),1)]),_:2},1024)]),_:2},1032,["onClick"]))),128))]),_:1})]),_:1})]),_:1})):i("v-if",!0),i(" 更多功能面板 "),M.showMorePanel?(s(),t(C,{key:1,class:"more-panel"},{default:o(()=>[n(C,{class:"more-header"},{default:o(()=>[n(T,{class:"more-title"},{default:o(()=>[l("更多功能")]),_:1})]),_:1}),n(C,{class:"more-grid"},{default:o(()=>[n(C,{class:"more-item",onClick:I.chooseImage},{default:o(()=>[n(C,{class:"more-icon-wrapper"},{default:o(()=>[n(T,{class:"more-icon"},{default:o(()=>[l("📷")]),_:1})]),_:1}),n(T,{class:"more-text"},{default:o(()=>[l("相册")]),_:1})]),_:1},8,["onClick"]),n(C,{class:"more-item",onClick:I.takePhoto},{default:o(()=>[n(C,{class:"more-icon-wrapper"},{default:o(()=>[n(T,{class:"more-icon"},{default:o(()=>[l("📸")]),_:1})]),_:1}),n(T,{class:"more-text"},{default:o(()=>[l("拍照")]),_:1})]),_:1},8,["onClick"]),n(C,{class:"more-item",onClick:I.chooseVideo},{default:o(()=>[n(C,{class:"more-icon-wrapper"},{default:o(()=>[n(T,{class:"more-icon"},{default:o(()=>[l("🎥")]),_:1})]),_:1}),n(T,{class:"more-text"},{default:o(()=>[l("视频")]),_:1})]),_:1},8,["onClick"]),n(C,{class:"more-item",onClick:I.chooseFile},{default:o(()=>[n(C,{class:"more-icon-wrapper"},{default:o(()=>[n(T,{class:"more-icon"},{default:o(()=>[l("📁")]),_:1})]),_:1}),n(T,{class:"more-text"},{default:o(()=>[l("文件")]),_:1})]),_:1},8,["onClick"]),n(C,{class:"more-item",onClick:I.shareLocation},{default:o(()=>[n(C,{class:"more-icon-wrapper"},{default:o(()=>[n(T,{class:"more-icon"},{default:o(()=>[l("📍")]),_:1})]),_:1}),n(T,{class:"more-text"},{default:o(()=>[l("位置")]),_:1})]),_:1},8,["onClick"]),n(C,{class:"more-item",onClick:I.sendVoice},{default:o(()=>[n(C,{class:"more-icon-wrapper"},{default:o(()=>[n(T,{class:"more-icon"},{default:o(()=>[l("🎤")]),_:1})]),_:1}),n(T,{class:"more-text"},{default:o(()=>[l("语音")]),_:1})]),_:1},8,["onClick"]),n(C,{class:"more-item",onClick:I.sendRedPacket},{default:o(()=>[n(C,{class:"more-icon-wrapper"},{default:o(()=>[n(T,{class:"more-icon"},{default:o(()=>[l("🧧")]),_:1})]),_:1}),n(T,{class:"more-text"},{default:o(()=>[l("红包")]),_:1})]),_:1},8,["onClick"]),n(C,{class:"more-item",onClick:I.transfer},{default:o(()=>[n(C,{class:"more-icon-wrapper"},{default:o(()=>[n(T,{class:"more-icon"},{default:o(()=>[l("💰")]),_:1})]),_:1}),n(T,{class:"more-text"},{default:o(()=>[l("转账")]),_:1})]),_:1},8,["onClick"])]),_:1})]),_:1})):i("v-if",!0)]),_:1},8,["class"]),i(" 聊天菜单弹窗 "),M.showChatMenuModal?(s(),t(C,{key:0,class:"chat-menu-modal",onClick:I.hideChatMenu},{default:o(()=>[n(C,{class:"chat-menu",onClick:_[1]||(_[1]=m(()=>{},["stop"]))},{default:o(()=>[n(C,{class:"chat-menu-item",onClick:I.viewProfile},{default:o(()=>[n(T,{class:"menu-icon"},{default:o(()=>[l("👤")]),_:1}),n(T,{class:"menu-text"},{default:o(()=>[l("查看资料")]),_:1})]),_:1},8,["onClick"]),n(C,{class:"chat-menu-item",onClick:I.clearHistory},{default:o(()=>[n(T,{class:"menu-icon"},{default:o(()=>[l("🗑️")]),_:1}),n(T,{class:"menu-text"},{default:o(()=>[l("清空聊天记录")]),_:1})]),_:1},8,["onClick"]),n(C,{class:"chat-menu-item",onClick:I.setBackground},{default:o(()=>[n(T,{class:"menu-icon"},{default:o(()=>[l("🖼️")]),_:1}),n(T,{class:"menu-text"},{default:o(()=>[l("设置背景")]),_:1})]),_:1},8,["onClick"])]),_:1})]),_:1},8,["onClick"])):i("v-if",!0)]),_:1})}],["__scopeId","data-v-c631c451"]]);export{C as default};

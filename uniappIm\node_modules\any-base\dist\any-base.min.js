!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var t;t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,t.AnyBase=e()}}(function(){return function e(t,n,r){function o(f,s){if(!n[f]){if(!t[f]){var u="function"==typeof require&&require;if(!s&&u)return u(f,!0);if(i)return i(f,!0);var c=new Error("Cannot find module '"+f+"'");throw c.code="MODULE_NOT_FOUND",c}var l=n[f]={exports:{}};t[f][0].call(l.exports,function(e){var n=t[f][1][e];return o(n||e)},l,l.exports,e,t,n,r)}return n[f].exports}for(var i="function"==typeof require&&require,f=0;f<r.length;f++)o(r[f]);return o}({1:[function(e,t,n){"use strict";function r(e,t){if(!(e&&t&&e.length&&t.length))throw new Error("Bad alphabet");this.srcAlphabet=e,this.dstAlphabet=t}r.prototype.convert=function(e){var t,n,r,o={},i=this.srcAlphabet.length,f=this.dstAlphabet.length,s=e.length,u="string"==typeof e?"":[];if(this.srcAlphabet===this.dstAlphabet)return e;for(t=0;t<s;t++)o[t]=this.srcAlphabet.indexOf(e[t]);do{for(n=0,r=0,t=0;t<s;t++)n=n*i+o[t],n>=f?(o[r++]=parseInt(n/f,10),n%=f):r>0&&(o[r++]=0);s=r,u=this.dstAlphabet.slice(n,n+1).concat(u)}while(0!==r);return u},t.exports=r},{}],2:[function(e,t,n){function r(e,t){var n=new o(e,t);return function(e){return n.convert(e)}}var o=e("./src/converter");r.BIN="01",r.OCT="01234567",r.DEC="0123456789",r.HEX="0123456789abcdef",t.exports=r},{"./src/converter":1}]},{},[2])(2)});

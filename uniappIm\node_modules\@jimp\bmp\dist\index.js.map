{"version": 3, "sources": ["../src/index.js"], "names": ["MIME_TYPE", "MIME_TYPE_SECOND", "toAGBR", "image", "bitmap", "width", "height", "x", "y", "index", "red", "data", "green", "blue", "alpha", "fromAGBR", "is_with_alpha", "decode", "BMP", "encode", "mime", "constants", "MIME_BMP", "MIME_X_MS_BMP", "decoders", "encoders"], "mappings": ";;;;;;;;;;;AAAA;;AACA;;AAEA,IAAMA,SAAS,GAAG,WAAlB;AACA,IAAMC,gBAAgB,GAAG,gBAAzB;;AAEA,SAASC,MAAT,CAAgBC,KAAhB,EAAuB;AACrB,SAAO,iBAAKA,KAAL,EAAY,CAAZ,EAAe,CAAf,EAAkBA,KAAK,CAACC,MAAN,CAAaC,KAA/B,EAAsCF,KAAK,CAACC,MAAN,CAAaE,MAAnD,EAA2D,UAChEC,CADgE,EAEhEC,CAFgE,EAGhEC,KAHgE,EAIhE;AACA,QAAMC,GAAG,GAAG,KAAKN,MAAL,CAAYO,IAAZ,CAAiBF,KAAK,GAAG,CAAzB,CAAZ;AACA,QAAMG,KAAK,GAAG,KAAKR,MAAL,CAAYO,IAAZ,CAAiBF,KAAK,GAAG,CAAzB,CAAd;AACA,QAAMI,IAAI,GAAG,KAAKT,MAAL,CAAYO,IAAZ,CAAiBF,KAAK,GAAG,CAAzB,CAAb;AACA,QAAMK,KAAK,GAAG,KAAKV,MAAL,CAAYO,IAAZ,CAAiBF,KAAK,GAAG,CAAzB,CAAd;AAEA,SAAKL,MAAL,CAAYO,IAAZ,CAAiBF,KAAK,GAAG,CAAzB,IAA8BK,KAA9B;AACA,SAAKV,MAAL,CAAYO,IAAZ,CAAiBF,KAAK,GAAG,CAAzB,IAA8BI,IAA9B;AACA,SAAKT,MAAL,CAAYO,IAAZ,CAAiBF,KAAK,GAAG,CAAzB,IAA8BG,KAA9B;AACA,SAAKR,MAAL,CAAYO,IAAZ,CAAiBF,KAAK,GAAG,CAAzB,IAA8BC,GAA9B;AACD,GAdM,EAcJN,MAdH;AAeD;;AAED,SAASW,QAAT,CAAkBX,MAAlB,EAA0B;AACxB,SAAO,iBAAK;AAAEA,IAAAA,MAAM,EAANA;AAAF,GAAL,EAAiB,CAAjB,EAAoB,CAApB,EAAuBA,MAAM,CAACC,KAA9B,EAAqCD,MAAM,CAACE,MAA5C,EAAoD,UACzDC,CADyD,EAEzDC,CAFyD,EAGzDC,KAHyD,EAIzD;AACA,QAAMK,KAAK,GAAG,KAAKV,MAAL,CAAYO,IAAZ,CAAiBF,KAAK,GAAG,CAAzB,CAAd;AACA,QAAMI,IAAI,GAAG,KAAKT,MAAL,CAAYO,IAAZ,CAAiBF,KAAK,GAAG,CAAzB,CAAb;AACA,QAAMG,KAAK,GAAG,KAAKR,MAAL,CAAYO,IAAZ,CAAiBF,KAAK,GAAG,CAAzB,CAAd;AACA,QAAMC,GAAG,GAAG,KAAKN,MAAL,CAAYO,IAAZ,CAAiBF,KAAK,GAAG,CAAzB,CAAZ;AAEA,SAAKL,MAAL,CAAYO,IAAZ,CAAiBF,KAAK,GAAG,CAAzB,IAA8BC,GAA9B;AACA,SAAKN,MAAL,CAAYO,IAAZ,CAAiBF,KAAK,GAAG,CAAzB,IAA8BG,KAA9B;AACA,SAAKR,MAAL,CAAYO,IAAZ,CAAiBF,KAAK,GAAG,CAAzB,IAA8BI,IAA9B;AACA,SAAKT,MAAL,CAAYO,IAAZ,CAAiBF,KAAK,GAAG,CAAzB,IAA8BL,MAAM,CAACY,aAAP,GAAuBF,KAAvB,GAA+B,IAA7D;AACD,GAdM,EAcJV,MAdH;AAeD;;AAED,IAAMa,MAAM,GAAG,SAATA,MAAS,CAAAN,IAAI;AAAA,SAAII,QAAQ,CAACG,kBAAID,MAAJ,CAAWN,IAAX,CAAD,CAAZ;AAAA,CAAnB;;AACA,IAAMQ,MAAM,GAAG,SAATA,MAAS,CAAAhB,KAAK;AAAA,SAAIe,kBAAIC,MAAJ,CAAWjB,MAAM,CAACC,KAAD,CAAjB,EAA0BQ,IAA9B;AAAA,CAApB;;eAEe;AAAA;;AAAA,SAAO;AACpBS,IAAAA,IAAI,uCAAKpB,SAAL,EAAiB,CAAC,KAAD,CAAjB,CADgB;AAGpBqB,IAAAA,SAAS,EAAE;AACTC,MAAAA,QAAQ,EAAEtB,SADD;AAETuB,MAAAA,aAAa,EAAEtB;AAFN,KAHS;AAQpBuB,IAAAA,QAAQ,+DACLxB,SADK,EACOiB,MADP,+CAELhB,gBAFK,EAEcgB,MAFd,aARY;AAapBQ,IAAAA,QAAQ,+DACLzB,SADK,EACOmB,MADP,+CAELlB,gBAFK,EAEckB,MAFd;AAbY,GAAP;AAAA,C", "sourcesContent": ["import BMP from 'bmp-js';\nimport { scan } from '@jimp/utils';\n\nconst MIME_TYPE = 'image/bmp';\nconst MIME_TYPE_SECOND = 'image/x-ms-bmp';\n\nfunction toAGBR(image) {\n  return scan(image, 0, 0, image.bitmap.width, image.bitmap.height, function(\n    x,\n    y,\n    index\n  ) {\n    const red = this.bitmap.data[index + 0];\n    const green = this.bitmap.data[index + 1];\n    const blue = this.bitmap.data[index + 2];\n    const alpha = this.bitmap.data[index + 3];\n\n    this.bitmap.data[index + 0] = alpha;\n    this.bitmap.data[index + 1] = blue;\n    this.bitmap.data[index + 2] = green;\n    this.bitmap.data[index + 3] = red;\n  }).bitmap;\n}\n\nfunction fromAGBR(bitmap) {\n  return scan({ bitmap }, 0, 0, bitmap.width, bitmap.height, function(\n    x,\n    y,\n    index\n  ) {\n    const alpha = this.bitmap.data[index + 0];\n    const blue = this.bitmap.data[index + 1];\n    const green = this.bitmap.data[index + 2];\n    const red = this.bitmap.data[index + 3];\n\n    this.bitmap.data[index + 0] = red;\n    this.bitmap.data[index + 1] = green;\n    this.bitmap.data[index + 2] = blue;\n    this.bitmap.data[index + 3] = bitmap.is_with_alpha ? alpha : 0xff;\n  }).bitmap;\n}\n\nconst decode = data => fromAGBR(BMP.decode(data));\nconst encode = image => BMP.encode(toAGBR(image)).data;\n\nexport default () => ({\n  mime: { [MIME_TYPE]: ['bmp'] },\n\n  constants: {\n    MIME_BMP: MIME_TYPE,\n    MIME_X_MS_BMP: MIME_TYPE_SECOND\n  },\n\n  decoders: {\n    [MIME_TYPE]: decode,\n    [MIME_TYPE_SECOND]: decode\n  },\n\n  encoders: {\n    [MIME_TYPE]: encode,\n    [MIME_TYPE_SECOND]: encode\n  }\n});\n"], "file": "index.js"}
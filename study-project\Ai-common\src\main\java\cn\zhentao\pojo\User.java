package cn.zhentao.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 用户信息表
 * @TableName user
 */
@TableName(value = "user")
@Data
@EqualsAndHashCode(callSuper = true)
public class User extends BaseEntity implements UserDetails{

    /**
     * 用户ID (继承自BaseEntity的id字段)
     */
    // @TableId注解已在BaseEntity中定义，此处不需要重复定义
    // 使用BaseEntity的id字段作为主键

    /**
     * 用户账号
     */
    private String userName;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户类型 (00系统用户)
     */
    private String userType;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 手机号码
     */
    private String phonenumber;

    /**
     * 用户性别 (0男 1女 2未知)
     */
    private String sex;

    /**
     * 头像地址
     */
    private String avatar;

    /**
     * 密码
     */
    private String password;

    /**
     * 帐号状态 (0正常 1停用)
     */
    private String status;

    /**
     * 删除标志 (0代表存在 2代表删除)
     */
    private String delFlag;

    /**
     * 最后登录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime loginDate;

    /**
     * 人脸识别特征数据
     */
    @TableField(value = "face_features")
    private String faceFeatures;

    /**
     * 人脸图像路径
     */
    @TableField(value = "face_image_path")
    private String faceImagePath;

    /**
     * 是否启用人脸识别 (0禁用 1启用)
     */
    @TableField(value = "face_enabled")
    private Integer faceEnabled;

    /**
     * 人脸识别训练状态 (0未训练 1已训练)
     */
    @TableField(value = "face_trained")
    private Integer faceTrained;

    /**
     * 最后人脸识别时间
     */
    @TableField(value = "last_face_login")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastFaceLogin;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private List<String> pnames;
    @TableField(exist = false)
    private List<String> rnames;

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        if(pnames!=null && pnames.size()>0){
            List<GrantedAuthority> list=new ArrayList<>();
            for(String pname:pnames){
                list.add(new SimpleGrantedAuthority(pname));
            }
            return list;
        }
        return null;
    }
    @Override
    public String getUsername() {
        return null;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }
}
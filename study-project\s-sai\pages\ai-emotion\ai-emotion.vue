<template>
  <view class="container">
    <!-- 顶部标题 -->
    <view class="header">
      <view class="header-icon">💝</view>
      <view class="header-info">
        <text class="header-title">情感陪伴</text>
        <text class="header-desc">情感识别回应，温暖陪伴</text>
      </view>
    </view>

    <!-- 情感状态选择 -->
    <view class="emotion-selector">
      <text class="selector-title">💭 当前心情</text>
      <view class="emotion-grid">
        <view 
          v-for="(emotion, index) in emotions" 
          :key="index"
          class="emotion-item"
          :class="{ active: selectedEmotion === emotion.key }"
          @tap="selectEmotion(emotion.key)"
        >
          <text class="emotion-icon">{{ emotion.icon }}</text>
          <text class="emotion-name">{{ emotion.name }}</text>
        </view>
      </view>
    </view>

    <!-- 聊天区域 -->
    <scroll-view class="chat-area" scroll-y="true" :scroll-top="scrollTop" scroll-with-animation="true">
      <view class="message-list">
        <view v-for="(message, index) in messages" :key="index" class="message-item" :class="message.type">
          <view class="message-avatar">
            <text class="avatar-text">{{ message.type === 'user' ? '我' : '💝' }}</text>
          </view>
          <view class="message-content">
            <view class="message-bubble">
              <text class="message-text">{{ message.content }}</text>
              <text class="message-time">{{ message.time }}</text>
            </view>
          </view>
        </view>
        
        <!-- AI正在输入提示 -->
        <view v-if="isAiTyping" class="message-item ai typing">
          <view class="message-avatar">
            <text class="avatar-text">💝</text>
          </view>
          <view class="message-content">
            <view class="message-bubble">
              <view class="typing-indicator">
                <view class="dot"></view>
                <view class="dot"></view>
                <view class="dot"></view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 输入区域 -->
    <view class="input-area">
      <view class="input-container">
        <input 
          class="input-field" 
          v-model="inputText" 
          placeholder="分享您的感受，我会用心倾听..." 
          :disabled="isLoading"
          @confirm="sendMessage"
        />
        <button 
          class="send-btn" 
          :class="{ disabled: !inputText.trim() || isLoading }" 
          @tap="sendMessage"
        >
          {{ isLoading ? '发送中' : '发送' }}
        </button>
      </view>
    </view>

    <!-- 情感建议 -->
    <view v-if="messages.length === 0" class="emotion-suggestions">
      <text class="suggestions-title">💡 可以这样表达</text>
      <view class="suggestions-list">
        <view 
          v-for="(suggestion, index) in currentSuggestions" 
          :key="index" 
          class="suggestion-item" 
          @tap="useSuggestion(suggestion)"
        >
          <text class="suggestion-text">{{ suggestion }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      messages: [],
      inputText: '',
      isLoading: false,
      isAiTyping: false,
      scrollTop: 0,
      userId: 123,
      selectedEmotion: 'neutral',
      emotions: [
        { key: 'happy', name: '开心', icon: '😊' },
        { key: 'sad', name: '难过', icon: '😢' },
        { key: 'angry', name: '生气', icon: '😠' },
        { key: 'worried', name: '担心', icon: '😰' },
        { key: 'excited', name: '兴奋', icon: '🤩' },
        { key: 'tired', name: '疲惫', icon: '😴' },
        { key: 'confused', name: '困惑', icon: '😕' },
        { key: 'neutral', name: '平静', icon: '😐' }
      ],
      emotionSuggestions: {
        happy: [
          '我今天很开心，因为...',
          '有件好事想分享',
          '心情特别好',
          '感觉很幸福'
        ],
        sad: [
          '我今天心情不太好',
          '感觉有点难过',
          '遇到了一些困难',
          '需要一些安慰'
        ],
        angry: [
          '我今天很生气',
          '有些事情让我很不爽',
          '感觉很愤怒',
          '心情很烦躁'
        ],
        worried: [
          '我有些担心',
          '对未来感到不安',
          '有些事情让我焦虑',
          '心里有些不安'
        ],
        excited: [
          '我超级兴奋！',
          '有个好消息要分享',
          '感觉充满活力',
          '今天特别有动力'
        ],
        tired: [
          '我感觉很累',
          '今天很疲惫',
          '需要休息一下',
          '感觉没有精神'
        ],
        confused: [
          '我有些困惑',
          '不知道该怎么办',
          '感觉很迷茫',
          '需要一些建议'
        ],
        neutral: [
          '今天过得还好',
          '心情比较平静',
          '想聊聊天',
          '分享一下今天的事'
        ]
      }
    }
  },
  computed: {
    currentSuggestions() {
      return this.emotionSuggestions[this.selectedEmotion] || this.emotionSuggestions.neutral;
    }
  },
  onLoad() {
    this.loadUserInfo();
    this.addWelcomeMessage();
  },
  methods: {
    loadUserInfo() {
      const userInfo = uni.getStorageSync('userInfo');
      if (userInfo && userInfo.id) {
        this.userId = userInfo.id;
      }
    },

    addWelcomeMessage() {
      this.messages.push({
        type: 'ai',
        content: '你好！我是你的情感陪伴助手💝\n\n无论你现在的心情如何，我都会用心倾听，给你温暖的陪伴和支持。请选择你当前的心情，或者直接告诉我你的感受吧～',
        time: this.getCurrentTime()
      });
      this.scrollToBottom();
    },

    selectEmotion(emotionKey) {
      this.selectedEmotion = emotionKey;
      const emotion = this.emotions.find(e => e.key === emotionKey);
      if (emotion && this.messages.length > 1) {
        this.messages.push({
          type: 'ai',
          content: `我看到你现在的心情是${emotion.icon}${emotion.name}，想和我聊聊吗？我会认真倾听你的感受。`,
          time: this.getCurrentTime()
        });
        this.scrollToBottom();
      }
    },

    useSuggestion(suggestion) {
      this.inputText = suggestion;
      this.sendMessage();
    },

    async sendMessage() {
      if (!this.inputText.trim() || this.isLoading) return;

      const userMessage = {
        type: 'user',
        content: this.inputText.trim(),
        time: this.getCurrentTime()
      };

      this.messages.push(userMessage);
      const message = this.inputText.trim();
      this.inputText = '';
      this.isLoading = true;
      this.isAiTyping = true;
      this.scrollToBottom();

      try {
        // 调用AI情感陪伴API
        const response = await this.callEmotionAPI(message);
        
        this.isAiTyping = false;
        
        if (response && response.success) {
          this.messages.push({
            type: 'ai',
            content: response.response || '我理解你的感受，请继续和我分享吧。',
            time: this.getCurrentTime()
          });
        } else {
          this.messages.push({
            type: 'ai',
            content: '我现在有些忙，但我一直在这里陪伴你。请继续告诉我你的感受。',
            time: this.getCurrentTime()
          });
        }
      } catch (error) {
        this.isAiTyping = false;
        this.messages.push({
          type: 'ai',
          content: '抱歉，我现在无法很好地回应你，但请记住，你的感受很重要，我会一直在这里支持你。',
          time: this.getCurrentTime()
        });
        console.error('API调用失败:', error);
      }

      this.isLoading = false;
      this.scrollToBottom();
    },

    async callEmotionAPI(message) {
      const apiUrl = 'http://localhost:8082/api/ai/miniprogram/emotion/companion';
      
      const response = await uni.request({
        url: apiUrl,
        method: 'POST',
        header: {
          'Content-Type': 'application/json'
        },
        data: {
          userId: this.userId,
          message: message
        }
      });

      if (response.statusCode === 200 && response.data.code === 200) {
        return response.data.data;
      } else {
        throw new Error('API调用失败');
      }
    },

    getCurrentTime() {
      const now = new Date();
      return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    },

    scrollToBottom() {
      this.$nextTick(() => {
        this.scrollTop = 999999;
      });
    }
  }
}
</script>

<style>
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #ffeef8 0%, #f0f8ff 100%);
}

.header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  border-bottom: 1rpx solid rgba(233, 236, 239, 0.5);
}

.header-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.header-info {
  flex: 1;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  display: block;
}

.header-desc {
  font-size: 24rpx;
  color: #6c757d;
  margin-top: 8rpx;
  display: block;
}

.emotion-selector {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  padding: 30rpx;
  border-bottom: 1rpx solid rgba(233, 236, 239, 0.5);
}

.selector-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20rpx;
  display: block;
}

.emotion-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.emotion-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 10rpx;
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 0.7);
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.emotion-item.active {
  background: rgba(0, 123, 255, 0.1);
  border-color: #007bff;
  transform: scale(1.05);
}

.emotion-item:active {
  transform: scale(0.95);
}

.emotion-icon {
  font-size: 36rpx;
  margin-bottom: 8rpx;
}

.emotion-name {
  font-size: 22rpx;
  color: #495057;
  font-weight: 500;
}

.emotion-item.active .emotion-name {
  color: #007bff;
  font-weight: bold;
}

.chat-area {
  flex: 1;
  padding: 20rpx;
}

.message-item {
  display: flex;
  margin-bottom: 30rpx;
  animation: fadeInUp 0.3s ease;
}

.message-item.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6b9d, #c44569);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 157, 0.3);
}

.message-item.user .message-avatar {
  background: linear-gradient(135deg, #007bff, #0056b3);
  box-shadow: 0 4rpx 12rpx rgba(0, 123, 255, 0.3);
}

.avatar-text {
  font-size: 24rpx;
  color: #fff;
  font-weight: bold;
}

.message-content {
  flex: 1;
  max-width: 70%;
}

.message-bubble {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  padding: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.message-item.user .message-bubble {
  background: linear-gradient(135deg, #007bff, #0056b3);
}

.message-text {
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
  display: block;
  white-space: pre-wrap;
}

.message-item.user .message-text {
  color: #fff;
}

.message-time {
  font-size: 20rpx;
  color: #999;
  margin-top: 10rpx;
  display: block;
}

.message-item.user .message-time {
  color: rgba(255, 255, 255, 0.8);
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #ff6b9d;
  animation: typing 1.4s infinite;
}

.dot:nth-child(2) { animation-delay: 0.2s; }
.dot:nth-child(3) { animation-delay: 0.4s; }

@keyframes typing {
  0%, 60%, 100% { opacity: 0.3; }
  30% { opacity: 1; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.input-area {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  padding: 20rpx;
  border-top: 1rpx solid rgba(233, 236, 239, 0.5);
}

.input-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.input-field {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  border: 1rpx solid rgba(233, 236, 239, 0.5);
  border-radius: 40rpx;
  font-size: 28rpx;
  background: rgba(248, 249, 250, 0.8);
}

.send-btn {
  width: 120rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #ff6b9d, #c44569);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 157, 0.3);
}

.send-btn.disabled {
  background: #ccc;
  color: #999;
  box-shadow: none;
}

.emotion-suggestions {
  position: absolute;
  bottom: 140rpx;
  left: 20rpx;
  right: 20rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.suggestions-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20rpx;
  display: block;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.suggestion-item {
  padding: 20rpx;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 12rpx;
  border: 1rpx solid rgba(233, 236, 239, 0.5);
  transition: all 0.3s ease;
}

.suggestion-item:active {
  background: rgba(233, 236, 239, 0.8);
  transform: scale(0.98);
}

.suggestion-text {
  font-size: 26rpx;
  color: #495057;
}
</style>

# AI对话功能数据库表创建脚本
# PowerShell版本

Write-Host "====================================" -ForegroundColor Green
Write-Host "AI对话功能数据库表创建脚本" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green
Write-Host ""

Write-Host "正在检查MySQL客户端..." -ForegroundColor Yellow

# 检查MySQL客户端是否可用
$mysqlPath = Get-Command mysql -ErrorAction SilentlyContinue
if (-not $mysqlPath) {
    Write-Host "错误: 未找到MySQL客户端工具" -ForegroundColor Red
    Write-Host "请确保MySQL客户端已安装并添加到系统PATH中" -ForegroundColor Red
    Write-Host ""
    Write-Host "手动执行方法:" -ForegroundColor Yellow
    Write-Host "1. 打开MySQL客户端或数据库管理工具" -ForegroundColor White
    Write-Host "2. 连接到数据库: **************:3306/tutor" -ForegroundColor White
    Write-Host "3. 执行文件: ai_conversation_tables.sql" -ForegroundColor White
    Write-Host ""
    Read-Host "按Enter键退出"
    exit 1
}

Write-Host "MySQL客户端已找到: $($mysqlPath.Source)" -ForegroundColor Green
Write-Host ""

Write-Host "正在连接数据库并执行SQL脚本..." -ForegroundColor Yellow
Write-Host "数据库地址: **************:3306" -ForegroundColor Cyan
Write-Host "数据库名称: tutor" -ForegroundColor Cyan
Write-Host ""

# 执行SQL脚本
try {
    $sqlContent = Get-Content "ai_conversation_tables.sql" -Raw -Encoding UTF8
    $sqlContent | mysql -h ************** -P 3306 -u root -p tutor
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "====================================" -ForegroundColor Green
        Write-Host "表创建成功！" -ForegroundColor Green
        Write-Host "====================================" -ForegroundColor Green
        Write-Host ""
        Write-Host "已创建以下表:" -ForegroundColor Yellow
        Write-Host "- ai_conversation        (AI对话会话表)" -ForegroundColor White
        Write-Host "- ai_message            (AI消息记录表)" -ForegroundColor White
        Write-Host "- ai_conversation_config (AI对话配置表)" -ForegroundColor White
        Write-Host "- ai_usage_statistics   (AI使用统计表)" -ForegroundColor White
        Write-Host "- ai_feedback           (AI反馈表)" -ForegroundColor White
        Write-Host ""
        Write-Host "请查看 'AI对话功能数据库设计说明.md' 了解详细设计" -ForegroundColor Cyan
        
        # 询问是否插入示例数据
        Write-Host ""
        $insertSample = Read-Host "是否插入示例数据进行测试？(y/n)"
        if ($insertSample -eq "y" -or $insertSample -eq "Y") {
            Write-Host "正在插入示例数据..." -ForegroundColor Yellow
            $sampleContent = Get-Content "ai_conversation_sample_data.sql" -Raw -Encoding UTF8
            $sampleContent | mysql -h ************** -P 3306 -u root -p tutor
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "示例数据插入成功！" -ForegroundColor Green
            } else {
                Write-Host "示例数据插入失败，请检查数据库连接" -ForegroundColor Red
            }
        }
    } else {
        Write-Host ""
        Write-Host "====================================" -ForegroundColor Red
        Write-Host "执行失败！" -ForegroundColor Red
        Write-Host "====================================" -ForegroundColor Red
        Write-Host ""
        Write-Host "可能的原因:" -ForegroundColor Yellow
        Write-Host "1. 数据库连接失败" -ForegroundColor White
        Write-Host "2. 权限不足" -ForegroundColor White
        Write-Host "3. SQL语法错误" -ForegroundColor White
        Write-Host ""
        Write-Host "请检查网络连接和数据库权限" -ForegroundColor Yellow
    }
} catch {
    Write-Host "执行过程中发生错误: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Read-Host "按Enter键退出"

/* es-module-lexer 1.7.0 */
let e,a,r,i=2<<19;const s=1===new Uint8Array(new Uint16Array([1]).buffer)[0]?function(e,a){const r=e.length;let i=0;for(;i<r;)a[i]=e.charCodeAt(i++)}:function(e,a){const r=e.length;let i=0;for(;i<r;){const r=e.charCodeAt(i);a[i++]=(255&r)<<8|r>>>8}},f="xportmportlassforetaourceeferromsyncunctionssertvoyiedelecontininstantybreareturdebuggeawaithrwhileifcatcfinallels";let t,c,n;export function parse(k,l="@"){t=k,c=l;const u=2*t.length+(2<<18);if(u>i||!e){for(;u>i;)i*=2;a=new ArrayBuffer(i),s(f,new Uint16Array(a,16,114)),e=function(e,a,r){"use asm";var i=new e.Int8Array(r),s=new e.Int16Array(r),f=new e.Int32Array(r),t=new e.Uint8Array(r),c=new e.Uint16Array(r),n=1040;function b(){var e=0,a=0,r=0,t=0,c=0,b=0,u=0;u=n;n=n+10240|0;i[812]=1;i[811]=0;s[403]=0;s[404]=0;f[71]=f[2];i[813]=0;f[70]=0;i[810]=0;f[72]=u+2048;f[73]=u;i[814]=0;e=(f[3]|0)+-2|0;f[74]=e;a=e+(f[68]<<1)|0;f[75]=a;e:while(1){r=e+2|0;f[74]=r;if(e>>>0>=a>>>0){t=18;break}a:do{switch(s[r>>1]|0){case 9:case 10:case 11:case 12:case 13:case 32:break;case 101:{if((((s[404]|0)==0?H(r)|0:0)?(m(e+4|0,16,10)|0)==0:0)?(k(),(i[812]|0)==0):0){t=9;break e}else t=17;break}case 105:{if(H(r)|0?(m(e+4|0,26,10)|0)==0:0){l();t=17}else t=17;break}case 59:{t=17;break}case 47:switch(s[e+4>>1]|0){case 47:{P();break a}case 42:{y(1);break a}default:{t=16;break e}}default:{t=16;break e}}}while(0);if((t|0)==17){t=0;f[71]=f[74]}e=f[74]|0;a=f[75]|0}if((t|0)==9){e=f[74]|0;f[71]=e;t=19}else if((t|0)==16){i[812]=0;f[74]=e;t=19}else if((t|0)==18)if(!(i[810]|0)){e=r;t=19}else e=0;do{if((t|0)==19){e:while(1){a=e+2|0;f[74]=a;if(e>>>0>=(f[75]|0)>>>0){t=92;break}a:do{switch(s[a>>1]|0){case 9:case 10:case 11:case 12:case 13:case 32:break;case 101:{if(((s[404]|0)==0?H(a)|0:0)?(m(e+4|0,16,10)|0)==0:0){k();t=91}else t=91;break}case 105:{if(H(a)|0?(m(e+4|0,26,10)|0)==0:0){l();t=91}else t=91;break}case 99:{if((H(a)|0?(m(e+4|0,36,8)|0)==0:0)?V(s[e+12>>1]|0)|0:0){i[814]=1;t=91}else t=91;break}case 40:{r=f[72]|0;e=s[404]|0;t=e&65535;f[r+(t<<3)>>2]=1;a=f[71]|0;s[404]=e+1<<16>>16;f[r+(t<<3)+4>>2]=a;t=91;break}case 41:{a=s[404]|0;if(!(a<<16>>16)){t=36;break e}r=a+-1<<16>>16;s[404]=r;t=s[403]|0;a=t&65535;if(t<<16>>16!=0?(f[(f[72]|0)+((r&65535)<<3)>>2]|0)==5:0){a=f[(f[73]|0)+(a+-1<<2)>>2]|0;r=a+4|0;if(!(f[r>>2]|0))f[r>>2]=(f[71]|0)+2;f[a+12>>2]=e+4;s[403]=t+-1<<16>>16;t=91}else t=91;break}case 123:{t=f[71]|0;r=f[65]|0;e=t;do{if((s[t>>1]|0)==41&(r|0)!=0?(f[r+4>>2]|0)==(t|0):0){a=f[66]|0;f[65]=a;if(!a){f[61]=0;break}else{f[a+32>>2]=0;break}}}while(0);r=f[72]|0;a=s[404]|0;t=a&65535;f[r+(t<<3)>>2]=(i[814]|0)==0?2:6;s[404]=a+1<<16>>16;f[r+(t<<3)+4>>2]=e;i[814]=0;t=91;break}case 125:{e=s[404]|0;if(!(e<<16>>16)){t=49;break e}r=f[72]|0;t=e+-1<<16>>16;s[404]=t;if((f[r+((t&65535)<<3)>>2]|0)==4){h();t=91}else t=91;break}case 39:{v(39);t=91;break}case 34:{v(34);t=91;break}case 47:switch(s[e+4>>1]|0){case 47:{P();break a}case 42:{y(1);break a}default:{e=f[71]|0;a=s[e>>1]|0;r:do{if(!(U(a)|0))if(a<<16>>16==41){r=s[404]|0;if(!(D(f[(f[72]|0)+((r&65535)<<3)+4>>2]|0)|0))t=65}else t=64;else switch(a<<16>>16){case 46:if(((s[e+-2>>1]|0)+-48&65535)<10){t=64;break r}else break r;case 43:if((s[e+-2>>1]|0)==43){t=64;break r}else break r;case 45:if((s[e+-2>>1]|0)==45){t=64;break r}else break r;default:break r}}while(0);if((t|0)==64){r=s[404]|0;t=65}r:do{if((t|0)==65){t=0;if(r<<16>>16!=0?(c=f[72]|0,b=(r&65535)+-1|0,a<<16>>16==102?(f[c+(b<<3)>>2]|0)==1:0):0){if((s[e+-2>>1]|0)==111?$(f[c+(b<<3)+4>>2]|0,44,3)|0:0)break}else t=69;if((t|0)==69?(0,a<<16>>16==125):0){t=f[72]|0;r=r&65535;if(p(f[t+(r<<3)+4>>2]|0)|0)break;if((f[t+(r<<3)>>2]|0)==6)break}if(!(o(e)|0)){switch(a<<16>>16){case 0:break r;case 47:{if(i[813]|0)break r;break}default:{}}t=f[67]|0;if((t|0?e>>>0>=(f[t>>2]|0)>>>0:0)?e>>>0<=(f[t+4>>2]|0)>>>0:0){g();i[813]=0;t=91;break a}r=f[3]|0;do{if(e>>>0<=r>>>0)break;e=e+-2|0;f[71]=e;a=s[e>>1]|0}while(!(E(a)|0));if(F(a)|0){do{if(e>>>0<=r>>>0)break;e=e+-2|0;f[71]=e}while(F(s[e>>1]|0)|0);if(j(e)|0){g();i[813]=0;t=91;break a}}i[813]=1;t=91;break a}}}while(0);g();i[813]=0;t=91;break a}}case 96:{r=f[72]|0;a=s[404]|0;t=a&65535;f[r+(t<<3)+4>>2]=f[71];s[404]=a+1<<16>>16;f[r+(t<<3)>>2]=3;h();t=91;break}default:t=91}}while(0);if((t|0)==91){t=0;f[71]=f[74]}e=f[74]|0}if((t|0)==36){T();e=0;break}else if((t|0)==49){T();e=0;break}else if((t|0)==92){e=(i[810]|0)==0?(s[403]|s[404])<<16>>16==0:0;break}}}while(0);n=u;return e|0}function k(){var e=0,a=0,r=0,t=0,c=0,n=0,b=0,k=0,l=0,o=0,h=0,d=0,C=0,g=0;k=f[74]|0;l=f[67]|0;g=k+12|0;f[74]=g;r=w(1)|0;e=f[74]|0;if(!((e|0)==(g|0)?!(I(r)|0):0))C=3;e:do{if((C|0)==3){a:do{switch(r<<16>>16){case 123:{f[74]=e+2;e=w(1)|0;a=f[74]|0;while(1){if(W(e)|0){v(e);e=(f[74]|0)+2|0;f[74]=e}else{q(e)|0;e=f[74]|0}w(1)|0;e=A(a,e)|0;if(e<<16>>16==44){f[74]=(f[74]|0)+2;e=w(1)|0}if(e<<16>>16==125){C=15;break}g=a;a=f[74]|0;if((a|0)==(g|0)){C=12;break}if(a>>>0>(f[75]|0)>>>0){C=14;break}}if((C|0)==12){T();break e}else if((C|0)==14){T();break e}else if((C|0)==15){i[811]=1;f[74]=(f[74]|0)+2;break a}break}case 42:{f[74]=e+2;w(1)|0;g=f[74]|0;A(g,g)|0;break}default:{i[812]=0;switch(r<<16>>16){case 100:{k=e+14|0;f[74]=k;switch((w(1)|0)<<16>>16){case 97:{a=f[74]|0;if((m(a+2|0,80,8)|0)==0?(c=a+10|0,F(s[c>>1]|0)|0):0){f[74]=c;w(0)|0;C=22}break}case 102:{C=22;break}case 99:{a=f[74]|0;if(((m(a+2|0,36,8)|0)==0?(t=a+10|0,g=s[t>>1]|0,V(g)|0|g<<16>>16==123):0)?(f[74]=t,n=w(1)|0,n<<16>>16!=123):0){d=n;C=31}break}default:{}}r:do{if((C|0)==22?(b=f[74]|0,(m(b+2|0,88,14)|0)==0):0){r=b+16|0;a=s[r>>1]|0;if(!(V(a)|0))switch(a<<16>>16){case 40:case 42:break;default:break r}f[74]=r;a=w(1)|0;if(a<<16>>16==42){f[74]=(f[74]|0)+2;a=w(1)|0}if(a<<16>>16!=40){d=a;C=31}}}while(0);if((C|0)==31?(o=f[74]|0,q(d)|0,h=f[74]|0,h>>>0>o>>>0):0){O(e,k,o,h);f[74]=(f[74]|0)+-2;break e}O(e,k,0,0);f[74]=e+12;break e}case 97:{f[74]=e+10;w(0)|0;e=f[74]|0;C=35;break}case 102:{C=35;break}case 99:{if((m(e+2|0,36,8)|0)==0?(a=e+10|0,E(s[a>>1]|0)|0):0){f[74]=a;g=w(1)|0;C=f[74]|0;q(g)|0;g=f[74]|0;O(C,g,C,g);f[74]=(f[74]|0)+-2;break e}e=e+4|0;f[74]=e;break}case 108:case 118:break;default:break e}if((C|0)==35){f[74]=e+16;e=w(1)|0;if(e<<16>>16==42){f[74]=(f[74]|0)+2;e=w(1)|0}C=f[74]|0;q(e)|0;g=f[74]|0;O(C,g,C,g);f[74]=(f[74]|0)+-2;break e}f[74]=e+6;i[812]=0;r=w(1)|0;e=f[74]|0;r=(q(r)|0|32)<<16>>16==123;t=f[74]|0;if(r){f[74]=t+2;g=w(1)|0;e=f[74]|0;q(g)|0}r:while(1){a=f[74]|0;if((a|0)==(e|0))break;O(e,a,e,a);a=w(1)|0;if(r)switch(a<<16>>16){case 93:case 125:break e;default:{}}e=f[74]|0;if(a<<16>>16!=44){C=51;break}f[74]=e+2;a=w(1)|0;e=f[74]|0;switch(a<<16>>16){case 91:case 123:{C=51;break r}default:{}}q(a)|0}if((C|0)==51)f[74]=e+-2;if(!r)break e;f[74]=t+-2;break e}}}while(0);g=(w(1)|0)<<16>>16==102;e=f[74]|0;if(g?(m(e+2|0,74,6)|0)==0:0){f[74]=e+8;u(k,w(1)|0,0);e=(l|0)==0?248:l+16|0;while(1){e=f[e>>2]|0;if(!e)break e;f[e+12>>2]=0;f[e+8>>2]=0;e=e+16|0}}f[74]=e+-2}}while(0);return}function l(){var e=0,a=0,r=0,t=0,c=0,n=0,b=0;b=f[74]|0;c=b+12|0;f[74]=c;e=w(1)|0;t=f[74]|0;e:do{if(e<<16>>16!=46){if(!(e<<16>>16==115&t>>>0>c>>>0)){if(!(e<<16>>16==100&t>>>0>(b+10|0)>>>0)){t=0;n=28;break}if(m(t+2|0,66,8)|0){a=t;e=100;t=0;n=59;break}e=t+10|0;if(!(V(s[e>>1]|0)|0)){a=t;e=100;t=0;n=59;break}f[74]=e;e=w(1)|0;if(e<<16>>16==42){e=42;t=2;n=61;break}f[74]=t;t=0;n=28;break}if((m(t+2|0,56,10)|0)==0?(r=t+12|0,V(s[r>>1]|0)|0):0){f[74]=r;e=w(1)|0;a=f[74]|0;if((a|0)!=(r|0)){if(e<<16>>16!=102){t=1;n=28;break}if(m(a+2|0,74,6)|0){e=102;t=1;n=59;break}if(!(E(s[a+8>>1]|0)|0)){e=102;t=1;n=59;break}}f[74]=t;t=0;n=28}else{a=t;e=115;t=0;n=59}}else{f[74]=t+2;switch((w(1)|0)<<16>>16){case 109:{e=f[74]|0;if(m(e+2|0,50,6)|0)break e;a=f[71]|0;if(!(G(a)|0)?(s[a>>1]|0)==46:0)break e;d(b,b,e+8|0,2);break e}case 115:{e=f[74]|0;if(m(e+2|0,56,10)|0)break e;a=f[71]|0;if(!(G(a)|0)?(s[a>>1]|0)==46:0)break e;f[74]=e+12;e=w(1)|0;t=1;n=28;break e}case 100:{e=f[74]|0;if(m(e+2|0,66,8)|0)break e;a=f[71]|0;if(!(G(a)|0)?(s[a>>1]|0)==46:0)break e;f[74]=e+10;e=w(1)|0;t=2;n=28;break e}default:break e}}}while(0);e:do{if((n|0)==28){if(e<<16>>16==40){r=f[72]|0;a=s[404]|0;c=a&65535;f[r+(c<<3)>>2]=5;e=f[74]|0;s[404]=a+1<<16>>16;f[r+(c<<3)+4>>2]=e;if((s[f[71]>>1]|0)==46)break;f[74]=e+2;a=w(1)|0;d(b,f[74]|0,0,e);if(!t)e=f[65]|0;else{e=f[65]|0;f[e+28>>2]=(t|0)==1?5:7}c=f[73]|0;b=s[403]|0;s[403]=b+1<<16>>16;f[c+((b&65535)<<2)>>2]=e;switch(a<<16>>16){case 39:{v(39);break}case 34:{v(34);break}default:{f[74]=(f[74]|0)+-2;break e}}e=(f[74]|0)+2|0;f[74]=e;switch((w(1)|0)<<16>>16){case 44:{f[74]=(f[74]|0)+2;w(1)|0;c=f[65]|0;f[c+4>>2]=e;b=f[74]|0;f[c+16>>2]=b;i[c+24>>0]=1;f[74]=b+-2;break e}case 41:{s[404]=(s[404]|0)+-1<<16>>16;b=f[65]|0;f[b+4>>2]=e;f[b+12>>2]=(f[74]|0)+2;i[b+24>>0]=1;s[403]=(s[403]|0)+-1<<16>>16;break e}default:{f[74]=(f[74]|0)+-2;break e}}}if(!((t|0)==0&e<<16>>16==123)){switch(e<<16>>16){case 42:case 39:case 34:{n=61;break e}default:{}}a=f[74]|0;n=59;break}e=f[74]|0;if(s[404]|0){f[74]=e+-2;break}while(1){if(e>>>0>=(f[75]|0)>>>0)break;e=w(1)|0;if(!(W(e)|0)){if(e<<16>>16==125){n=49;break}}else v(e);e=(f[74]|0)+2|0;f[74]=e}if((n|0)==49)f[74]=(f[74]|0)+2;c=(w(1)|0)<<16>>16==102;e=f[74]|0;if(c?m(e+2|0,74,6)|0:0){T();break}f[74]=e+8;e=w(1)|0;if(W(e)|0){u(b,e,0);break}else{T();break}}}while(0);if((n|0)==59)if((a|0)==(c|0))f[74]=b+10;else n=61;do{if((n|0)==61){if(!((e<<16>>16==42|(t|0)!=2)&(s[404]|0)==0)){f[74]=(f[74]|0)+-2;break}e=f[75]|0;a=f[74]|0;while(1){if(a>>>0>=e>>>0){n=68;break}r=s[a>>1]|0;if(W(r)|0){n=66;break}n=a+2|0;f[74]=n;a=n}if((n|0)==66){u(b,r,t);break}else if((n|0)==68){T();break}}}while(0);return}function u(e,a,r){e=e|0;a=a|0;r=r|0;var i=0,t=0;i=(f[74]|0)+2|0;switch(a<<16>>16){case 39:{v(39);t=5;break}case 34:{v(34);t=5;break}default:T()}do{if((t|0)==5){d(e,i,f[74]|0,1);if((r|0)>0)f[(f[65]|0)+28>>2]=(r|0)==1?4:6;f[74]=(f[74]|0)+2;a=w(0)|0;r=a<<16>>16==97;if(r){i=f[74]|0;if(m(i+2|0,102,10)|0)t=13}else{i=f[74]|0;if(!(((a<<16>>16==119?(s[i+2>>1]|0)==105:0)?(s[i+4>>1]|0)==116:0)?(s[i+6>>1]|0)==104:0))t=13}if((t|0)==13){f[74]=i+-2;break}f[74]=i+((r?6:4)<<1);if((w(1)|0)<<16>>16!=123){f[74]=i;break}r=f[74]|0;a=r;e:while(1){f[74]=a+2;a=w(1)|0;switch(a<<16>>16){case 39:{v(39);f[74]=(f[74]|0)+2;a=w(1)|0;break}case 34:{v(34);f[74]=(f[74]|0)+2;a=w(1)|0;break}default:a=q(a)|0}if(a<<16>>16!=58){t=22;break}f[74]=(f[74]|0)+2;switch((w(1)|0)<<16>>16){case 39:{v(39);break}case 34:{v(34);break}default:{t=26;break e}}f[74]=(f[74]|0)+2;switch((w(1)|0)<<16>>16){case 125:{t=31;break e}case 44:break;default:{t=30;break e}}f[74]=(f[74]|0)+2;if((w(1)|0)<<16>>16==125){t=31;break}a=f[74]|0}if((t|0)==22){f[74]=i;break}else if((t|0)==26){f[74]=i;break}else if((t|0)==30){f[74]=i;break}else if((t|0)==31){t=f[65]|0;f[t+16>>2]=r;f[t+12>>2]=(f[74]|0)+2;break}}}while(0);return}function o(e){e=e|0;e:do{switch(s[e>>1]|0){case 100:switch(s[e+-2>>1]|0){case 105:{e=$(e+-4|0,112,2)|0;break e}case 108:{e=$(e+-4|0,116,3)|0;break e}default:{e=0;break e}}case 101:switch(s[e+-2>>1]|0){case 115:switch(s[e+-4>>1]|0){case 108:{e=B(e+-6|0,101)|0;break e}case 97:{e=B(e+-6|0,99)|0;break e}default:{e=0;break e}}case 116:{e=$(e+-4|0,122,4)|0;break e}case 117:{e=$(e+-4|0,130,6)|0;break e}default:{e=0;break e}}case 102:{if((s[e+-2>>1]|0)==111?(s[e+-4>>1]|0)==101:0)switch(s[e+-6>>1]|0){case 99:{e=$(e+-8|0,142,6)|0;break e}case 112:{e=$(e+-8|0,154,2)|0;break e}default:{e=0;break e}}else e=0;break}case 107:{e=$(e+-2|0,158,4)|0;break}case 110:{e=e+-2|0;if(B(e,105)|0)e=1;else e=$(e,166,5)|0;break}case 111:{e=B(e+-2|0,100)|0;break}case 114:{e=$(e+-2|0,176,7)|0;break}case 116:{e=$(e+-2|0,190,4)|0;break}case 119:switch(s[e+-2>>1]|0){case 101:{e=B(e+-4|0,110)|0;break e}case 111:{e=$(e+-4|0,198,3)|0;break e}default:{e=0;break e}}default:e=0}}while(0);return e|0}function h(){var e=0,a=0,r=0,i=0;a=f[75]|0;r=f[74]|0;e:while(1){e=r+2|0;if(r>>>0>=a>>>0){a=10;break}switch(s[e>>1]|0){case 96:{a=7;break e}case 36:{if((s[r+4>>1]|0)==123){a=6;break e}break}case 92:{e=r+4|0;break}default:{}}r=e}if((a|0)==6){e=r+4|0;f[74]=e;a=f[72]|0;i=s[404]|0;r=i&65535;f[a+(r<<3)>>2]=4;s[404]=i+1<<16>>16;f[a+(r<<3)+4>>2]=e}else if((a|0)==7){f[74]=e;r=f[72]|0;i=(s[404]|0)+-1<<16>>16;s[404]=i;if((f[r+((i&65535)<<3)>>2]|0)!=3)T()}else if((a|0)==10){f[74]=e;T()}return}function w(e){e=e|0;var a=0,r=0,i=0;r=f[74]|0;e:do{a=s[r>>1]|0;a:do{if(a<<16>>16!=47)if(e)if(V(a)|0)break;else break e;else if(F(a)|0)break;else break e;else switch(s[r+2>>1]|0){case 47:{P();break a}case 42:{y(e);break a}default:{a=47;break e}}}while(0);i=f[74]|0;r=i+2|0;f[74]=r}while(i>>>0<(f[75]|0)>>>0);return a|0}function d(e,a,r,s){e=e|0;a=a|0;r=r|0;s=s|0;var t=0,c=0;c=f[69]|0;f[69]=c+36;t=f[65]|0;f[((t|0)==0?244:t+32|0)>>2]=c;f[66]=t;f[65]=c;f[c+8>>2]=e;if(2==(s|0)){e=3;t=r}else{t=1==(s|0);e=t?1:2;t=t?r+2|0:0}f[c+12>>2]=t;f[c+28>>2]=e;f[c>>2]=a;f[c+4>>2]=r;f[c+16>>2]=0;f[c+20>>2]=s;a=1==(s|0);i[c+24>>0]=a&1;f[c+32>>2]=0;if(a|2==(s|0))i[811]=1;return}function v(e){e=e|0;var a=0,r=0,i=0,t=0;t=f[75]|0;a=f[74]|0;while(1){i=a+2|0;if(a>>>0>=t>>>0){a=9;break}r=s[i>>1]|0;if(r<<16>>16==e<<16>>16){a=10;break}if(r<<16>>16==92){r=a+4|0;if((s[r>>1]|0)==13){a=a+6|0;a=(s[a>>1]|0)==10?a:r}else a=r}else if(Z(r)|0){a=9;break}else a=i}if((a|0)==9){f[74]=i;T()}else if((a|0)==10)f[74]=i;return}function A(e,a){e=e|0;a=a|0;var r=0,i=0,t=0,c=0;r=f[74]|0;i=s[r>>1]|0;c=(e|0)==(a|0);t=c?0:e;c=c?0:a;if(i<<16>>16==97){f[74]=r+4;r=w(1)|0;e=f[74]|0;if(W(r)|0){v(r);a=(f[74]|0)+2|0;f[74]=a}else{q(r)|0;a=f[74]|0}i=w(1)|0;r=f[74]|0}if((r|0)!=(e|0))O(e,a,t,c);return i|0}function C(){var e=0,a=0,r=0;r=f[75]|0;a=f[74]|0;e:while(1){e=a+2|0;if(a>>>0>=r>>>0){a=6;break}switch(s[e>>1]|0){case 13:case 10:{a=6;break e}case 93:{a=7;break e}case 92:{e=a+4|0;break}default:{}}a=e}if((a|0)==6){f[74]=e;T();e=0}else if((a|0)==7){f[74]=e;e=93}return e|0}function g(){var e=0,a=0,r=0;e:while(1){e=f[74]|0;a=e+2|0;f[74]=a;if(e>>>0>=(f[75]|0)>>>0){r=7;break}switch(s[a>>1]|0){case 13:case 10:{r=7;break e}case 47:break e;case 91:{C()|0;break}case 92:{f[74]=e+4;break}default:{}}}if((r|0)==7)T();return}function p(e){e=e|0;switch(s[e>>1]|0){case 62:{e=(s[e+-2>>1]|0)==61;break}case 41:case 59:{e=1;break}case 104:{e=$(e+-2|0,218,4)|0;break}case 121:{e=$(e+-2|0,226,6)|0;break}case 101:{e=$(e+-2|0,238,3)|0;break}default:e=0}return e|0}function y(e){e=e|0;var a=0,r=0,i=0,t=0,c=0;t=(f[74]|0)+2|0;f[74]=t;r=f[75]|0;while(1){a=t+2|0;if(t>>>0>=r>>>0)break;i=s[a>>1]|0;if(!e?Z(i)|0:0)break;if(i<<16>>16==42?(s[t+4>>1]|0)==47:0){c=8;break}t=a}if((c|0)==8){f[74]=a;a=t+4|0}f[74]=a;return}function m(e,a,r){e=e|0;a=a|0;r=r|0;var s=0,f=0;e:do{if(!r)e=0;else{while(1){s=i[e>>0]|0;f=i[a>>0]|0;if(s<<24>>24!=f<<24>>24)break;r=r+-1|0;if(!r){e=0;break e}else{e=e+1|0;a=a+1|0}}e=(s&255)-(f&255)|0}}while(0);return e|0}function I(e){e=e|0;e:do{switch(e<<16>>16){case 38:case 37:case 33:{e=1;break}default:if((e&-8)<<16>>16==40|(e+-58&65535)<6)e=1;else{switch(e<<16>>16){case 91:case 93:case 94:{e=1;break e}default:{}}e=(e+-123&65535)<4}}}while(0);return e|0}function U(e){e=e|0;e:do{switch(e<<16>>16){case 38:case 37:case 33:break;default:if(!((e+-58&65535)<6|(e+-40&65535)<7&e<<16>>16!=41)){switch(e<<16>>16){case 91:case 94:break e;default:{}}return e<<16>>16!=125&(e+-123&65535)<4|0}}}while(0);return 1}function x(e){e=e|0;var a=0;a=s[e>>1]|0;e:do{if((a+-9&65535)>=5){switch(a<<16>>16){case 160:case 32:{a=1;break e}default:{}}if(I(a)|0)return a<<16>>16!=46|(G(e)|0)|0;else a=0}else a=1}while(0);return a|0}function S(e){e=e|0;var a=0,r=0,i=0,t=0;r=n;n=n+16|0;i=r;f[i>>2]=0;f[68]=e;a=f[3]|0;t=a+(e<<1)|0;e=t+2|0;s[t>>1]=0;f[i>>2]=e;f[69]=e;f[61]=0;f[65]=0;f[63]=0;f[62]=0;f[67]=0;f[64]=0;n=r;return a|0}function O(e,a,r,s){e=e|0;a=a|0;r=r|0;s=s|0;var t=0,c=0;t=f[69]|0;f[69]=t+20;c=f[67]|0;f[((c|0)==0?248:c+16|0)>>2]=t;f[67]=t;f[t>>2]=e;f[t+4>>2]=a;f[t+8>>2]=r;f[t+12>>2]=s;f[t+16>>2]=0;i[811]=1;return}function $(e,a,r){e=e|0;a=a|0;r=r|0;var i=0,s=0;i=e+(0-r<<1)|0;s=i+2|0;e=f[3]|0;if(s>>>0>=e>>>0?(m(s,a,r<<1)|0)==0:0)if((s|0)==(e|0))e=1;else e=x(i)|0;else e=0;return e|0}function j(e){e=e|0;switch(s[e>>1]|0){case 107:{e=$(e+-2|0,158,4)|0;break}case 101:{if((s[e+-2>>1]|0)==117)e=$(e+-4|0,130,6)|0;else e=0;break}default:e=0}return e|0}function B(e,a){e=e|0;a=a|0;var r=0;r=f[3]|0;if(r>>>0<=e>>>0?(s[e>>1]|0)==a<<16>>16:0)if((r|0)==(e|0))r=1;else r=E(s[e+-2>>1]|0)|0;else r=0;return r|0}function E(e){e=e|0;e:do{if((e+-9&65535)<5)e=1;else{switch(e<<16>>16){case 32:case 160:{e=1;break e}default:{}}e=e<<16>>16!=46&(I(e)|0)}}while(0);return e|0}function P(){var e=0,a=0,r=0;e=f[75]|0;r=f[74]|0;e:while(1){a=r+2|0;if(r>>>0>=e>>>0)break;switch(s[a>>1]|0){case 13:case 10:break e;default:r=a}}f[74]=a;return}function q(e){e=e|0;while(1){if(V(e)|0)break;if(I(e)|0)break;e=(f[74]|0)+2|0;f[74]=e;e=s[e>>1]|0;if(!(e<<16>>16)){e=0;break}}return e|0}function z(){var e=0;e=f[(f[63]|0)+20>>2]|0;switch(e|0){case 1:{e=-1;break}case 2:{e=-2;break}default:e=e-(f[3]|0)>>1}return e|0}function D(e){e=e|0;if(!($(e,204,5)|0)?!($(e,44,3)|0):0)e=$(e,214,2)|0;else e=1;return e|0}function F(e){e=e|0;switch(e<<16>>16){case 160:case 32:case 12:case 11:case 9:{e=1;break}default:e=0}return e|0}function G(e){e=e|0;if((s[e>>1]|0)==46?(s[e+-2>>1]|0)==46:0)e=(s[e+-4>>1]|0)==46;else e=0;return e|0}function H(e){e=e|0;if((f[3]|0)==(e|0))e=1;else e=x(e+-2|0)|0;return e|0}function J(){var e=0;e=f[(f[64]|0)+12>>2]|0;if(!e)e=-1;else e=e-(f[3]|0)>>1;return e|0}function K(){var e=0;e=f[(f[63]|0)+12>>2]|0;if(!e)e=-1;else e=e-(f[3]|0)>>1;return e|0}function L(){var e=0;e=f[(f[64]|0)+8>>2]|0;if(!e)e=-1;else e=e-(f[3]|0)>>1;return e|0}function M(){var e=0;e=f[(f[63]|0)+16>>2]|0;if(!e)e=-1;else e=e-(f[3]|0)>>1;return e|0}function N(){var e=0;e=f[(f[63]|0)+4>>2]|0;if(!e)e=-1;else e=e-(f[3]|0)>>1;return e|0}function Q(){var e=0;e=f[63]|0;e=f[((e|0)==0?244:e+32|0)>>2]|0;f[63]=e;return(e|0)!=0|0}function R(){var e=0;e=f[64]|0;e=f[((e|0)==0?248:e+16|0)>>2]|0;f[64]=e;return(e|0)!=0|0}function T(){i[810]=1;f[70]=(f[74]|0)-(f[3]|0)>>1;f[74]=(f[75]|0)+2;return}function V(e){e=e|0;return(e|128)<<16>>16==160|(e+-9&65535)<5|0}function W(e){e=e|0;return e<<16>>16==39|e<<16>>16==34|0}function X(){return(f[(f[63]|0)+8>>2]|0)-(f[3]|0)>>1|0}function Y(){return(f[(f[64]|0)+4>>2]|0)-(f[3]|0)>>1|0}function Z(e){e=e|0;return e<<16>>16==13|e<<16>>16==10|0}function _(){return(f[f[63]>>2]|0)-(f[3]|0)>>1|0}function ee(){return(f[f[64]>>2]|0)-(f[3]|0)>>1|0}function ae(){return t[(f[63]|0)+24>>0]|0|0}function re(e){e=e|0;f[3]=e;return}function ie(){return f[(f[63]|0)+28>>2]|0}function se(){return(i[811]|0)!=0|0}function fe(){return(i[812]|0)!=0|0}function te(){return f[70]|0}function ce(e){e=e|0;n=e+992+15&-16;return 992}return{su:ce,ai:M,e:te,ee:Y,ele:J,els:L,es:ee,f:fe,id:z,ie:N,ip:ae,is:_,it:ie,ms:se,p:b,re:R,ri:Q,sa:S,se:K,ses:re,ss:X}}("undefined"!=typeof self?self:global,{},a),r=e.su(i-(2<<17))}const h=t.length+1;e.ses(r),e.sa(h-1),s(t,new Uint16Array(a,r,h)),e.p()||(n=e.e(),o());const w=[],d=[];for(;e.ri();){const a=e.is(),r=e.ie(),i=e.ai(),s=e.id(),f=e.ss(),c=e.se(),n=e.it();let k;e.ip()&&(k=b(-1===s?a:a+1,t.charCodeAt(-1===s?a-1:a))),w.push({t:n,n:k,s:a,e:r,ss:f,se:c,d:s,a:i})}for(;e.re();){const a=e.es(),r=e.ee(),i=e.els(),s=e.ele(),f=t.charCodeAt(a),c=i>=0?t.charCodeAt(i):-1;d.push({s:a,e:r,ls:i,le:s,n:34===f||39===f?b(a+1,f):t.slice(a,r),ln:i<0?void 0:34===c||39===c?b(i+1,c):t.slice(i,s)})}return[w,d,!!e.f(),!!e.ms()]}function b(e,a){n=e;let r="",i=n;for(;;){n>=t.length&&o();const e=t.charCodeAt(n);if(e===a)break;92===e?(r+=t.slice(i,n),r+=k(),i=n):(8232===e||8233===e||u(e)&&o(),++n)}return r+=t.slice(i,n++),r}function k(){let e=t.charCodeAt(++n);switch(++n,e){case 110:return"\n";case 114:return"\r";case 120:return String.fromCharCode(l(2));case 117:return function(){const e=t.charCodeAt(n);let a;123===e?(++n,a=l(t.indexOf("}",n)-n),++n,a>1114111&&o()):a=l(4);return a<=65535?String.fromCharCode(a):(a-=65536,String.fromCharCode(55296+(a>>10),56320+(1023&a)))}();case 116:return"\t";case 98:return"\b";case 118:return"\v";case 102:return"\f";case 13:10===t.charCodeAt(n)&&++n;case 10:return"";case 56:case 57:o();default:if(e>=48&&e<=55){let a=t.substr(n-1,3).match(/^[0-7]+/)[0],r=parseInt(a,8);return r>255&&(a=a.slice(0,-1),r=parseInt(a,8)),n+=a.length-1,e=t.charCodeAt(n),"0"===a&&56!==e&&57!==e||o(),String.fromCharCode(r)}return u(e)?"":String.fromCharCode(e)}}function l(e){const a=n;let r=0,i=0;for(let a=0;a<e;++a,++n){let e,s=t.charCodeAt(n);if(95!==s){if(s>=97)e=s-97+10;else if(s>=65)e=s-65+10;else{if(!(s>=48&&s<=57))break;e=s-48}if(e>=16)break;i=s,r=16*r+e}else 95!==i&&0!==a||o(),i=s}return 95!==i&&n-a===e||o(),r}function u(e){return 13===e||10===e}function o(){throw Object.assign(Error(`Parse error ${c}:${t.slice(0,n).split("\n").length}:${n-t.lastIndexOf("\n",n-1)}`),{idx:n})}

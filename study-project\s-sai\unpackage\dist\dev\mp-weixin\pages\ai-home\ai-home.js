"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      userInfo: {},
      aiActive: true,
      monitorActive: false,
      pageLoaded: false
    };
  },
  onLoad() {
    this.loadUserInfo();
    this.initAIStatus();
    setTimeout(() => {
      this.pageLoaded = true;
    }, 300);
  },
  onPullDownRefresh() {
    this.refreshData();
  },
  methods: {
    loadUserInfo() {
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      if (userInfo) {
        this.userInfo = userInfo;
      }
    },
    initAIStatus() {
      this.aiActive = true;
    },
    refreshData() {
      setTimeout(() => {
        this.loadUserInfo();
        this.initAIStatus();
        common_vendor.index.stopPullDownRefresh();
        common_vendor.index.showToast({
          title: "刷新成功",
          icon: "success"
        });
      }, 1e3);
    },
    startVoiceChat() {
      common_vendor.index.showToast({
        title: "正在启动AI对话...",
        icon: "loading"
      });
    },
    openFunction(type) {
      const functionRoutes = {
        "knowledge": "/pages/ai-knowledge/ai-knowledge",
        "search": "/pages/ai-search/ai-search",
        "writing": "/pages/ai-writing/ai-writing",
        "translate": "/pages/ai-translate/ai-translate",
        "emotion": "/pages/ai-emotion/ai-emotion",
        "recommend": "/pages/ai-recommend/ai-recommend",
        "reminder": "/pages/ai-reminder/ai-reminder",
        "game": "/pages/ai-game/ai-game",
        "health": "/pages/ai-health/ai-health",
        "photo-search": "/pages/ai-photo/ai-photo",
        "video-call": "/pages/ai-video/ai-video",
        "exam": "/pages/ai-exam/ai-exam",
        "textbook": "/pages/ai-textbook/ai-textbook"
      };
      const route = functionRoutes[type];
      if (route) {
        const availablePages = ["knowledge", "search", "writing", "translate", "emotion", "recommend"];
        if (availablePages.includes(type)) {
          common_vendor.index.navigateTo({
            url: route,
            fail: (err) => {
              common_vendor.index.__f__("error", "at pages/ai-home/ai-home.vue:178", "页面跳转失败:", err);
              common_vendor.index.showToast({
                title: "页面跳转失败",
                icon: "none"
              });
            }
          });
        } else {
          common_vendor.index.showToast({
            title: "功能开发中，敬请期待",
            icon: "none"
          });
        }
      } else {
        common_vendor.index.showToast({
          title: "功能开发中，敬请期待",
          icon: "none"
        });
      }
    },
    toggleMonitor() {
      this.monitorActive = !this.monitorActive;
      common_vendor.index.showToast({
        title: this.monitorActive ? "监控已开启" : "监控已关闭",
        icon: "success"
      });
    },
    showFunctionDetail(type) {
      const functionDetails = {
        "knowledge": {
          title: "知识问答",
          icon: "📚",
          description: "让AI能够回答各种问题，如历史、科学、技术、文化等方面的问题，就像一个知识渊博的助手。",
          examples: [
            "地球的直径是多少？",
            "中国古代四大发明是什么？",
            "光的传播速度是多少？"
          ]
        },
        "search": {
          title: "信息查询",
          icon: "🔍",
          description: "帮助学生查询各类信息，如天气、父母电话等实用信息。",
          examples: [
            "查询明天保定的天气",
            "查询爸爸的电话号码",
            "今天的新闻热点"
          ]
        },
        "writing": {
          title: "文本生成",
          icon: "✍️",
          description: "包括写作文章、故事、诗歌、摘要等各类文本创作。",
          examples: [
            "写一首关于春天的诗",
            "写一个关于友谊的小故事",
            "帮我写一篇关于环保的作文"
          ]
        },
        "translate": {
          title: "语言翻译",
          icon: "🌐",
          description: "实现不同语言之间的即时翻译，支持多种主流语言。",
          examples: [
            '把"我喜欢振涛"翻译成英语',
            "这句日语是什么意思？",
            "帮我翻译这段法语"
          ]
        },
        "emotion": {
          title: "情感陪伴",
          icon: "💝",
          description: "识别用户的情感状态，如高兴、生气、悲伤等，并给予相应的情感回应和安慰。",
          examples: [
            "我今天很难过",
            "我考试考得不好",
            "我很开心今天学会了新知识"
          ]
        },
        "recommend": {
          title: "智能推荐",
          icon: "🎯",
          description: "根据用户的兴趣、历史记录等，推荐相关的服务、音乐、书籍等。",
          examples: [
            "推荐一些适合我的书籍",
            "推荐一些好听的音乐",
            "推荐一些学习资源"
          ]
        },
        "reminder": {
          title: "任务提醒",
          icon: "⏰",
          description: "帮助用户设置提醒事项，如生日、闹钟、节日、老师安排的重要事情等。",
          examples: [
            "提醒我明天下午六点给妈妈洗脚",
            "提醒我下周一交作业",
            "设置每天早上7点的闹钟"
          ]
        },
        "game": {
          title: "游戏娱乐",
          icon: "🎮",
          description: "提供各种语音游戏，如猜谜语、成语接龙、问答游戏等，为学生带来娱乐。",
          examples: [
            "我们来玩成语接龙吧",
            "给我出个谜语",
            "我们来玩问答游戏"
          ]
        },
        "health": {
          title: "健康管理",
          icon: "💪",
          description: "记录用户日常运动、饮食、睡眠数据，评估健康状况，提供个性化健康建议。",
          examples: [
            "记录我今天走了8000步",
            "我今天吃了什么",
            "提醒我要多喝水"
          ]
        },
        "photo-search": {
          title: "拍照搜题与辅导",
          icon: "📷",
          description: "支持高清拍照搜题，AI老师语音讲解，关联相似题目拓展训练，错题收集与分类。",
          examples: [
            "拍照识别数学题目",
            "拍照翻译英文文章",
            "错题自动收集整理"
          ]
        },
        "video-call": {
          title: "视频通话",
          icon: "📹",
          description: "家长可以通过外部设备与孩子进行实时对话，高清视频通话，清晰语音通话。",
          examples: [
            "与家长视频通话",
            "摄像头控制调节",
            "通话记录回放"
          ]
        },
        "exam": {
          title: "模拟考试",
          icon: "📝",
          description: "AI智能组卷、监考、判卷，限时2小时，根据成绩定制提升计划。",
          examples: [
            "AI智能组卷考试",
            "自动监考判卷",
            "生成学习报告"
          ]
        },
        "textbook": {
          title: "课本学习",
          icon: "📖",
          description: "配套人教版/部编版教材，AI逐句讲解，口语训练跟读。",
          examples: [
            "AI逐句讲解课文",
            "跟AI练习口语",
            "课本内容同步学习"
          ]
        }
      };
      const detail = functionDetails[type];
      if (detail) {
        common_vendor.index.showModal({
          title: `${detail.icon} ${detail.title}`,
          content: `${detail.description}

示例：
${detail.examples.join("\n")}`,
          showCancel: true,
          cancelText: "了解了",
          confirmText: "立即体验",
          success: (res) => {
            if (res.confirm) {
              this.openFunction(type);
            }
          }
        });
      }
    },
    openSettings() {
      common_vendor.index.showActionSheet({
        itemList: ["个性化设置", "学习报告", "家长中心", "会员权益", "帮助反馈"],
        success: (res) => {
          const actions = [
            () => this.showPersonalSettings(),
            () => this.showLearningReport(),
            () => this.showParentCenter(),
            () => this.showMemberBenefits(),
            () => this.showHelpFeedback()
          ];
          actions[res.tapIndex]();
        }
      });
    },
    showPersonalSettings() {
      common_vendor.index.showModal({
        title: "⚙️ 个性化设置",
        content: "• AI语音设置（语速、音色、风格）\n• 简易指令模式\n• 学习提醒设置\n• 界面主题选择",
        showCancel: true,
        cancelText: "取消",
        confirmText: "进入设置",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "功能开发中...",
              icon: "none"
            });
          }
        }
      });
    },
    showLearningReport() {
      common_vendor.index.showModal({
        title: "📊 学习报告",
        content: "• 学习时间统计\n• 专注度分析\n• 错题本查看\n• 学习进度报告\n• 家长同步通知",
        showCancel: true,
        cancelText: "取消",
        confirmText: "查看报告",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "功能开发中...",
              icon: "none"
            });
          }
        }
      });
    },
    showParentCenter() {
      common_vendor.index.showModal({
        title: "👨‍👩‍👧‍👦 家长中心",
        content: "• 实时监控学习状态\n• 视频通话功能\n• 学习报告查看\n• 账号管理（最多5个孩子）\n• 心理健康导师",
        showCancel: true,
        cancelText: "取消",
        confirmText: "进入中心",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "功能开发中...",
              icon: "none"
            });
          }
        }
      });
    },
    showMemberBenefits() {
      common_vendor.index.showModal({
        title: "💎 会员权益",
        content: "• AI监控学习功能\n• 错题本导出\n• 专属学习报告\n• 名校试卷库\n• 优惠券福利\n\n🎁 新用户注册送3个月VIP",
        showCancel: true,
        cancelText: "取消",
        confirmText: "了解详情",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "功能开发中...",
              icon: "none"
            });
          }
        }
      });
    },
    showHelpFeedback() {
      common_vendor.index.showModal({
        title: "💬 帮助反馈",
        content: "• 功能使用指南\n• 常见问题解答\n• 提交功能建议\n• 问题反馈\n• 联系客服",
        showCancel: true,
        cancelText: "取消",
        confirmText: "获取帮助",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "功能开发中...",
              icon: "none"
            });
          }
        }
      });
    },
    logout() {
      common_vendor.index.showModal({
        title: "退出登录",
        content: "确定要退出登录吗？",
        showCancel: true,
        cancelText: "取消",
        confirmText: "确定",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.removeStorageSync("userInfo");
            common_vendor.index.removeStorageSync("token");
            common_vendor.index.showToast({
              title: "退出成功",
              icon: "success",
              duration: 1500
            });
            setTimeout(() => {
              common_vendor.index.reLaunch({
                url: "/pages/index/index"
              });
            }, 1500);
          }
        }
      });
    },
    // 跳转到首页
    goToHome() {
      common_vendor.index.switchTab({
        url: "/pages/home/<USER>"
      });
    },
    switchTab(tab) {
      switch (tab) {
        case "profile":
          common_vendor.index.showToast({
            title: "个人中心功能开发中",
            icon: "none"
          });
          break;
      }
    },
    // 简化的拍照按钮提示
    showCameraInfo() {
      common_vendor.index.showToast({
        title: "拍照功能开发中",
        icon: "none",
        duration: 2e3
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t($data.userInfo.name ? $data.userInfo.name.charAt(0) : "A"),
    b: common_vendor.t($data.userInfo.name || "AI学习者"),
    c: $data.aiActive ? 1 : "",
    d: common_vendor.t($data.aiActive ? "AI在线" : "AI离线"),
    e: common_vendor.o((...args) => $options.openSettings && $options.openSettings(...args)),
    f: common_vendor.t($data.monitorActive ? "监控中" : "未开启"),
    g: $data.monitorActive ? 1 : "",
    h: $data.monitorActive ? 1 : "",
    i: common_vendor.o((...args) => $options.toggleMonitor && $options.toggleMonitor(...args)),
    j: common_vendor.o((...args) => $options.logout && $options.logout(...args)),
    k: common_vendor.o((...args) => $options.goToHome && $options.goToHome(...args)),
    l: common_vendor.o((...args) => $options.showCameraInfo && $options.showCameraInfo(...args)),
    m: common_vendor.o(($event) => $options.switchTab("profile"))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/ai-home/ai-home.js.map

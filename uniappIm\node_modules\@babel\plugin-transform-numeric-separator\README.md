# @babel/plugin-transform-numeric-separator

> Remove numeric separators from Decimal, Binary, Hex and Octal literals

See our website [@babel/plugin-transform-numeric-separator](https://babeljs.io/docs/babel-plugin-transform-numeric-separator) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-transform-numeric-separator
```

or using yarn:

```sh
yarn add @babel/plugin-transform-numeric-separator --dev
```


.container {
  min-height: 100vh;
  background: #f8f9fa;
}
.header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: #fff;
  border-bottom: 1rpx solid #e9ecef;
}
.header-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}
.header-info {
  flex: 1;
}
.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  display: block;
}
.header-desc {
  font-size: 24rpx;
  color: #6c757d;
  margin-top: 8rpx;
  display: block;
}
.language-selector {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: #fff;
  border-bottom: 1rpx solid #e9ecef;
}
.language-item {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.language-label {
  font-size: 24rpx;
  color: #6c757d;
  margin-bottom: 12rpx;
}
.language-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
}
.language-text {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 500;
}
.picker-arrow {
  font-size: 20rpx;
  color: #6c757d;
}
.swap-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: #007bff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 30rpx;
  transition: all 0.3s ease;
}
.swap-btn:active {
  transform: scale(0.9);
  background: #0056b3;
}
.swap-icon {
  font-size: 32rpx;
  color: #fff;
  font-weight: bold;
}
.translate-area {
  padding: 30rpx;
}
.input-section, .output-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
}
.char-count {
  font-size: 24rpx;
  color: #6c757d;
}
.input-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.5;
  background: #f8f9fa;
  box-sizing: border-box;
}
.input-actions, .output-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 20rpx;
}
.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
  transition: all 0.3s ease;
}
.action-btn.clear {
  background: #dc3545;
  color: #fff;
}
.action-btn.paste {
  background: #6c757d;
  color: #fff;
}
.action-btn.copy {
  background: #28a745;
  color: #fff;
}
.action-btn:active {
  transform: scale(0.95);
}
.translate-btn-section {
  margin-bottom: 30rpx;
}
.translate-btn {
  width: 100%;
  height: 100rpx;
  background: #007bff;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  transition: all 0.3s ease;
}
.translate-btn.disabled {
  background: #ccc;
  color: #999;
}
.translate-btn:not(.disabled):active {
  transform: scale(0.98);
  background: #0056b3;
}
.translating-animation {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 20rpx;
}
.dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #007bff;
  animation: typing 1.4s infinite;
}
.dot:nth-child(2) { animation-delay: 0.2s;
}
.dot:nth-child(3) { animation-delay: 0.4s;
}
@keyframes typing {
0%, 60%, 100% { opacity: 0.3; transform: scale(1);
}
30% { opacity: 1; transform: scale(1.2);
}
}
.translating-text {
  font-size: 28rpx;
  color: #6c757d;
}
.output-content {
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 30rpx;
  background: #f8f9fa;
}
.output-text {
  font-size: 28rpx;
  line-height: 1.8;
  color: #333;
  white-space: pre-wrap;
}
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}
.empty-icon {
  font-size: 64rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}
.empty-text {
  font-size: 28rpx;
  color: #6c757d;
}
.quick-translate {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
}
.quick-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20rpx;
  display: block;
}
.quick-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.quick-item {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s ease;
}
.quick-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}
.quick-text {
  font-size: 26rpx;
  color: #495057;
}

package cn.zhentao.controller;


import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.sql.DataSource;
import java.sql.Connection;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 * 用于验证系统基本功能是否正常
 *
 * <AUTHOR>
 * @since 2024-07-29
 */
@Slf4j
@CrossOrigin(origins = "*")
@RestController
@RequestMapping("/test")
public class TestController {

    @Autowired(required = false)
    private DataSource dataSource;

    /**
     * 基本健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("service", "ai-userlogin");
        result.put("timestamp", LocalDateTime.now());
        result.put("message", "AI User Login Service is running successfully!");
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * POST方式的健康检查（与测试页面兼容）
     */
    @PostMapping("/health")
    public ResponseEntity<Map<String, Object>> healthPost() {
        return health();
    }

    /**
     * 数据库连接测试
     */
    @GetMapping("/db")
    public ResponseEntity<Map<String, Object>> testDatabase() {
        Map<String, Object> result = new HashMap<>();

        try {
            if (dataSource == null) {
                result.put("status", "ERROR");
                result.put("message", "DataSource not configured");
                return new ResponseEntity<>(result, HttpStatus.INTERNAL_SERVER_ERROR);
            }

            try (Connection connection = dataSource.getConnection()) {
                result.put("status", "OK");
                result.put("message", "Database connection successful");
                result.put("url", connection.getMetaData().getURL());
                result.put("driver", connection.getMetaData().getDriverName());
                result.put("timestamp", LocalDateTime.now());
            }

            return new ResponseEntity<>(result, HttpStatus.OK);

        } catch (Exception e) {
            log.error("Database connection test failed", e);
            result.put("status", "ERROR");
            result.put("message", "Database connection failed: " + e.getMessage());
            return new ResponseEntity<>(result, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 系统信息
     */
    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> systemInfo() {
        Map<String, Object> result = new HashMap<>();

        // JVM信息
        Runtime runtime = Runtime.getRuntime();
        result.put("jvm", Map.of(
            "version", System.getProperty("java.version"),
            "vendor", System.getProperty("java.vendor"),
            "totalMemory", runtime.totalMemory() / 1024 / 1024 + " MB",
            "freeMemory", runtime.freeMemory() / 1024 / 1024 + " MB",
            "maxMemory", runtime.maxMemory() / 1024 / 1024 + " MB"
        ));

        // 系统信息
        result.put("system", Map.of(
            "os", System.getProperty("os.name"),
            "arch", System.getProperty("os.arch"),
            "version", System.getProperty("os.version"),
            "user", System.getProperty("user.name"),
            "workingDir", System.getProperty("user.dir")
        ));

        result.put("timestamp", LocalDateTime.now());

        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * 简单的Echo测试
     */
    @PostMapping("/echo")
    public ResponseEntity<Map<String, Object>> echo(@RequestBody(required = false) Map<String, Object> requestBody) {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "OK");
        result.put("message", "Echo test successful");
        result.put("receivedData", requestBody);
        result.put("timestamp", LocalDateTime.now());

        log.info("Echo test - received: {}", requestBody);

        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * 错误测试
     */
    @GetMapping("/error")
    public ResponseEntity<Map<String, Object>> testError() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "ERROR");
        result.put("message", "This is a test error");
        result.put("timestamp", LocalDateTime.now());

        return new ResponseEntity<>(result, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * CORS预检请求处理
     */
    @RequestMapping(value = "/**", method = RequestMethod.OPTIONS)
    public ResponseEntity<Void> handleOptions() {
        return new ResponseEntity<>(HttpStatus.OK);
    }
}

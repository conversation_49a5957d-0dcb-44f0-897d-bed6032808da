package cn.zhentao.pojo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* 用户信息表
* @TableName user
*/
@Data
@TableName("user")
public class FactInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
    * 用户ID
    */
    @TableField(value = "id")
    private Long id;
    /**
    * 用户账号
    */
@TableField(value = "username")
    private String name;
    /**
    * 用户昵称
    */
    @TableField(value = "nickname")
    private String nickname;

    /**
    * 用户类型 (00系统用户)
    */
    @TableField(value = "user_type")
    private String userType;
    /**
    * 用户邮箱
    */
@TableField(value = "email")
    private String email;
    /**
    * 手机号码
    */
    @TableField(value = "phonenumber")
    private String phonenumber;
    /**
    * 
    */
    @TableField(value = "sex")
    private String sex;
    /**
    * 头像地址
    */
    @TableField(value = "avatar")
    private String avatar;
    /**
    * 密码
    */
    @TableField(value = "password")
    private String password;
    /**
    * 帐号状态 (0正常 1停用)
    */
    @TableField(value = "status")
    private Integer status;

    /**
    * 
    */
    @TableField(value = "del_flag")
    private String delFlag;
    /**
    * 最后登录时间
    */
    @TableField(value = "login_date")
    private Date loginDate;
    /**
    * 创建者
    */
    @TableField(value = "create_by")
    private String createBy;
    /**
    * 创建时间
    */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
    * 更新者
    */
    @TableField(value = "update_by")
    private String updateBy;
    /**
    * 更新时间
    */
    @TableField("update_time")
    private Date updateTime;
    /**
    * 备注
    */
    @TableField("remark")
    private String remark;
    @TableField("user_status")
    private String userStatus;
    /**
     *
     */
    private byte[] user;

    /**
    * 
    */
    @TableField("face_data")
    private String faceData;
    /**
    * 
    */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;
    /**
    * 
    */
    @TableField("image_url")
    private String imageUrl;
}

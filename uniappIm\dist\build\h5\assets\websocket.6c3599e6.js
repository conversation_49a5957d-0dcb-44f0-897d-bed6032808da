let e=null,o=null,l=null,t=null,c=0,n=!1;function s(a,S){e&&e.close(),o=S,e=uni.connectSocket({url:"ws://localhost:9999/ws",success:()=>{console.log("WebSocket连接成功")},fail:e=>{console.error("WebSocket连接失败:",e)}}),e.onOpen(()=>{console.log("WebSocket已打开"),c=0,n=!1,r({type:"auth",token:a}),t=setInterval(()=>{r({type:"heartbeat"})},3e4)}),e.onMessage(e=>{try{const l=JSON.parse(e.data);console.log("收到WebSocket消息:",l),"auth_success"===l.type?console.log("WebSocket认证成功"):"heartbeat"===l.type?console.log("心跳响应"):o&&o(l)}catch(l){console.error("解析WebSocket消息失败:",l)}}),e.onClose(()=>{if(console.log("WebSocket连接关闭"),u(),!n&&c<5&&!l){c++;const e=Math.min(1e3*Math.pow(2,c),3e4);console.log(`第${c}次重连WebSocket，${e}ms后重试`),l=setTimeout(()=>{s(a,o),l=null},e)}else c>=5&&(console.error("WebSocket重连次数已达上限，停止重连"),uni.showToast({title:"网络连接失败，请检查网络",icon:"none"}))}),e.onError(e=>{console.error("WebSocket错误:",e)})}function r(o){e&&1===e.readyState?e.send({data:JSON.stringify(o),success:()=>{console.log("WebSocket消息发送成功:",o)},fail:e=>{console.error("WebSocket消息发送失败:",e)}}):console.error("WebSocket未连接")}function a(){n=!0,u(),l&&(clearTimeout(l),l=null),e&&(e.close(),e=null),c=0}function u(){t&&(clearInterval(t),t=null)}export{a,s as c,r as s};

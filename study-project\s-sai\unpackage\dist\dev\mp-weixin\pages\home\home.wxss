
/* 自定义状态栏 */
.custom-status-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 88rpx;
  background: #2c3e50;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.status-content {
  display: flex;
  align-items: center;
  justify-content: center;
}
.app-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
}
.container {
  min-height: 100vh;
  background: #f8f9fa;
  padding-top: 108rpx;
  padding-bottom: 140rpx;
}

/* 主要内容 */
.main-content {
  flex: 1;
  padding: 20rpx;
  padding-bottom: 120rpx;
}

/* 欢迎区域 */
.welcome-section {
  text-align: center;
  padding: 60rpx 40rpx;
  background: white;
  border-radius: 20rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.welcome-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20rpx;
  display: block;
}
.welcome-desc {
  font-size: 28rpx;
  color: #7f8c8d;
  display: block;
}
/* 轮播图样式 */
.banner-section {
  margin-bottom: 40rpx;
}
.banner-swiper {
  height: 300rpx;
  border-radius: 20rpx;
  overflow: hidden;
}
.banner-item {
  height: 100%;
  border-radius: 20rpx;
  position: relative;
  overflow: hidden;
}
.banner-content {
  position: absolute;
  left: 40rpx;
  top: 50%;
  transform: translateY(-50%);
  color: white;
}
.banner-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.banner-desc {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
}
.banner-icon {
  position: absolute;
  right: -200rpx;
  top: -50rpx;
  font-size: 200rpx;
  opacity: 0.2;
}

/* AI对话模块 */
.ai-chat-section {
  margin-bottom: 40rpx;
}
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.title-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}
.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.chat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  display: flex;
  align-items: center;
  color: white;
  box-shadow: 0 10rpx 30rpx rgba(102, 126, 234, 0.3);
}
.voice-icon {
  font-size: 60rpx;
  margin-right: 30rpx;
}
.chat-content {
  flex: 1;
}
.chat-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.chat-desc {
  display: block;
  font-size: 26rpx;
  opacity: 0.9;
}
.chat-arrow {
  font-size: 40rpx;
  opacity: 0.8;
}
/* 智能功能模块 */
.functions-section {
  margin-bottom: 40rpx;
}
.functions-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}
.function-item {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}
.function-item:active {
  transform: scale(0.95);
}
.function-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15rpx;
  font-size: 40rpx;
}
.knowledge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.search {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}
.writing {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}
.translate {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}
.emotion {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}
.recommend {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}
.reminder {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}
.game {
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
}
.health {
  background: linear-gradient(135deg, #fad0c4 0%, #ffd1ff 100%);
}
.function-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.function-desc {
  display: block;
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
}

/* 快捷功能区域 */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  margin-bottom: 40rpx;
}
.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 15rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}
.action-item:active {
  transform: scale(0.95);
}
.action-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 50rpx;
  margin-bottom: 15rpx;
  background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
  color: white;
}
.action-text {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
  text-align: center;
}





/* 底部导航栏 */
.bottom-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: white;
  display: flex;
  align-items: center;
  justify-content: space-around;
  border-top: 1rpx solid #e5e5e5;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 拍照搜题按钮区域 */
.camera-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.camera-btn {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 15rpx rgba(255, 107, 107, 0.4);
  transform: translateY(-10rpx);
  transition: all 0.3s ease;
  margin-bottom: 8rpx;
}
.camera-btn:active {
  transform: translateY(-8rpx) scale(0.95);
  box-shadow: 0 2rpx 10rpx rgba(255, 107, 107, 0.6);
}
.camera-icon {
  font-size: 45rpx;
  color: white;
}
.camera-text {
  font-size: 20rpx;
  color: #666;
  font-weight: 500;
  text-align: center;
  transform: translateY(-10rpx);
}
.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 10rpx 0;
  transition: all 0.3s ease;
}
.nav-item.active .nav-icon {
  color: #007AFF;
  transform: scale(1.1);
}
.nav-item.active .nav-text {
  color: #007AFF;
  font-weight: 600;
}
.nav-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
  color: #666;
  transition: all 0.3s ease;
}
.nav-text {
  font-size: 20rpx;
  color: #666;
  transition: all 0.3s ease;
}

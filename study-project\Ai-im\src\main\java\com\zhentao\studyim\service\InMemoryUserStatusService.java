package com.zhentao.studyim.service;

import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 内存用户状态服务
 * 当Redis不可用时的备用方案
 */
@Service
public class InMemoryUserStatusService {

    // 线程安全的HashMap，存储用户在线状态
    private final ConcurrentHashMap<Long, Long> onlineUsers = new ConcurrentHashMap<>();

    // 定时任务执行器
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    public InMemoryUserStatusService() {
        // 每小时清理过期的在线状态
        scheduler.scheduleAtFixedRate(this::cleanExpiredUsers, 1, 1, TimeUnit.HOURS);
    }

    /**
     * 设置用户在线
     * @param userId 用户ID
     */
    public void setUserOnline(Long userId) {
        // 存储用户ID和过期时间（24小时后）
        onlineUsers.put(userId, System.currentTimeMillis() + TimeUnit.HOURS.toMillis(24));
    }

    /**
     * 设置用户离线
     * @param userId 用户ID
     */
    public void setUserOffline(Long userId) {
        onlineUsers.remove(userId);
    }

    /**
     * 检查用户是否在线
     * @param userId 用户ID
     * @return true-在线，false-离线
     */
    public boolean isUserOnline(Long userId) {
        Long expireTime = onlineUsers.get(userId);
        if (expireTime == null) {
            return false;
        }

        // 检查是否过期
        if (System.currentTimeMillis() > expireTime) {
            onlineUsers.remove(userId);
            return false;
        }

        return true;
    }

    /**
     * 清理过期的用户状态
     */
    private void cleanExpiredUsers() {
        long currentTime = System.currentTimeMillis();
        onlineUsers.entrySet().removeIf(entry -> currentTime > entry.getValue());
    }
}
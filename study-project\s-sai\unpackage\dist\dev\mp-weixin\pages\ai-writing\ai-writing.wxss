
.container {
  min-height: 100vh;
  background: #f8f9fa;
}
.header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: #fff;
  border-bottom: 1rpx solid #e9ecef;
}
.header-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}
.header-info {
  flex: 1;
}
.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  display: block;
}
.header-desc {
  font-size: 24rpx;
  color: #6c757d;
  margin-top: 8rpx;
  display: block;
}
.type-selector {
  display: flex;
  background: #fff;
  border-bottom: 1rpx solid #e9ecef;
  padding: 0 20rpx;
}
.type-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 10rpx;
  transition: all 0.3s ease;
  border-bottom: 4rpx solid transparent;
}
.type-item.active {
  border-bottom-color: #007bff;
}
.type-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}
.type-text {
  font-size: 24rpx;
  color: #6c757d;
}
.type-item.active .type-text {
  color: #007bff;
  font-weight: bold;
}
.writing-area {
  padding: 30rpx;
}
.prompt-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
}
.prompt-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20rpx;
  display: block;
}
.prompt-input {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.5;
  background: #f8f9fa;
  box-sizing: border-box;
}
.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #6c757d;
  margin-top: 10rpx;
}
.generate-section {
  margin-bottom: 30rpx;
}
.generate-btn {
  width: 100%;
  height: 100rpx;
  background: #007bff;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  transition: all 0.3s ease;
}
.generate-btn.disabled {
  background: #ccc;
  color: #999;
}
.generate-btn:not(.disabled):active {
  transform: scale(0.98);
  background: #0056b3;
}
.btn-icon {
  font-size: 36rpx;
}
.btn-text {
  font-weight: bold;
}
.result-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
}
.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.result-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}
.result-actions {
  display: flex;
  gap: 16rpx;
}
.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
  transition: all 0.3s ease;
}
.action-btn.copy {
  background: #28a745;
  color: #fff;
}
.action-btn.regenerate {
  background: #ffc107;
  color: #333;
}
.action-btn:active {
  transform: scale(0.95);
}
.generating-animation {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 20rpx;
}
.dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #007bff;
  animation: typing 1.4s infinite;
}
.dot:nth-child(2) { animation-delay: 0.2s;
}
.dot:nth-child(3) { animation-delay: 0.4s;
}
@keyframes typing {
0%, 60%, 100% { opacity: 0.3; transform: scale(1);
}
30% { opacity: 1; transform: scale(1.2);
}
}
.generating-text {
  font-size: 28rpx;
  color: #6c757d;
}
.result-content {
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 30rpx;
  background: #f8f9fa;
}
.result-text {
  font-size: 28rpx;
  line-height: 1.8;
  color: #333;
  white-space: pre-wrap;
}
.examples-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
}
.examples-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20rpx;
  display: block;
}
.examples-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.example-item {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s ease;
}
.example-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}
.example-text {
  font-size: 26rpx;
  color: #495057;
}

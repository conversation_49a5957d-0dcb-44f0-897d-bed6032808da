<template>
  <view class="container">
    <view class="header">
      <view class="logo">👤</view>
      <view class="title">人脸注册</view>
      <view class="subtitle">请上传清晰的人脸照片进行注册</view>
    </view>
    
    <view class="card">
      <view class="upload-section">
        <view class="upload-area" @tap="chooseImage">
          <view v-if="!registerImage" class="upload-placeholder">
            <view class="upload-icon">📷</view>
            <text class="upload-text">点击上传人脸图片</text>
            <text class="upload-hint">支持拍照或从相册选择</text>
          </view>
          <image v-else :src="registerImage" class="uploaded-image"></image>
        </view>
      </view>
      
      <view class="form-section">
        <view class="form-item">
          <text class="label">姓名</text>
          <input 
            v-model="registerName" 
            placeholder="请输入您的姓名" 
            class="input"
            maxlength="20"
          />
        </view>
        
        <view class="form-item">
          <text class="label">备注</text>
          <input 
            v-model="registerRemark" 
            placeholder="请输入备注信息（可选）" 
            class="input"
            maxlength="50"
          />
        </view>
      </view>
      
      <button 
        class="submit-btn" 
        @tap="submitRegister" 
        :loading="registerLoading"
        :disabled="!canSubmit"
      >
        <text v-if="!registerLoading" class="btn-text">{{ registerLoading ? '注册中...' : '立即注册' }}</text>
        <view v-else class="loading-dots">
          <view class="dot"></view>
          <view class="dot"></view>
          <view class="dot"></view>
        </view>
      </button>
      
      <view v-if="registerResult" class="result-message">
        {{ registerResult }}
      </view>
    </view>
    
    <view class="footer">
      <text class="footer-text">已有账号？</text>
      <text class="login-link" @tap="goToLogin">立即登录</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      registerImage: '',
      registerName: '',
      registerRemark: '',
      registerLoading: false,
      registerResult: ''
    }
  },
  computed: {
    canSubmit() {
      return this.registerImage && this.registerName.trim();
    }
  },
  methods: {
    chooseImage() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: res => {
          this.registerImage = res.tempFilePaths[0];
          this.registerResult = '';
        }
      });
    },
    
    submitRegister() {
      if (!this.canSubmit) {
        this.registerResult = '请上传人脸图片并填写姓名';
        return;
      }
      
      this.registerLoading = true;
      this.registerResult = '';
      
      uni.uploadFile({
        url: 'http://localhost:8081/fact-info/save',
        filePath: this.registerImage,
        name: 'files',
        formData: {
          name: this.registerName.trim(),
          remark: this.registerRemark.trim()
        },
        success: res => {
          let data = {};
          try {
            data = JSON.parse(res.data);
          } catch (e) {
            this.registerResult = '注册失败，返回数据异常';
            return;
          }
          
          if (data.code === 200) {
            this.registerResult = '✅ 注册成功！欢迎 ' + data.data.name;
            setTimeout(() => {
              this.registerImage = '';
              this.registerName = '';
              this.registerRemark = '';
            }, 2000);
          } else {
            this.registerResult = '❌ 注册失败：' + data.message;
          }
        },
        fail: err => {
          this.registerResult = '❌ 注册失败：' + err.errMsg;
        },
        complete: () => {
          this.registerLoading = false;
        }
      });
    },
    
    goToLogin() {
      uni.navigateTo({
        url: '/pages/login/login'
      });
    }
  }
}
</script>

<style>
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  position: relative;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
  padding-top: 60rpx;
  position: relative;
  z-index: 1;
}

.logo {
  font-size: 100rpx;
  margin-bottom: 30rpx;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20rpx;
  text-shadow: 0 4rpx 8rpx rgba(0,0,0,0.2);
  letter-spacing: 2rpx;
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255,255,255,0.9);
  line-height: 1.5;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);
}

.card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 40rpx 32rpx;
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  margin-bottom: 40rpx;
  position: relative;
  z-index: 1;
}

.upload-section {
  margin-bottom: 40rpx;
}

.upload-area {
  border: 3rpx dashed #ddd;
  border-radius: 20rpx;
  padding: 60rpx 20rpx;
  text-align: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #f0f2ff 100%);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.upload-area::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
  transition: left 0.5s ease;
}

.upload-area:active::before {
  left: 100%;
}

.upload-area:active {
  border-color: #667eea;
  background: linear-gradient(135deg, #f0f2ff 0%, #e8ecff 100%);
  transform: scale(0.98);
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
}

.upload-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.upload-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.upload-hint {
  font-size: 24rpx;
  color: #999;
}

.uploaded-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 20rpx;
  border: 4rpx solid #e1e5e9;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: scale(0.8); }
  to { opacity: 1; transform: scale(1); }
}

.form-section {
  margin-bottom: 40rpx;
}

.form-item {
  margin-bottom: 32rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.input {
  width: 100%;
  border: 2rpx solid #e1e5e9;
  border-radius: 16rpx;
  padding: 24rpx 20rpx;
  font-size: 28rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #f0f2ff 100%);
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.input:focus {
  border-color: #667eea;
  background: #fff;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
  transform: translateY(-2rpx);
}

.submit-btn {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 20rpx;
  padding: 28rpx 0;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.submit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
}

.submit-btn:active::before {
  left: 100%;
}

.submit-btn:active {
  transform: translateY(4rpx);
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.submit-btn[disabled] {
  background: #ccc;
  box-shadow: none;
  transform: none;
}

.btn-text {
  position: relative;
  z-index: 1;
}

.loading-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.dot {
  width: 12rpx;
  height: 12rpx;
  background: #fff;
  border-radius: 50%;
  animation: loading 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes loading {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

.result-message {
  margin-top: 24rpx;
  padding: 24rpx 32rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.2);
  animation: slideIn 0.5s ease;
}

@keyframes slideIn {
  from { opacity: 0; transform: translateY(20rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.footer {
  text-align: center;
  margin-top: auto;
  padding-bottom: 40rpx;
  position: relative;
  z-index: 1;
}

.footer-text {
  font-size: 28rpx;
  color: rgba(255,255,255,0.8);
}

.login-link {
  font-size: 28rpx;
  color: #fff;
  font-weight: 600;
  margin-left: 12rpx;
  text-decoration: underline;
  transition: all 0.3s ease;
}

.login-link:active {
  transform: scale(0.95);
}
</style> 
{"name": "dom-walk", "version": "0.1.2", "description": "iteratively walk a DOM node", "keywords": [], "author": "Raynos <<EMAIL>>", "repository": "git://github.com/Raynos/dom-walk.git", "main": "index", "homepage": "https://github.com/Raynos/dom-walk", "contributors": [{"name": "<PERSON>"}], "bugs": {"url": "https://github.com/Raynos/dom-walk/issues", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"budo": "11.6.3"}, "licenses": [{"type": "MIT", "url": "http://github.com/Raynos/dom-walk/raw/master/LICENSE"}], "scripts": {"example": "budo example/index.js"}}
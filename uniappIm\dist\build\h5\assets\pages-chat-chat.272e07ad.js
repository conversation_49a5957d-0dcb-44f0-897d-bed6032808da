import{_ as e,o as t,c as s,w as a,i as n,a as o,b as i,d,h as u,r as l,F as c,t as r,e as h,f,S as g,n as m}from"./index-0fa1fc91.js";import{g as _,a as C,m as k}from"./api.44778c12.js";import{c as p,a as y}from"./websocket.6c3599e6.js";const M=e({data:()=>({userInfo:null,userList:[],showAddMenuModal:!1,pendingRequestCount:0,loading:!1}),onLoad(){this.checkLogin(),this.loadUserInfo(),this.loadFriendList(),this.loadPendingRequestCount(),this.initWebSocket()},onUnload(){y()},onShow(){this.loadFriendList(),this.loadPendingRequestCount()},methods:{checkLogin(){uni.getStorageSync("token")||uni.reLaunch({url:"/pages/login/login"})},loadUserInfo(){this.userInfo=uni.getStorageSync("userInfo")},async loadFriendList(){this.loading=!0;try{console.log("=== 开始加载好友列表 ===");const e=await _();if(console.log("好友列表API响应:",e),200===e.code){const t=e.data||[];console.log("原始好友数据:",t),this.userList=t.map(e=>({...e,id:e.userId||e.id,userId:e.userId||e.id,nickname:e.nickname||e.username,username:e.username,remark:e.remark,lastMessage:"点击开始聊天",lastMessageTime:new Date,unreadCount:0,isMuted:!1})),this.loadUnreadCounts(),console.log("处理后的好友列表:",this.userList)}else console.error("获取好友列表失败:",e.message),uni.showToast({title:e.message||"获取好友列表失败",icon:"none"})}catch(e){console.error("获取好友列表失败:",e),uni.showToast({title:"网络错误: "+e.message,icon:"none"})}finally{this.loading=!1}},async loadPendingRequestCount(){try{const e=await C();200===e.code&&(this.pendingRequestCount=e.data||0)}catch(e){console.error("获取待处理申请数量失败:",e)}},initWebSocket(){const e=uni.getStorageSync("token");e&&p(e,e=>{if(console.log("收到消息:",e),"chat"===e.type){const t=this.userList.findIndex(t=>(t.userId||t.id)===e.fromUserId);if(-1!==t){const s=this.userList[t];s.lastMessage=e.content,s.lastMessageTime=new Date(e.sendTime||new Date),s.unreadCount=(s.unreadCount||0)+1,this.saveUnreadCount(s.userId||s.id,s.unreadCount),this.userList.splice(t,1),this.userList.unshift(s);const a=e.content.length>20?e.content.substring(0,20)+"...":e.content;uni.showToast({title:`${this.getDisplayName(s)}: ${a}`,icon:"none",duration:2e3})}}})},async openChat(e){const t=e.userId||e.id,s=this.getDisplayName(e);console.log("打开聊天:",{userId:t,displayName:s,user:e});try{await k(t),e.unreadCount=0,this.saveUnreadCount(t,0)}catch(a){console.error("标记会话已读失败:",a),e.unreadCount=0,this.saveUnreadCount(t,0)}uni.navigateTo({url:`/pages/chatDetail/chatDetail?userId=${t}&nickname=${encodeURIComponent(s)}`})},async loadUnreadCounts(){try{for(let e of this.userList){const t=`unread_${e.userId||e.id}`,s=uni.getStorageSync(t)||0;e.unreadCount=s}}catch(e){console.error("加载未读消息数量失败:",e)}},saveUnreadCount(e,t){try{const s=`unread_${e}`;uni.setStorageSync(s,t)}catch(s){console.error("保存未读消息数量失败:",s)}},getDisplayName:e=>e.remark&&e.remark.trim()?e.remark:e.nickname||e.username||"未知用户",openTest(){this.hideAddMenu(),uni.navigateTo({url:"/pages/test/test"})},showAddMenu(){this.showAddMenuModal=!0},hideAddMenu(){this.showAddMenuModal=!1},addFriend(){this.hideAddMenu(),uni.navigateTo({url:"/pages/addFriend/addFriend"})},openFriendManage(){this.hideAddMenu(),uni.navigateTo({url:"/pages/friendRequests/friendRequests"})},testUnreadMessage(){if(this.hideAddMenu(),this.userList.length>0){const e=this.userList[0];e.unreadCount=(e.unreadCount||0)+1,e.lastMessage="这是一条测试消息",e.lastMessageTime=new Date,this.saveUnreadCount(e.userId||e.id,e.unreadCount),this.userList.splice(0,1),this.userList.unshift(e),uni.showToast({title:"测试消息已添加",icon:"success"})}else uni.showToast({title:"没有好友可以测试",icon:"none"})},logout(){this.hideAddMenu(),uni.showModal({title:"提示",content:"确定要退出登录吗？",success:e=>{e.confirm&&(uni.removeStorageSync("token"),uni.removeStorageSync("userInfo"),uni.reLaunch({url:"/pages/login/login"}))}})},formatTime(e){if(!e)return"";const t=new Date,s=new Date(e),a=t-s;if(a<6e4)return"刚刚";if(a<36e5)return Math.floor(a/6e4)+"分钟前";if(a<864e5)return Math.floor(a/36e5)+"小时前";const n=new Date(t.getFullYear(),t.getMonth(),t.getDate()),o=new Date(n.getTime()-864e5);return s>=n?s.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}):s>=o?"昨天":s.toLocaleDateString("zh-CN",{month:"2-digit",day:"2-digit"})}}},[["render",function(e,_,C,k,p,y){const M=f,w=n,L=g;return t(),s(w,{class:"chat-container"},{default:a(()=>[o(" 顶部导航栏 "),i(w,{class:"header"},{default:a(()=>[i(w,{class:"header-content"},{default:a(()=>[i(M,{class:"title"},{default:a(()=>[d("即时通讯")]),_:1}),i(w,{class:"header-right"},{default:a(()=>[i(w,{class:"add-icon",onClick:y.showAddMenu},{default:a(()=>[i(M,{class:"icon"},{default:a(()=>[d("+")]),_:1})]),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1}),o(" 聊天列表 "),i(L,{class:"chat-list","scroll-y":"true"},{default:a(()=>[(t(!0),u(c,null,l(p.userList,(e,n)=>(t(),s(w,{key:e.id,class:"chat-item",onClick:t=>y.openChat(e)},{default:a(()=>[i(w,{class:"avatar"},{default:a(()=>[i(M,{class:"avatar-text"},{default:a(()=>[d(r(y.getDisplayName(e).charAt(0)),1)]),_:2},1024),o(" 未读消息红点 "),e.unreadCount>0?(t(),s(w,{key:0,class:"unread-badge"},{default:a(()=>[i(M,{class:"unread-count"},{default:a(()=>[d(r(e.unreadCount>99?"99+":e.unreadCount),1)]),_:2},1024)]),_:2},1024)):o("v-if",!0)]),_:2},1024),i(w,{class:"chat-content"},{default:a(()=>[i(w,{class:"chat-header"},{default:a(()=>[i(M,{class:"friend-name"},{default:a(()=>[d(r(y.getDisplayName(e)),1)]),_:2},1024),i(M,{class:"chat-time"},{default:a(()=>[d(r(y.formatTime(e.lastMessageTime)),1)]),_:2},1024)]),_:2},1024),i(w,{class:"message-row"},{default:a(()=>[i(M,{class:m(["last-message",{"unread-message":e.unreadCount>0}])},{default:a(()=>[d(r(e.lastMessage||"点击开始聊天"),1)]),_:2},1032,["class"]),o(" 静音图标 "),e.isMuted?(t(),s(w,{key:0,class:"mute-icon"},{default:a(()=>[i(M,null,{default:a(()=>[d("🔇")]),_:1})]),_:1})):o("v-if",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onClick"]))),128))]),_:1}),o(" 加载状态 "),p.loading?(t(),s(w,{key:0,class:"loading-state"},{default:a(()=>[i(M,null,{default:a(()=>[d("加载中...")]),_:1})]),_:1})):o("v-if",!0),o(" 空状态 "),p.loading||0!==p.userList.length?o("v-if",!0):(t(),s(w,{key:1,class:"empty-state"},{default:a(()=>[i(M,{class:"empty-text"},{default:a(()=>[d("暂无好友")]),_:1}),i(M,{class:"empty-hint"},{default:a(()=>[d('点击右下角"+"添加好友开始聊天')]),_:1})]),_:1})),o(" 浮动添加按钮 "),i(w,{class:"floating-add-btn",onClick:y.showAddMenu},{default:a(()=>[i(M,{class:"floating-icon"},{default:a(()=>[d("+")]),_:1})]),_:1},8,["onClick"]),o(" 添加菜单弹窗 "),p.showAddMenuModal?(t(),s(w,{key:2,class:"add-menu-modal",onClick:y.hideAddMenu},{default:a(()=>[i(w,{class:"add-menu",onClick:_[0]||(_[0]=h(()=>{},["stop"]))},{default:a(()=>[i(w,{class:"add-menu-item",onClick:y.addFriend},{default:a(()=>[i(w,{class:"menu-item-content"},{default:a(()=>[i(M,{class:"add-menu-icon"},{default:a(()=>[d("👤")]),_:1}),i(M,{class:"add-menu-text"},{default:a(()=>[d("添加好友")]),_:1})]),_:1})]),_:1},8,["onClick"]),i(w,{class:"add-menu-item",onClick:y.openFriendManage},{default:a(()=>[i(w,{class:"menu-item-content"},{default:a(()=>[i(M,{class:"add-menu-icon"},{default:a(()=>[d("👥")]),_:1}),i(M,{class:"add-menu-text"},{default:a(()=>[d("好友申请")]),_:1}),p.pendingRequestCount>0?(t(),s(M,{key:0,class:"badge"},{default:a(()=>[d(r(p.pendingRequestCount>99?"99+":p.pendingRequestCount),1)]),_:1})):o("v-if",!0)]),_:1})]),_:1},8,["onClick"]),i(w,{class:"add-menu-item",onClick:y.testUnreadMessage},{default:a(()=>[i(w,{class:"menu-item-content"},{default:a(()=>[i(M,{class:"add-menu-icon"},{default:a(()=>[d("🧪")]),_:1}),i(M,{class:"add-menu-text"},{default:a(()=>[d("测试未读消息")]),_:1})]),_:1})]),_:1},8,["onClick"]),i(w,{class:"add-menu-item",onClick:y.logout},{default:a(()=>[i(w,{class:"menu-item-content"},{default:a(()=>[i(M,{class:"add-menu-icon"},{default:a(()=>[d("🚪")]),_:1}),i(M,{class:"add-menu-text"},{default:a(()=>[d("退出登录")]),_:1})]),_:1})]),_:1},8,["onClick"])]),_:1})]),_:1},8,["onClick"])):o("v-if",!0)]),_:1})}],["__scopeId","data-v-5fde8cf0"]]);export{M as default};

{"version": 3, "sources": ["../src/index.js"], "names": ["configure", "configuration", "jimpInstance", "<PERSON><PERSON>", "jimpConfig", "has<PERSON><PERSON><PERSON>", "encoders", "decoders", "constants", "addToConfig", "newConfig", "Object", "entries", "for<PERSON>ach", "key", "value", "addImageType", "typeModule", "type", "Array", "isArray", "mime", "addType", "mimeType", "addPlugin", "pluginModule", "plugin", "jimp<PERSON>v<PERSON><PERSON><PERSON>", "types", "plugins"], "mappings": ";;;;;;;;;;;;;;;;;AAAA;;;;;;AAOe,SAASA,SAAT,CAAmBC,aAAnB,EAAuD;AAAA,MAArBC,YAAqB,uEAANC,gBAAM;AACpE,MAAMC,UAAU,GAAG;AACjBC,IAAAA,QAAQ,EAAE,EADO;AAEjBC,IAAAA,QAAQ,EAAE,EAFO;AAGjBC,IAAAA,QAAQ,EAAE,EAHO;AAIjB,aAAO,EAJU;AAKjBC,IAAAA,SAAS,EAAE;AALM,GAAnB;;AAQA,WAASC,WAAT,CAAqBC,SAArB,EAAgC;AAC9BC,IAAAA,MAAM,CAACC,OAAP,CAAeF,SAAf,EAA0BG,OAA1B,CAAkC,gBAAkB;AAAA;AAAA,UAAhBC,GAAgB;AAAA,UAAXC,KAAW;;AAClDX,MAAAA,UAAU,CAACU,GAAD,CAAV,qBACKV,UAAU,CAACU,GAAD,CADf,MAEKC,KAFL;AAID,KALD;AAMD;;AAED,WAASC,YAAT,CAAsBC,UAAtB,EAAkC;AAChC,QAAMC,IAAI,GAAGD,UAAU,EAAvB;;AAEA,QAAIE,KAAK,CAACC,OAAN,CAAcF,IAAI,CAACG,IAAnB,CAAJ,EAA8B;AAC5BC,sEAAWJ,IAAI,CAACG,IAAhB;AACD,KAFD,MAEO;AACLV,MAAAA,MAAM,CAACC,OAAP,CAAeM,IAAI,CAACG,IAApB,EAA0BR,OAA1B,CAAkC,UAAAU,QAAQ;AAAA,eAAID,gEAAWC,QAAX,EAAJ;AAAA,OAA1C;AACD;;AAED,WAAOL,IAAI,CAACG,IAAZ;AACAZ,IAAAA,WAAW,CAACS,IAAD,CAAX;AACD;;AAED,WAASM,SAAT,CAAmBC,YAAnB,EAAiC;AAC/B,QAAMC,MAAM,GAAGD,YAAY,CAACE,kBAAD,CAAZ,IAA8B,EAA7C;;AACA,QAAI,CAACD,MAAM,SAAP,IAAiB,CAACA,MAAM,CAAClB,SAA7B,EAAwC;AACtC;AACAC,MAAAA,WAAW,CAAC;AAAE,iBAAOiB;AAAT,OAAD,CAAX;AACD,KAHD,MAGO;AACLjB,MAAAA,WAAW,CAACiB,MAAD,CAAX;AACD;AACF;;AAED,MAAIzB,aAAa,CAAC2B,KAAlB,EAAyB;AACvB3B,IAAAA,aAAa,CAAC2B,KAAd,CAAoBf,OAApB,CAA4BG,YAA5B;AAEAd,IAAAA,YAAY,CAACK,QAAb,qBACKL,YAAY,CAACK,QADlB,MAEKH,UAAU,CAACG,QAFhB;AAIAL,IAAAA,YAAY,CAACI,QAAb,qBACKJ,YAAY,CAACI,QADlB,MAEKF,UAAU,CAACE,QAFhB;AAIAJ,IAAAA,YAAY,CAACG,QAAb,qBACKH,YAAY,CAACG,QADlB,MAEKD,UAAU,CAACC,QAFhB;AAID;;AAED,MAAIJ,aAAa,CAAC4B,OAAlB,EAA2B;AACzB5B,IAAAA,aAAa,CAAC4B,OAAd,CAAsBhB,OAAtB,CAA8BW,SAA9B;AACD;;AAED,4BAAepB,UAAU,SAAzB,EAAiCF,YAAjC;AACA,0BAAaE,UAAU,CAACI,SAAxB,EAAmCN,YAAnC;AAEA,SAAOC,gBAAP;AACD", "sourcesContent": ["import Jimp, {\n  addType,\n  addJimpMethods,\n  addConstants,\n  jimpEvChange\n} from '@jimp/core';\n\nexport default function configure(configuration, jimpInstance = Jimp) {\n  const jimpConfig = {\n    hasAlpha: {},\n    encoders: {},\n    decoders: {},\n    class: {},\n    constants: {}\n  };\n\n  function addToConfig(newConfig) {\n    Object.entries(newConfig).forEach(([key, value]) => {\n      jimpConfig[key] = {\n        ...jimpConfig[key],\n        ...value\n      };\n    });\n  }\n\n  function addImageType(typeModule) {\n    const type = typeModule();\n\n    if (Array.isArray(type.mime)) {\n      addType(...type.mime);\n    } else {\n      Object.entries(type.mime).forEach(mimeType => addType(...mimeType));\n    }\n\n    delete type.mime;\n    addToConfig(type);\n  }\n\n  function addPlugin(pluginModule) {\n    const plugin = pluginModule(jimpEvChange) || {};\n    if (!plugin.class && !plugin.constants) {\n      // Default to class function\n      addToConfig({ class: plugin });\n    } else {\n      addToConfig(plugin);\n    }\n  }\n\n  if (configuration.types) {\n    configuration.types.forEach(addImageType);\n\n    jimpInstance.decoders = {\n      ...jimpInstance.decoders,\n      ...jimpConfig.decoders\n    };\n    jimpInstance.encoders = {\n      ...jimpInstance.encoders,\n      ...jimpConfig.encoders\n    };\n    jimpInstance.hasAlpha = {\n      ...jimpInstance.hasAlpha,\n      ...jimpConfig.hasAlpha\n    };\n  }\n\n  if (configuration.plugins) {\n    configuration.plugins.forEach(addPlugin);\n  }\n\n  addJimpMethods(jimpConfig.class, jimpInstance);\n  addConstants(jimpConfig.constants, jimpInstance);\n\n  return Jimp;\n}\n"], "file": "index.js"}